from rest_framework import serializers
from base.models import (
    User,
    Client,
    CaseModel,
    ProviderModel,
    Patient,
    Assurance,
    FileCase,
    FileComment,
    Comment,
    HistoryUser,
    ProviderService,
    ProviderCaseService,
    CaseStatusModel,
    ProviderInfo,
    AssistanceModel
)
from datetime import datetime
from dateutil.relativedelta import relativedelta
from django.utils import timezone
from datetime import timedelta
import os


class UserSerializer(serializers.ModelSerializer):
    is_online = serializers.SerializerMethodField(read_only=True)

    class Meta:
        model = User
        fields = [
            "id",
            "phone",
            "password",
            "role",
            "email",
            "first_name",
            "last_name",
            "full_name",
            "photo",
            "is_active",
            "token_fb",
            "login_by",
            "login_by",
            "created_at",
            "updated_at",
            "is_online",
            "last_active",
        ]
        extra_kwargs = {
            "password": {"write_only": True, "required": True},
            "id": {"read_only": True},
            "is_active": {"read_only": True},
            "email": {"required": True},
        }

    def create(self, validated_data):
        user = User.objects.create_user(**validated_data)
        return user

    def get_is_online(self, obj):
        if obj.last_active:
            return timezone.now() - obj.last_active < timedelta(minutes=30)
        return False


class PatientSerializer(serializers.ModelSerializer):

    class Meta:
        model = Patient
        fields = "__all__"


class ProviderInfoSerializer(serializers.ModelSerializer):

    class Meta:
        model = ProviderInfo
        fields = "__all__"


class HistoryUserSerializer(serializers.ModelSerializer):

    class Meta:
        model = HistoryUser
        fields = "__all__"


class ClientSerializer(serializers.ModelSerializer):

    class Meta:
        model = Client
        fields = "__all__"


class AssistanceModelSerializer(serializers.ModelSerializer):
    provider_services = serializers.SerializerMethodField(read_only=True)
    created_user = serializers.SerializerMethodField(read_only=True)

    class Meta:
        model = AssistanceModel
        fields = "__all__"
        
    def get_provider_services(self, obj):
        try:
            providerServices = ProviderCaseService.objects.filter(
                assistance=obj, is_deleted=False
            ).order_by("-created_at")
            serializer = ProviderCaseServiceSerializer(providerServices, many=True)
            return serializer.data
        except Exception as e:
            return []
    
    def get_created_user(self, obj):
        try:
            if obj.created_by is None:
                created_by = None
            else:
                created_by = UserSerializer(obj.created_by, many=False).data
        except Exception as e:
            created_by = None
        return created_by
        
    

class ProviderCaseServiceSerializer(serializers.ModelSerializer):
    provider = serializers.SerializerMethodField(read_only=True)
    provider_service = serializers.SerializerMethodField(read_only=True)

    class Meta:
        model = ProviderCaseService
        fields = "__all__"

    def get_provider_service(self, obj):
        try:
            if obj.provider_service is None:
                provider_service = None
            else:
                provider_service = ProviderServiceSerializer(
                    obj.provider_service, many=False
                ).data
        except Exception as e:
            provider_service = None
        return provider_service

    def get_provider(self, obj):
        try:
            if obj.provider is None:
                provider = None
            else:
                provider = ProviderModelSerializer(obj.provider, many=False).data
        except Exception as e:
            provider = None
        return provider


# Lightweight serializers for map display
class ProviderInfoMapSerializer(serializers.ModelSerializer):
    class Meta:
        model = ProviderInfo
        fields = ['info_type', 'info_value']


class ProviderServiceMapSerializer(serializers.ModelSerializer):
    class Meta:
        model = ProviderService
        fields = ['id', 'service_type', 'service_specialist']


class ProviderModelMapSerializer(serializers.ModelSerializer):
    provider_infos = serializers.SerializerMethodField(read_only=True)
    services = serializers.SerializerMethodField(read_only=True)

    class Meta:
        model = ProviderModel
        fields = [
            'id', 'full_name', 'service_type', 'service_specialist',
            'country', 'city', 'address', 'location_x', 'location_y',
            'provider_infos', 'services',"payment_method"
        ]

    def get_provider_infos(self, obj):
        try:
            items = ProviderInfo.objects.filter(provider=obj).order_by("-created_at")
            serializer = ProviderInfoMapSerializer(items, many=True)
            return serializer.data
        except Exception as e:
            return []

    def get_services(self, obj):
        try:
            items = ProviderService.objects.filter(
                provider=obj, is_deleted=False
            ).order_by("-created_at")
            serializer = ProviderServiceMapSerializer(items, many=True)
            return serializer.data
        except Exception as e:
            return []


class ProviderCaseServiceMapSerializer(serializers.ModelSerializer):
    provider = serializers.SerializerMethodField(read_only=True)
    provider_service = serializers.SerializerMethodField(read_only=True)

    class Meta:
        model = ProviderCaseService
        fields = ['id', 'service_type', 'service_specialist', 'provider_date', 'provider', 'provider_service']

    def get_provider_service(self, obj):
        try:
            if obj.provider_service is None:
                return None
            else:
                return ProviderServiceMapSerializer(obj.provider_service, many=False).data
        except Exception as e:
            return None

    def get_provider(self, obj):
        try:
            if obj.provider is None:
                return None
            else:
                return ProviderModelMapSerializer(obj.provider, many=False).data
        except Exception as e:
            return None


class CaseModelMapSerializer(serializers.ModelSerializer):
    provider_services = serializers.SerializerMethodField(read_only=True)

    class Meta:
        model = CaseModel
        fields = [
            'id', 'case_date', 'case_type', 'case_type_item', 'case_description',
            'status_coordination', 'appointment_date', 'start_date', 'end_date',
            'service_location', 'assurance_number', 'policy_number', 'assurance_status','provider_services'
        ]

    def get_provider_services(self, obj):
        try:
            providerServices = ProviderCaseService.objects.filter(
                case=obj, is_deleted=False
            ).order_by("-created_at")
            serializer = ProviderCaseServiceMapSerializer(providerServices, many=True)
            return serializer.data
        except Exception as e:
            return []


class CaseDashboardSerializer(serializers.ModelSerializer):
    """
    Lightweight serializer for dashboard table view that only includes
    fields needed for the dashboard display
    """
    patient = serializers.SerializerMethodField(read_only=True)
    assurance = serializers.SerializerMethodField(read_only=True)
    provider_services = serializers.SerializerMethodField(read_only=True)
    case_status = serializers.SerializerMethodField(read_only=True)

    class Meta:
        model = CaseModel
        fields = [
            'id', 'case_date', 'case_type', 'patient',
            'assurance', 'case_status','provider_services'
        ]

    def get_patient(self, obj):
        try:
            if obj.patient is None:
                return None
            else:
                # Only return minimal patient data needed for dashboard
                return {
                    'id': obj.patient.id,
                    'full_name': obj.patient.full_name
                }
        except Exception as e:
            return None

    def get_assurance(self, obj):
        try:
            if obj.assurance is None:
                return None
            else:
                # Only return minimal assurance data needed for dashboard
                return {
                    'id': obj.assurance.id,
                    'assurance_name': obj.assurance.assurance_name
                }
        except Exception as e:
            return None

    def get_provider_services(self, obj):
        try:
            # Just count the number of providers for the dashboard view
            count = ProviderCaseService.objects.filter(
                case=obj, is_deleted=False
            ).count()
            return count
        except Exception as e:
            return 0

    def get_case_status(self, obj):
        try:
            statuses = CaseStatusModel.objects.filter(case=obj).order_by("-created_at")
            # Only return the status values, not the full objects
            return [{'status_coordination': status.status_coordination} for status in statuses]
        except Exception as e:
            return []


class CaseScreenSerializer(serializers.ModelSerializer):
    """
    Lightweight serializer for CaseScreen table view that only includes
    fields needed for the case list display
    """
    patient = serializers.SerializerMethodField(read_only=True)
    assurance = serializers.SerializerMethodField(read_only=True)

    class Meta:
        model = CaseModel
        fields = [
            'id', 'case_date', 'patient', 'assurance'
        ]

    def get_patient(self, obj):
        try:
            if obj.patient is None:
                return None
            else:
                # Only return patient data needed for CaseScreen
                return {
                    'id': obj.patient.id,
                    'full_name': obj.patient.full_name,
                    'patient_country': obj.patient.patient_country,
                    'patient_city': obj.patient.patient_city
                }
        except Exception as e:
            return None

    def get_assurance(self, obj):
        try:
            if obj.assurance is None:
                return None
            else:
                # Only return minimal assurance data needed for CaseScreen
                return {
                    'id': obj.assurance.id,
                    'assurance_name': obj.assurance.assurance_name
                }
        except Exception as e:
            return None


class ProviderDashboardSerializer(serializers.ModelSerializer):
    """
    Ultra-lightweight serializer for provider dropdown in dashboard
    Only includes the minimal fields needed for the filter dropdown
    Optimized for performance - no nested queries or additional data
    """
    class Meta:
        model = ProviderModel
        fields = ['id', 'full_name']

    def to_representation(self, instance):
        """
        Override to ensure only essential data is returned
        """
        return {
            'id': instance.id,
            'full_name': instance.full_name or f"{instance.first_name} {instance.last_name}".strip()
        }


class ProviderListDashboardSerializer(serializers.ModelSerializer):
    """
    Lightweight serializer for provider list views in dashboard
    Includes essential fields for provider display without heavy nested data
    """
    services_count = serializers.SerializerMethodField(read_only=True)
    primary_service = serializers.SerializerMethodField(read_only=True)

    class Meta:
        model = ProviderModel
        fields = [
            'id', 'full_name', 'email', 'phone', 'city', 'country',
            'services_count', 'primary_service', 'created_at'
        ]

    def get_services_count(self, obj):
        """
        Return count of services instead of full service objects
        """
        try:
            return ProviderService.objects.filter(
                provider=obj, is_deleted=False
            ).count()
        except Exception:
            return 0

    def get_primary_service(self, obj):
        """
        Return only the primary/first service type for quick display
        """
        try:
            service = ProviderService.objects.filter(
                provider=obj, is_deleted=False
            ).first()
            return service.service_type if service else None
        except Exception:
            return None


class ProviderMapScreenSerializer(serializers.ModelSerializer):
    """
    Optimized serializer specifically for ProvidersMapScreen
    Only includes fields needed for map display and provider details popup
    Minimizes data transfer and improves performance
    """
    provider_infos = serializers.SerializerMethodField(read_only=True)
    services = serializers.SerializerMethodField(read_only=True)

    class Meta:
        model = ProviderModel
        fields = [
            'id', 'full_name', 'country', 'city', 'address',
            'location_x', 'location_y', 'payment_method',
            'provider_infos', 'services'
        ]

    def get_provider_infos(self, obj):
        """
        Return only essential contact info for map popup
        """
        try:
            items = ProviderInfo.objects.filter(provider=obj).order_by("-created_at")
            return [
                {
                    'info_type': item.info_type,
                    'info_value': item.info_value
                }
                for item in items
            ]
        except Exception:
            return []

    def get_services(self, obj):
        """
        Return only essential service info for map popup
        """
        try:
            items = ProviderService.objects.filter(
                provider=obj, is_deleted=False
            ).order_by("-created_at")
            return [
                {
                    'id': item.id,
                    'service_type': item.service_type,
                    'service_specialist': item.service_specialist
                }
                for item in items
            ]
        except Exception:
            return []


class CaseModelSerializer(serializers.ModelSerializer):
    patient = serializers.SerializerMethodField(read_only=True)
    provider = serializers.SerializerMethodField(read_only=True)
    assistance_services = serializers.SerializerMethodField(read_only=True)
    provider_services = serializers.SerializerMethodField(read_only=True)
    assurance = serializers.SerializerMethodField(read_only=True)
    coordinator_user = serializers.SerializerMethodField(read_only=True)
    created_user = serializers.SerializerMethodField(read_only=True)
    medical_reports = serializers.SerializerMethodField(read_only=True)
    upload_invoices = serializers.SerializerMethodField(read_only=True)
    upload_authorization = serializers.SerializerMethodField(read_only=True)
    case_status = serializers.SerializerMethodField(read_only=True)

    class Meta:
        model = CaseModel
        fields = "__all__"

    def get_patient(self, obj):
        try:
            if obj.patient is None:
                patient = None
            else:
                patient = PatientSerializer(obj.patient, many=False).data
        except Exception as e:
            patient = None
        return patient

    def get_coordinator_user(self, obj):
        try:
            if obj.coordinator is None:
                coordinator = None
            else:
                coordinator = UserSerializer(obj.coordinator, many=False).data
        except Exception as e:
            coordinator = None
        return coordinator

    def get_created_user(self, obj):
        try:
            if obj.created_by is None:
                created_by = None
            else:
                created_by = UserSerializer(obj.created_by, many=False).data
        except Exception as e:
            created_by = None
        return created_by

    def get_provider(self, obj):
        try:
            if obj.provider is None:
                provider = None
            else:
                provider = ProviderModelSerializer(obj.provider, many=False).data
        except Exception as e:
            provider = None
        return provider

    def get_assurance(self, obj):
        try:
            if obj.assurance is None:
                assurance = None
            else:
                assurance = AssuranceSerializer(obj.assurance, many=False).data
        except Exception as e:
            assurance = None
        return assurance

    def get_assistance_services(self, obj):
        try:
            assistanceServices = AssistanceModel.objects.filter(
                case=obj, is_deleted=False
            ).order_by("-created_at")
            serializer = AssistanceModelSerializer(assistanceServices, many=True)
            return serializer.data
        except Exception as e:
            return []
    
    def get_provider_services(self, obj):
        try:
            # return []
            providerServices = ProviderCaseService.objects.filter(
                case=obj, is_deleted=False
            ).order_by("-created_at")
            serializer = ProviderCaseServiceSerializer(providerServices, many=True)
            return serializer.data
        except Exception as e:
            return []

    def get_case_status(self, obj):
        try:
            status = CaseStatusModel.objects.filter(case=obj).order_by("-created_at")
            serializer = CaseStatusModelSerializer(status, many=True)
            return serializer.data
        except Exception as e:
            return []

    def get_medical_reports(self, obj):
        try:
            medicalReports = FileCase.objects.filter(
                case=obj, type="Initial Medical Reports", is_deleted=False
            ).order_by("-created_at")
            serializer = FileCaseSerializer(medicalReports, many=True)
            return serializer.data
        except Exception as e:
            return []

    def get_upload_invoices(self, obj):
        try:
            medicalReports = FileCase.objects.filter(
                case=obj, type="Upload Invoice", is_deleted=False
            ).order_by("-created_at")
            serializer = FileCaseSerializer(medicalReports, many=True)
            return serializer.data
        except Exception as e:
            return []

    def get_upload_authorization(self, obj):
        try:
            medicalReports = FileCase.objects.filter(
                case=obj, type="Upload Authorization Documents", is_deleted=False
            ).order_by("-created_at")
            serializer = FileCaseSerializer(medicalReports, many=True)
            return serializer.data
        except Exception as e:
            return []


class CommentSerializer(serializers.ModelSerializer):
    files = serializers.SerializerMethodField(read_only=True)
    coordinator = serializers.SerializerMethodField(read_only=True)
    can_delete = serializers.SerializerMethodField(read_only=True)

    class Meta:
        model = Comment
        fields = "__all__"

    def get_can_delete(self, obj):
        request = self.context.get("request", None)
        if request and (request.user.role in [1, 2]):
        # if request and (request.user == obj.user or request.user.role in [1, 2]):
            return True
        return False

    def get_files(self, obj):
        try:
            files = FileComment.objects.filter(comment=obj, is_deleted=False).order_by(
                "-created_at"
            )
            serializer = FileCommentSerializer(files, many=True)
            return serializer.data
        except Exception as e:
            return []

    def get_coordinator(self, obj):
        try:
            if obj.user is None:
                user = None
            else:
                user = UserSerializer(obj.user, many=False).data
        except Exception as e:
            user = None
        return user


class ProviderModelSerializer(serializers.ModelSerializer):
    provider_infos = serializers.SerializerMethodField(read_only=True)
    services = serializers.SerializerMethodField(read_only=True)

    class Meta:
        model = ProviderModel
        fields = "__all__"

    def get_provider_infos(self, obj):
        try:
            items = ProviderInfo.objects.filter(provider=obj).order_by("-created_at")
            serializer = ProviderInfoSerializer(items, many=True)
            return serializer.data
        except Exception as e:
            print(e)
            return []

    def get_services(self, obj):

        try:
            items = ProviderService.objects.filter(
                provider=obj, is_deleted=False
            ).order_by("-created_at")
            serializer = ProviderServiceSerializer(items, many=True)
            return serializer.data
        except Exception as e:
            print(e)
            return []


class ProviderServiceSerializer(serializers.ModelSerializer):

    class Meta:
        model = ProviderService
        fields = "__all__"


class CaseStatusModelSerializer(serializers.ModelSerializer):

    class Meta:
        model = CaseStatusModel
        fields = "__all__"


class AssuranceSerializer(serializers.ModelSerializer):

    class Meta:
        model = Assurance
        fields = "__all__"


class AssuranceDashboardSerializer(serializers.ModelSerializer):
    """
    Ultra-lightweight serializer for insurance dropdown in dashboard
    Only includes the minimal fields needed for the filter dropdown
    Optimized for performance - no nested queries or additional data
    """
    class Meta:
        model = Assurance
        fields = ['id', 'assurance_name']

    def to_representation(self, instance):
        """
        Override to ensure only essential data is returned
        """
        return {
            'id': instance.id,
            'assurance_name': instance.assurance_name or 'Unknown Insurance'
        }


class AssuranceListDashboardSerializer(serializers.ModelSerializer):
    """
    Lightweight serializer for insurance list views in dashboard
    Includes essential fields for insurance display without heavy nested data
    """
    cases_count = serializers.SerializerMethodField(read_only=True)

    class Meta:
        model = Assurance
        fields = [
            'id', 'assurance_name', 'assurance_country', 'assurance_phone',
            'assurance_email', 'cases_count', 'created_at'
        ]

    def get_cases_count(self, obj):
        """
        Return count of cases instead of full case objects
        """
        try:
            from base.models import CaseModel
            return CaseModel.objects.filter(
                assurance=obj, is_deleted=False
            ).count()
        except Exception:
            return 0


class CoordinatorDashboardSerializer(serializers.ModelSerializer):
    """
    Ultra-lightweight serializer for coordinator dropdown in AddCaseScreen
    Only includes the minimal fields needed for the coordinator dropdown
    Optimized for performance - no nested queries or additional data
    """
    class Meta:
        model = User
        fields = ['id', 'full_name']

    def to_representation(self, instance):
        """
        Override to ensure only essential data is returned
        """
        return {
            'id': instance.id,
            'full_name': instance.full_name or f"{instance.first_name} {instance.last_name}".strip()
        }


class ProviderEditCaseSerializer(serializers.ModelSerializer):
    """
    Optimized serializer for provider dropdown in EditCaseScreen
    Includes id, full_name, and services for provider service selection
    Lightweight services data with only essential fields
    """
    services = serializers.SerializerMethodField(read_only=True)

    class Meta:
        model = ProviderModel
        fields = ['id', 'full_name', 'services']

    def get_services(self, obj):
        """
        Return only essential service info for provider service dropdown
        """
        try:
            items = ProviderService.objects.filter(
                provider=obj, is_deleted=False
            ).order_by("-created_at")
            return [
                {
                    'id': item.id,
                    'service_type': item.service_type,
                    'service_specialist': item.service_specialist
                }
                for item in items
            ]
        except Exception:
            return []


class FileCaseSerializer(serializers.ModelSerializer):
    file_name = serializers.SerializerMethodField(read_only=True)
    file_size = serializers.SerializerMethodField(read_only=True)

    class Meta:
        model = FileCase
        fields = "__all__"

    def get_file_name(self, obj):
        try:
            if obj.file:
                full_file_path = obj.file.name
                file_name = os.path.basename(full_file_path)
                return file_name
            else:
                return ""
        except Exception as e:
            return ""

    def get_file_size(self, obj):
        try:
            if obj.file:
                size = os.path.getsize(obj.file.path) / (1024 * 1024)
                return f"{size:.2f}"
            else:
                return ""
        except Exception as e:
            return ""


class FileCommentSerializer(serializers.ModelSerializer):
    file_name = serializers.SerializerMethodField(read_only=True)
    file_size = serializers.SerializerMethodField(read_only=True)

    class Meta:
        model = FileComment
        fields = "__all__"

    def get_file_name(self, obj):
        try:
            if obj.file:
                full_file_path = obj.file.name
                file_name = os.path.basename(full_file_path)
                return file_name
            else:
                return ""
        except Exception as e:
            return ""

    def get_file_size(self, obj):
        try:
            if obj.file:
                size = os.path.getsize(obj.file.path) / (1024 * 1024)
                return f"{size:.2f}"
            else:
                return ""
        except Exception as e:
            return ""
