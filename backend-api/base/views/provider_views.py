from rest_framework.response import Response
from rest_framework.decorators import api_view, permission_classes
from rest_framework_simplejwt.tokens import RefreshToken
from rest_framework.permissions import IsAuthenticated
from django.core.exceptions import ObjectDoesNotExist

from base.models import User, ProviderModel, ProviderService, ProviderInfo
from django.contrib.auth import authenticate
from base.permissions import Is<PERSON>ser, IsAdmin, IsSuperAdmin
from django.contrib.auth.hashers import make_password
from django.core.paginator import Paginator, EmptyPage, PageNotAnInteger

from base.serializers import (
    ClientSerializer,
    ProviderModelSerializer,
    ProviderDashboardSerializer,
    ProviderListDashboardSerializer,
    ProviderMapScreenSerializer,
    ProviderServiceSerializer,
)


from math import radians, cos, sin, sqrt, atan2


def haversine(lat1, lon1, lat2, lon2):
    """
    Calculate the great-circle distance between two points
    on the Earth (specified in decimal degrees).
    """
    # Radius of the Earth in kilometers
    R = 6371.0

    # Convert latitude and longitude from degrees to radians
    lat1, lon1, lat2, lon2 = map(radians, [lat1, lon1, lat2, lon2])

    # Differences in coordinates
    dlat = lat2 - lat1
    dlon = lon2 - lon1

    # Haversine formula
    a = sin(dlat / 2) ** 2 + cos(lat1) * cos(lat2) * sin(dlon / 2) ** 2
    c = 2 * atan2(sqrt(a), sqrt(1 - a))

    # Distance in kilometers
    return R * c


# update
@api_view(["GET"])
def update_provider_service(request):
    providers = ProviderModel.objects.all()

    count = 0

    # Iterate through each provider and create entries in ProviderService
    for provider in providers:
        # Check if an entry already exists
        if not ProviderService.objects.filter(
            provider=provider,
            service_type=provider.service_type,
            service_specialist=provider.service_specialist,
        ).exists():
            # Create the entry if it doesn't exist
            ProviderService.objects.create(
                provider=provider,
                service_type=provider.service_type,
                service_specialist=provider.service_specialist,
            )
            count += 1

    return Response(
        {
            "detail": "Provider services updated successfully.",
            "new_entries": count,
            "total_providers": len(providers),
        }
    )


# update provider
@api_view(["PUT"])
@permission_classes([IsAuthenticated])
def update_provider_detail(request, pk):

    try:
        providerModel = ProviderModel.objects.get(pk=pk)
        if providerModel:
            providerModel.first_name = request.data["first_name"]
            providerModel.last_name = request.data["last_name"]
            providerModel.full_name = request.data["full_name"]
            # providerModel.service_type = request.data["service_type"]
            # providerModel.service_specialist = request.data["service_specialist"]
            providerModel.email = request.data["email"]
            providerModel.second_email = request.data["second_email"]
            providerModel.phone = request.data["phone"]
            providerModel.second_phone = request.data["second_phone"]
            providerModel.address = request.data["address"]
            providerModel.country = request.data["country"]
            providerModel.city = request.data["city"]
            providerModel.location_x = request.data["location_x"]
            providerModel.location_y = request.data["location_y"]
            providerModel.payment_method = request.data["payment_method"]
            providerModel.provider_note = request.data["provider_note"]

            providerModel.save()

            for service_data in request.data["services"]:
                ProviderService.objects.create(
                    provider=providerModel,
                    service_type=service_data["service_type"],
                    service_specialist=service_data["service_specialist"],
                )

            ProviderInfo.objects.filter(provider=providerModel).delete()
            for contact_data in request.data["contacts_info"]:
                ProviderInfo.objects.create(
                    provider=providerModel,
                    info_type=contact_data["info_type"],
                    info_value=contact_data["info_value"],
                )

            for service_deleted_ids in request.data["service_deleted"]:
                ProviderService.objects.filter(
                    pk=service_deleted_ids, provider=providerModel
                ).delete()

            return Response({"detail": "This Provider has been updated successfully."})
        else:
            return Response(
                {"detail": "Sorry, This Provider does not exist"}, status=401
            )
    except ObjectDoesNotExist:
        return Response({"detail": "Sorry, This Provider does not exist"}, status=401)
    except Exception as error:
        print(error)
        return Response(
            {
                "detail": "Sorry, an error occurred while sending the data. Please try again."
            },
            status=401,
        )


# get provider info
@api_view(["GET"])
@permission_classes([IsAuthenticated])
def provider_detail(request, pk):
    try:
        provider = ProviderModel.objects.get(pk=pk)
        if provider:
            serializer = ProviderModelSerializer(provider, many=False)
            return Response(serializer.data, status=200)
        else:
            return Response(
                {"detail": "Sorry, this Provider does not exist"}, status=401
            )

    except ObjectDoesNotExist:
        return Response({"detail": "Sorry, this Provider does not exist"}, status=401)
    except Exception as error:
        print(error)
        return Response(
            {
                "detail": "Sorry, an error occurred while sending the data. Please try again."
            },
            status=401,
        )


# delete Provider
@api_view(["DELETE"])
@permission_classes([IsAuthenticated])
def delete_provider(request, pk):
    try:
        provider = ProviderModel.objects.get(pk=pk)
        if provider:
            # change
            provider.is_deleted = True
            provider.save()
            return Response({"detail": "This Provider has been successfully deleted."})
        else:
            return Response(
                {"detail": "Sorry, This Provider does not exist"}, status=401
            )
    except ObjectDoesNotExist:
        return Response({"detail": "Sorry, This Provider does not exist"}, status=401)
    except Exception as error:

        return Response(
            {
                "detail": "Sorry, an error occurred while sending the data. Please try again."
            },
            status=401,
        )


@api_view(["Post"])
@permission_classes([IsAuthenticated])
def create_new_provider(request):

    try:
        provider = ProviderModel.objects.create(
            created_by=request.user,
            first_name=request.data["first_name"],
            last_name=request.data["last_name"],
            full_name=request.data["full_name"],
            # service_type=request.data["service_type"],
            # service_specialist=request.data["service_specialist"],
            email=request.data["email"],
            second_email=request.data["second_email"],
            phone=request.data["phone"],
            second_phone=request.data["second_phone"],
            address=request.data["address"],
            country=request.data["country"],
            city=request.data["city"],
            location_x=request.data["location_x"],
            location_y=request.data["location_y"],
            provider_note=request.data["provider_note"],
            payment_method=request.data["payment_method"],
            #  contacts_info: infoProvider,
        )
        if provider:
            for service_data in request.data["services"]:
                ProviderService.objects.create(
                    provider=provider,
                    service_type=service_data["service_type"],
                    service_specialist=service_data["service_specialist"],
                )

            for contact_data in request.data["contacts_info"]:
                ProviderInfo.objects.create(
                    provider=provider,
                    info_type=contact_data["info_type"],
                    info_value=contact_data["info_value"],
                )

        return Response(
            {"detail": "This Provider has been added successfully"}, status=200
        )

    except Exception as error:
        print(error)
        return Response(
            {
                "detail": "Sorry, an error occurred while sending the data. Please try again."
            },
            status=401,
        )


@api_view(["GET"])
@permission_classes([IsAuthenticated])
def provider_detail(request, pk):
    try:
        providerModel = ProviderModel.objects.get(pk=pk)
        if providerModel:
            serializer = ProviderModelSerializer(providerModel, many=False)
            return Response(serializer.data, status=200)
        else:
            return Response(
                {"detail": "Sorry, this Provider does not exist"}, status=401
            )

    except ObjectDoesNotExist:
        return Response({"detail": "Sorry, this Provider does not exist"}, status=401)
    except Exception as error:
        print(error)
        return Response(
            {
                "detail": "Sorry, an error occurred while sending the data. Please try again."
            },
            status=401,
        )


# get list providers with optimized serializers for dashboard
@api_view(["GET"])
@permission_classes([IsAuthenticated])
def get_list_providers(request):
    """
    Get list of providers with different serialization levels:
    - isdashboard=true: Ultra-lightweight for dropdowns (id, full_name only)
    - isdashboardlist=true: Lightweight for dashboard lists (essential fields only)
    - ismapscreen=true: Optimized for ProvidersMapScreen (map markers and popup data)
    - iseditcase=true: Optimized for EditCaseScreen (id, full_name, services)
    - default: Full serialization with all related data
    """
    filter_name = request.GET.get("filtername", "")
    filter_type = request.GET.get("filtertype", "")
    filter_city = request.GET.get("filtercity", "")
    filter_country = request.GET.get("filtercountry", "")
    location_x = request.GET.get("locationx", "0")
    location_y = request.GET.get("locationy", "0")
    range_max = request.GET.get("rangemax", "")
    is_dashboard = request.GET.get("isdashboard")
    is_dashboard_list = request.GET.get("isdashboardlist")
    is_map_screen = request.GET.get("ismapscreen")
    is_edit_case = request.GET.get("iseditcase")

    providers = ProviderModel.objects.filter(is_deleted=False).order_by("-created_at")

    if filter_name:
        providers = providers.filter(full_name__icontains=filter_name)

    if filter_type:
        providers = providers.filter(service_type__icontains=filter_type)

    if filter_country:
        providers = providers.filter(country=filter_country)

    if filter_city:
        range_max = float(request.GET.get("rangemax", "0"))
        if (
            location_x
            and location_y
            and float(location_x) != 0
            and float(location_y) != 0
            and range_max > 0
        ):
            # Convert user-provided location to float
            location_x = float(location_x)
            location_y = float(location_y)

            # Filter providers within the range or city matches
            filtered_providers = []
            for provider in providers:
                try:
                    provider_x = float(provider.location_x or 0)
                    provider_y = float(provider.location_y or 0)

                    # Calculate distance
                    distance = haversine(location_y, location_x, provider_y, provider_x)

                    # Check if within range or city matches
                    if (
                        distance <= range_max
                        or filter_city.lower() in (provider.city or "").lower()
                    ):
                        filtered_providers.append(provider)
                except (ValueError, TypeError):
                    # Skip providers with invalid or missing coordinates
                    continue

            # providers = filtered_providers
            providers = ProviderModel.objects.filter(
                id__in=[provider.id for provider in filtered_providers]
            )

        else:
            # Default city filter if range conditions are not met
            providers = providers.filter(city__icontains=filter_city)

    # Paginate
    page = request.query_params.get("page")
    if page != "0":
        paginator = Paginator(providers, 10)
        try:
            providers = paginator.page(page)
        except PageNotAnInteger:
            providers = paginator.page(1)
        except EmptyPage:
            providers = paginator.page(paginator.num_pages)
        pages = paginator.num_pages
        count = paginator.count
    else:
        pages = 1
        count = providers.count()

    # Choose the appropriate serializer based on the request parameters
    if is_dashboard:
        # Ultra-lightweight serializer for dropdown filters
        serializer = ProviderDashboardSerializer(providers, many=True)
    elif is_dashboard_list:
        # Lightweight serializer for dashboard list views
        serializer = ProviderListDashboardSerializer(providers, many=True)
    elif is_map_screen:
        # Optimized serializer for ProvidersMapScreen
        serializer = ProviderMapScreenSerializer(providers, many=True)
    elif is_edit_case:
        # Optimized serializer for EditCaseScreen (includes services)
        from base.serializers import ProviderEditCaseSerializer
        serializer = ProviderEditCaseSerializer(providers, many=True)
    else:
        # Default to full serializer for backward compatibility
        serializer = ProviderModelSerializer(providers, many=True)

    return Response(
        data={
            "providers": serializer.data,
            "page": page,
            "pages": pages,
            "count": count,
        }
    )
