from rest_framework.response import Response
from rest_framework.decorators import api_view, permission_classes
from rest_framework_simplejwt.tokens import RefreshToken
from rest_framework.permissions import IsAuthenticated
from django.core.exceptions import ObjectDoesNotExist

from django.contrib.auth.tokens import default_token_generator
from django.utils.http import urlsafe_base64_encode
from django.utils.encoding import force_bytes


from base.models import User, HistoryUser
from django.contrib.auth import authenticate
from base.permissions import <PERSON><PERSON><PERSON>, IsAdmin, IsSuperAdmin
from django.contrib.auth.hashers import make_password
from django.core.paginator import Paginator, EmptyPage, PageNotAnInteger
from django.utils import timezone

from base.emails import send_reset_password_mail

from base.serializers import (
    UserSerializer,
    HistoryUserSerializer,
)
from django.core.mail import send_mail
from django.utils.http import urlsafe_base64_decode
from datetime import datetime


@api_view(["POST"])
def confirm_reset_password(request):
    try:
        passowrd = request.data["password"]
        uidb64 = request.data["uidb64"]
        token = request.data["token"]
        timestamp = int(request.data["timestamp"])

        uid = urlsafe_base64_decode(uidb64).decode()
        user = User.objects.get(pk=uid)
        if default_token_generator.check_token(user, token):
            current_timestamp = int(datetime.now().timestamp())
            time_difference = current_timestamp - timestamp
            if time_difference > 3600:  # 3600 seconds = 1 hour
                return Response(
                    {"detail": "Token has expired. Please request a new reset link."},
                    status=400,
                )
            else:
                user.set_password(passowrd)
                user.save()
                return Response({"detail": "Password reset successful!"}, status=200)
        else:
            return Response(
                {
                    "detail": "Token is Invalid or expired. Please request a new reset link."
                },
                status=400,
            )

    except Exception as e:
        return Response(
            {
                "detail": "Sorry, an error occurred while sending the data. Please try again."
            },
            status=401,
        )


@api_view(["POST"])
def send_reset_password(request):
    try:
        # email = "<EMAIL>"
        email = request.data["email"]
        # email = request.data["email"]
        user = User.objects.get(email=email)
        if user:
            if user.is_active == False:
                return Response(
                    {
                        "detail": "Sorry, This email has been deactivated, Please contact support to activate your account."
                    },
                    status=401,
                )
            elif user.is_deleted:
                return Response(
                    {
                        "detail": "Sorry, This email has been deleted, Please contact support to activate your account."
                    },
                    status=401,
                )
            else:

                token = default_token_generator.make_token(user)

                uidb64 = urlsafe_base64_encode(force_bytes(user.pk))

                timestamp = int(datetime.now().timestamp())

                reset_link = (
                    f"https://unmcrm.com/confirm-password/{uidb64}/{token}/{timestamp}"
                )
                print("reset_link")
                print(reset_link)

                base_url = request.build_absolute_uri("/")

                send_reset_password_mail(
                    email,
                    "Reset Your Password",
                    base_url,
                    user.full_name,
                    reset_link,
                )
                return Response(
                    {"detail": "An email has been sent on your email address"},
                    status=200,
                )

        else:
            return Response({"detail": "Sorry, This Email Does Not Exist"}, status=401)

    except ObjectDoesNotExist:
        return Response({"detail": "Sorry, This Email Does Not Exist"}, status=401)
    except Exception as e:
        print(e)
        return Response(
            {
                "detail": "Sorry, an error occurred while sending the data. Please try again."
            },
            status=401,
        )


@api_view(["POST"])
@permission_classes([IsAuthenticated])
def add_history_byuser(request):
    try:
        HistoryUser.objects.create(
            user=request.user,
            device=request.data["device"],
            location="",
            browser=request.data["browser"],
            type_history="Logout",
        )
        return Response({"detail": "You are logged in this time."}, status=200)
    except Exception as e:
        return Response(
            {"detail": "Sorry, This Coordinator Does Not Exist"}, status=401
        )


# get list history by user
@api_view(["GET"])
@permission_classes([IsAuthenticated])
def get_list_history_byuser(request, pk):
    try:
        coordinator = User.objects.get(pk=pk)
        if coordinator:
            historys = HistoryUser.objects.filter(user=coordinator).order_by(
                "-created_at"
            )

            # Paginate
            page = request.query_params.get("page")
            if page != "0":
                paginator = Paginator(historys, 10)
                try:
                    historys = paginator.page(page)
                except PageNotAnInteger:
                    historys = paginator.page(1)
                except EmptyPage:
                    historys = paginator.page(paginator.num_pages)
                pages = paginator.num_pages
                count = paginator.count
            else:
                pages = 1
                count = historys.count()

            serializer = HistoryUserSerializer(historys, many=True)
            return Response(
                data={
                    "historys": serializer.data,
                    "page": page,
                    "pages": pages,
                    "count": count,
                }
            )
        else:
            return Response(
                {"detail": "Sorry, This Coordinator Does Not Exist"}, status=401
            )
    except ObjectDoesNotExist:
        return Response(
            {"detail": "Sorry, This Coordinator Does Not Exist"}, status=401
        )
    except Exception as e:
        return Response(
            {
                "detail": "Sorry, an error occurred while sending the data. Please try again."
            },
            status=401,
        )


@api_view(["GET"])
@permission_classes([IsAuthenticated])
def get_list_history_byloged(request):
    try:
        user = request.user
        if user:
            historys = HistoryUser.objects.filter(user=user).order_by("-created_at")

            # Paginate
            page = request.query_params.get("page")
            if page != "0":
                paginator = Paginator(historys, 10)
                try:
                    historys = paginator.page(page)
                except PageNotAnInteger:
                    historys = paginator.page(1)
                except EmptyPage:
                    historys = paginator.page(paginator.num_pages)
                pages = paginator.num_pages
                count = paginator.count
            else:
                pages = 1
                count = historys.count()

            serializer = HistoryUserSerializer(historys, many=True)
            return Response(
                data={
                    "historys": serializer.data,
                    "page": page,
                    "pages": pages,
                    "count": count,
                }
            )
        else:
            return Response(
                {"detail": "Sorry, This Coordinator Does Not Exist"}, status=401
            )
    except ObjectDoesNotExist:
        return Response(
            {"detail": "Sorry, This Coordinator Does Not Exist"}, status=401
        )
    except Exception as e:
        return Response(
            {
                "detail": "Sorry, an error occurred while sending the data. Please try again."
            },
            status=401,
        )


@api_view(["PUT"])
@permission_classes([IsAuthenticated])
def update_login_time(request):
    if request.user:
        request.user.last_active = timezone.now()
        request.user.save()
        return Response({"detail": "last login updated"})
    else:
        return Response({"detail": "last login not updated"}, status=401)


@api_view(["PUT"])
@permission_classes([IsAuthenticated])
def update_coordinator_detail(request, pk):
    try:
        coordinator = User.objects.get(pk=pk)
        if coordinator:
            coordinator_image = coordinator.photo
            if (
                "coordinator_image" in request.data
                and request.data["coordinator_image"] != ""
            ):
                coordinator_image = request.FILES.get("coordinator_image")
                coordinator.photo = coordinator_image
            coordinator.first_name = request.data["first_name"]
            coordinator.last_name = request.data["last_name"]
            coordinator.full_name = request.data["full_name"]
            coordinator.phone = request.data["phone"]
            coordinator.email = request.data["email"]
            if "password" in request.data and request.data["password"] != "":
                coordinator.set_password(request.data["password"])

            coordinator.save()
            return Response(
                {"detail": "This Coordinator has been updated successfully."}
            )

        else:
            return Response(
                {"detail": "Sorry, This Coordinator does not exist"}, status=401
            )
    except ObjectDoesNotExist:
        return Response(
            {"detail": "Sorry, This Coordinator does not exist"}, status=401
        )
    except Exception as error:
        print(error)
        return Response(
            {
                "detail": "Sorry, an error occurred while sending the data. Please try again."
            },
            status=401,
        )


@api_view(["GET"])
@permission_classes([IsAuthenticated])
def coordinator_profile(request, pk):
    try:
        coordinator = User.objects.get(pk=pk)
        if coordinator and coordinator.role == 3:
            serializer = UserSerializer(coordinator, many=False)
            return Response(data={"coordinator": serializer.data})
        else:
            return Response(
                {"detail": "Sorry, This Coordinator Does Not Exist"}, status=401
            )
    except ObjectDoesNotExist:
        return Response(
            {"detail": "Sorry, This Coordinator Does Not Exist"}, status=401
        )
    except Exception as e:
        print(e)
        return Response(
            {
                "detail": "Sorry, an error occurred while sending the data. Please try again."
            },
            status=401,
        )


@api_view(["PUT"])
@permission_classes([IsAuthenticated])
def update_password(request):
    try:
        user = request.user
        if not user.check_password(request.data["old_password"]):
            return Response(
                {"detail": "The old password is incorrect."},
                status=401,
            )

        user.set_password(request.data["new_password"])
        user.save()
        return Response({"detail": "Your profile has been successfully updated"})

    except Exception as e:
        return Response(
            {
                "detail": "Sorry, an error occurred while sending the data. Please try again."
            },
            status=401,
        )


@api_view(["POST"])
@permission_classes([IsAuthenticated])
def create_coordinator(request):
    first_name = request.data["first_name"]
    last_name = request.data["last_name"]
    full_name = request.data["full_name"]
    email = request.data["email"]
    phone = request.data["phone"]
    password = request.data["password"]
    coordinator_image = None
    if "coordinator_image" in request.data and request.data["coordinator_image"] != "":
        coordinator_image = request.FILES.get("coordinator_image")
    # check mail is exist
    try:
        user = User.objects.filter(email=email).all()
        if user:
            return Response({"detail": "Sorry, This email already exists"}, status=402)
        else:
            newuser = User.objects.create_user(
                email=email,
                phone=phone,
                first_name=first_name,
                last_name=last_name,
                full_name=full_name,
                photo=coordinator_image,
                role=3,
                password=password,
            )
            return Response({"detail": "This coordinator has been added successfully"})
    except Exception as e:
        return Response(
            {
                "detail": "Sorry, an error occurred while sending the data. Please try again."
            },
            status=401,
        )


@api_view(["GET"])
@permission_classes([IsAuthenticated])
def get_list_coordinator(request):
    """
    Get list of coordinators with different serialization levels:
    - isdashboard=true: Ultra-lightweight for dropdowns (id, full_name only)
    - default: Full serialization with all related data
    """
    is_dashboard = request.GET.get("isdashboard")

    users = User.objects.filter(role=3, is_deleted=False).order_by("-created_at")

    # paginate
    page = request.query_params.get("page")
    pages = 1
    count = 1
    if page != "0":
        paginator = Paginator(users, 10)

        try:
            users = paginator.page(page)
        except PageNotAnInteger:
            users = paginator.page(1)
        except EmptyPage:
            users = paginator.page(paginator.num_pages)

        if page == None:
            page = 1
        pages = paginator.num_pages
        count = paginator.count

    # Choose the appropriate serializer based on the request parameters
    if is_dashboard:
        # Ultra-lightweight serializer for dropdown filters
        from base.serializers import CoordinatorDashboardSerializer
        serializer = CoordinatorDashboardSerializer(users, many=True)
        return Response(
            data={
                "coordinators": serializer.data,
                "page": page,
                "pages": pages,
                "count": count,
            }
        )
    else:
        # Default to full serializer for backward compatibility
        serializer = UserSerializer(users, many=True)
        return Response(
            data={
                "users": serializer.data,
                "page": page,
                "pages": pages,
                "count": count,
            }
        )


@api_view(["DELETE"])
@permission_classes([IsAuthenticated])
def delete_user(request, pk):
    try:
        user = User.objects.get(pk=pk)
        if user:
            user.is_deleted = True
            user.save()
            return Response(
                {"detail": "This user has been successfully deleted."}, status=200
            )
        else:
            return Response({"detail": "Sorry, This User Does Not Exist"}, status=401)
    except ObjectDoesNotExist:
        return Response({"detail": "Sorry, This User Does Not Exist"}, status=401)
    except Exception as error:
        print(error)
        return Response(
            {
                "detail": "Sorry, an error occurred while sending the data. Please try again."
            },
            status=401,
        )


@api_view(["PUT"])
@permission_classes([IsAuthenticated])
def update_profile(request):
    try:
        user = request.user

        if (
            User.objects.filter(email=request.data["email"])
            .exclude(id=user.id)
            .exists()
        ):
            return Response(
                {"detail": "This email is already in use by another user"},
                status=401,
            )

        coordinator_image = user.photo
        if (
            "coordinator_image" in request.data
            and request.data["coordinator_image"] != ""
        ):
            coordinator_image = request.FILES.get("coordinator_image")
            user.photo = coordinator_image
        if "delete_image" in request.data and request.data["delete_image"] != "":
            coordinator_image = None
            user.photo = coordinator_image

        user.email = request.data["email"]
        user.phone = request.data["phone"]
        user.first_name = request.data["first_name"]
        user.last_name = request.data["last_name"]
        user.full_name = request.data["full_name"]

        user.save()
        return Response({"detail": "Your profile has been successfully updated"})

    except Exception as e:
        return Response(
            {
                "detail": "Sorry, an error occurred while sending the data. Please try again."
            },
            status=401,
        )


@api_view(["GET"])
@permission_classes([IsAuthenticated])
def get_profile(request):
    user = request.user
    serializer = UserSerializer(user, many=False)
    return Response(data={"profile": serializer.data})


@api_view(["GET"])
@permission_classes([IsAuthenticated])
def get_list_users(request):
    users = User.objects.filter(is_deleted=False).order_by("-created_at")

    # paginate
    page = request.query_params.get("page")
    pages = 1
    count = 1
    if page != "0":
        paginator = Paginator(users, 10)

        try:
            users = paginator.page(page)
        except PageNotAnInteger:
            users = paginator.page(1)
        except EmptyPage:
            users = paginator.page(paginator.num_pages)

        if page == None:
            page = 1
        pages = paginator.num_pages
        count = paginator.count

    serializer = UserSerializer(users, many=True)
    return Response(
        data={
            "users": serializer.data,
            "page": page,
            "pages": pages,
            "count": count,
        }
    )


@api_view(["POST"])
@permission_classes([IsAuthenticated])
def create_user(request):
    first_name = request.data["first_name"]
    last_name = request.data["last_name"]
    email = request.data["email"]
    phone = request.data["phone"]
    role = request.data["role"]
    password = request.data["password"]
    # check mail is exist
    try:
        user = User.objects.filter(email=email).all()
        if user:
            return Response(
                {"detail": "This email is already in use by another user"}, status=402
            )
        else:
            newuser = User.objects.create_user(
                email=email,
                phone=phone,
                first_name=first_name,
                last_name=last_name,
                role=role,
                password=password,
            )
            return Response({"detail": "This user has been added successfully"})
    except Exception as e:
        return Response(
            {
                "detail": "Sorry, an error occurred while sending the data. Please try again."
            },
            status=401,
        )


@api_view(["POST"])
def login(request):
    username = request.data["username"]
    password = request.data["password"]
    _user = None
    success = None

    print(request)
    print(request.data)

    try:
        _user = User.objects.get(email=request.data["username"])
        _success = _user.check_password(request.data["password"])

    except User.DoesNotExist:
        return Response({"detail": "Incorrect email or password"}, status=401)

    if _user and _user.is_deleted:
        return Response({"detail": "Incorrect email or password"}, status=401)
    if _user and not _user.is_active:
        return Response({"detail": "Please activate your account"}, status=401)

    if not _user or not _success:
        return Response({"detail": "Incorrect email or password"}, status=401)

    # check id driver
    if _user and _success:

        user = authenticate(request, username=username, password=password)
        if "token_fb" in request.data and request.data["token_fb"] != "":
            user.token_fb = request.data["token_fb"]
            user.save()
        refresh = RefreshToken.for_user(user)
        photo = None
        print(_user.photo)
        if _user.photo:
            photo = str(_user.photo.url)

        auth_user = {
            "refresh": str(refresh),
            "access": str(refresh.access_token),
            "role": _user.role,
            "email": _user.email,
            "phone": _user.phone,
            "photo": photo,
            "first_name": _user.first_name,
            "last_name": _user.last_name,
            "is_active": _user.is_active,
        }

        HistoryUser.objects.create(
            user=user,
            device=request.data["device"],
            location="",
            browser=request.data["browser"],
            type_history="Login",
        )

        return Response(auth_user)
