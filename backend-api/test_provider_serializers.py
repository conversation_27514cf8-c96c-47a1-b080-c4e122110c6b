#!/usr/bin/env python3
"""
Test script to verify the optimized serializers are working correctly.
This script demonstrates the performance difference between different serializer types
for both providers and insurances.
"""

import os
import sys
import django
import time
import json
from django.test import RequestFactory

# Add the project root to the Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'backend.settings')
django.setup()

from base.models import ProviderModel, Assurance
from base.serializers import (
    ProviderModelSerializer,
    ProviderDashboardSerializer,
    ProviderListDashboardSerializer,
    AssuranceSerializer,
    AssuranceDashboardSerializer,
    AssuranceListDashboardSerializer
)


def test_provider_serializer_performance():
    """
    Test the performance difference between different provider serializers
    """
    print("🔍 Testing Provider Serializer Performance...")
    print("=" * 60)
    
    # Get some providers for testing
    providers = ProviderModel.objects.filter(is_deleted=False)[:10]
    
    if not providers.exists():
        print("❌ No providers found in database. Please add some providers first.")
        return
    
    print(f"📊 Testing with {providers.count()} providers")
    print()
    
    # Test Full Serializer
    print("1️⃣ Testing ProviderModelSerializer (Full)...")
    start_time = time.time()
    full_serializer = ProviderModelSerializer(providers, many=True)
    full_data = full_serializer.data
    full_time = time.time() - start_time
    
    print(f"   ⏱️  Time: {full_time:.4f} seconds")
    print(f"   📦 Data size: {len(json.dumps(full_data))} characters")
    print(f"   🔑 Fields per provider: {len(full_data[0].keys()) if full_data else 0}")
    print()
    
    # Test Dashboard Serializer
    print("2️⃣ Testing ProviderDashboardSerializer (Ultra-lightweight)...")
    start_time = time.time()
    dashboard_serializer = ProviderDashboardSerializer(providers, many=True)
    dashboard_data = dashboard_serializer.data
    dashboard_time = time.time() - start_time
    
    print(f"   ⏱️  Time: {dashboard_time:.4f} seconds")
    print(f"   📦 Data size: {len(json.dumps(dashboard_data))} characters")
    print(f"   🔑 Fields per provider: {len(dashboard_data[0].keys()) if dashboard_data else 0}")
    print()
    
    # Test Dashboard List Serializer
    print("3️⃣ Testing ProviderListDashboardSerializer (Lightweight)...")
    start_time = time.time()
    list_serializer = ProviderListDashboardSerializer(providers, many=True)
    list_data = list_serializer.data
    list_time = time.time() - start_time
    
    print(f"   ⏱️  Time: {list_time:.4f} seconds")
    print(f"   📦 Data size: {len(json.dumps(list_data))} characters")
    print(f"   🔑 Fields per provider: {len(list_data[0].keys()) if list_data else 0}")
    print()
    
    # Performance comparison
    print("📈 Performance Comparison:")
    print("=" * 40)
    if full_time > 0:
        dashboard_speedup = (full_time - dashboard_time) / full_time * 100
        list_speedup = (full_time - list_time) / full_time * 100
        
        print(f"🚀 Dashboard serializer is {dashboard_speedup:.1f}% faster")
        print(f"🚀 List serializer is {list_speedup:.1f}% faster")
    
    full_size = len(json.dumps(full_data))
    dashboard_size = len(json.dumps(dashboard_data))
    list_size = len(json.dumps(list_data))
    
    if full_size > 0:
        dashboard_reduction = (full_size - dashboard_size) / full_size * 100
        list_reduction = (full_size - list_size) / full_size * 100
        
        print(f"📉 Dashboard data is {dashboard_reduction:.1f}% smaller")
        print(f"📉 List data is {list_reduction:.1f}% smaller")
    
    print()
    
    # Show sample data
    print("📋 Sample Data Structures:")
    print("=" * 40)
    
    if dashboard_data:
        print("🔹 Dashboard Serializer Output:")
        print(f"   {json.dumps(dashboard_data[0], indent=2)}")
        print()
    
    if list_data:
        print("🔹 List Serializer Output:")
        print(f"   {json.dumps(list_data[0], indent=2)}")
        print()


def test_insurance_serializer_performance():
    """
    Test the performance difference between different insurance serializers
    """
    print("🔍 Testing Insurance Serializer Performance...")
    print("=" * 60)

    # Get some insurances for testing
    insurances = Assurance.objects.filter(is_deleted=False)[:10]

    if not insurances.exists():
        print("❌ No insurances found in database. Please add some insurances first.")
        return

    print(f"� Testing with {insurances.count()} insurances")
    print()

    # Test Full Serializer
    print("1️⃣ Testing AssuranceSerializer (Full)...")
    start_time = time.time()
    full_serializer = AssuranceSerializer(insurances, many=True)
    full_data = full_serializer.data
    full_time = time.time() - start_time

    print(f"   ⏱️  Time: {full_time:.4f} seconds")
    print(f"   📦 Data size: {len(json.dumps(full_data))} characters")
    print(f"   🔑 Fields per insurance: {len(full_data[0].keys()) if full_data else 0}")
    print()

    # Test Dashboard Serializer
    print("2️⃣ Testing AssuranceDashboardSerializer (Ultra-lightweight)...")
    start_time = time.time()
    dashboard_serializer = AssuranceDashboardSerializer(insurances, many=True)
    dashboard_data = dashboard_serializer.data
    dashboard_time = time.time() - start_time

    print(f"   ⏱️  Time: {dashboard_time:.4f} seconds")
    print(f"   📦 Data size: {len(json.dumps(dashboard_data))} characters")
    print(f"   🔑 Fields per insurance: {len(dashboard_data[0].keys()) if dashboard_data else 0}")
    print()

    # Test Dashboard List Serializer
    print("3️⃣ Testing AssuranceListDashboardSerializer (Lightweight)...")
    start_time = time.time()
    list_serializer = AssuranceListDashboardSerializer(insurances, many=True)
    list_data = list_serializer.data
    list_time = time.time() - start_time

    print(f"   ⏱️  Time: {list_time:.4f} seconds")
    print(f"   📦 Data size: {len(json.dumps(list_data))} characters")
    print(f"   🔑 Fields per insurance: {len(list_data[0].keys()) if list_data else 0}")
    print()

    # Performance comparison
    print("📈 Performance Comparison:")
    print("=" * 40)
    if full_time > 0:
        dashboard_speedup = (full_time - dashboard_time) / full_time * 100
        list_speedup = (full_time - list_time) / full_time * 100

        print(f"🚀 Dashboard serializer is {dashboard_speedup:.1f}% faster")
        print(f"🚀 List serializer is {list_speedup:.1f}% faster")

    full_size = len(json.dumps(full_data))
    dashboard_size = len(json.dumps(dashboard_data))
    list_size = len(json.dumps(list_data))

    if full_size > 0:
        dashboard_reduction = (full_size - dashboard_size) / full_size * 100
        list_reduction = (full_size - list_size) / full_size * 100

        print(f"📉 Dashboard data is {dashboard_reduction:.1f}% smaller")
        print(f"📉 List data is {list_reduction:.1f}% smaller")

    print()


def test_serializer_fields():
    """
    Test that the serializers return the expected fields
    """
    print("🔍 Testing Serializer Field Output...")
    print("=" * 60)

    # Test Provider Serializers
    provider = ProviderModel.objects.filter(is_deleted=False).first()

    if provider:
        print("📋 PROVIDER SERIALIZERS:")
        print("-" * 30)

        # Test Dashboard Serializer
        dashboard_serializer = ProviderDashboardSerializer(provider)
        dashboard_data = dashboard_serializer.data

        print("✅ ProviderDashboardSerializer fields:")
        for key, value in dashboard_data.items():
            print(f"   • {key}: {value}")

        print()

        # Test List Serializer
        list_serializer = ProviderListDashboardSerializer(provider)
        list_data = list_serializer.data

        print("✅ ProviderListDashboardSerializer fields:")
        for key, value in list_data.items():
            print(f"   • {key}: {value}")

        print()
    else:
        print("❌ No providers found in database.")

    # Test Insurance Serializers
    insurance = Assurance.objects.filter(is_deleted=False).first()

    if insurance:
        print("📋 INSURANCE SERIALIZERS:")
        print("-" * 30)

        # Test Dashboard Serializer
        dashboard_serializer = AssuranceDashboardSerializer(insurance)
        dashboard_data = dashboard_serializer.data

        print("✅ AssuranceDashboardSerializer fields:")
        for key, value in dashboard_data.items():
            print(f"   • {key}: {value}")

        print()

        # Test List Serializer
        list_serializer = AssuranceListDashboardSerializer(insurance)
        list_data = list_serializer.data

        print("✅ AssuranceListDashboardSerializer fields:")
        for key, value in list_data.items():
            print(f"   • {key}: {value}")
    else:
        print("❌ No insurances found in database.")


if __name__ == "__main__":
    print("🧪 Provider Serializer Optimization Test")
    print("=" * 60)
    print()
    
    try:
        test_serializer_performance()
        print()
        test_serializer_fields()
        print()
        print("✅ All tests completed successfully!")
        
    except Exception as e:
        print(f"❌ Test failed with error: {e}")
        import traceback
        traceback.print_exc()
