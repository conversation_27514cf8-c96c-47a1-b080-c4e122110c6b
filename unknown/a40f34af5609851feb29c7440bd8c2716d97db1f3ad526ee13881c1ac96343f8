from rest_framework.response import Response
from rest_framework.decorators import api_view, permission_classes
from rest_framework_simplejwt.tokens import RefreshToken
from rest_framework.permissions import IsAuthenticated
from django.core.exceptions import ObjectDoesNotExist

from base.models import User, Assurance
from django.contrib.auth import authenticate
from base.permissions import <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>rAdmin
from django.contrib.auth.hashers import make_password
from django.core.paginator import <PERSON><PERSON>ator, EmptyPage, PageNotAnInteger

from base.serializers import (
    ClientSerializer,
    ProviderModelSerializer,
    AssuranceSerializer,
    AssuranceDashboardSerializer,
    AssuranceListDashboardSerializer,
)


# update insurance
@api_view(["PUT"])
@permission_classes([IsAuthenticated])
def update_insurance_detail(request, pk):
    try:
        insurance = Assurance.objects.get(pk=pk)
        if insurance:
            assurance_logo = insurance.assurance_logo
            if (
                "assurance_logo" in request.data
                and request.data["assurance_logo"] != ""
            ):
                assurance_logo = request.FILES.get("assurance_logo")
                insurance.assurance_logo = assurance_logo
            insurance.assurance_name = request.data["assurance_name"]
            insurance.assurance_country = request.data["assurance_country"]
            insurance.assurance_phone = request.data["assurance_phone"]
            insurance.assurance_phone_two = request.data["assurance_phone_two"]
            insurance.assurance_phone_three = request.data["assurance_phone_three"]
            insurance.assurance_email = request.data["assurance_email"]
            insurance.assurance_email_two = request.data["assurance_email_two"]
            insurance.assurance_email_three = request.data["assurance_email_three"]

            insurance.save()
            return Response({"detail": "This Insurance has been updated successfully."})

        else:
            return Response(
                {"detail": "Sorry, This Insurance does not exist"}, status=401
            )
    except ObjectDoesNotExist:
        return Response({"detail": "Sorry, This Insurance does not exist"}, status=401)
    except Exception as error:
        print(error)
        return Response(
            {
                "detail": "Sorry, an error occurred while sending the data. Please try again."
            },
            status=401,
        )


# get provider info
@api_view(["GET"])
@permission_classes([IsAuthenticated])
def insurance_detail(request, pk):
    try:
        insurance = Assurance.objects.get(pk=pk)
        if insurance:
            serializer = AssuranceSerializer(insurance, many=False)
            return Response(serializer.data, status=200)
        else:
            return Response(
                {"detail": "Sorry, this Insurance does not exist"}, status=401
            )

    except ObjectDoesNotExist:
        return Response({"detail": "Sorry, this Insurance does not exist"}, status=401)
    except Exception as error:
        print(error)
        return Response(
            {
                "detail": "Sorry, an error occurred while sending the data. Please try again."
            },
            status=401,
        )


# delete Insurance
@api_view(["DELETE"])
@permission_classes([IsAuthenticated])
def delete_insurance(request, pk):
    try:
        insurance = Assurance.objects.get(pk=pk)
        if insurance:
            # change
            insurance.is_deleted = True
            insurance.save()
            return Response({"detail": "This Insurance has been successfully deleted."})
        else:
            return Response(
                {"detail": "Sorry, This Insurance does not exist"}, status=401
            )
    except ObjectDoesNotExist:
        return Response({"detail": "Sorry, This Insurance does not exist"}, status=401)
    except Exception as error:

        return Response(
            {
                "detail": "Sorry, an error occurred while sending the data. Please try again."
            },
            status=401,
        )


# create insurance
@api_view(["POST"])
@permission_classes([IsAuthenticated])
def create_new_insurance(request):
    try:
        print(request.data)
        assurance_logo = None
        if "assurance_logo" in request.data and request.data["assurance_logo"] != "":
            assurance_logo = request.FILES.get("assurance_logo")
        assurance = Assurance.objects.create(
            # created_by=request.user,
            assurance_name=request.data["assurance_name"],
            assurance_country=request.data["assurance_country"],
            assurance_phone=request.data["assurance_phone"],
            assurance_phone_two=request.data["assurance_phone_two"],
            assurance_phone_three=request.data["assurance_phone_three"],
            assurance_email=request.data["assurance_email"],
            assurance_email_two=request.data["assurance_email_two"],
            assurance_email_three=request.data["assurance_email_three"],
            assurance_logo=assurance_logo,
        )
        return Response(
            {"detail": "This Insurance has been added successfully"}, status=200
        )

    except Exception as error:
        print(error)
        return Response(
            {
                "detail": "Sorry, an error occurred while sending the data. Please try again."
            },
            status=401,
        )


# get list Insurance with optimized serializers for dashboard
@api_view(["GET"])
@permission_classes([IsAuthenticated])
def get_list_insurances(request):
    """
    Get list of insurances with different serialization levels:
    - isdashboard=true: Ultra-lightweight for dropdowns (id, assurance_name only)
    - isdashboardlist=true: Lightweight for dashboard lists (essential fields only)
    - default: Full serialization with all related data
    """
    is_dashboard = request.GET.get("isdashboard")
    is_dashboard_list = request.GET.get("isdashboardlist")

    assurances = Assurance.objects.filter(is_deleted=False).order_by("-created_at")

    # Paginate
    page = request.query_params.get("page")
    if page != "0":
        paginator = Paginator(assurances, 10)
        try:
            assurances = paginator.page(page)
        except PageNotAnInteger:
            assurances = paginator.page(1)
        except EmptyPage:
            assurances = paginator.page(paginator.num_pages)
        pages = paginator.num_pages
        count = paginator.count
    else:
        pages = 1
        count = assurances.count()

    # Choose the appropriate serializer based on the request parameters
    if is_dashboard:
        # Ultra-lightweight serializer for dropdown filters
        serializer = AssuranceDashboardSerializer(assurances, many=True)
    elif is_dashboard_list:
        # Lightweight serializer for dashboard list views
        serializer = AssuranceListDashboardSerializer(assurances, many=True)
    else:
        # Default to full serializer for backward compatibility
        serializer = AssuranceSerializer(assurances, many=True)

    return Response(
        data={
            "insurances": serializer.data,
            "page": page,
            "pages": pages,
            "count": count,
        }
    )
