{"ast": null, "code": "import React,{useEffect,useState}from\"react\";import{useDispatch,useSelector}from\"react-redux\";import{useLocation,useNavigate}from\"react-router-dom\";import DefaultLayout from\"../../layouts/DefaultLayout\";import addreactionface from\"../../images/icon/add_reaction.png\";import{toast}from\"react-toastify\";import{providersListDashboard}from\"../../redux/actions/providerActions\";import{addNewCase}from\"../../redux/actions/caseActions\";import Select from\"react-select\";import{useDropzone}from\"react-dropzone\";import{insurancesListDashboard}from\"../../redux/actions/insuranceActions\";import{coordinatorsListDashboard}from\"../../redux/actions/userActions\";import{COUNTRIES,CURRENCYITEMS}from\"../../constants\";import GoogleComponent from\"react-google-autocomplete\";import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const STEPSLIST=[{index:0,title:\"General Information\",description:\"Please enter the general information about the patient and the case.\"},{index:1,title:\"Coordination Details\",description:\"Provide information about the initial coordination & Assistance Details for this case.\"},{index:2,title:\"Medical Reports\",description:\"Upload any initial medical reports related to the case.\"},{index:3,title:\"Invoices\",description:\"If there are any initial invoices related to the case, please provide the details and upload the documents.\"},{index:4,title:\"Insurance Authorization\",description:\"Please provide the details of the insurance authorization for this case, and upload any relevant documents.\"},{index:5,title:\"Finish\",description:\"You can go back to any step to make changes.\"}];const thumbsContainer={display:\"flex\",flexDirection:\"row\",flexWrap:\"wrap\",marginTop:16};function AddCaseScreen(){const navigate=useNavigate();const location=useLocation();const dispatch=useDispatch();//\nconst[firstName,setFirstName]=useState(\"\");const[firstNameError,setFirstNameError]=useState(\"\");const[lastName,setLastName]=useState(\"\");const[lastNameError,setLastNameError]=useState(\"\");const[email,setEmail]=useState(\"\");const[emailError,setEmailError]=useState(\"\");const[birthDate,setBirthDate]=useState(\"\");const[birthDateError,setBirthDateError]=useState(\"\");const[phone,setPhone]=useState(\"\");const[phoneError,setPhoneError]=useState(\"\");const[address,setAddress]=useState(\"\");const[addressError,setAddressError]=useState(\"\");const[city,setCity]=useState(\"\");const[cityError,setCityError]=useState(\"\");const[country,setCountry]=useState(\"\");const[countryError,setCountryError]=useState(\"\");const[isPay,setIsPay]=useState(false);const[currencyCode,setCurrencyCode]=useState(\"\");const[currencyCodeError,setCurrencyCodeError]=useState(\"\");const[priceTotal,setPriceTotal]=useState(0);const[priceTotalError,setPriceTotalError]=useState(\"\");//\nconst[coordinator,setCoordinator]=useState(\"\");const[coordinatorError,setCoordinatorError]=useState(\"\");const[providerServices,setProviderServices]=useState([]);const[providerMultiSelect,setProviderMultiSelect]=useState([]);const[providerService,setProviderService]=useState(\"\");const[providerServiceError,setProviderServiceError]=useState(\"\");const[providerDate,setProviderDate]=useState(\"\");const[providerDateError,setProviderDateError]=useState(\"\");const[caseDate,setCaseDate]=useState(new Date().toISOString().split(\"T\")[0]);const[caseDateError,setCaseDateError]=useState(\"\");const[caseType,setCaseType]=useState(\"\");const[caseTypeError,setCaseTypeError]=useState(\"\");const[caseTypeItem,setCaseTypeItem]=useState(\"\");const[caseTypeItemError,setCaseTypeItemError]=useState(\"\");const[caseDescription,setCaseDescription]=useState(\"\");const[caseDescriptionError,setCaseDescriptionError]=useState(\"\");//\nconst[coordinatStatus,setCoordinatStatus]=useState(\"\");const[coordinatStatusError,setCoordinatStatusError]=useState(\"\");const[coordinatStatusList,setCoordinatStatusList]=useState([]);const[coordinatStatusListError,setCoordinatStatusListError]=useState(\"\");const[appointmentDate,setAppointmentDate]=useState(\"\");const[appointmentDateError,setAppointmentDateError]=useState(\"\");const[startDate,setStartDate]=useState(\"\");const[startDateError,setStartDateError]=useState(\"\");const[endDate,setEndDate]=useState(\"\");const[endDateError,setEndDateError]=useState(\"\");const[serviceLocation,setServiceLocation]=useState(\"\");const[serviceLocationError,setServiceLocationError]=useState(\"\");//\nconst[providerName,setProviderName]=useState(\"\");const[providerNameError,setProviderNameError]=useState(\"\");const[providerPhone,setProviderPhone]=useState(\"\");const[providerPhoneError,setProviderPhoneError]=useState(\"\");const[providerEmail,setProviderEmail]=useState(\"\");const[providerEmailError,setProviderEmailError]=useState(\"\");const[providerAddress,setProviderAddress]=useState(\"\");const[providerAddressError,setProviderAddressError]=useState(\"\");//\nconst[invoiceNumber,setInvoiceNumber]=useState(\"\");const[invoiceNumberError,setInvoiceNumberError]=useState(\"\");const[dateIssued,setDateIssued]=useState(\"\");const[dateIssuedError,setDateIssuedError]=useState(\"\");const[amount,setAmount]=useState(0);const[amountError,setAmountError]=useState(\"\");//\nconst[insuranceCompany,setInsuranceCompany]=useState(\"\");const[insuranceCompanyError,setInsuranceCompanyError]=useState(\"\");const[insuranceNumber,setInsuranceNumber]=useState(\"\");const[insuranceNumberError,setInsuranceNumberError]=useState(\"\");const[policyNumber,setPolicyNumber]=useState(\"\");const[policyNumberError,setPolicyNumberError]=useState(\"\");const[initialStatus,setInitialStatus]=useState(\"\");const[initialStatusError,setInitialStatusError]=useState(\"\");// fils\n// initialMedicalReports\nconst[filesInitialMedicalReports,setFilesInitialMedicalReports]=useState([]);const{getRootProps:getRootPropsInitialMedical,getInputProps:getInputPropsInitialMedical}=useDropzone({accept:{\"*\":[]},onDrop:acceptedFiles=>{setFilesInitialMedicalReports(prevFiles=>[...prevFiles,...acceptedFiles.map(file=>Object.assign(file,{preview:URL.createObjectURL(file)}))]);}});useEffect(()=>{return()=>filesInitialMedicalReports.forEach(file=>URL.revokeObjectURL(file.preview));},[]);// Upload Invoice\nconst[filesUploadInvoice,setFilesUploadInvoice]=useState([]);const{getRootProps:getRootPropsUploadInvoice,getInputProps:getInputPropsUploadInvoice}=useDropzone({accept:{\"*\":[]},onDrop:acceptedFiles=>{setFilesUploadInvoice(prevFiles=>[...prevFiles,...acceptedFiles.map(file=>Object.assign(file,{preview:URL.createObjectURL(file)}))]);}});useEffect(()=>{return()=>filesUploadInvoice.forEach(file=>URL.revokeObjectURL(file.preview));},[]);// Upload Authorization Documents\nconst[filesUploadAuthorizationDocuments,setFilesUploadAuthorizationDocuments]=useState([]);const{getRootProps:getRootPropsUploadAuthorizationDocuments,getInputProps:getInputPropsUploadAuthorizationDocuments}=useDropzone({accept:{\"*\":[]},onDrop:acceptedFiles=>{setFilesUploadAuthorizationDocuments(prevFiles=>[...prevFiles,...acceptedFiles.map(file=>Object.assign(file,{preview:URL.createObjectURL(file)}))]);}});useEffect(()=>{return()=>filesUploadAuthorizationDocuments.forEach(file=>URL.revokeObjectURL(file.preview));},[]);// Configure react-dropzone\n//\nconst[stepSelect,setStepSelect]=useState(0);const[isLoading,setIsLoading]=useState(true);const userLogin=useSelector(state=>state.userLogin);const{userInfo}=userLogin;const listProviders=useSelector(state=>state.providerList);const{providers,loadingProviders,errorProviders}=listProviders;const createCase=useSelector(state=>state.createNewCase);const{loadingCaseAdd,successCaseAdd,errorCaseAdd}=createCase;const listInsurances=useSelector(state=>state.insuranceList);const{insurances,loadingInsurances,errorInsurances}=listInsurances;const listCoordinators=useSelector(state=>state.coordinatorsList);const{coordinators,loadingCoordinators,errorCoordinators}=listCoordinators;const redirect=\"/\";useEffect(()=>{if(!userInfo){navigate(redirect);}else{// Set loading state to true when starting to fetch data\nsetIsLoading(true);setStepSelect(0);dispatch(coordinatorsListDashboard(\"0\"));dispatch(providersListDashboard(\"0\"));dispatch(insurancesListDashboard(\"0\"));//   dispatch(clientList(\"0\"));\n// Set a maximum timeout for the loading indicator (30 seconds) as a fallback\nconst timeoutId=setTimeout(()=>{setIsLoading(false);console.log(\"Maximum loading time reached, hiding loading indicator\");},6000);// Clean up the timeout when the component unmounts\nreturn()=>clearTimeout(timeoutId);}},[navigate,userInfo,dispatch]);useEffect(()=>{if(successCaseAdd){setStepSelect(5);setIsLoading(false);}},[successCaseAdd]);// Update loading state when case add is in progress\nuseEffect(()=>{if(loadingCaseAdd){setIsLoading(true);}},[loadingCaseAdd]);// Update loading state based on data loading status\nuseEffect(()=>{// Check if essential data is loaded\nif(!loadingProviders&&!loadingInsurances&&!loadingCoordinators&&providers&&providers.length>0&&coordinators&&coordinators.length>0){// Hide loading indicator as soon as we have the essential data\nsetIsLoading(false);}},[loadingProviders,loadingInsurances,loadingCoordinators,providers,coordinators]);return/*#__PURE__*/_jsxs(DefaultLayout,{children:[isLoading&&/*#__PURE__*/_jsx(\"div\",{className:\"fixed top-0 left-0 w-full h-full bg-black bg-opacity-50 flex items-center justify-center z-50\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"bg-white p-5 rounded-lg shadow-lg flex flex-col items-center\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"animate-spin rounded-full h-16 w-16 border-t-4 border-b-4 border-[#0388A6] mb-3\"}),/*#__PURE__*/_jsx(\"div\",{className:\"text-gray-700 font-medium\",children:\"Loading data...\"})]})}),/*#__PURE__*/_jsxs(\"div\",{className:\"\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"flex flex-row text-sm items-center my-1\",children:[/*#__PURE__*/_jsx(\"a\",{href:\"/dashboard\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"flex flex-row  items-center hover:text-black \",children:[/*#__PURE__*/_jsx(\"svg\",{xmlns:\"http://www.w3.org/2000/svg\",fill:\"none\",viewBox:\"0 0 24 24\",\"stroke-width\":\"1.5\",stroke:\"currentColor\",className:\"w-4 h-4\",children:/*#__PURE__*/_jsx(\"path\",{strokeLinecap:\"round\",strokeLinejoin:\"round\",d:\"m2.25 12 8.954-8.955c.44-.439 1.152-.439 1.591 0L21.75 12M4.5 9.75v10.125c0 .621.504 1.125 1.125 1.125H9.75v-4.875c0-.621.504-1.125 1.125-1.125h2.25c.621 0 1.125.504 1.125 1.125V21h4.125c.621 0 1.125-.504 1.125-1.125V9.75M8.25 21h8.25\"})}),/*#__PURE__*/_jsx(\"span\",{className:\"mx-1\",children:\"Dashboard\"})]})}),/*#__PURE__*/_jsx(\"span\",{children:/*#__PURE__*/_jsx(\"svg\",{xmlns:\"http://www.w3.org/2000/svg\",fill:\"none\",viewBox:\"0 0 24 24\",\"stroke-width\":\"1.5\",stroke:\"currentColor\",className:\"w-4 h-4\",children:/*#__PURE__*/_jsx(\"path\",{strokeLinecap:\"round\",strokeLinejoin:\"round\",d:\"m8.25 4.5 7.5 7.5-7.5 7.5\"})})}),/*#__PURE__*/_jsx(\"div\",{className:\"\",children:\"Create New Case\"})]}),/*#__PURE__*/_jsx(\"div\",{className:\"py-5 px-4 flex justify-between\",children:/*#__PURE__*/_jsx(\"h4\",{className:\" uppercase font-semibold text-black dark:text-white\",children:\"New Case\"})}),/*#__PURE__*/_jsx(\"div\",{className:\"rounded-sm border border-stroke bg-white px-5 pt-6 pb-2.5 shadow-default dark:border-strokedark dark:bg-boxdark sm:px-7.5 xl:pb-1\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"flex md:flex-row flex-col\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"md:w-1/3 w-full flex md:flex-col flex-wrap px-3  relative\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"w-[1px] h-full absolute top-0 bg-[#0388A6] bg-opacity-60 left-[27.5px] md:block hidden\"}),STEPSLIST===null||STEPSLIST===void 0?void 0:STEPSLIST.map((step,index)=>/*#__PURE__*/_jsxs(\"div\",{onClick:()=>{if(stepSelect>step.index&&stepSelect!==5){setStepSelect(step.index);}},className:`flex flex-row mb-3 md:min-h-20 ${stepSelect>step.index&&stepSelect!==5?\"cursor-pointer\":\"\"} md:items-start items-center`,children:[stepSelect<step.index?/*#__PURE__*/_jsx(\"div\",{className:\"size-8 bg-[#E2EFF2] z-10  rounded-full text-center items-center flex justify-center text-[#0388A6]\",children:/*#__PURE__*/_jsx(\"img\",{src:addreactionface,className:\"size-5\",onError:e=>{e.target.onerror=null;e.target.src=\"/assets/placeholder.png\";}})}):stepSelect===step.index?/*#__PURE__*/_jsx(\"div\",{className:\"size-8 bg-white z-10  border-[11px] rounded-full\"}):/*#__PURE__*/_jsx(\"div\",{className:\"size-8 bg-[#E2EFF2] z-10  rounded-full text-center items-center flex justify-center text-[#0388A6]\",children:/*#__PURE__*/_jsx(\"svg\",{xmlns:\"http://www.w3.org/2000/svg\",fill:\"none\",viewBox:\"0 0 24 24\",\"stroke-width\":\"1.5\",stroke:\"currentColor\",className:\"size-5\",children:/*#__PURE__*/_jsx(\"path\",{\"stroke-linecap\":\"round\",\"stroke-linejoin\":\"round\",d:\"m4.5 12.75 6 6 9-13.5\"})})}),/*#__PURE__*/_jsxs(\"div\",{className:\"text-black flex-1 px-2\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"font-medium text-sm\",children:step.title}),stepSelect===step.index?/*#__PURE__*/_jsx(\"div\",{className:\"text-xs font-light md:block hidden\",children:step.description}):null]})]}))]}),/*#__PURE__*/_jsxs(\"div\",{className:\"md:w-2/3 w-full  px-3 py-4  bg-[#F8F9FF]\",children:[stepSelect===0?/*#__PURE__*/_jsxs(\"div\",{className:\"\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"text-[#0388A6] font-semibold text-xl\",children:\"General Information\"}),/*#__PURE__*/_jsx(\"div\",{className:\"text-xs font-medium mt-5 mb-2 text-black\",children:\"Patient Details:\"}),/*#__PURE__*/_jsxs(\"div\",{className:\"my-2 bg-white py-4 px-2 rounded-md\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"flex md:flex-row flex-col  \",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"md:w-1/2 w-full  md:pr-1 my-1\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"text-[#B4B4B4] text-xs  mb-1\",children:[\"First Name \",/*#__PURE__*/_jsx(\"strong\",{className:\"text-danger\",children:\"*\"})]}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"input\",{className:` outline-none border ${firstNameError?\"border-danger\":\"border-[#F1F3FF]\"} px-3 py-2 w-full rounded text-sm`,type:\"text\",placeholder:\"First Name\",value:firstName,onChange:v=>setFirstName(v.target.value)}),/*#__PURE__*/_jsx(\"div\",{className:\" text-[8px] text-danger\",children:firstNameError?firstNameError:\"\"})]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"md:w-1/2 w-full  md:pl-1 my-1\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"text-[#B4B4B4] text-xs mb-1\",children:\"Last Name\"}),/*#__PURE__*/_jsx(\"div\",{children:/*#__PURE__*/_jsx(\"input\",{className:\" outline-none border border-[#F1F3FF] px-3 py-2 w-full rounded text-sm\",type:\"text\",placeholder:\"Last Name\",value:lastName,onChange:v=>setLastName(v.target.value)})})]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex md:flex-row flex-col \",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"md:w-1/2 w-full md:pr-1 my-1\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"text-[#B4B4B4] text-xs  mb-1\",children:\"Email\"}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"input\",{className:` outline-none border ${emailError?\"border-danger\":\"border-[#F1F3FF]\"} px-3 py-2 w-full rounded text-sm`,type:\"email\",placeholder:\"Email Address\",value:email,onChange:v=>setEmail(v.target.value)}),/*#__PURE__*/_jsx(\"div\",{className:\" text-[8px] text-danger\",children:emailError?emailError:\"\"})]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"md:w-1/2 w-full  md:pl-1 my-1\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"text-[#B4B4B4] text-xs mb-1\",children:[\"phone \",/*#__PURE__*/_jsx(\"strong\",{className:\"text-danger\",children:\"*\"})]}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"input\",{className:`outline-none border ${phoneError?\"border-danger\":\"border-[#F1F3FF]\"} px-3 py-2 w-full rounded text-sm`,type:\"text\",placeholder:\"Phone no\",value:phone,onChange:v=>setPhone(v.target.value)}),/*#__PURE__*/_jsx(\"div\",{className:\" text-[8px] text-danger\",children:phoneError?phoneError:\"\"})]})]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex md:flex-row flex-col \",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"w-full md:pr-1 my-1\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"text-[#B4B4B4] text-xs  mb-1\",children:[\"Country \",/*#__PURE__*/_jsx(\"strong\",{className:\"text-danger\",children:\"*\"})]}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(Select,{value:country,onChange:option=>{setCountry(option);},options:COUNTRIES.map(country=>({value:country.title,label:/*#__PURE__*/_jsxs(\"div\",{className:`${country.title===\"\"?\"py-2\":\"\"} flex flex-row items-center`,children:[/*#__PURE__*/_jsx(\"span\",{className:\"mr-2\",children:country.icon}),/*#__PURE__*/_jsx(\"span\",{children:country.title})]})})),className:\"text-sm\",placeholder:\"Select a country...\",isSearchable:true,styles:{control:(base,state)=>({...base,background:\"#fff\",border:countryError?\"1px solid #d34053\":\"1px solid #F1F3FF\",boxShadow:state.isFocused?\"none\":\"none\",\"&:hover\":{border:\"1px solid #F1F3FF\"}}),option:base=>({...base,display:\"flex\",alignItems:\"center\"}),singleValue:base=>({...base,display:\"flex\",alignItems:\"center\"})}}),/*#__PURE__*/_jsx(\"div\",{className:\" text-[8px] text-danger\",children:countryError?countryError:\"\"})]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"w-full md:pr-1 my-1\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"text-[#B4B4B4] text-xs  mb-1\",children:[\"City \",/*#__PURE__*/_jsx(\"strong\",{className:\"text-danger\",children:\"*\"})]}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(GoogleComponent,{apiKey:\"AIzaSyD5rhJGfXPbttVQzftoMPVjJOWbBvfcTFE\",className:` outline-none border ${cityError?\"border-danger\":\"border-[#F1F3FF]\"} px-3 py-2 w-full rounded text-sm`,onChange:v=>{setCity(v.target.value);},onPlaceSelected:place=>{if(place&&place.geometry){var _place$formatted_addr;setCity((_place$formatted_addr=place.formatted_address)!==null&&_place$formatted_addr!==void 0?_place$formatted_addr:\"\");// setCityVl(place.formatted_address ?? \"\");\n//   const latitude = place.geometry.location.lat();\n//   const longitude = place.geometry.location.lng();\n//   setLocationX(latitude ?? \"\");\n//   setLocationY(longitude ?? \"\");\n}},defaultValue:city,types:[\"city\"],language:\"en\"}),/*#__PURE__*/_jsx(\"div\",{className:\" text-[8px] text-danger\",children:cityError?cityError:\"\"})]})]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex md:flex-row flex-col \",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"md:w-1/2 w-full  md:pr-1 my-1\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"text-[#B4B4B4] text-xs  mb-1\",children:\"CIA\"}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(Select,{value:insuranceCompany,onChange:option=>{setInsuranceCompany(option);},options:insurances===null||insurances===void 0?void 0:insurances.map(assurance=>({value:assurance.id,label:assurance.assurance_name||\"\"})),filterOption:(option,inputValue)=>option.label.toLowerCase().includes(inputValue.toLowerCase()),className:\"text-sm\",placeholder:\"Select Insurance...\",isSearchable:true,styles:{control:(base,state)=>({...base,background:\"#fff\",border:insuranceCompanyError?\"1px solid #d34053\":\"1px solid #F1F3FF\",boxShadow:state.isFocused?\"none\":\"none\",\"&:hover\":{border:\"1px solid #F1F3FF\"}}),option:base=>({...base,display:\"flex\",alignItems:\"center\"}),singleValue:base=>({...base,display:\"flex\",alignItems:\"center\"})}}),/*#__PURE__*/_jsx(\"div\",{className:\" text-[8px] text-danger\",children:insuranceCompanyError?insuranceCompanyError:\"\"})]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"md:w-1/2 w-full  md:pr-1 my-1\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"text-[#B4B4B4] text-xs  mb-1\",children:\"CIA Reference\"}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"input\",{className:` outline-none border ${insuranceNumberError?\"border-danger\":\"border-[#F1F3FF]\"}  px-3 py-2 w-full rounded text-sm`,type:\"text\",placeholder:\"CIA Reference\",value:insuranceNumber,onChange:v=>setInsuranceNumber(v.target.value)}),/*#__PURE__*/_jsx(\"div\",{className:\" text-[8px] text-danger\",children:insuranceNumberError?insuranceNumberError:\"\"})]})]})]})]}),/*#__PURE__*/_jsx(\"div\",{className:\"text-xs font-medium mt-5 mb-2 text-black\",children:\"Case Details:\"}),/*#__PURE__*/_jsxs(\"div\",{className:\"my-2 bg-white py-4 px-2 rounded-md\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"flex md:flex-row flex-col  \",children:/*#__PURE__*/_jsxs(\"div\",{className:\" w-full  md:pr-1 my-1\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"text-[#B4B4B4] text-xs  mb-1\",children:[\"Assigned Coordinator\",\" \",/*#__PURE__*/_jsx(\"strong\",{className:\"text-danger\",children:\"*\"})]}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(Select,{value:coordinator,onChange:option=>{setCoordinator(option);},className:\"text-sm\",options:coordinators===null||coordinators===void 0?void 0:coordinators.map(item=>({value:item.id,label:item.full_name||\"\"})),filterOption:(option,inputValue)=>option.label.toLowerCase().includes(inputValue.toLowerCase()),placeholder:\"Select Coordinator...\",isSearchable:true,styles:{control:(base,state)=>({...base,background:\"#fff\",border:coordinatorError?\"1px solid #d34053\":\"1px solid #F1F3FF\",boxShadow:state.isFocused?\"none\":\"none\",\"&:hover\":{border:\"1px solid #F1F3FF\"}}),option:base=>({...base,display:\"flex\",alignItems:\"center\"}),singleValue:base=>({...base,display:\"flex\",alignItems:\"center\"})}}),/*#__PURE__*/_jsx(\"div\",{className:\" text-[8px] text-danger\",children:coordinatorError?coordinatorError:\"\"})]})]})}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex md:flex-row flex-col  \",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"md:w-1/2 w-full  md:pr-1 my-1\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"text-[#B4B4B4] text-xs mb-1\",children:[\"Case Creation Date\",\" \",/*#__PURE__*/_jsx(\"strong\",{className:\"text-danger\",children:\"*\"})]}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"input\",{className:` outline-none border ${caseDateError?\"border-danger\":\"border-[#F1F3FF]\"} px-3 py-2 w-full rounded text-sm`,type:\"date\",placeholder:\"Case Creation Date\",value:caseDate,onChange:v=>setCaseDate(v.target.value)}),/*#__PURE__*/_jsx(\"div\",{className:\" text-[8px] text-danger\",children:caseDateError?caseDateError:\"\"})]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"md:w-1/2 w-full  md:pl-1 my-1\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"text-[#B4B4B4] text-xs  mb-1\",children:[\"Type \",/*#__PURE__*/_jsx(\"strong\",{className:\"text-danger\",children:\"*\"})]}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsxs(\"select\",{value:caseType,onChange:v=>setCaseType(v.target.value),className:` outline-none border ${caseTypeError?\"border-danger\":\"border-[#F1F3FF]\"} px-3 py-3 w-full rounded text-sm`,children:[/*#__PURE__*/_jsx(\"option\",{value:\"\",children:\"Select Type\"}),/*#__PURE__*/_jsx(\"option\",{value:\"Medical\",children:\"Medical\"}),/*#__PURE__*/_jsx(\"option\",{value:\"Technical\",children:\"Technical\"})]}),/*#__PURE__*/_jsx(\"div\",{className:\" text-[8px] text-danger\",children:caseTypeError?caseTypeError:\"\"})]})]})]}),caseType===\"Medical\"&&/*#__PURE__*/_jsxs(\"div\",{className:\"md:w-1/2  w-full  md:pl-1 my-1\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"text-[#B4B4B4] text-xs  mb-1\",children:[\"Type Item \",/*#__PURE__*/_jsx(\"strong\",{className:\"text-danger\",children:\"*\"})]}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsxs(\"select\",{value:caseTypeItem,onChange:v=>setCaseTypeItem(v.target.value),className:` outline-none border ${caseTypeItemError?\"border-danger\":\"border-[#F1F3FF]\"} px-3 py-3 w-full rounded text-sm`,children:[/*#__PURE__*/_jsx(\"option\",{value:\"\",children:\"Select Type Item\"}),/*#__PURE__*/_jsx(\"option\",{value:\"Outpatient\",children:\"Outpatient\"}),/*#__PURE__*/_jsx(\"option\",{value:\"Inpatient\",children:\"Inpatient\"})]}),/*#__PURE__*/_jsx(\"div\",{className:\" text-[8px] text-danger\",children:caseTypeItemError?caseTypeItemError:\"\"})]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex md:flex-row flex-col \",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"md:w-1/2 w-full  md:pr-1 my-1\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"text-[#B4B4B4] text-xs  mb-1\",children:[\"Currency Code\",\" \",/*#__PURE__*/_jsx(\"strong\",{className:\"text-danger\",children:\"*\"})]}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(Select,{value:currencyCode,onChange:option=>{setCurrencyCode(option);},options:CURRENCYITEMS===null||CURRENCYITEMS===void 0?void 0:CURRENCYITEMS.map(currency=>({value:currency.code,label:currency.name!==\"\"?currency.name+\" (\"+currency.code+\") \"||\"\":\"\"})),filterOption:(option,inputValue)=>option.label.toLowerCase().includes(inputValue.toLowerCase()),className:\"text-sm\",placeholder:\"Select Currency Code ...\",isSearchable:true,styles:{control:(base,state)=>({...base,background:\"#fff\",border:currencyCodeError?\"1px solid #d34053\":\"1px solid #F1F3FF\",boxShadow:state.isFocused?\"none\":\"none\",\"&:hover\":{border:\"1px solid #F1F3FF\"}}),option:base=>({...base,display:\"flex\",alignItems:\"center\"}),singleValue:base=>({...base,display:\"flex\",alignItems:\"center\"})}}),/*#__PURE__*/_jsx(\"div\",{className:\" text-[8px] text-danger\",children:currencyCodeError?currencyCodeError:\"\"})]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"md:w-1/2 w-full  md:pr-1 my-1\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"text-[#B4B4B4] text-xs  mb-1\",children:[\"Price of service\",\" \",/*#__PURE__*/_jsx(\"strong\",{className:\"text-danger\",children:\"*\"})]}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"input\",{className:` outline-none border ${priceTotalError?\"border-danger\":\"border-[#F1F3FF]\"}  px-3 py-2 w-full rounded text-sm`,type:\"number\",min:0,step:0.01,placeholder:\"0.00\",value:priceTotal,onChange:v=>setPriceTotal(v.target.value)}),/*#__PURE__*/_jsx(\"div\",{className:\" text-[8px] text-danger\",children:priceTotalError?priceTotalError:\"\"})]})]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex md:flex-row flex-col \",children:[/*#__PURE__*/_jsx(\"div\",{className:\"md:w-1/2 w-full  md:pr-1 my-1\",children:/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"input\",{type:\"checkbox\",name:\"ispay\",id:\"ispay\",checked:isPay===true,onChange:v=>{setIsPay(true);}}),/*#__PURE__*/_jsx(\"label\",{className:\"mx-1 text-[#B4B4B4] text-sm  cursor-pointer\",for:\"ispay\",children:\"Paid\"})]})}),/*#__PURE__*/_jsx(\"div\",{className:\"md:w-1/2 w-full  md:pr-1 my-1\",children:/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"input\",{type:\"checkbox\",name:\"notpay\",id:\"notpay\",checked:isPay===false,onChange:v=>{setIsPay(false);}}),/*#__PURE__*/_jsx(\"label\",{className:\"mx-1 text-[#B4B4B4] text-sm  cursor-pointer\",for:\"notpay\",children:\"Unpaid\"})]})})]}),/*#__PURE__*/_jsx(\"div\",{className:\"flex md:flex-row flex-col  \",children:/*#__PURE__*/_jsxs(\"div\",{className:\" w-full  md:pr-1 my-1\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"text-[#B4B4B4] text-xs  mb-1\",children:\"Description\"}),/*#__PURE__*/_jsx(\"div\",{children:/*#__PURE__*/_jsx(\"textarea\",{value:caseDescription,rows:5,onChange:v=>setCaseDescription(v.target.value),className:\" outline-none border border-[#F1F3FF] px-3 py-2 w-full rounded text-sm\"})})]})})]}),/*#__PURE__*/_jsx(\"div\",{className:\"flex flex-row items-center justify-end my-3\",children:/*#__PURE__*/_jsx(\"button\",{onClick:()=>{var check=true;setFirstNameError(\"\");setLastNameError(\"\");setBirthDateError(\"\");setPhoneError(\"\");setEmailError(\"\");setAddressError(\"\");setCaseTypeError(\"\");setCaseTypeItemError(\"\");setCaseDateError(\"\");setCoordinatorError(\"\");setCityError(\"\");setCountryError(\"\");setCurrencyCodeError(\"\");setPriceTotalError(\"\");if(firstName===\"\"){setFirstNameError(\"This field is required.\");check=false;}if(phone===\"\"){setPhoneError(\"This field is required.\");check=false;}if(country===\"\"||country.value===\"\"){setCountryError(\"This field is required.\");check=false;}if(city===\"\"){setCityError(\"This field is required.\");check=false;}if(currencyCode===\"\"||currencyCode.value===\"\"){setCurrencyCodeError(\"This field is required.\");check=false;}if(priceTotal===\"\"){setPriceTotalError(\"This field is required.\");check=false;}if(coordinator===\"\"||coordinator.value===\"\"){setCoordinatorError(\"This field is required.\");check=false;}if(caseDate===\"\"){setCaseDateError(\"This field is required.\");check=false;}if(caseType===\"\"){setCaseTypeError(\"This field is required.\");check=false;}else if(caseType===\"Medical\"&&caseTypeItem===\"\"){setCaseTypeItemError(\"This field is required.\");check=false;}if(check){setStepSelect(1);}else{toast.error(\"Some fields are empty or invalid. please try again\");}},className:\"text-white bg-[#0388A6] text-sm px-5 py-3 rounded-full\",children:\"Save & Continue\"})})]}):null,stepSelect===1?/*#__PURE__*/_jsxs(\"div\",{className:\"\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"text-[#0388A6] font-semibold text-xl\",children:\"Coordination Details\"}),/*#__PURE__*/_jsx(\"div\",{className:\"text-xs font-medium mt-5 mb-2 text-black\",children:\"Initial Coordination Status:\"}),/*#__PURE__*/_jsx(\"div\",{className:\"my-2 bg-white py-4 px-2 rounded-md\",children:/*#__PURE__*/_jsx(\"div\",{className:\"flex md:flex-row flex-col  \",children:/*#__PURE__*/_jsxs(\"div\",{className:\"w-full  md:pr-1 my-1\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"text-[#B4B4B4] text-xs  mb-1\",children:[\"Status \",/*#__PURE__*/_jsx(\"strong\",{className:\"text-danger\",children:\"*\"})]}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsxs(\"div\",{className:\"flex flex-wrap\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"flex flex-row text-xs items-center my-3 text-danger\",children:[/*#__PURE__*/_jsx(\"input\",{onChange:v=>{if(!coordinatStatusList.includes(\"pending-coordination\")){setCoordinatStatusList([...coordinatStatusList,\"pending-coordination\"]);}else{setCoordinatStatusList(coordinatStatusList.filter(status=>status!==\"pending-coordination\"));}},id:\"pending-coordination\",type:\"checkbox\",checked:coordinatStatusList.includes(\"pending-coordination\"),className:\"mx-1\"}),/*#__PURE__*/_jsx(\"label\",{for:\"pending-coordination\",className:\"flex-1 mx-1  cursor-pointer \",children:\"Pending Coordination\"})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex flex-row text-xs items-center my-3 text-[#FFA500]\",children:[/*#__PURE__*/_jsx(\"input\",{onChange:v=>{if(!coordinatStatusList.includes(\"coordinated-missing-m-r\")){setCoordinatStatusList([...coordinatStatusList,\"coordinated-missing-m-r\"]);}else{setCoordinatStatusList(coordinatStatusList.filter(status=>status!==\"coordinated-missing-m-r\"));}},checked:coordinatStatusList.includes(\"coordinated-missing-m-r\"),id:\"coordinated-Missing-m-r\",type:\"checkbox\",className:\"mx-1\"}),/*#__PURE__*/_jsx(\"label\",{for:\"coordinated-Missing-m-r\",className:\"flex-1 mx-1  cursor-pointer \",children:\"Coordinated, Missing M.R.\"})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex flex-row text-xs items-center my-3 text-[#FFA500]\",children:[/*#__PURE__*/_jsx(\"input\",{onChange:v=>{if(!coordinatStatusList.includes(\"coordinated-missing-invoice\")){setCoordinatStatusList([...coordinatStatusList,\"coordinated-missing-invoice\"]);}else{setCoordinatStatusList(coordinatStatusList.filter(status=>status!==\"coordinated-missing-invoice\"));}},checked:coordinatStatusList.includes(\"coordinated-missing-invoice\"),id:\"coordinated-missing-invoice\",type:\"checkbox\",className:\"mx-1\"}),/*#__PURE__*/_jsx(\"label\",{for:\"coordinated-missing-invoice\",className:\"flex-1 mx-1  cursor-pointer \",children:\"Coordinated, Missing Invoice\"})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex flex-row text-xs items-center my-3 text-primary\",children:[/*#__PURE__*/_jsx(\"input\",{onChange:v=>{if(!coordinatStatusList.includes(\"waiting-for-insurance-authorization\")){setCoordinatStatusList([...coordinatStatusList,\"waiting-for-insurance-authorization\"]);}else{setCoordinatStatusList(coordinatStatusList.filter(status=>status!==\"waiting-for-insurance-authorization\"));}},checked:coordinatStatusList.includes(\"waiting-for-insurance-authorization\"),id:\"waiting-for-insurance-authorization\",type:\"checkbox\",className:\"mx-1\"}),/*#__PURE__*/_jsx(\"label\",{for:\"waiting-for-insurance-authorization\",className:\"flex-1 mx-1  cursor-pointer \",children:\"Waiting for Insurance Authorization\"})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex flex-row text-xs items-center my-3 text-primary\",children:[/*#__PURE__*/_jsx(\"input\",{onChange:v=>{if(!coordinatStatusList.includes(\"coordinated-patient-not-seen-yet\")){setCoordinatStatusList([...coordinatStatusList,\"coordinated-patient-not-seen-yet\"]);}else{setCoordinatStatusList(coordinatStatusList.filter(status=>status!==\"coordinated-patient-not-seen-yet\"));}},checked:coordinatStatusList.includes(\"coordinated-patient-not-seen-yet\"),id:\"coordinated-patient-not-seen-yet\",type:\"checkbox\",className:\"mx-1\"}),/*#__PURE__*/_jsx(\"label\",{for:\"coordinated-patient-not-seen-yet\",className:\"flex-1 mx-1  cursor-pointer \",children:\"Coordinated, Patient not seen yet\"})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex flex-row text-xs items-center my-3 text-primary\",children:[/*#__PURE__*/_jsx(\"input\",{onChange:v=>{if(!coordinatStatusList.includes(\"coordination-fee\")){setCoordinatStatusList([...coordinatStatusList,\"coordination-fee\"]);}else{setCoordinatStatusList(coordinatStatusList.filter(status=>status!==\"coordination-fee\"));}},checked:coordinatStatusList.includes(\"coordination-fee\"),id:\"coordination-fee\",type:\"checkbox\",className:\"mx-1\"}),/*#__PURE__*/_jsx(\"label\",{for:\"coordination-fee\",className:\"flex-1 mx-1  cursor-pointer \",children:\"Coordination Fee\"})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex flex-row text-xs items-center my-3 text-primary\",children:[/*#__PURE__*/_jsx(\"input\",{onChange:v=>{if(!coordinatStatusList.includes(\"coordinated-missing-payment\")){setCoordinatStatusList([...coordinatStatusList,\"coordinated-missing-payment\"]);}else{setCoordinatStatusList(coordinatStatusList.filter(status=>status!==\"coordinated-missing-payment\"));}},checked:coordinatStatusList.includes(\"coordinated-missing-payment\"),id:\"coordinated-missing-payment\",type:\"checkbox\",className:\"mx-1\"}),/*#__PURE__*/_jsx(\"label\",{for:\"coordinated-missing-payment\",className:\"flex-1 mx-1  cursor-pointer \",children:\"Coordinated, Missing Payment\"})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex flex-row text-xs items-center my-3 text-[#008000]\",children:[/*#__PURE__*/_jsx(\"input\",{onChange:v=>{if(!coordinatStatusList.includes(\"fully-coordinated\")){setCoordinatStatusList([...coordinatStatusList,\"fully-coordinated\"]);}else{setCoordinatStatusList(coordinatStatusList.filter(status=>status!==\"fully-coordinated\"));}},checked:coordinatStatusList.includes(\"fully-coordinated\"),id:\"fully-coordinated\",type:\"checkbox\",className:\"mx-1\"}),/*#__PURE__*/_jsx(\"label\",{for:\"fully-coordinated\",className:\"flex-1 mx-1  cursor-pointer \",children:\"Fully Coordinated\"})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex flex-row text-xs items-center my-3 text-[#d34053]\",children:[/*#__PURE__*/_jsx(\"input\",{onChange:v=>{if(!coordinatStatusList.includes(\"failed\")){setCoordinatStatusList([...coordinatStatusList,\"failed\"]);}else{setCoordinatStatusList(coordinatStatusList.filter(status=>status!==\"failed\"));}},checked:coordinatStatusList.includes(\"failed\"),id:\"failed\",type:\"checkbox\",className:\"mx-1\"}),/*#__PURE__*/_jsx(\"label\",{for:\"failed\",className:\"flex-1 mx-1  cursor-pointer \",children:\"Failed\"})]})]}),/*#__PURE__*/_jsx(\"div\",{className:\" text-[8px] text-danger\",children:coordinatStatusListError?coordinatStatusListError:\"\"})]})]})})}),/*#__PURE__*/_jsx(\"div\",{className:\"text-xs font-medium mt-5 mb-2 text-black\",children:\"Assistance Details:\"}),/*#__PURE__*/_jsxs(\"div\",{className:\"my-2 bg-white py-4 px-2 rounded-md\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"text-xs font-medium mt-2 mb-2 text-black\",children:\"Appointment Details:\"}),/*#__PURE__*/_jsx(\"div\",{className:\"flex md:flex-row flex-col w-full \",children:caseType===\"Medical\"&&caseTypeItem===\"Inpatient\"?/*#__PURE__*/_jsxs(\"div\",{className:\"flex md:flex-row flex-col w-full\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"md:w-1/2 w-full  md:pr-1 my-1\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"text-[#B4B4B4] text-xs  mb-1\",children:\"Hospital Starting Date\"}),/*#__PURE__*/_jsx(\"div\",{children:/*#__PURE__*/_jsx(\"input\",{className:\" outline-none border border-[#F1F3FF] px-3 py-2 w-full rounded text-sm\",type:\"date\",placeholder:\"Hospital Starting Date\",value:startDate,onChange:v=>{setStartDate(v.target.value);// If end date is earlier than new start date, update end date\nif(endDate&&endDate<v.target.value){setEndDate(v.target.value);}}})})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"md:w-1/2 w-full  md:pr-1 my-1\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"text-[#B4B4B4] text-xs  mb-1\",children:\"Hospital Ending Date\"}),/*#__PURE__*/_jsx(\"div\",{children:/*#__PURE__*/_jsx(\"input\",{className:\" outline-none border border-[#F1F3FF] px-3 py-2 w-full rounded text-sm\",type:\"date\",placeholder:\"Hospital Ending Date\",value:endDate,onChange:v=>setEndDate(v.target.value),disabled:!startDate,min:startDate})})]})]}):/*#__PURE__*/_jsxs(\"div\",{className:\" w-full  md:pr-1 my-1\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"text-[#B4B4B4] text-xs  mb-1\",children:\"Appointment Date\"}),/*#__PURE__*/_jsx(\"div\",{children:/*#__PURE__*/_jsx(\"input\",{className:\" outline-none border border-[#F1F3FF] px-3 py-2 w-full rounded text-sm\",type:\"date\",placeholder:\"Appointment Date\",value:appointmentDate,onChange:v=>setAppointmentDate(v.target.value)})})]})}),/*#__PURE__*/_jsx(\"div\",{className:\"flex md:flex-row flex-col  \",children:/*#__PURE__*/_jsxs(\"div\",{className:\"md:w-1/2 w-full  md:pr-1 my-1\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"text-[#B4B4B4] text-xs  mb-1\",children:\"Service Location\"}),/*#__PURE__*/_jsx(\"div\",{children:/*#__PURE__*/_jsx(\"input\",{className:\" outline-none border border-[#F1F3FF] px-3 py-2 w-full rounded text-sm\",type:\"text\",placeholder:\" Service Location\",value:serviceLocation,onChange:v=>setServiceLocation(v.target.value)})})]})}),/*#__PURE__*/_jsx(\"div\",{className:\"text-xs font-medium mt-2 mb-2 text-black\",children:\"Provider Information:\"}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex md:flex-row flex-col  \",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"md:w-1/2  w-full  md:pr-1 my-1\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"text-[#B4B4B4] text-xs  mb-1\",children:\"Provider Name\"}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(Select,{value:providerName,onChange:option=>{var _option$value;setProviderName(option);//\nvar initialProvider=(_option$value=option===null||option===void 0?void 0:option.value)!==null&&_option$value!==void 0?_option$value:\"\";// Show loading indicator while fetching provider services\nsetIsLoading(true);const foundProvider=providers===null||providers===void 0?void 0:providers.find(item=>item.id===initialProvider);if(foundProvider){var _foundProvider$servic;setProviderServices((_foundProvider$servic=foundProvider.services)!==null&&_foundProvider$servic!==void 0?_foundProvider$servic:[]);// Hide loading indicator after services are loaded\nsetTimeout(()=>{setIsLoading(false);},100);}else{setProviderServices([]);setIsLoading(false);}},className:\"text-sm\",options:providers===null||providers===void 0?void 0:providers.map(item=>({value:item.id,label:item.full_name||\"\",city:item.city||\"\",country:item.country||\"\"})),filterOption:(option,inputValue)=>{var _option$label,_option$city,_option$country;// تحسين البحث ليشمل الاسم والمدينة والبلد\nconst searchTerm=inputValue===null||inputValue===void 0?void 0:inputValue.toLowerCase();return((_option$label=option.label)===null||_option$label===void 0?void 0:_option$label.toLowerCase().includes(searchTerm))||((_option$city=option.city)===null||_option$city===void 0?void 0:_option$city.toLowerCase().includes(searchTerm))||((_option$country=option.country)===null||_option$country===void 0?void 0:_option$country.toLowerCase().includes(searchTerm));},placeholder:\"Select Provider...\",isSearchable:true// Add loading indicator\n,isLoading:loadingProviders,styles:{control:(base,state)=>({...base,background:\"#fff\",border:providerNameError?\"1px solid #d34053\":\"1px solid #F1F3FF\",boxShadow:state.isFocused?\"none\":\"none\",\"&:hover\":{border:\"1px solid #F1F3FF\"}}),option:base=>({...base,display:\"flex\",alignItems:\"center\"}),singleValue:base=>({...base,display:\"flex\",alignItems:\"center\"})}}),/*#__PURE__*/_jsx(\"div\",{className:\" text-[8px] text-danger\",children:providerNameError?providerNameError:\"\"})]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"md:w-1/2  w-full  md:pr-1 my-1\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"text-[#B4B4B4] text-xs  mb-1\",children:\"Provider Service\"}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsxs(\"select\",{className:`outline-none border ${providerServiceError?\"border-danger\":\"border-[#F1F3FF]\"}  px-3 py-2 w-full rounded text-sm`,onChange:v=>{setProviderService(v.target.value);},value:providerService,children:[/*#__PURE__*/_jsx(\"option\",{value:\"\"}),providerServices===null||providerServices===void 0?void 0:providerServices.map((service,index)=>{var _service$service_type;return/*#__PURE__*/_jsxs(\"option\",{value:service.id,children:[(_service$service_type=service.service_type)!==null&&_service$service_type!==void 0?_service$service_type:\"\",service.service_specialist!==\"\"?\" : \"+service.service_specialist:\"\"]});})]}),/*#__PURE__*/_jsx(\"div\",{className:\" text-[8px] text-danger\",children:providerServiceError?providerServiceError:\"\"})]})]})]}),/*#__PURE__*/_jsx(\"div\",{className:\"flex md:flex-row flex-col  \",children:/*#__PURE__*/_jsxs(\"div\",{className:\" w-full  md:pr-1 my-1\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"text-[#B4B4B4] text-xs  mb-1\",children:\"Visit Date\"}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"input\",{className:`outline-none border ${providerDateError?\"border-danger\":\"border-[#F1F3FF]\"}  px-3 py-2 w-full rounded text-sm`,type:\"date\",placeholder:\"Visit Date\",value:providerDate,onChange:v=>setProviderDate(v.target.value)}),/*#__PURE__*/_jsx(\"div\",{className:\" text-[8px] text-danger\",children:providerDateError?providerDateError:\"\"})]})]})}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex flex-col  \",children:[/*#__PURE__*/_jsxs(\"button\",{onClick:()=>{// providerMultiSelect\nvar check=true;setProviderNameError(\"\");setProviderServiceError(\"\");setProviderDateError(\"\");if(providerName===\"\"||providerName.value===\"\"){setProviderNameError(\"These fields are required.\");toast.error(\"Provider is required\");check=false;}if(providerService===\"\"){setProviderServiceError(\"These fields are required.\");toast.error(\"Provider Service is required\");check=false;}if(providerDate===\"\"){setProviderDateError(\"These fields are required.\");toast.error(\"Visit Date is required\");check=false;}if(check){const exists=false;// const exists = providerMultiSelect.some(\n//   (provider) =>\n//     String(provider?.provider?.id) ===\n//       String(providerName.value) &&\n//     String(provider?.service?.id) ===\n//       String(providerService)\n// );\nif(!exists){var _providerName$value;// find provider\nvar initialProvider=(_providerName$value=providerName.value)!==null&&_providerName$value!==void 0?_providerName$value:\"\";const foundProvider=providers===null||providers===void 0?void 0:providers.find(item=>String(item.id)===String(initialProvider));console.log(foundProvider);if(foundProvider){var _foundProvider$servic2,_foundProvider$servic3;// found service\nvar initialService=providerService!==null&&providerService!==void 0?providerService:\"\";foundProvider===null||foundProvider===void 0?void 0:(_foundProvider$servic2=foundProvider.services)===null||_foundProvider$servic2===void 0?void 0:_foundProvider$servic2.forEach(element=>{console.log(element.id);});const foundService=foundProvider===null||foundProvider===void 0?void 0:(_foundProvider$servic3=foundProvider.services)===null||_foundProvider$servic3===void 0?void 0:_foundProvider$servic3.find(item=>String(item.id)===String(initialService));if(foundService){// Add the new item if it doesn't exist\nsetProviderMultiSelect([...providerMultiSelect,{provider:foundProvider,service:foundService,date:providerDate}]);setProviderName(\"\");setProviderService(\"\");setProviderDate(\"\");console.log(providerMultiSelect);}else{setProviderNameError(\"This provider service not exist!\");toast.error(\"This provider service not exist!\");}}else{setProviderNameError(\"This provider not exist!\");toast.error(\"This provider not exist!\");}}else{setProviderNameError(\"This provider or service is already added!\");toast.error(\"This provider or service is already added!\");}}},className:\"text-primary  flex flex-row items-center my-2 text-sm\",children:[/*#__PURE__*/_jsx(\"svg\",{xmlns:\"http://www.w3.org/2000/svg\",fill:\"none\",viewBox:\"0 0 24 24\",\"stroke-width\":\"1.5\",stroke:\"currentColor\",class:\"size-4\",children:/*#__PURE__*/_jsx(\"path\",{\"stroke-linecap\":\"round\",\"stroke-linejoin\":\"round\",d:\"M12 9v6m3-3H9m12 0a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z\"})}),/*#__PURE__*/_jsx(\"span\",{children:\" Add Provider \"})]}),/*#__PURE__*/_jsxs(\"div\",{className:\" w-full  md:pr-1 my-1\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"text-[#000000bf] font-bold text-xs  mb-1\",children:\"Providers\"}),/*#__PURE__*/_jsx(\"div\",{className:\"my-2 text-black text-sm\",children:providerMultiSelect===null||providerMultiSelect===void 0?void 0:providerMultiSelect.map((itemProvider,index)=>{var _itemProvider$provide,_itemProvider$provide2,_itemProvider$service,_itemProvider$service2,_itemProvider$service3,_itemProvider$service4,_itemProvider$date;return/*#__PURE__*/_jsxs(\"div\",{className:\"flex flex-row items-center my-1\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"min-w-6 text-center\",children:/*#__PURE__*/_jsx(\"button\",{onClick:()=>{const updatedServices=providerMultiSelect.filter((_,indexF)=>indexF!==index);setProviderMultiSelect(updatedServices);},children:/*#__PURE__*/_jsx(\"svg\",{xmlns:\"http://www.w3.org/2000/svg\",fill:\"none\",viewBox:\"0 0 24 24\",\"stroke-width\":\"1.5\",stroke:\"currentColor\",class:\"size-6\",children:/*#__PURE__*/_jsx(\"path\",{\"stroke-linecap\":\"round\",\"stroke-linejoin\":\"round\",d:\"m9.75 9.75 4.5 4.5m0-4.5-4.5 4.5M21 12a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z\"})})})}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex-1 mx-1 border-l px-1\",children:[/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"b\",{children:\"Provider:\"}),\" \",(_itemProvider$provide=(_itemProvider$provide2=itemProvider.provider)===null||_itemProvider$provide2===void 0?void 0:_itemProvider$provide2.full_name)!==null&&_itemProvider$provide!==void 0?_itemProvider$provide:\"---\"]}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"b\",{children:\"Service:\"}),\" \",(_itemProvider$service=(_itemProvider$service2=itemProvider.service)===null||_itemProvider$service2===void 0?void 0:_itemProvider$service2.service_type)!==null&&_itemProvider$service!==void 0?_itemProvider$service:\"--\"]}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"b\",{children:\"Speciality:\"}),\" \",(_itemProvider$service3=(_itemProvider$service4=itemProvider.service)===null||_itemProvider$service4===void 0?void 0:_itemProvider$service4.service_specialist)!==null&&_itemProvider$service3!==void 0?_itemProvider$service3:\"---\"]}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"b\",{children:\"Date:\"}),\" \",(_itemProvider$date=itemProvider.date)!==null&&_itemProvider$date!==void 0?_itemProvider$date:\"---\"]})]})]},index);})})]})]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex flex-row items-center justify-end my-3\",children:[/*#__PURE__*/_jsx(\"button\",{onClick:()=>setStepSelect(0),className:\"bg-white text-[#0388A6] text-sm px-5 py-3 rounded-full mr-3\",children:\"Back\"}),/*#__PURE__*/_jsx(\"button\",{onClick:()=>{var check=true;setCoordinatStatusListError(\"\");setProviderNameError(\"\");// if (coordinatStatusList.length === 0) {\n//   setCoordinatStatusListError(\n//     \"This fields is required.\"\n//   );\n//   check = false;\n// }\n// if (providerMultiSelect.length === 0) {\n//   setProviderNameError(\n//     \"Please select this and click Add Provider.\"\n//   );\n//   check = false;\n// }\nif(check){setStepSelect(2);}else{toast.error(\"Some fields are empty or invalid. please try again\");}},className:\"text-white bg-[#0388A6] text-sm px-5 py-3 rounded-full\",children:\"Save & Continue\"})]})]}):null,stepSelect===2?/*#__PURE__*/_jsxs(\"div\",{className:\"\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"text-[#0388A6] font-semibold text-xl\",children:\"Medical Reports\"}),/*#__PURE__*/_jsx(\"div\",{className:\"text-xs font-medium mt-5 mb-2 text-black\",children:\"Initial Medical Reports:\"}),/*#__PURE__*/_jsxs(\"div\",{className:\"my-2 bg-white py-4 px-2 rounded-md\",children:[/*#__PURE__*/_jsxs(\"div\",{...getRootPropsInitialMedical({className:\"dropzone\"}),// style={dropzoneStyle}\nclassName:\"bg-[#F5F6FF] w-full min-h-50 flex flex-col items-center justify-center  cursor-pointer\",children:[/*#__PURE__*/_jsx(\"input\",{...getInputPropsInitialMedical()}),/*#__PURE__*/_jsx(\"div\",{className:\"my-2\",children:/*#__PURE__*/_jsx(\"svg\",{xmlns:\"http://www.w3.org/2000/svg\",fill:\"none\",viewBox:\"0 0 24 24\",\"stroke-width\":\"1.5\",stroke:\"currentColor\",className:\"size-8 p-2 bg-[#0388A6] rounded-full text-white\",children:/*#__PURE__*/_jsx(\"path\",{\"stroke-linecap\":\"round\",\"stroke-linejoin\":\"round\",d:\"M3 16.5v2.25A2.25 2.25 0 0 0 5.25 21h13.5A2.25 2.25 0 0 0 21 18.75V16.5m-13.5-9L12 3m0 0 4.5 4.5M12 3v13.5\"})})}),/*#__PURE__*/_jsx(\"div\",{className:\"my-2\",children:\"Drag & Drop Image File or BROWSE\"})]}),/*#__PURE__*/_jsx(\"aside\",{style:thumbsContainer,children:/*#__PURE__*/_jsx(\"div\",{className:\"w-full flex flex-col \",children:filesInitialMedicalReports===null||filesInitialMedicalReports===void 0?void 0:filesInitialMedicalReports.map((file,index)=>/*#__PURE__*/_jsxs(\"div\",{className:\"bg-[#F3F5F7] rounded-xl px-5 py-5 w-full my-2 flex flex-row items-center\",children:[/*#__PURE__*/_jsx(\"div\",{className:\" rounded-full  bg-white text-[#81838E] text-center p-2 shadow-1 \",children:/*#__PURE__*/_jsxs(\"svg\",{xmlns:\"http://www.w3.org/2000/svg\",viewBox:\"0 0 24 24\",fill:\"currentColor\",class:\"size-4\",children:[/*#__PURE__*/_jsx(\"path\",{d:\"M5.625 1.5c-1.036 0-1.875.84-1.875 1.875v17.25c0 1.035.84 1.875 1.875 1.875h12.75c1.035 0 1.875-.84 1.875-1.875V12.75A3.75 3.75 0 0 0 16.5 9h-1.875a1.875 1.875 0 0 1-1.875-1.875V5.25A3.75 3.75 0 0 0 9 1.5H5.625Z\"}),/*#__PURE__*/_jsx(\"path\",{d:\"M12.971 1.816A5.23 5.23 0 0 1 14.25 5.25v1.875c0 .207.168.375.375.375H16.5a5.23 5.23 0 0 1 3.434 1.279 9.768 9.768 0 0 0-6.963-6.963Z\"})]})}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex-1 px-5 text-[#303030] text-sm\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"whitespace-nowrap overflow-hidden text-ellipsis max-w-full flex-shrink text-xs\",children:file.name}),/*#__PURE__*/_jsxs(\"div\",{children:[(file.size/(1024*1024)).toFixed(2),\" mb\"]})]}),/*#__PURE__*/_jsx(\"button\",{onClick:()=>{setFilesInitialMedicalReports(prevFiles=>prevFiles.filter((_,indexToRemove)=>index!==indexToRemove));},className:\"rounded-full p-1 bg-[#E6E7E8] font-bold text-[#9D9D9D]\",children:/*#__PURE__*/_jsx(\"svg\",{xmlns:\"http://www.w3.org/2000/svg\",fill:\"none\",viewBox:\"0 0 24 24\",\"stroke-width\":\"1.5\",stroke:\"currentColor\",class:\"size-5\",children:/*#__PURE__*/_jsx(\"path\",{\"stroke-linecap\":\"round\",\"stroke-linejoin\":\"round\",d:\"M6 18 18 6M6 6l12 12\"})})})]},file.name))})})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex flex-row items-center justify-end my-3\",children:[/*#__PURE__*/_jsx(\"button\",{onClick:()=>setStepSelect(1),className:\"bg-white text-[#0388A6] text-sm px-5 py-3 rounded-full mr-3\",children:\"Back\"}),/*#__PURE__*/_jsx(\"button\",{onClick:()=>setStepSelect(3),className:\"text-white bg-[#0388A6] text-sm px-5 py-3 rounded-full\",children:\"Save & Continue\"})]})]}):null,stepSelect===3?/*#__PURE__*/_jsxs(\"div\",{className:\"\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"text-[#0388A6] font-semibold text-xl\",children:\"Invoices\"}),/*#__PURE__*/_jsx(\"div\",{className:\"text-xs font-medium mt-5 mb-2 text-black\",children:\"Invoice Information:\"}),/*#__PURE__*/_jsxs(\"div\",{className:\"my-2 bg-white py-4 px-2 rounded-md\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"flex md:flex-row flex-col  \",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"md:w-1/2 w-full  md:pr-1 my-1\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"text-[#B4B4B4] text-xs  mb-1\",children:\"Invoice Number (Optional)\"}),/*#__PURE__*/_jsx(\"div\",{children:/*#__PURE__*/_jsx(\"input\",{className:\" outline-none border border-[#F1F3FF] px-3 py-2 w-full rounded text-sm\",type:\"text\",placeholder:\"Invoice Number (Optional)\",value:invoiceNumber,onChange:v=>setInvoiceNumber(v.target.value)})})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"md:w-1/2 w-full  md:pr-1 my-1\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"text-[#B4B4B4] text-xs  mb-1\",children:\"Date Issued (Optional)\"}),/*#__PURE__*/_jsx(\"div\",{children:/*#__PURE__*/_jsx(\"input\",{className:\" outline-none border border-[#F1F3FF] px-3 py-2 w-full rounded text-sm\",type:\"date\",placeholder:\"Date Issued (Optional)\",value:dateIssued,onChange:v=>setDateIssued(v.target.value)})})]})]}),/*#__PURE__*/_jsx(\"div\",{className:\"flex md:flex-row flex-col  \",children:/*#__PURE__*/_jsxs(\"div\",{className:\"w-full  md:pr-1 my-1\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"text-[#B4B4B4] text-xs  mb-1\",children:\"Amount (Optional)\"}),/*#__PURE__*/_jsx(\"div\",{children:/*#__PURE__*/_jsx(\"input\",{className:\" outline-none border border-[#F1F3FF] px-3 py-2 w-full rounded text-sm\",type:\"number\",placeholder:\"Amount (Optional)\",value:amount,onChange:v=>setAmount(v.target.value)})})]})})]}),/*#__PURE__*/_jsx(\"div\",{className:\"text-xs font-medium mt-5 mb-2 text-black\",children:\"Upload Invoice\"}),/*#__PURE__*/_jsxs(\"div\",{className:\"my-2 bg-white py-4 px-2 rounded-md\",children:[/*#__PURE__*/_jsxs(\"div\",{...getRootPropsUploadInvoice({className:\"dropzone\"}),// style={dropzoneStyle}\nclassName:\"bg-[#F5F6FF] w-full min-h-50 flex flex-col items-center justify-center  cursor-pointer\",children:[/*#__PURE__*/_jsx(\"input\",{...getInputPropsUploadInvoice()}),/*#__PURE__*/_jsx(\"div\",{className:\"my-2\",children:/*#__PURE__*/_jsx(\"svg\",{xmlns:\"http://www.w3.org/2000/svg\",fill:\"none\",viewBox:\"0 0 24 24\",\"stroke-width\":\"1.5\",stroke:\"currentColor\",className:\"size-8 p-2 bg-[#0388A6] rounded-full text-white\",children:/*#__PURE__*/_jsx(\"path\",{\"stroke-linecap\":\"round\",\"stroke-linejoin\":\"round\",d:\"M3 16.5v2.25A2.25 2.25 0 0 0 5.25 21h13.5A2.25 2.25 0 0 0 21 18.75V16.5m-13.5-9L12 3m0 0 4.5 4.5M12 3v13.5\"})})}),/*#__PURE__*/_jsx(\"div\",{className:\"my-2\",children:\"Drag & Drop Image File or BROWSE\"})]}),/*#__PURE__*/_jsx(\"aside\",{style:thumbsContainer,children:/*#__PURE__*/_jsx(\"div\",{className:\"w-full flex flex-col \",children:filesUploadInvoice===null||filesUploadInvoice===void 0?void 0:filesUploadInvoice.map((file,index)=>/*#__PURE__*/_jsxs(\"div\",{className:\"bg-[#F3F5F7] rounded-xl px-5 py-5 w-full my-2 flex flex-row items-center\",children:[/*#__PURE__*/_jsx(\"div\",{className:\" rounded-full  bg-white text-[#81838E] text-center p-2 shadow-1 \",children:/*#__PURE__*/_jsxs(\"svg\",{xmlns:\"http://www.w3.org/2000/svg\",viewBox:\"0 0 24 24\",fill:\"currentColor\",class:\"size-4\",children:[/*#__PURE__*/_jsx(\"path\",{d:\"M5.625 1.5c-1.036 0-1.875.84-1.875 1.875v17.25c0 1.035.84 1.875 1.875 1.875h12.75c1.035 0 1.875-.84 1.875-1.875V12.75A3.75 3.75 0 0 0 16.5 9h-1.875a1.875 1.875 0 0 1-1.875-1.875V5.25A3.75 3.75 0 0 0 9 1.5H5.625Z\"}),/*#__PURE__*/_jsx(\"path\",{d:\"M12.971 1.816A5.23 5.23 0 0 1 14.25 5.25v1.875c0 .207.168.375.375.375H16.5a5.23 5.23 0 0 1 3.434 1.279 9.768 9.768 0 0 0-6.963-6.963Z\"})]})}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex-1 px-5 text-[#303030] text-sm\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"whitespace-nowrap overflow-hidden text-ellipsis max-w-full flex-shrink text-xs\",children:file.name}),/*#__PURE__*/_jsxs(\"div\",{children:[(file.size/(1024*1024)).toFixed(2),\" mb\"]})]}),/*#__PURE__*/_jsx(\"button\",{onClick:()=>{setFilesUploadInvoice(prevFiles=>prevFiles.filter((_,indexToRemove)=>index!==indexToRemove));},className:\"rounded-full p-1 bg-[#E6E7E8] font-bold text-[#9D9D9D]\",children:/*#__PURE__*/_jsx(\"svg\",{xmlns:\"http://www.w3.org/2000/svg\",fill:\"none\",viewBox:\"0 0 24 24\",\"stroke-width\":\"1.5\",stroke:\"currentColor\",class:\"size-5\",children:/*#__PURE__*/_jsx(\"path\",{\"stroke-linecap\":\"round\",\"stroke-linejoin\":\"round\",d:\"M6 18 18 6M6 6l12 12\"})})})]},file.name))})})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex flex-row items-center justify-end my-3\",children:[/*#__PURE__*/_jsx(\"button\",{onClick:()=>setStepSelect(2),className:\"bg-white text-[#0388A6] text-sm px-5 py-3 rounded-full mr-3\",children:\"Back\"}),/*#__PURE__*/_jsx(\"button\",{onClick:()=>setStepSelect(4),className:\"text-white bg-[#0388A6] text-sm px-5 py-3 rounded-full\",children:\"Save & Continue\"})]})]}):null,stepSelect===4?/*#__PURE__*/_jsxs(\"div\",{className:\"\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"text-[#0388A6] font-semibold text-xl\",children:\"Insurance Authorization\"}),/*#__PURE__*/_jsx(\"div\",{className:\"text-xs font-medium mt-5 mb-2 text-black\",children:\"Insurance Details:\"}),/*#__PURE__*/_jsx(\"div\",{className:\"my-2 bg-white py-4 px-2 rounded-md\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"flex md:flex-row flex-col  \",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"md:w-1/2 w-full  md:pr-1 my-1\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"text-[#B4B4B4] text-xs  mb-1\",children:\"Insurance Company Name\"}),/*#__PURE__*/_jsx(\"div\",{children:/*#__PURE__*/_jsx(Select,{value:insuranceCompany,onChange:option=>{setInsuranceCompany(option);},options:insurances===null||insurances===void 0?void 0:insurances.map(assurance=>({value:assurance.id,label:assurance.assurance_name||\"\"})),filterOption:(option,inputValue)=>option.label.toLowerCase().includes(inputValue.toLowerCase()),className:\"text-sm\",placeholder:\"Select Insurance...\",isSearchable:true,styles:{control:(base,state)=>({...base,background:\"#fff\",border:insuranceCompanyError?\"1px solid #d34053\":\"1px solid #F1F3FF\",boxShadow:state.isFocused?\"none\":\"none\",\"&:hover\":{border:\"1px solid #F1F3FF\"}}),option:base=>({...base,display:\"flex\",alignItems:\"center\"}),singleValue:base=>({...base,display:\"flex\",alignItems:\"center\"})}})})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"md:w-1/2 w-full  md:pr-1 my-1\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"text-[#B4B4B4] text-xs  mb-1\",children:\"Policy Number\"}),/*#__PURE__*/_jsx(\"div\",{children:/*#__PURE__*/_jsx(\"input\",{className:\" outline-none border border-[#F1F3FF] px-3 py-2 w-full rounded text-sm\",type:\"text\",placeholder:\"Policy Number\",value:policyNumber,onChange:v=>setPolicyNumber(v.target.value)})})]})]})}),/*#__PURE__*/_jsx(\"div\",{className:\"text-xs font-medium mt-5 mb-2 text-black\",children:\"Authorization Status:\"}),/*#__PURE__*/_jsx(\"div\",{className:\"my-2 bg-white py-4 px-2 rounded-md\",children:/*#__PURE__*/_jsx(\"div\",{className:\"flex md:flex-row flex-col  \",children:/*#__PURE__*/_jsxs(\"div\",{className:\"w-full  md:pr-1 my-1\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"text-[#B4B4B4] text-xs  mb-1\",children:\"Initial Status\"}),/*#__PURE__*/_jsx(\"div\",{children:/*#__PURE__*/_jsxs(\"select\",{value:initialStatus,onChange:v=>setInitialStatus(v.target.value),className:\" outline-none border border-[#F1F3FF] px-3 py-2 w-full rounded text-sm\",children:[/*#__PURE__*/_jsx(\"option\",{value:\"\",children:\"Select Status\"}),/*#__PURE__*/_jsx(\"option\",{value:\"Pending\",children:\"Pending\"}),/*#__PURE__*/_jsx(\"option\",{value:\"Approved\",children:\"Approved\"}),/*#__PURE__*/_jsx(\"option\",{value:\"Denied\",children:\"Denied\"})]})})]})})}),/*#__PURE__*/_jsx(\"div\",{className:\"text-xs font-medium mt-5 mb-2 text-black\",children:\"Upload Authorization Documents\"}),/*#__PURE__*/_jsxs(\"div\",{className:\"my-2 bg-white py-4 px-2 rounded-md\",children:[/*#__PURE__*/_jsxs(\"div\",{...getRootPropsUploadAuthorizationDocuments({className:\"dropzone\"}),// style={dropzoneStyle}\nclassName:\"bg-[#F5F6FF] w-full min-h-50 flex flex-col items-center justify-center  cursor-pointer\",children:[/*#__PURE__*/_jsx(\"input\",{...getInputPropsUploadAuthorizationDocuments()}),/*#__PURE__*/_jsx(\"div\",{className:\"my-2\",children:/*#__PURE__*/_jsx(\"svg\",{xmlns:\"http://www.w3.org/2000/svg\",fill:\"none\",viewBox:\"0 0 24 24\",\"stroke-width\":\"1.5\",stroke:\"currentColor\",className:\"size-8 p-2 bg-[#0388A6] rounded-full text-white\",children:/*#__PURE__*/_jsx(\"path\",{\"stroke-linecap\":\"round\",\"stroke-linejoin\":\"round\",d:\"M3 16.5v2.25A2.25 2.25 0 0 0 5.25 21h13.5A2.25 2.25 0 0 0 21 18.75V16.5m-13.5-9L12 3m0 0 4.5 4.5M12 3v13.5\"})})}),/*#__PURE__*/_jsx(\"div\",{className:\"my-2\",children:\"Drag & Drop Image File or BROWSE\"})]}),/*#__PURE__*/_jsx(\"aside\",{style:thumbsContainer,children:/*#__PURE__*/_jsx(\"div\",{className:\"w-full flex flex-col \",children:filesUploadAuthorizationDocuments===null||filesUploadAuthorizationDocuments===void 0?void 0:filesUploadAuthorizationDocuments.map((file,index)=>/*#__PURE__*/_jsxs(\"div\",{className:\"bg-[#F3F5F7] rounded-xl px-5 py-5 w-full my-2 flex flex-row items-center\",children:[/*#__PURE__*/_jsx(\"div\",{className:\" rounded-full  bg-white text-[#81838E] text-center p-2 shadow-1 \",children:/*#__PURE__*/_jsxs(\"svg\",{xmlns:\"http://www.w3.org/2000/svg\",viewBox:\"0 0 24 24\",fill:\"currentColor\",class:\"size-4\",children:[/*#__PURE__*/_jsx(\"path\",{d:\"M5.625 1.5c-1.036 0-1.875.84-1.875 1.875v17.25c0 1.035.84 1.875 1.875 1.875h12.75c1.035 0 1.875-.84 1.875-1.875V12.75A3.75 3.75 0 0 0 16.5 9h-1.875a1.875 1.875 0 0 1-1.875-1.875V5.25A3.75 3.75 0 0 0 9 1.5H5.625Z\"}),/*#__PURE__*/_jsx(\"path\",{d:\"M12.971 1.816A5.23 5.23 0 0 1 14.25 5.25v1.875c0 .207.168.375.375.375H16.5a5.23 5.23 0 0 1 3.434 1.279 9.768 9.768 0 0 0-6.963-6.963Z\"})]})}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex-1 px-5 text-[#303030] text-sm\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"whitespace-nowrap overflow-hidden text-ellipsis max-w-full flex-shrink text-xs\",children:file.name}),/*#__PURE__*/_jsxs(\"div\",{children:[(file.size/(1024*1024)).toFixed(2),\" mb\"]})]}),/*#__PURE__*/_jsx(\"button\",{onClick:()=>{setFilesUploadAuthorizationDocuments(prevFiles=>prevFiles.filter((_,indexToRemove)=>index!==indexToRemove));},className:\"rounded-full p-1 bg-[#E6E7E8] font-bold text-[#9D9D9D]\",children:/*#__PURE__*/_jsx(\"svg\",{xmlns:\"http://www.w3.org/2000/svg\",fill:\"none\",viewBox:\"0 0 24 24\",\"stroke-width\":\"1.5\",stroke:\"currentColor\",class:\"size-5\",children:/*#__PURE__*/_jsx(\"path\",{\"stroke-linecap\":\"round\",\"stroke-linejoin\":\"round\",d:\"M6 18 18 6M6 6l12 12\"})})})]},file.name))})})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex flex-row items-center justify-end my-3\",children:[/*#__PURE__*/_jsx(\"button\",{onClick:()=>setStepSelect(3),className:\"bg-white text-[#0388A6] text-sm px-5 py-3 rounded-full mr-3\",children:\"Back\"}),/*#__PURE__*/_jsx(\"button\",{disabled:loadingCaseAdd,onClick:async()=>{var _currencyCode$value;const providerItems=providerMultiSelect===null||providerMultiSelect===void 0?void 0:providerMultiSelect.map(item=>{var _item$service,_item$provider;return{service:(_item$service=item.service)===null||_item$service===void 0?void 0:_item$service.id,provider:(_item$provider=item.provider)===null||_item$provider===void 0?void 0:_item$provider.id,date:item.date};});await dispatch(addNewCase({first_name:firstName,last_name:lastName,full_name:firstName+\" \"+lastName,birth_day:birthDate,patient_phone:phone,patient_email:email,patient_address:address,patient_city:city,patient_country:country.value,//\ncoordinator:coordinator.value,case_date:caseDate,case_type:caseType,case_type_item:caseType===\"Medical\"?caseTypeItem:\"\",case_description:caseDescription,//\nstatus_coordination:coordinatStatus,case_status:coordinatStatusList,appointment_date:caseTypeItem===\"Inpatient\"?\"\":appointmentDate,start_date:caseTypeItem===\"Inpatient\"?startDate:\"\",end_date:caseTypeItem===\"Inpatient\"?endDate:\"\",service_location:serviceLocation,provider:providerName.value,//\ninvoice_number:invoiceNumber,date_issued:dateIssued,invoice_amount:amount,assurance:insuranceCompany.value,assurance_number:insuranceNumber,policy_number:policyNumber,assurance_status:initialStatus,// files\ninitial_medical_reports:filesInitialMedicalReports,upload_invoice:filesUploadInvoice,upload_authorization_documents:filesUploadAuthorizationDocuments,//\nproviders:providerItems!==null&&providerItems!==void 0?providerItems:[],//\nis_pay:isPay?\"True\":\"False\",price_tatal:priceTotal,currency_price:(_currencyCode$value=currencyCode.value)!==null&&_currencyCode$value!==void 0?_currencyCode$value:\"\"}));},className:\"text-white bg-[#0388A6] text-sm px-5 py-3 rounded-full\",children:loadingCaseAdd?\"Loading..\":\"Submit\"})]})]}):null,stepSelect===5?/*#__PURE__*/_jsx(\"div\",{className:\"\",children:/*#__PURE__*/_jsx(\"div\",{className:\"my-2 bg-white py-4 px-2 rounded-md\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"min-h-30 flex flex-col items-center justify-center\",children:[/*#__PURE__*/_jsx(\"svg\",{xmlns:\"http://www.w3.org/2000/svg\",fill:\"none\",viewBox:\"0 0 24 24\",\"stroke-width\":\"1.5\",stroke:\"currentColor\",className:\"size-13 p-3 rounded-full text-[#0388A6] bg-[#0388A6] bg-opacity-10\",children:/*#__PURE__*/_jsx(\"path\",{\"stroke-linecap\":\"round\",\"stroke-linejoin\":\"round\",d:\"m4.5 12.75 6 6 9-13.5\"})}),/*#__PURE__*/_jsx(\"div\",{className:\"my-5 font-semibold text-2xl text-black\",children:\"Case Created Successfully!\"}),/*#__PURE__*/_jsx(\"div\",{className:\"text-base text-center md:w-2/3 mx-auto w-full px-3\",children:\"Your case has been successfully created and saved. You can now view the case details or create another case.\"}),/*#__PURE__*/_jsx(\"div\",{className:\"flex flex-row items-center justify-end my-3\",children:/*#__PURE__*/_jsx(\"a\",{href:\"/dashboard\",className:\"text-white bg-[#0388A6] text-sm px-5 py-3 rounded-full\",children:\"Go to Dahboard\"})})]})})}):null]})]})})]})]});}export default AddCaseScreen;", "map": {"version": 3, "names": ["React", "useEffect", "useState", "useDispatch", "useSelector", "useLocation", "useNavigate", "DefaultLayout", "addreactionface", "toast", "providersListDashboard", "addNewCase", "Select", "useDropzone", "insurancesListDashboard", "coordinatorsList<PERSON>ash<PERSON>", "COUNTRIES", "CURRENCYITEMS", "GoogleComponent", "jsx", "_jsx", "jsxs", "_jsxs", "STEPSLIST", "index", "title", "description", "thumbsContainer", "display", "flexDirection", "flexWrap", "marginTop", "AddCaseScreen", "navigate", "location", "dispatch", "firstName", "setFirstName", "firstNameError", "setFirstNameError", "lastName", "setLastName", "lastNameError", "setLastNameError", "email", "setEmail", "emailError", "setEmailError", "birthDate", "setBirthDate", "birthDateE<PERSON>r", "setBirthDateError", "phone", "setPhone", "phoneError", "setPhoneError", "address", "<PERSON><PERSON><PERSON><PERSON>", "addressError", "setAddressError", "city", "setCity", "cityError", "setCityError", "country", "setCountry", "countryError", "setCountryError", "isPay", "setIsPay", "currencyCode", "setCurrencyCode", "currencyCodeError", "setCurrencyCodeError", "priceTotal", "setPriceTotal", "priceTotalError", "setPriceTotalError", "coordinator", "setCoordinator", "coordinator<PERSON><PERSON><PERSON>", "setCoordinatorError", "providerServices", "setProviderServices", "providerMultiSelect", "setProviderMultiSelect", "providerService", "setProviderService", "providerServiceError", "setProviderServiceError", "providerDate", "setProviderDate", "providerDateError", "setProviderDateError", "caseDate", "setCaseDate", "Date", "toISOString", "split", "caseDateError", "setCaseDateError", "caseType", "setCaseType", "caseTypeError", "setCaseTypeError", "caseTypeItem", "setCaseTypeItem", "caseTypeItemError", "setCaseTypeItemError", "caseDescription", "setCaseDescription", "caseDescriptionError", "setCaseDescriptionError", "coordinatStatus", "setCoordinatStatus", "coordinatStatusError", "setCoordinatStatusError", "coordinatStatusList", "setCoordinatStatusList", "coordinatStatusListError", "setCoordinatStatusListError", "appointmentDate", "setAppointmentDate", "appointmentDateError", "setAppointmentDateError", "startDate", "setStartDate", "startDateError", "setStartDateError", "endDate", "setEndDate", "endDateError", "setEndDateError", "serviceLocation", "setServiceLocation", "serviceLocationError", "setServiceLocationError", "providerName", "setProviderName", "providerNameError", "setProviderNameError", "providerPhone", "setProviderPhone", "providerPhoneError", "setProviderPhoneError", "providerEmail", "setProviderEmail", "providerEmailError", "setProviderEmailError", "providerAddress", "set<PERSON>roviderAddress", "providerAddressError", "setProviderAddressError", "invoiceNumber", "setInvoiceNumber", "invoiceNumberError", "setInvoiceNumberError", "dateIssued", "setDateIssued", "dateIssuedError", "setDateIssuedError", "amount", "setAmount", "amountError", "setAmountError", "insuranceCompany", "setInsuranceCompany", "insuranceCompanyError", "setInsuranceCompanyError", "insuranceNumber", "setInsuranceNumber", "insuranceNumberError", "setInsuranceNumberError", "policyNumber", "setPolicyNumber", "policyNumberError", "setPolicyNumberError", "initialStatus", "setInitialStatus", "initialStatusError", "setInitialStatusError", "filesInitialMedicalReports", "setFilesInitialMedicalReports", "getRootProps", "getRootPropsInitialMedical", "getInputProps", "getInputPropsInitialMedical", "accept", "onDrop", "acceptedFiles", "prevFiles", "map", "file", "Object", "assign", "preview", "URL", "createObjectURL", "for<PERSON>ach", "revokeObjectURL", "filesUploadInvoice", "setFilesUploadInvoice", "getRootPropsUploadInvoice", "getInputPropsUploadInvoice", "filesUploadAuthorizationDocuments", "setFilesUploadAuthorizationDocuments", "getRootPropsUploadAuthorizationDocuments", "getInputPropsUploadAuthorizationDocuments", "stepSelect", "setStepSelect", "isLoading", "setIsLoading", "userLogin", "state", "userInfo", "listProviders", "providerList", "providers", "loadingProviders", "errorProviders", "createCase", "createNewCase", "loadingCaseAdd", "successCaseAdd", "errorCaseAdd", "listInsurances", "insuranceList", "insurances", "loadingInsurances", "errorInsurances", "listCoordinators", "coordinatorsList", "coordinators", "loadingCoordinators", "errorCoordinators", "redirect", "timeoutId", "setTimeout", "console", "log", "clearTimeout", "length", "children", "className", "href", "xmlns", "fill", "viewBox", "stroke", "strokeLinecap", "strokeLinejoin", "d", "step", "onClick", "src", "onError", "e", "target", "onerror", "type", "placeholder", "value", "onChange", "v", "option", "options", "label", "icon", "isSearchable", "styles", "control", "base", "background", "border", "boxShadow", "isFocused", "alignItems", "singleValue", "<PERSON><PERSON><PERSON><PERSON>", "onPlaceSelected", "place", "geometry", "_place$formatted_addr", "formatted_address", "defaultValue", "types", "language", "assurance", "id", "assurance_name", "filterOption", "inputValue", "toLowerCase", "includes", "item", "full_name", "currency", "code", "name", "min", "checked", "for", "rows", "check", "error", "filter", "status", "disabled", "_option$value", "initialProvider", "<PERSON><PERSON><PERSON><PERSON>", "find", "_foundProvider$servic", "services", "_option$label", "_option$city", "_option$country", "searchTerm", "service", "_service$service_type", "service_type", "service_specialist", "exists", "_providerName$value", "String", "_foundProvider$servic2", "_foundProvider$servic3", "initialService", "element", "foundService", "provider", "date", "class", "itemProvider", "_itemProvider$provide", "_itemProvider$provide2", "_itemProvider$service", "_itemProvider$service2", "_itemProvider$service3", "_itemProvider$service4", "_itemProvider$date", "updatedServices", "_", "indexF", "style", "size", "toFixed", "indexToRemove", "_currencyCode$value", "providerItems", "_item$service", "_item$provider", "first_name", "last_name", "birth_day", "patient_phone", "patient_email", "patient_address", "patient_city", "patient_country", "case_date", "case_type", "case_type_item", "case_description", "status_coordination", "case_status", "appointment_date", "start_date", "end_date", "service_location", "invoice_number", "date_issued", "invoice_amount", "assurance_number", "policy_number", "assurance_status", "initial_medical_reports", "upload_invoice", "upload_authorization_documents", "is_pay", "price_tatal", "currency_price"], "sources": ["/Users/<USER>/Desktop/Freelances/<PERSON><PERSON><PERSON>/UNIMEDCARE/web-front/src/screens/cases/AddCaseScreen.js"], "sourcesContent": ["import React, { useEffect, useState } from \"react\";\nimport { useDispatch, useSelector } from \"react-redux\";\nimport { useLocation, useNavigate } from \"react-router-dom\";\nimport DefaultLayout from \"../../layouts/DefaultLayout\";\nimport addreactionface from \"../../images/icon/add_reaction.png\";\nimport { toast } from \"react-toastify\";\nimport { providersListDashboard } from \"../../redux/actions/providerActions\";\nimport { addNewCase } from \"../../redux/actions/caseActions\";\n\nimport Select from \"react-select\";\n\nimport { useDropzone } from \"react-dropzone\";\nimport { insurancesListDashboard } from \"../../redux/actions/insuranceActions\";\nimport { coordinatorsListDashboard } from \"../../redux/actions/userActions\";\nimport { COUNTRIES, CURRENCYITEMS } from \"../../constants\";\nimport GoogleComponent from \"react-google-autocomplete\";\n\nconst STEPSLIST = [\n  {\n    index: 0,\n    title: \"General Information\",\n    description:\n      \"Please enter the general information about the patient and the case.\",\n  },\n  {\n    index: 1,\n    title: \"Coordination Details\",\n    description:\n      \"Provide information about the initial coordination & Assistance Details for this case.\",\n  },\n  {\n    index: 2,\n    title: \"Medical Reports\",\n    description: \"Upload any initial medical reports related to the case.\",\n  },\n  {\n    index: 3,\n    title: \"Invoices\",\n    description:\n      \"If there are any initial invoices related to the case, please provide the details and upload the documents.\",\n  },\n  {\n    index: 4,\n    title: \"Insurance Authorization\",\n    description:\n      \"Please provide the details of the insurance authorization for this case, and upload any relevant documents.\",\n  },\n  {\n    index: 5,\n    title: \"Finish\",\n    description: \"You can go back to any step to make changes.\",\n  },\n];\n\nconst thumbsContainer = {\n  display: \"flex\",\n  flexDirection: \"row\",\n  flexWrap: \"wrap\",\n  marginTop: 16,\n};\n\nfunction AddCaseScreen() {\n  const navigate = useNavigate();\n  const location = useLocation();\n  const dispatch = useDispatch();\n\n  //\n  const [firstName, setFirstName] = useState(\"\");\n  const [firstNameError, setFirstNameError] = useState(\"\");\n\n  const [lastName, setLastName] = useState(\"\");\n  const [lastNameError, setLastNameError] = useState(\"\");\n\n  const [email, setEmail] = useState(\"\");\n  const [emailError, setEmailError] = useState(\"\");\n\n  const [birthDate, setBirthDate] = useState(\"\");\n  const [birthDateError, setBirthDateError] = useState(\"\");\n\n  const [phone, setPhone] = useState(\"\");\n  const [phoneError, setPhoneError] = useState(\"\");\n\n  const [address, setAddress] = useState(\"\");\n  const [addressError, setAddressError] = useState(\"\");\n\n  const [city, setCity] = useState(\"\");\n  const [cityError, setCityError] = useState(\"\");\n\n  const [country, setCountry] = useState(\"\");\n  const [countryError, setCountryError] = useState(\"\");\n\n  const [isPay, setIsPay] = useState(false);\n\n  const [currencyCode, setCurrencyCode] = useState(\"\");\n  const [currencyCodeError, setCurrencyCodeError] = useState(\"\");\n\n  const [priceTotal, setPriceTotal] = useState(0);\n  const [priceTotalError, setPriceTotalError] = useState(\"\");\n  //\n  const [coordinator, setCoordinator] = useState(\"\");\n  const [coordinatorError, setCoordinatorError] = useState(\"\");\n\n  const [providerServices, setProviderServices] = useState([]);\n  const [providerMultiSelect, setProviderMultiSelect] = useState([]);\n\n  const [providerService, setProviderService] = useState(\"\");\n  const [providerServiceError, setProviderServiceError] = useState(\"\");\n\n  const [providerDate, setProviderDate] = useState(\"\");\n  const [providerDateError, setProviderDateError] = useState(\"\");\n\n  const [caseDate, setCaseDate] = useState(\n    new Date().toISOString().split(\"T\")[0]\n  );\n  const [caseDateError, setCaseDateError] = useState(\"\");\n\n  const [caseType, setCaseType] = useState(\"\");\n  const [caseTypeError, setCaseTypeError] = useState(\"\");\n\n  const [caseTypeItem, setCaseTypeItem] = useState(\"\");\n  const [caseTypeItemError, setCaseTypeItemError] = useState(\"\");\n\n  const [caseDescription, setCaseDescription] = useState(\"\");\n  const [caseDescriptionError, setCaseDescriptionError] = useState(\"\");\n  //\n  const [coordinatStatus, setCoordinatStatus] = useState(\"\");\n  const [coordinatStatusError, setCoordinatStatusError] = useState(\"\");\n\n  const [coordinatStatusList, setCoordinatStatusList] = useState([]);\n  const [coordinatStatusListError, setCoordinatStatusListError] = useState(\"\");\n\n  const [appointmentDate, setAppointmentDate] = useState(\"\");\n  const [appointmentDateError, setAppointmentDateError] = useState(\"\");\n\n  const [startDate, setStartDate] = useState(\"\");\n  const [startDateError, setStartDateError] = useState(\"\");\n\n  const [endDate, setEndDate] = useState(\"\");\n  const [endDateError, setEndDateError] = useState(\"\");\n\n  const [serviceLocation, setServiceLocation] = useState(\"\");\n  const [serviceLocationError, setServiceLocationError] = useState(\"\");\n  //\n  const [providerName, setProviderName] = useState(\"\");\n  const [providerNameError, setProviderNameError] = useState(\"\");\n\n  const [providerPhone, setProviderPhone] = useState(\"\");\n  const [providerPhoneError, setProviderPhoneError] = useState(\"\");\n\n  const [providerEmail, setProviderEmail] = useState(\"\");\n  const [providerEmailError, setProviderEmailError] = useState(\"\");\n\n  const [providerAddress, setProviderAddress] = useState(\"\");\n  const [providerAddressError, setProviderAddressError] = useState(\"\");\n  //\n  const [invoiceNumber, setInvoiceNumber] = useState(\"\");\n  const [invoiceNumberError, setInvoiceNumberError] = useState(\"\");\n\n  const [dateIssued, setDateIssued] = useState(\"\");\n  const [dateIssuedError, setDateIssuedError] = useState(\"\");\n\n  const [amount, setAmount] = useState(0);\n  const [amountError, setAmountError] = useState(\"\");\n  //\n  const [insuranceCompany, setInsuranceCompany] = useState(\"\");\n  const [insuranceCompanyError, setInsuranceCompanyError] = useState(\"\");\n\n  const [insuranceNumber, setInsuranceNumber] = useState(\"\");\n  const [insuranceNumberError, setInsuranceNumberError] = useState(\"\");\n\n  const [policyNumber, setPolicyNumber] = useState(\"\");\n  const [policyNumberError, setPolicyNumberError] = useState(\"\");\n\n  const [initialStatus, setInitialStatus] = useState(\"\");\n  const [initialStatusError, setInitialStatusError] = useState(\"\");\n\n  // fils\n  // initialMedicalReports\n  const [filesInitialMedicalReports, setFilesInitialMedicalReports] = useState(\n    []\n  );\n  const {\n    getRootProps: getRootPropsInitialMedical,\n    getInputProps: getInputPropsInitialMedical,\n  } = useDropzone({\n    accept: {\n      \"*\": [],\n    },\n    onDrop: (acceptedFiles) => {\n      setFilesInitialMedicalReports((prevFiles) => [\n        ...prevFiles,\n        ...acceptedFiles.map((file) =>\n          Object.assign(file, {\n            preview: URL.createObjectURL(file),\n          })\n        ),\n      ]);\n    },\n  });\n\n  useEffect(() => {\n    return () =>\n      filesInitialMedicalReports.forEach((file) =>\n        URL.revokeObjectURL(file.preview)\n      );\n  }, []);\n\n  // Upload Invoice\n  const [filesUploadInvoice, setFilesUploadInvoice] = useState([]);\n  const {\n    getRootProps: getRootPropsUploadInvoice,\n    getInputProps: getInputPropsUploadInvoice,\n  } = useDropzone({\n    accept: {\n      \"*\": [],\n    },\n    onDrop: (acceptedFiles) => {\n      setFilesUploadInvoice((prevFiles) => [\n        ...prevFiles,\n        ...acceptedFiles.map((file) =>\n          Object.assign(file, {\n            preview: URL.createObjectURL(file),\n          })\n        ),\n      ]);\n    },\n  });\n\n  useEffect(() => {\n    return () =>\n      filesUploadInvoice.forEach((file) => URL.revokeObjectURL(file.preview));\n  }, []);\n  // Upload Authorization Documents\n  const [\n    filesUploadAuthorizationDocuments,\n    setFilesUploadAuthorizationDocuments,\n  ] = useState([]);\n  const {\n    getRootProps: getRootPropsUploadAuthorizationDocuments,\n    getInputProps: getInputPropsUploadAuthorizationDocuments,\n  } = useDropzone({\n    accept: {\n      \"*\": [],\n    },\n    onDrop: (acceptedFiles) => {\n      setFilesUploadAuthorizationDocuments((prevFiles) => [\n        ...prevFiles,\n        ...acceptedFiles.map((file) =>\n          Object.assign(file, {\n            preview: URL.createObjectURL(file),\n          })\n        ),\n      ]);\n    },\n  });\n\n  useEffect(() => {\n    return () =>\n      filesUploadAuthorizationDocuments.forEach((file) =>\n        URL.revokeObjectURL(file.preview)\n      );\n  }, []);\n\n  // Configure react-dropzone\n\n  //\n\n  const [stepSelect, setStepSelect] = useState(0);\n  const [isLoading, setIsLoading] = useState(true);\n\n  const userLogin = useSelector((state) => state.userLogin);\n  const { userInfo } = userLogin;\n\n  const listProviders = useSelector((state) => state.providerList);\n  const { providers, loadingProviders, errorProviders } = listProviders;\n\n  const createCase = useSelector((state) => state.createNewCase);\n  const { loadingCaseAdd, successCaseAdd, errorCaseAdd } = createCase;\n\n  const listInsurances = useSelector((state) => state.insuranceList);\n  const { insurances, loadingInsurances, errorInsurances } = listInsurances;\n\n  const listCoordinators = useSelector((state) => state.coordinatorsList);\n  const { coordinators, loadingCoordinators, errorCoordinators } =\n    listCoordinators;\n\n  const redirect = \"/\";\n  useEffect(() => {\n    if (!userInfo) {\n      navigate(redirect);\n    } else {\n      // Set loading state to true when starting to fetch data\n      setIsLoading(true);\n\n      setStepSelect(0);\n      dispatch(coordinatorsListDashboard(\"0\"));\n      dispatch(providersListDashboard(\"0\"));\n      dispatch(insurancesListDashboard(\"0\"));\n      //   dispatch(clientList(\"0\"));\n\n      // Set a maximum timeout for the loading indicator (30 seconds) as a fallback\n      const timeoutId = setTimeout(() => {\n        setIsLoading(false);\n        console.log(\"Maximum loading time reached, hiding loading indicator\");\n      }, 6000);\n\n      // Clean up the timeout when the component unmounts\n      return () => clearTimeout(timeoutId);\n    }\n  }, [navigate, userInfo, dispatch]);\n\n  useEffect(() => {\n    if (successCaseAdd) {\n      setStepSelect(5);\n      setIsLoading(false);\n    }\n  }, [successCaseAdd]);\n\n  // Update loading state when case add is in progress\n  useEffect(() => {\n    if (loadingCaseAdd) {\n      setIsLoading(true);\n    }\n  }, [loadingCaseAdd]);\n\n  // Update loading state based on data loading status\n  useEffect(() => {\n    // Check if essential data is loaded\n    if (\n      !loadingProviders &&\n      !loadingInsurances &&\n      !loadingCoordinators &&\n      providers &&\n      providers.length > 0 &&\n      coordinators &&\n      coordinators.length > 0\n    ) {\n      // Hide loading indicator as soon as we have the essential data\n      setIsLoading(false);\n    }\n  }, [\n    loadingProviders,\n    loadingInsurances,\n    loadingCoordinators,\n    providers,\n    coordinators,\n  ]);\n\n  return (\n    <DefaultLayout>\n      {/* Global Loading Indicator */}\n      {isLoading && (\n        <div className=\"fixed top-0 left-0 w-full h-full bg-black bg-opacity-50 flex items-center justify-center z-50\">\n          <div className=\"bg-white p-5 rounded-lg shadow-lg flex flex-col items-center\">\n            <div className=\"animate-spin rounded-full h-16 w-16 border-t-4 border-b-4 border-[#0388A6] mb-3\"></div>\n            <div className=\"text-gray-700 font-medium\">Loading data...</div>\n          </div>\n        </div>\n      )}\n\n      <div className=\"\">\n        <div className=\"flex flex-row text-sm items-center my-1\">\n          {/* home */}\n          <a href=\"/dashboard\">\n            <div className=\"flex flex-row  items-center hover:text-black \">\n              <svg\n                xmlns=\"http://www.w3.org/2000/svg\"\n                fill=\"none\"\n                viewBox=\"0 0 24 24\"\n                stroke-width=\"1.5\"\n                stroke=\"currentColor\"\n                className=\"w-4 h-4\"\n              >\n                <path\n                  strokeLinecap=\"round\"\n                  strokeLinejoin=\"round\"\n                  d=\"m2.25 12 8.954-8.955c.44-.439 1.152-.439 1.591 0L21.75 12M4.5 9.75v10.125c0 .621.504 1.125 1.125 1.125H9.75v-4.875c0-.621.504-1.125 1.125-1.125h2.25c.621 0 1.125.504 1.125 1.125V21h4.125c.621 0 1.125-.504 1.125-1.125V9.75M8.25 21h8.25\"\n                />\n              </svg>\n              <span className=\"mx-1\">Dashboard</span>\n            </div>\n          </a>\n          <span>\n            <svg\n              xmlns=\"http://www.w3.org/2000/svg\"\n              fill=\"none\"\n              viewBox=\"0 0 24 24\"\n              stroke-width=\"1.5\"\n              stroke=\"currentColor\"\n              className=\"w-4 h-4\"\n            >\n              <path\n                strokeLinecap=\"round\"\n                strokeLinejoin=\"round\"\n                d=\"m8.25 4.5 7.5 7.5-7.5 7.5\"\n              />\n            </svg>\n          </span>\n          <div className=\"\">Create New Case</div>\n        </div>\n        {/*  */}\n        <div className=\"py-5 px-4 flex justify-between\">\n          <h4 className=\" uppercase font-semibold text-black dark:text-white\">\n            New Case\n          </h4>\n        </div>\n        {/*  */}\n        <div className=\"rounded-sm border border-stroke bg-white px-5 pt-6 pb-2.5 shadow-default dark:border-strokedark dark:bg-boxdark sm:px-7.5 xl:pb-1\">\n          <div className=\"flex md:flex-row flex-col\">\n            <div className=\"md:w-1/3 w-full flex md:flex-col flex-wrap px-3  relative\">\n              <div className=\"w-[1px] h-full absolute top-0 bg-[#0388A6] bg-opacity-60 left-[27.5px] md:block hidden\"></div>\n              {STEPSLIST?.map((step, index) => (\n                <div\n                  onClick={() => {\n                    if (stepSelect > step.index && stepSelect !== 5) {\n                      setStepSelect(step.index);\n                    }\n                  }}\n                  className={`flex flex-row mb-3 md:min-h-20 ${\n                    stepSelect > step.index && stepSelect !== 5\n                      ? \"cursor-pointer\"\n                      : \"\"\n                  } md:items-start items-center`}\n                >\n                  {stepSelect < step.index ? (\n                    <div className=\"size-8 bg-[#E2EFF2] z-10  rounded-full text-center items-center flex justify-center text-[#0388A6]\">\n                      <img\n                        src={addreactionface}\n                        className=\"size-5\"\n                        onError={(e) => {\n                          e.target.onerror = null;\n                          e.target.src = \"/assets/placeholder.png\";\n                        }}\n                      />\n                    </div>\n                  ) : stepSelect === step.index ? (\n                    <div className=\"size-8 bg-white z-10  border-[11px] rounded-full\"></div>\n                  ) : (\n                    <div className=\"size-8 bg-[#E2EFF2] z-10  rounded-full text-center items-center flex justify-center text-[#0388A6]\">\n                      <svg\n                        xmlns=\"http://www.w3.org/2000/svg\"\n                        fill=\"none\"\n                        viewBox=\"0 0 24 24\"\n                        stroke-width=\"1.5\"\n                        stroke=\"currentColor\"\n                        className=\"size-5\"\n                      >\n                        <path\n                          stroke-linecap=\"round\"\n                          stroke-linejoin=\"round\"\n                          d=\"m4.5 12.75 6 6 9-13.5\"\n                        />\n                      </svg>\n                    </div>\n                  )}\n\n                  <div className=\"text-black flex-1 px-2\">\n                    <div className=\"font-medium text-sm\">{step.title}</div>\n                    {stepSelect === step.index ? (\n                      <div className=\"text-xs font-light md:block hidden\">\n                        {step.description}\n                      </div>\n                    ) : null}\n                  </div>\n                </div>\n              ))}\n            </div>\n            <div className=\"md:w-2/3 w-full  px-3 py-4  bg-[#F8F9FF]\">\n              {/* step 1 - General Information */}\n              {stepSelect === 0 ? (\n                <div className=\"\">\n                  <div className=\"text-[#0388A6] font-semibold text-xl\">\n                    General Information\n                  </div>\n                  {/* Patient Details: */}\n                  <div className=\"text-xs font-medium mt-5 mb-2 text-black\">\n                    Patient Details:\n                  </div>\n                  <div className=\"my-2 bg-white py-4 px-2 rounded-md\">\n                    <div className=\"flex md:flex-row flex-col  \">\n                      <div className=\"md:w-1/2 w-full  md:pr-1 my-1\">\n                        <div className=\"text-[#B4B4B4] text-xs  mb-1\">\n                          First Name <strong className=\"text-danger\">*</strong>\n                        </div>\n                        <div>\n                          <input\n                            className={` outline-none border ${\n                              firstNameError\n                                ? \"border-danger\"\n                                : \"border-[#F1F3FF]\"\n                            } px-3 py-2 w-full rounded text-sm`}\n                            type=\"text\"\n                            placeholder=\"First Name\"\n                            value={firstName}\n                            onChange={(v) => setFirstName(v.target.value)}\n                          />\n                          <div className=\" text-[8px] text-danger\">\n                            {firstNameError ? firstNameError : \"\"}\n                          </div>\n                        </div>\n                      </div>\n                      {/*  */}\n                      <div className=\"md:w-1/2 w-full  md:pl-1 my-1\">\n                        <div className=\"text-[#B4B4B4] text-xs mb-1\">\n                          Last Name\n                        </div>\n                        <div>\n                          <input\n                            className=\" outline-none border border-[#F1F3FF] px-3 py-2 w-full rounded text-sm\"\n                            type=\"text\"\n                            placeholder=\"Last Name\"\n                            value={lastName}\n                            onChange={(v) => setLastName(v.target.value)}\n                          />\n                        </div>\n                      </div>\n                    </div>\n                    {/*  */}\n                    <div className=\"flex md:flex-row flex-col \">\n                      <div className=\"md:w-1/2 w-full md:pr-1 my-1\">\n                        <div className=\"text-[#B4B4B4] text-xs  mb-1\">\n                          Email\n                        </div>\n                        <div>\n                          <input\n                            className={` outline-none border ${\n                              emailError ? \"border-danger\" : \"border-[#F1F3FF]\"\n                            } px-3 py-2 w-full rounded text-sm`}\n                            type=\"email\"\n                            placeholder=\"Email Address\"\n                            value={email}\n                            onChange={(v) => setEmail(v.target.value)}\n                          />\n                          <div className=\" text-[8px] text-danger\">\n                            {emailError ? emailError : \"\"}\n                          </div>\n                        </div>\n                      </div>\n                      {/*  */}\n                      <div className=\"md:w-1/2 w-full  md:pl-1 my-1\">\n                        <div className=\"text-[#B4B4B4] text-xs mb-1\">\n                          phone <strong className=\"text-danger\">*</strong>\n                        </div>\n                        <div>\n                          <input\n                            className={`outline-none border ${\n                              phoneError ? \"border-danger\" : \"border-[#F1F3FF]\"\n                            } px-3 py-2 w-full rounded text-sm`}\n                            type=\"text\"\n                            placeholder=\"Phone no\"\n                            value={phone}\n                            onChange={(v) => setPhone(v.target.value)}\n                          />\n                          <div className=\" text-[8px] text-danger\">\n                            {phoneError ? phoneError : \"\"}\n                          </div>\n                        </div>\n                      </div>\n                    </div>\n\n                    {/*  */}\n                    <div className=\"flex md:flex-row flex-col \">\n                      <div className=\"w-full md:pr-1 my-1\">\n                        <div className=\"text-[#B4B4B4] text-xs  mb-1\">\n                          Country <strong className=\"text-danger\">*</strong>\n                        </div>\n                        <div>\n                          <Select\n                            value={country}\n                            onChange={(option) => {\n                              setCountry(option);\n                            }}\n                            options={COUNTRIES.map((country) => ({\n                              value: country.title,\n                              label: (\n                                <div\n                                  className={`${\n                                    country.title === \"\" ? \"py-2\" : \"\"\n                                  } flex flex-row items-center`}\n                                >\n                                  <span className=\"mr-2\">{country.icon}</span>\n                                  <span>{country.title}</span>\n                                </div>\n                              ),\n                            }))}\n                            className=\"text-sm\"\n                            placeholder=\"Select a country...\"\n                            isSearchable\n                            styles={{\n                              control: (base, state) => ({\n                                ...base,\n                                background: \"#fff\",\n                                border: countryError\n                                  ? \"1px solid #d34053\"\n                                  : \"1px solid #F1F3FF\",\n                                boxShadow: state.isFocused ? \"none\" : \"none\",\n                                \"&:hover\": {\n                                  border: \"1px solid #F1F3FF\",\n                                },\n                              }),\n                              option: (base) => ({\n                                ...base,\n                                display: \"flex\",\n                                alignItems: \"center\",\n                              }),\n                              singleValue: (base) => ({\n                                ...base,\n                                display: \"flex\",\n                                alignItems: \"center\",\n                              }),\n                            }}\n                          />\n\n                          <div className=\" text-[8px] text-danger\">\n                            {countryError ? countryError : \"\"}\n                          </div>\n                        </div>\n                      </div>\n                      <div className=\"w-full md:pr-1 my-1\">\n                        <div className=\"text-[#B4B4B4] text-xs  mb-1\">\n                          City <strong className=\"text-danger\">*</strong>\n                        </div>\n                        <div>\n                          <GoogleComponent\n                            apiKey=\"AIzaSyD5rhJGfXPbttVQzftoMPVjJOWbBvfcTFE\"\n                            className={` outline-none border ${\n                              cityError ? \"border-danger\" : \"border-[#F1F3FF]\"\n                            } px-3 py-2 w-full rounded text-sm`}\n                            onChange={(v) => {\n                              setCity(v.target.value);\n                            }}\n                            onPlaceSelected={(place) => {\n                              if (place && place.geometry) {\n                                setCity(place.formatted_address ?? \"\");\n                                // setCityVl(place.formatted_address ?? \"\");\n                                //   const latitude = place.geometry.location.lat();\n                                //   const longitude = place.geometry.location.lng();\n                                //   setLocationX(latitude ?? \"\");\n                                //   setLocationY(longitude ?? \"\");\n                              }\n                            }}\n                            defaultValue={city}\n                            types={[\"city\"]}\n                            language=\"en\"\n                          />\n                          {/* <input\n                            className={` outline-none border ${\n                              cityError ? \"border-danger\" : \"border-[#F1F3FF]\"\n                            }  px-3 py-2 w-full rounded text-sm`}\n                            type=\"text\"\n                            placeholder=\"City\"\n                            value={city}\n                            onChange={(v) => setCity(v.target.value)}\n                          /> */}\n                          <div className=\" text-[8px] text-danger\">\n                            {cityError ? cityError : \"\"}\n                          </div>\n                        </div>\n                      </div>\n                    </div>\n                    {/*  */}\n                    <div className=\"flex md:flex-row flex-col \">\n                      <div className=\"md:w-1/2 w-full  md:pr-1 my-1\">\n                        <div className=\"text-[#B4B4B4] text-xs  mb-1\">CIA</div>\n                        <div>\n                          <Select\n                            value={insuranceCompany}\n                            onChange={(option) => {\n                              setInsuranceCompany(option);\n                            }}\n                            options={insurances?.map((assurance) => ({\n                              value: assurance.id,\n                              label: assurance.assurance_name || \"\",\n                            }))}\n                            filterOption={(option, inputValue) =>\n                              option.label\n                                .toLowerCase()\n                                .includes(inputValue.toLowerCase())\n                            }\n                            className=\"text-sm\"\n                            placeholder=\"Select Insurance...\"\n                            isSearchable\n                            styles={{\n                              control: (base, state) => ({\n                                ...base,\n                                background: \"#fff\",\n                                border: insuranceCompanyError\n                                  ? \"1px solid #d34053\"\n                                  : \"1px solid #F1F3FF\",\n                                boxShadow: state.isFocused ? \"none\" : \"none\",\n                                \"&:hover\": {\n                                  border: \"1px solid #F1F3FF\",\n                                },\n                              }),\n                              option: (base) => ({\n                                ...base,\n                                display: \"flex\",\n                                alignItems: \"center\",\n                              }),\n                              singleValue: (base) => ({\n                                ...base,\n                                display: \"flex\",\n                                alignItems: \"center\",\n                              }),\n                            }}\n                          />\n                          <div className=\" text-[8px] text-danger\">\n                            {insuranceCompanyError ? insuranceCompanyError : \"\"}\n                          </div>\n                        </div>\n                      </div>\n                      <div className=\"md:w-1/2 w-full  md:pr-1 my-1\">\n                        <div className=\"text-[#B4B4B4] text-xs  mb-1\">\n                          CIA Reference\n                        </div>\n                        <div>\n                          <input\n                            className={` outline-none border ${\n                              insuranceNumberError\n                                ? \"border-danger\"\n                                : \"border-[#F1F3FF]\"\n                            }  px-3 py-2 w-full rounded text-sm`}\n                            type=\"text\"\n                            placeholder=\"CIA Reference\"\n                            value={insuranceNumber}\n                            onChange={(v) => setInsuranceNumber(v.target.value)}\n                          />\n                          <div className=\" text-[8px] text-danger\">\n                            {insuranceNumberError ? insuranceNumberError : \"\"}\n                          </div>\n                        </div>\n                      </div>\n                    </div>\n                    {/*  */}\n                  </div>\n                  {/* Case Details: */}\n                  <div className=\"text-xs font-medium mt-5 mb-2 text-black\">\n                    Case Details:\n                  </div>\n                  <div className=\"my-2 bg-white py-4 px-2 rounded-md\">\n                    <div className=\"flex md:flex-row flex-col  \">\n                      <div className=\" w-full  md:pr-1 my-1\">\n                        <div className=\"text-[#B4B4B4] text-xs  mb-1\">\n                          Assigned Coordinator{\" \"}\n                          <strong className=\"text-danger\">*</strong>\n                        </div>\n                        <div>\n                          <Select\n                            value={coordinator}\n                            onChange={(option) => {\n                              setCoordinator(option);\n                            }}\n                            className=\"text-sm\"\n                            options={coordinators?.map((item) => ({\n                              value: item.id,\n                              label: item.full_name || \"\",\n                            }))}\n                            filterOption={(option, inputValue) =>\n                              option.label\n                                .toLowerCase()\n                                .includes(inputValue.toLowerCase())\n                            }\n                            placeholder=\"Select Coordinator...\"\n                            isSearchable\n                            styles={{\n                              control: (base, state) => ({\n                                ...base,\n                                background: \"#fff\",\n                                border: coordinatorError\n                                  ? \"1px solid #d34053\"\n                                  : \"1px solid #F1F3FF\",\n                                boxShadow: state.isFocused ? \"none\" : \"none\",\n                                \"&:hover\": {\n                                  border: \"1px solid #F1F3FF\",\n                                },\n                              }),\n                              option: (base) => ({\n                                ...base,\n                                display: \"flex\",\n                                alignItems: \"center\",\n                              }),\n                              singleValue: (base) => ({\n                                ...base,\n                                display: \"flex\",\n                                alignItems: \"center\",\n                              }),\n                            }}\n                          />\n                          <div className=\" text-[8px] text-danger\">\n                            {coordinatorError ? coordinatorError : \"\"}\n                          </div>\n                        </div>\n                      </div>\n                    </div>\n                    {/*  */}\n                    <div className=\"flex md:flex-row flex-col  \">\n                      <div className=\"md:w-1/2 w-full  md:pr-1 my-1\">\n                        <div className=\"text-[#B4B4B4] text-xs mb-1\">\n                          Case Creation Date{\" \"}\n                          <strong className=\"text-danger\">*</strong>\n                        </div>\n                        <div>\n                          <input\n                            className={` outline-none border ${\n                              caseDateError\n                                ? \"border-danger\"\n                                : \"border-[#F1F3FF]\"\n                            } px-3 py-2 w-full rounded text-sm`}\n                            type=\"date\"\n                            placeholder=\"Case Creation Date\"\n                            value={caseDate}\n                            onChange={(v) => setCaseDate(v.target.value)}\n                          />\n                          <div className=\" text-[8px] text-danger\">\n                            {caseDateError ? caseDateError : \"\"}\n                          </div>\n                        </div>\n                      </div>\n                      {/*  */}\n                      <div className=\"md:w-1/2 w-full  md:pl-1 my-1\">\n                        <div className=\"text-[#B4B4B4] text-xs  mb-1\">\n                          Type <strong className=\"text-danger\">*</strong>\n                        </div>\n                        <div>\n                          <select\n                            value={caseType}\n                            onChange={(v) => setCaseType(v.target.value)}\n                            className={` outline-none border ${\n                              caseTypeError\n                                ? \"border-danger\"\n                                : \"border-[#F1F3FF]\"\n                            } px-3 py-3 w-full rounded text-sm`}\n                          >\n                            <option value={\"\"}>Select Type</option>\n                            <option value={\"Medical\"}>Medical</option>\n                            <option value={\"Technical\"}>Technical</option>\n                          </select>\n                          <div className=\" text-[8px] text-danger\">\n                            {caseTypeError ? caseTypeError : \"\"}\n                          </div>\n                        </div>\n                      </div>\n                    </div>\n                    {/*  */}\n                    {caseType === \"Medical\" && (\n                      <div className=\"md:w-1/2  w-full  md:pl-1 my-1\">\n                        <div className=\"text-[#B4B4B4] text-xs  mb-1\">\n                          Type Item <strong className=\"text-danger\">*</strong>\n                        </div>\n                        <div>\n                          <select\n                            value={caseTypeItem}\n                            onChange={(v) => setCaseTypeItem(v.target.value)}\n                            className={` outline-none border ${\n                              caseTypeItemError\n                                ? \"border-danger\"\n                                : \"border-[#F1F3FF]\"\n                            } px-3 py-3 w-full rounded text-sm`}\n                          >\n                            <option value={\"\"}>Select Type Item</option>\n                            <option value={\"Outpatient\"}>Outpatient</option>\n                            <option value={\"Inpatient\"}>Inpatient</option>\n                          </select>\n                          <div className=\" text-[8px] text-danger\">\n                            {caseTypeItemError ? caseTypeItemError : \"\"}\n                          </div>\n                        </div>\n                      </div>\n                    )}\n                    {/*  */}\n                    <div className=\"flex md:flex-row flex-col \">\n                      <div className=\"md:w-1/2 w-full  md:pr-1 my-1\">\n                        <div className=\"text-[#B4B4B4] text-xs  mb-1\">\n                          Currency Code{\" \"}\n                          <strong className=\"text-danger\">*</strong>\n                        </div>\n                        <div>\n                          <Select\n                            value={currencyCode}\n                            onChange={(option) => {\n                              setCurrencyCode(option);\n                            }}\n                            options={CURRENCYITEMS?.map((currency) => ({\n                              value: currency.code,\n                              label:\n                                currency.name !== \"\"\n                                  ? currency.name +\n                                      \" (\" +\n                                      currency.code +\n                                      \") \" || \"\"\n                                  : \"\",\n                            }))}\n                            filterOption={(option, inputValue) =>\n                              option.label\n                                .toLowerCase()\n                                .includes(inputValue.toLowerCase())\n                            }\n                            className=\"text-sm\"\n                            placeholder=\"Select Currency Code ...\"\n                            isSearchable\n                            styles={{\n                              control: (base, state) => ({\n                                ...base,\n                                background: \"#fff\",\n                                border: currencyCodeError\n                                  ? \"1px solid #d34053\"\n                                  : \"1px solid #F1F3FF\",\n                                boxShadow: state.isFocused ? \"none\" : \"none\",\n                                \"&:hover\": {\n                                  border: \"1px solid #F1F3FF\",\n                                },\n                              }),\n                              option: (base) => ({\n                                ...base,\n                                display: \"flex\",\n                                alignItems: \"center\",\n                              }),\n                              singleValue: (base) => ({\n                                ...base,\n                                display: \"flex\",\n                                alignItems: \"center\",\n                              }),\n                            }}\n                          />\n                          <div className=\" text-[8px] text-danger\">\n                            {currencyCodeError ? currencyCodeError : \"\"}\n                          </div>\n                        </div>\n                      </div>\n                      <div className=\"md:w-1/2 w-full  md:pr-1 my-1\">\n                        <div className=\"text-[#B4B4B4] text-xs  mb-1\">\n                          Price of service{\" \"}\n                          <strong className=\"text-danger\">*</strong>\n                        </div>\n                        <div>\n                          <input\n                            className={` outline-none border ${\n                              priceTotalError\n                                ? \"border-danger\"\n                                : \"border-[#F1F3FF]\"\n                            }  px-3 py-2 w-full rounded text-sm`}\n                            type=\"number\"\n                            min={0}\n                            step={0.01}\n                            placeholder=\"0.00\"\n                            value={priceTotal}\n                            onChange={(v) => setPriceTotal(v.target.value)}\n                          />\n                          <div className=\" text-[8px] text-danger\">\n                            {priceTotalError ? priceTotalError : \"\"}\n                          </div>\n                        </div>\n                      </div>\n                    </div>\n                    <div className=\"flex md:flex-row flex-col \">\n                      <div className=\"md:w-1/2 w-full  md:pr-1 my-1\">\n                        <div>\n                          <input\n                            type={\"checkbox\"}\n                            name=\"ispay\"\n                            id=\"ispay\"\n                            checked={isPay === true}\n                            onChange={(v) => {\n                              setIsPay(true);\n                            }}\n                          />\n                          <label\n                            className=\"mx-1 text-[#B4B4B4] text-sm  cursor-pointer\"\n                            for=\"ispay\"\n                          >\n                            Paid\n                          </label>\n                        </div>\n                      </div>\n                      <div className=\"md:w-1/2 w-full  md:pr-1 my-1\">\n                        <div>\n                          <input\n                            type={\"checkbox\"}\n                            name=\"notpay\"\n                            id=\"notpay\"\n                            checked={isPay === false}\n                            onChange={(v) => {\n                              setIsPay(false);\n                            }}\n                          />\n                          <label\n                            className=\"mx-1 text-[#B4B4B4] text-sm  cursor-pointer\"\n                            for=\"notpay\"\n                          >\n                            Unpaid\n                          </label>\n                        </div>\n                      </div>\n                    </div>\n\n                    {/*  */}\n                    <div className=\"flex md:flex-row flex-col  \">\n                      <div className=\" w-full  md:pr-1 my-1\">\n                        <div className=\"text-[#B4B4B4] text-xs  mb-1\">\n                          Description\n                        </div>\n                        <div>\n                          <textarea\n                            value={caseDescription}\n                            rows={5}\n                            onChange={(v) => setCaseDescription(v.target.value)}\n                            className=\" outline-none border border-[#F1F3FF] px-3 py-2 w-full rounded text-sm\"\n                          ></textarea>\n                        </div>\n                      </div>\n                    </div>\n                  </div>\n\n                  {/* Save & Continue - step 1 */}\n                  <div className=\"flex flex-row items-center justify-end my-3\">\n                    <button\n                      onClick={() => {\n                        var check = true;\n                        setFirstNameError(\"\");\n                        setLastNameError(\"\");\n                        setBirthDateError(\"\");\n                        setPhoneError(\"\");\n                        setEmailError(\"\");\n                        setAddressError(\"\");\n                        setCaseTypeError(\"\");\n                        setCaseTypeItemError(\"\");\n                        setCaseDateError(\"\");\n                        setCoordinatorError(\"\");\n                        setCityError(\"\");\n                        setCountryError(\"\");\n                        setCurrencyCodeError(\"\");\n                        setPriceTotalError(\"\");\n\n                        if (firstName === \"\") {\n                          setFirstNameError(\"This field is required.\");\n                          check = false;\n                        }\n\n                        if (phone === \"\") {\n                          setPhoneError(\"This field is required.\");\n                          check = false;\n                        }\n\n                        if (country === \"\" || country.value === \"\") {\n                          setCountryError(\"This field is required.\");\n                          check = false;\n                        }\n\n                        if (city === \"\") {\n                          setCityError(\"This field is required.\");\n                          check = false;\n                        }\n\n                        if (currencyCode === \"\" || currencyCode.value === \"\") {\n                          setCurrencyCodeError(\"This field is required.\");\n                          check = false;\n                        }\n                        if (priceTotal === \"\") {\n                          setPriceTotalError(\"This field is required.\");\n                          check = false;\n                        }\n\n                        if (coordinator === \"\" || coordinator.value === \"\") {\n                          setCoordinatorError(\"This field is required.\");\n                          check = false;\n                        }\n\n                        if (caseDate === \"\") {\n                          setCaseDateError(\"This field is required.\");\n                          check = false;\n                        }\n\n                        if (caseType === \"\") {\n                          setCaseTypeError(\"This field is required.\");\n                          check = false;\n                        } else if (\n                          caseType === \"Medical\" &&\n                          caseTypeItem === \"\"\n                        ) {\n                          setCaseTypeItemError(\"This field is required.\");\n                          check = false;\n                        }\n\n                        if (check) {\n                          setStepSelect(1);\n                        } else {\n                          toast.error(\n                            \"Some fields are empty or invalid. please try again\"\n                          );\n                        }\n                      }}\n                      className=\"text-white bg-[#0388A6] text-sm px-5 py-3 rounded-full\"\n                    >\n                      Save & Continue\n                    </button>\n                  </div>\n                </div>\n              ) : null}\n              {/* step 2 */}\n              {stepSelect === 1 ? (\n                <div className=\"\">\n                  <div className=\"text-[#0388A6] font-semibold text-xl\">\n                    Coordination Details\n                  </div>\n                  {/* Initial Coordination Status: */}\n                  <div className=\"text-xs font-medium mt-5 mb-2 text-black\">\n                    Initial Coordination Status:\n                  </div>\n                  <div className=\"my-2 bg-white py-4 px-2 rounded-md\">\n                    <div className=\"flex md:flex-row flex-col  \">\n                      <div className=\"w-full  md:pr-1 my-1\">\n                        <div className=\"text-[#B4B4B4] text-xs  mb-1\">\n                          Status <strong className=\"text-danger\">*</strong>\n                        </div>\n                        <div>\n                          <div className=\"flex flex-wrap\">\n                            <div className=\"flex flex-row text-xs items-center my-3 text-danger\">\n                              <input\n                                onChange={(v) => {\n                                  if (\n                                    !coordinatStatusList.includes(\n                                      \"pending-coordination\"\n                                    )\n                                  ) {\n                                    setCoordinatStatusList([\n                                      ...coordinatStatusList,\n                                      \"pending-coordination\",\n                                    ]);\n                                  } else {\n                                    setCoordinatStatusList(\n                                      coordinatStatusList.filter(\n                                        (status) =>\n                                          status !== \"pending-coordination\"\n                                      )\n                                    );\n                                  }\n                                }}\n                                id=\"pending-coordination\"\n                                type={\"checkbox\"}\n                                checked={coordinatStatusList.includes(\n                                  \"pending-coordination\"\n                                )}\n                                className=\"mx-1\"\n                              />\n                              <label\n                                for=\"pending-coordination\"\n                                className=\"flex-1 mx-1  cursor-pointer \"\n                              >\n                                Pending Coordination\n                              </label>\n                            </div>\n                            <div className=\"flex flex-row text-xs items-center my-3 text-[#FFA500]\">\n                              <input\n                                onChange={(v) => {\n                                  if (\n                                    !coordinatStatusList.includes(\n                                      \"coordinated-missing-m-r\"\n                                    )\n                                  ) {\n                                    setCoordinatStatusList([\n                                      ...coordinatStatusList,\n                                      \"coordinated-missing-m-r\",\n                                    ]);\n                                  } else {\n                                    setCoordinatStatusList(\n                                      coordinatStatusList.filter(\n                                        (status) =>\n                                          status !== \"coordinated-missing-m-r\"\n                                      )\n                                    );\n                                  }\n                                }}\n                                checked={coordinatStatusList.includes(\n                                  \"coordinated-missing-m-r\"\n                                )}\n                                id=\"coordinated-Missing-m-r\"\n                                type={\"checkbox\"}\n                                className=\"mx-1\"\n                              />\n                              <label\n                                for=\"coordinated-Missing-m-r\"\n                                className=\"flex-1 mx-1  cursor-pointer \"\n                              >\n                                Coordinated, Missing M.R.\n                              </label>\n                            </div>\n                            <div className=\"flex flex-row text-xs items-center my-3 text-[#FFA500]\">\n                              <input\n                                onChange={(v) => {\n                                  if (\n                                    !coordinatStatusList.includes(\n                                      \"coordinated-missing-invoice\"\n                                    )\n                                  ) {\n                                    setCoordinatStatusList([\n                                      ...coordinatStatusList,\n                                      \"coordinated-missing-invoice\",\n                                    ]);\n                                  } else {\n                                    setCoordinatStatusList(\n                                      coordinatStatusList.filter(\n                                        (status) =>\n                                          status !==\n                                          \"coordinated-missing-invoice\"\n                                      )\n                                    );\n                                  }\n                                }}\n                                checked={coordinatStatusList.includes(\n                                  \"coordinated-missing-invoice\"\n                                )}\n                                id=\"coordinated-missing-invoice\"\n                                type={\"checkbox\"}\n                                className=\"mx-1\"\n                              />\n                              <label\n                                for=\"coordinated-missing-invoice\"\n                                className=\"flex-1 mx-1  cursor-pointer \"\n                              >\n                                Coordinated, Missing Invoice\n                              </label>\n                            </div>\n                            <div className=\"flex flex-row text-xs items-center my-3 text-primary\">\n                              <input\n                                onChange={(v) => {\n                                  if (\n                                    !coordinatStatusList.includes(\n                                      \"waiting-for-insurance-authorization\"\n                                    )\n                                  ) {\n                                    setCoordinatStatusList([\n                                      ...coordinatStatusList,\n                                      \"waiting-for-insurance-authorization\",\n                                    ]);\n                                  } else {\n                                    setCoordinatStatusList(\n                                      coordinatStatusList.filter(\n                                        (status) =>\n                                          status !==\n                                          \"waiting-for-insurance-authorization\"\n                                      )\n                                    );\n                                  }\n                                }}\n                                checked={coordinatStatusList.includes(\n                                  \"waiting-for-insurance-authorization\"\n                                )}\n                                id=\"waiting-for-insurance-authorization\"\n                                type={\"checkbox\"}\n                                className=\"mx-1\"\n                              />\n                              <label\n                                for=\"waiting-for-insurance-authorization\"\n                                className=\"flex-1 mx-1  cursor-pointer \"\n                              >\n                                Waiting for Insurance Authorization\n                              </label>\n                            </div>\n                            <div className=\"flex flex-row text-xs items-center my-3 text-primary\">\n                              <input\n                                onChange={(v) => {\n                                  if (\n                                    !coordinatStatusList.includes(\n                                      \"coordinated-patient-not-seen-yet\"\n                                    )\n                                  ) {\n                                    setCoordinatStatusList([\n                                      ...coordinatStatusList,\n                                      \"coordinated-patient-not-seen-yet\",\n                                    ]);\n                                  } else {\n                                    setCoordinatStatusList(\n                                      coordinatStatusList.filter(\n                                        (status) =>\n                                          status !==\n                                          \"coordinated-patient-not-seen-yet\"\n                                      )\n                                    );\n                                  }\n                                }}\n                                checked={coordinatStatusList.includes(\n                                  \"coordinated-patient-not-seen-yet\"\n                                )}\n                                id=\"coordinated-patient-not-seen-yet\"\n                                type={\"checkbox\"}\n                                className=\"mx-1\"\n                              />\n                              <label\n                                for=\"coordinated-patient-not-seen-yet\"\n                                className=\"flex-1 mx-1  cursor-pointer \"\n                              >\n                                Coordinated, Patient not seen yet\n                              </label>\n                            </div>\n                            {/*  */}\n                            <div className=\"flex flex-row text-xs items-center my-3 text-primary\">\n                              <input\n                                onChange={(v) => {\n                                  if (\n                                    !coordinatStatusList.includes(\n                                      \"coordination-fee\"\n                                    )\n                                  ) {\n                                    setCoordinatStatusList([\n                                      ...coordinatStatusList,\n                                      \"coordination-fee\",\n                                    ]);\n                                  } else {\n                                    setCoordinatStatusList(\n                                      coordinatStatusList.filter(\n                                        (status) =>\n                                          status !== \"coordination-fee\"\n                                      )\n                                    );\n                                  }\n                                }}\n                                checked={coordinatStatusList.includes(\n                                  \"coordination-fee\"\n                                )}\n                                id=\"coordination-fee\"\n                                type={\"checkbox\"}\n                                className=\"mx-1\"\n                              />\n                              <label\n                                for=\"coordination-fee\"\n                                className=\"flex-1 mx-1  cursor-pointer \"\n                              >\n                                Coordination Fee\n                              </label>\n                            </div>\n                            {/*  */}\n                            <div className=\"flex flex-row text-xs items-center my-3 text-primary\">\n                              <input\n                                onChange={(v) => {\n                                  if (\n                                    !coordinatStatusList.includes(\n                                      \"coordinated-missing-payment\"\n                                    )\n                                  ) {\n                                    setCoordinatStatusList([\n                                      ...coordinatStatusList,\n                                      \"coordinated-missing-payment\",\n                                    ]);\n                                  } else {\n                                    setCoordinatStatusList(\n                                      coordinatStatusList.filter(\n                                        (status) =>\n                                          status !==\n                                          \"coordinated-missing-payment\"\n                                      )\n                                    );\n                                  }\n                                }}\n                                checked={coordinatStatusList.includes(\n                                  \"coordinated-missing-payment\"\n                                )}\n                                id=\"coordinated-missing-payment\"\n                                type={\"checkbox\"}\n                                className=\"mx-1\"\n                              />\n                              <label\n                                for=\"coordinated-missing-payment\"\n                                className=\"flex-1 mx-1  cursor-pointer \"\n                              >\n                                Coordinated, Missing Payment\n                              </label>\n                            </div>\n\n                            {/*  */}\n                            <div className=\"flex flex-row text-xs items-center my-3 text-[#008000]\">\n                              <input\n                                onChange={(v) => {\n                                  if (\n                                    !coordinatStatusList.includes(\n                                      \"fully-coordinated\"\n                                    )\n                                  ) {\n                                    setCoordinatStatusList([\n                                      ...coordinatStatusList,\n                                      \"fully-coordinated\",\n                                    ]);\n                                  } else {\n                                    setCoordinatStatusList(\n                                      coordinatStatusList.filter(\n                                        (status) =>\n                                          status !== \"fully-coordinated\"\n                                      )\n                                    );\n                                  }\n                                }}\n                                checked={coordinatStatusList.includes(\n                                  \"fully-coordinated\"\n                                )}\n                                id=\"fully-coordinated\"\n                                type={\"checkbox\"}\n                                className=\"mx-1\"\n                              />\n                              <label\n                                for=\"fully-coordinated\"\n                                className=\"flex-1 mx-1  cursor-pointer \"\n                              >\n                                Fully Coordinated\n                              </label>\n                            </div>\n                            <div className=\"flex flex-row text-xs items-center my-3 text-[#d34053]\">\n                              <input\n                                onChange={(v) => {\n                                  if (!coordinatStatusList.includes(\"failed\")) {\n                                    setCoordinatStatusList([\n                                      ...coordinatStatusList,\n                                      \"failed\",\n                                    ]);\n                                  } else {\n                                    setCoordinatStatusList(\n                                      coordinatStatusList.filter(\n                                        (status) => status !== \"failed\"\n                                      )\n                                    );\n                                  }\n                                }}\n                                checked={coordinatStatusList.includes(\"failed\")}\n                                id=\"failed\"\n                                type={\"checkbox\"}\n                                className=\"mx-1\"\n                              />\n                              <label\n                                for=\"failed\"\n                                className=\"flex-1 mx-1  cursor-pointer \"\n                              >\n                                Failed\n                              </label>\n                            </div>\n                          </div>\n                          {/* <select\n                            value={coordinatStatus}\n                            onChange={(v) => setCoordinatStatus(v.target.value)}\n                            className={`outline-none border ${\n                              coordinatStatusError\n                                ? \"border-danger\"\n                                : \"border-[#F1F3FF]\"\n                            }  px-3 py-2 w-full rounded text-sm`}\n                          >\n                            <option value={\"\"}>Select Status</option>\n                            <option value={\"pending-coordination\"}>\n                              Pending Coordination\n                            </option>\n                            <option value={\"coordinated-missing-m-r\"}>\n                              Coordinated, Missing M.R.\n                            </option>\n                            <option value={\"coordinated-missing-invoice\"}>\n                              Coordinated, Missing Invoice\n                            </option>\n                            <option\n                              value={\"waiting-for-insurance-authorization\"}\n                            >\n                              Waiting for Insurance Authorization\n                            </option>\n                            <option value={\"coordinated-patient-not-seen-yet\"}>\n                              Coordinated, Patient not seen yet\n                            </option>\n                            <option value={\"fully-coordinated\"}>\n                              Fully Coordinated\n                            </option>\n                            <option value={\"failed\"}>Failed</option>\n                          </select> */}\n                          <div className=\" text-[8px] text-danger\">\n                            {coordinatStatusListError\n                              ? coordinatStatusListError\n                              : \"\"}\n                          </div>\n                        </div>\n                      </div>\n                    </div>\n                  </div>\n                  {/* Assistance Details: */}\n                  <div className=\"text-xs font-medium mt-5 mb-2 text-black\">\n                    Assistance Details:\n                  </div>\n                  <div className=\"my-2 bg-white py-4 px-2 rounded-md\">\n                   {/* Appointment Details: */}\n                    <div className=\"text-xs font-medium mt-2 mb-2 text-black\">\n                      Appointment Details:\n                    </div>\n                    <div className=\"flex md:flex-row flex-col w-full \">\n                      {caseType === \"Medical\" &&\n                      caseTypeItem === \"Inpatient\" ? (\n                        <div className=\"flex md:flex-row flex-col w-full\">\n                          <div className=\"md:w-1/2 w-full  md:pr-1 my-1\">\n                            <div className=\"text-[#B4B4B4] text-xs  mb-1\">\n                              Hospital Starting Date\n                            </div>\n                            <div>\n                              <input\n                                className=\" outline-none border border-[#F1F3FF] px-3 py-2 w-full rounded text-sm\"\n                                type=\"date\"\n                                placeholder=\"Hospital Starting Date\"\n                                value={startDate}\n                                onChange={(v) => {\n                                  setStartDate(v.target.value);\n                                  // If end date is earlier than new start date, update end date\n                                  if (endDate && endDate < v.target.value) {\n                                    setEndDate(v.target.value);\n                                  }\n                                }}\n                              />\n                            </div>\n                          </div>\n                          <div className=\"md:w-1/2 w-full  md:pr-1 my-1\">\n                            <div className=\"text-[#B4B4B4] text-xs  mb-1\">\n                              Hospital Ending Date\n                            </div>\n                            <div>\n                              <input\n                                className=\" outline-none border border-[#F1F3FF] px-3 py-2 w-full rounded text-sm\"\n                                type=\"date\"\n                                placeholder=\"Hospital Ending Date\"\n                                value={endDate}\n                                onChange={(v) => setEndDate(v.target.value)}\n                                disabled={!startDate}\n                                min={startDate}\n                              />\n                            </div>\n                          </div>\n                        </div>\n                      ) : (\n                        <div className=\" w-full  md:pr-1 my-1\">\n                          <div className=\"text-[#B4B4B4] text-xs  mb-1\">\n                            Appointment Date\n                          </div>\n                          <div>\n                            <input\n                              className=\" outline-none border border-[#F1F3FF] px-3 py-2 w-full rounded text-sm\"\n                              type=\"date\"\n                              placeholder=\"Appointment Date\"\n                              value={appointmentDate}\n                              onChange={(v) =>\n                                setAppointmentDate(v.target.value)\n                              }\n                            />\n                          </div>\n                        </div>\n                      )}\n                    </div>\n\n                    <div className=\"flex md:flex-row flex-col  \">\n                      {/*  */}\n                      <div className=\"md:w-1/2 w-full  md:pr-1 my-1\">\n                        <div className=\"text-[#B4B4B4] text-xs  mb-1\">\n                          Service Location\n                        </div>\n                        <div>\n                          <input\n                            className=\" outline-none border border-[#F1F3FF] px-3 py-2 w-full rounded text-sm\"\n                            type=\"text\"\n                            placeholder=\" Service Location\"\n                            value={serviceLocation}\n                            onChange={(v) => setServiceLocation(v.target.value)}\n                          />\n                        </div>\n                      </div>\n                    </div>\n\n                    {/* Provider Information: */}\n                    <div className=\"text-xs font-medium mt-2 mb-2 text-black\">\n                      Provider Information:\n                    </div>\n                    <div className=\"flex md:flex-row flex-col  \">\n                      <div className=\"md:w-1/2  w-full  md:pr-1 my-1\">\n                        <div className=\"text-[#B4B4B4] text-xs  mb-1\">\n                          Provider Name\n                        </div>\n                        <div>\n                          <Select\n                            value={providerName}\n                            onChange={(option) => {\n                              setProviderName(option);\n                              //\n                              var initialProvider = option?.value ?? \"\";\n                              // Show loading indicator while fetching provider services\n                              setIsLoading(true);\n\n                              const foundProvider = providers?.find(\n                                (item) => item.id === initialProvider\n                              );\n                              if (foundProvider) {\n                                setProviderServices(\n                                  foundProvider.services ?? []\n                                );\n                                // Hide loading indicator after services are loaded\n                                setTimeout(() => {\n                                  setIsLoading(false);\n                                }, 100);\n                              } else {\n                                setProviderServices([]);\n                                setIsLoading(false);\n                              }\n                            }}\n                            className=\"text-sm\"\n                            options={providers?.map((item) => ({\n                              value: item.id,\n                              label: item.full_name || \"\",\n                              city: item.city || \"\",\n                              country: item.country || \"\",\n                            }))}\n                            filterOption={(option, inputValue) => {\n                              // تحسين البحث ليشمل الاسم والمدينة والبلد\n                              const searchTerm = inputValue?.toLowerCase();\n                              return (\n                                option.label\n                                  ?.toLowerCase()\n                                  .includes(searchTerm) ||\n                                option.city\n                                  ?.toLowerCase()\n                                  .includes(searchTerm) ||\n                                option.country\n                                  ?.toLowerCase()\n                                  .includes(searchTerm)\n                              );\n                            }}\n                            placeholder=\"Select Provider...\"\n                            isSearchable\n                            // Add loading indicator\n                            isLoading={loadingProviders}\n                            styles={{\n                              control: (base, state) => ({\n                                ...base,\n                                background: \"#fff\",\n                                border: providerNameError\n                                  ? \"1px solid #d34053\"\n                                  : \"1px solid #F1F3FF\",\n                                boxShadow: state.isFocused ? \"none\" : \"none\",\n                                \"&:hover\": {\n                                  border: \"1px solid #F1F3FF\",\n                                },\n                              }),\n                              option: (base) => ({\n                                ...base,\n                                display: \"flex\",\n                                alignItems: \"center\",\n                              }),\n                              singleValue: (base) => ({\n                                ...base,\n                                display: \"flex\",\n                                alignItems: \"center\",\n                              }),\n                            }}\n                          />\n                          <div className=\" text-[8px] text-danger\">\n                            {providerNameError ? providerNameError : \"\"}\n                          </div>\n                        </div>\n                      </div>\n                      {/*  */}\n                      <div className=\"md:w-1/2  w-full  md:pr-1 my-1\">\n                        <div className=\"text-[#B4B4B4] text-xs  mb-1\">\n                          Provider Service\n                        </div>\n                        <div>\n                          <select\n                            className={`outline-none border ${\n                              providerServiceError\n                                ? \"border-danger\"\n                                : \"border-[#F1F3FF]\"\n                            }  px-3 py-2 w-full rounded text-sm`}\n                            onChange={(v) => {\n                              setProviderService(v.target.value);\n                            }}\n                            value={providerService}\n                          >\n                            <option value={\"\"}></option>\n                            {providerServices?.map((service, index) => (\n                              <option value={service.id}>\n                                {service.service_type ?? \"\"}\n                                {service.service_specialist !== \"\"\n                                  ? \" : \" + service.service_specialist\n                                  : \"\"}\n                              </option>\n                            ))}\n                          </select>\n                          <div className=\" text-[8px] text-danger\">\n                            {providerServiceError ? providerServiceError : \"\"}\n                          </div>\n                        </div>\n                      </div>\n                    </div>\n                    <div className=\"flex md:flex-row flex-col  \">\n                      <div className=\" w-full  md:pr-1 my-1\">\n                        <div className=\"text-[#B4B4B4] text-xs  mb-1\">\n                          Visit Date\n                        </div>\n                        <div>\n                          <input\n                            className={`outline-none border ${\n                              providerDateError\n                                ? \"border-danger\"\n                                : \"border-[#F1F3FF]\"\n                            }  px-3 py-2 w-full rounded text-sm`}\n                            type=\"date\"\n                            placeholder=\"Visit Date\"\n                            value={providerDate}\n                            onChange={(v) => setProviderDate(v.target.value)}\n                          />\n                          <div className=\" text-[8px] text-danger\">\n                            {providerDateError ? providerDateError : \"\"}\n                          </div>\n                        </div>\n                      </div>\n                    </div>\n                    {/* add  */}\n                    <div className=\"flex flex-col  \">\n                      <button\n                        onClick={() => {\n                          // providerMultiSelect\n                          var check = true;\n                          setProviderNameError(\"\");\n                          setProviderServiceError(\"\");\n                          setProviderDateError(\"\");\n                          if (\n                            providerName === \"\" ||\n                            providerName.value === \"\"\n                          ) {\n                            setProviderNameError(\"These fields are required.\");\n                            toast.error(\"Provider is required\");\n                            check = false;\n                          }\n                          if (providerService === \"\") {\n                            setProviderServiceError(\n                              \"These fields are required.\"\n                            );\n                            toast.error(\"Provider Service is required\");\n                            check = false;\n                          }\n                          if (providerDate === \"\") {\n                            setProviderDateError(\"These fields are required.\");\n                            toast.error(\"Visit Date is required\");\n                            check = false;\n                          }\n                          if (check) {\n                            const exists = false;\n                            // const exists = providerMultiSelect.some(\n                            //   (provider) =>\n                            //     String(provider?.provider?.id) ===\n                            //       String(providerName.value) &&\n                            //     String(provider?.service?.id) ===\n                            //       String(providerService)\n                            // );\n\n                            if (!exists) {\n                              // find provider\n                              var initialProvider = providerName.value ?? \"\";\n                              const foundProvider = providers?.find(\n                                (item) =>\n                                  String(item.id) === String(initialProvider)\n                              );\n                              console.log(foundProvider);\n\n                              if (foundProvider) {\n                                // found service\n                                var initialService = providerService ?? \"\";\n\n                                foundProvider?.services?.forEach((element) => {\n                                  console.log(element.id);\n                                });\n\n                                const foundService =\n                                  foundProvider?.services?.find(\n                                    (item) =>\n                                      String(item.id) === String(initialService)\n                                  );\n\n                                if (foundService) {\n                                  // Add the new item if it doesn't exist\n                                  setProviderMultiSelect([\n                                    ...providerMultiSelect,\n                                    {\n                                      provider: foundProvider,\n                                      service: foundService,\n                                      date: providerDate,\n                                    },\n                                  ]);\n                                  setProviderName(\"\");\n                                  setProviderService(\"\");\n                                  setProviderDate(\"\");\n                                  console.log(providerMultiSelect);\n                                } else {\n                                  setProviderNameError(\n                                    \"This provider service not exist!\"\n                                  );\n                                  toast.error(\n                                    \"This provider service not exist!\"\n                                  );\n                                }\n                              } else {\n                                setProviderNameError(\n                                  \"This provider not exist!\"\n                                );\n                                toast.error(\"This provider not exist!\");\n                              }\n                            } else {\n                              setProviderNameError(\n                                \"This provider or service is already added!\"\n                              );\n                              toast.error(\n                                \"This provider or service is already added!\"\n                              );\n                            }\n                          }\n                        }}\n                        className=\"text-primary  flex flex-row items-center my-2 text-sm\"\n                      >\n                        <svg\n                          xmlns=\"http://www.w3.org/2000/svg\"\n                          fill=\"none\"\n                          viewBox=\"0 0 24 24\"\n                          stroke-width=\"1.5\"\n                          stroke=\"currentColor\"\n                          class=\"size-4\"\n                        >\n                          <path\n                            stroke-linecap=\"round\"\n                            stroke-linejoin=\"round\"\n                            d=\"M12 9v6m3-3H9m12 0a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z\"\n                          />\n                        </svg>\n                        <span> Add Provider </span>\n                      </button>\n                      <div className=\" w-full  md:pr-1 my-1\">\n                        <div className=\"text-[#000000bf] font-bold text-xs  mb-1\">\n                          Providers\n                        </div>\n                        <div className=\"my-2 text-black text-sm\">\n                          {providerMultiSelect?.map((itemProvider, index) => (\n                            <div\n                              key={index}\n                              className=\"flex flex-row items-center my-1\"\n                            >\n                              <div className=\"min-w-6 text-center\">\n                                <button\n                                  onClick={() => {\n                                    const updatedServices =\n                                      providerMultiSelect.filter(\n                                        (_, indexF) => indexF !== index\n                                      );\n                                    setProviderMultiSelect(updatedServices);\n                                  }}\n                                >\n                                  <svg\n                                    xmlns=\"http://www.w3.org/2000/svg\"\n                                    fill=\"none\"\n                                    viewBox=\"0 0 24 24\"\n                                    stroke-width=\"1.5\"\n                                    stroke=\"currentColor\"\n                                    class=\"size-6\"\n                                  >\n                                    <path\n                                      stroke-linecap=\"round\"\n                                      stroke-linejoin=\"round\"\n                                      d=\"m9.75 9.75 4.5 4.5m0-4.5-4.5 4.5M21 12a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z\"\n                                    />\n                                  </svg>\n                                </button>\n                              </div>\n                              <div className=\"flex-1 mx-1 border-l px-1\">\n                                <div>\n                                  <b>Provider:</b>{\" \"}\n                                  {itemProvider.provider?.full_name ?? \"---\"}\n                                </div>\n                                <div>\n                                  <b>Service:</b>{\" \"}\n                                  {itemProvider.service?.service_type ?? \"--\"}\n                                </div>\n                                <div>\n                                  <b>Speciality:</b>{\" \"}\n                                  {itemProvider.service?.service_specialist ??\n                                    \"---\"}\n                                </div>\n                                <div>\n                                  <b>Date:</b> {itemProvider.date ?? \"---\"}\n                                </div>\n                              </div>\n                            </div>\n                          ))}\n                        </div>\n                      </div>\n                    </div>\n                  </div>\n                  {/* Save & Continue - step 2 */}\n                  <div className=\"flex flex-row items-center justify-end my-3\">\n                    <button\n                      onClick={() => setStepSelect(0)}\n                      className=\"bg-white text-[#0388A6] text-sm px-5 py-3 rounded-full mr-3\"\n                    >\n                      Back\n                    </button>\n                    <button\n                      onClick={() => {\n                        var check = true;\n                        setCoordinatStatusListError(\"\");\n                        setProviderNameError(\"\");\n\n                        // if (coordinatStatusList.length === 0) {\n                        //   setCoordinatStatusListError(\n                        //     \"This fields is required.\"\n                        //   );\n                        //   check = false;\n                        // }\n\n                        // if (providerMultiSelect.length === 0) {\n                        //   setProviderNameError(\n                        //     \"Please select this and click Add Provider.\"\n                        //   );\n                        //   check = false;\n                        // }\n\n                        if (check) {\n                          setStepSelect(2);\n                        } else {\n                          toast.error(\n                            \"Some fields are empty or invalid. please try again\"\n                          );\n                        }\n                      }}\n                      className=\"text-white bg-[#0388A6] text-sm px-5 py-3 rounded-full\"\n                    >\n                      Save & Continue\n                    </button>\n                  </div>\n                </div>\n              ) : null}\n              {/* step 3 */}\n              {stepSelect === 2 ? (\n                <div className=\"\">\n                  <div className=\"text-[#0388A6] font-semibold text-xl\">\n                    Medical Reports\n                  </div>\n                  {/* Initial Medical Reports: */}\n                  <div className=\"text-xs font-medium mt-5 mb-2 text-black\">\n                    Initial Medical Reports:\n                  </div>\n                  <div className=\"my-2 bg-white py-4 px-2 rounded-md\">\n                    <div\n                      {...getRootPropsInitialMedical({ className: \"dropzone\" })}\n                      // style={dropzoneStyle}\n                      className=\"bg-[#F5F6FF] w-full min-h-50 flex flex-col items-center justify-center  cursor-pointer\"\n                    >\n                      <input {...getInputPropsInitialMedical()} />\n                      <div className=\"my-2\">\n                        <svg\n                          xmlns=\"http://www.w3.org/2000/svg\"\n                          fill=\"none\"\n                          viewBox=\"0 0 24 24\"\n                          stroke-width=\"1.5\"\n                          stroke=\"currentColor\"\n                          className=\"size-8 p-2 bg-[#0388A6] rounded-full text-white\"\n                        >\n                          <path\n                            stroke-linecap=\"round\"\n                            stroke-linejoin=\"round\"\n                            d=\"M3 16.5v2.25A2.25 2.25 0 0 0 5.25 21h13.5A2.25 2.25 0 0 0 21 18.75V16.5m-13.5-9L12 3m0 0 4.5 4.5M12 3v13.5\"\n                          />\n                        </svg>\n                      </div>\n                      <div className=\"my-2\">\n                        Drag & Drop Image File or BROWSE\n                      </div>\n                    </div>\n                    <aside style={thumbsContainer}>\n                      <div className=\"w-full flex flex-col \">\n                        {filesInitialMedicalReports?.map((file, index) => (\n                          <div\n                            className=\"bg-[#F3F5F7] rounded-xl px-5 py-5 w-full my-2 flex flex-row items-center\"\n                            key={file.name}\n                          >\n                            <div className=\" rounded-full  bg-white text-[#81838E] text-center p-2 shadow-1 \">\n                              <svg\n                                xmlns=\"http://www.w3.org/2000/svg\"\n                                viewBox=\"0 0 24 24\"\n                                fill=\"currentColor\"\n                                class=\"size-4\"\n                              >\n                                <path d=\"M5.625 1.5c-1.036 0-1.875.84-1.875 1.875v17.25c0 1.035.84 1.875 1.875 1.875h12.75c1.035 0 1.875-.84 1.875-1.875V12.75A3.75 3.75 0 0 0 16.5 9h-1.875a1.875 1.875 0 0 1-1.875-1.875V5.25A3.75 3.75 0 0 0 9 1.5H5.625Z\" />\n                                <path d=\"M12.971 1.816A5.23 5.23 0 0 1 14.25 5.25v1.875c0 .207.168.375.375.375H16.5a5.23 5.23 0 0 1 3.434 1.279 9.768 9.768 0 0 0-6.963-6.963Z\" />\n                              </svg>\n                            </div>\n                            <div className=\"flex-1 px-5 text-[#303030] text-sm\">\n                              <div className=\"whitespace-nowrap overflow-hidden text-ellipsis max-w-full flex-shrink text-xs\">\n                                {file.name}\n                              </div>\n                              <div>\n                                {(file.size / (1024 * 1024)).toFixed(2)} mb\n                              </div>\n                            </div>\n                            <button\n                              onClick={() => {\n                                setFilesInitialMedicalReports((prevFiles) =>\n                                  prevFiles.filter(\n                                    (_, indexToRemove) =>\n                                      index !== indexToRemove\n                                  )\n                                );\n                              }}\n                              className=\"rounded-full p-1 bg-[#E6E7E8] font-bold text-[#9D9D9D]\"\n                            >\n                              <svg\n                                xmlns=\"http://www.w3.org/2000/svg\"\n                                fill=\"none\"\n                                viewBox=\"0 0 24 24\"\n                                stroke-width=\"1.5\"\n                                stroke=\"currentColor\"\n                                class=\"size-5\"\n                              >\n                                <path\n                                  stroke-linecap=\"round\"\n                                  stroke-linejoin=\"round\"\n                                  d=\"M6 18 18 6M6 6l12 12\"\n                                />\n                              </svg>\n                            </button>\n                          </div>\n                        ))}\n                      </div>\n                    </aside>\n                  </div>\n                  {/* Save & Continue - step 3 */}\n                  <div className=\"flex flex-row items-center justify-end my-3\">\n                    <button\n                      onClick={() => setStepSelect(1)}\n                      className=\"bg-white text-[#0388A6] text-sm px-5 py-3 rounded-full mr-3\"\n                    >\n                      Back\n                    </button>\n                    <button\n                      onClick={() => setStepSelect(3)}\n                      className=\"text-white bg-[#0388A6] text-sm px-5 py-3 rounded-full\"\n                    >\n                      Save & Continue\n                    </button>\n                  </div>\n                </div>\n              ) : null}\n              {/* step 4 */}\n              {stepSelect === 3 ? (\n                <div className=\"\">\n                  <div className=\"text-[#0388A6] font-semibold text-xl\">\n                    Invoices\n                  </div>\n                  {/* Invoice Information: */}\n                  <div className=\"text-xs font-medium mt-5 mb-2 text-black\">\n                    Invoice Information:\n                  </div>\n                  <div className=\"my-2 bg-white py-4 px-2 rounded-md\">\n                    <div className=\"flex md:flex-row flex-col  \">\n                      <div className=\"md:w-1/2 w-full  md:pr-1 my-1\">\n                        <div className=\"text-[#B4B4B4] text-xs  mb-1\">\n                          Invoice Number (Optional)\n                        </div>\n                        <div>\n                          <input\n                            className=\" outline-none border border-[#F1F3FF] px-3 py-2 w-full rounded text-sm\"\n                            type=\"text\"\n                            placeholder=\"Invoice Number (Optional)\"\n                            value={invoiceNumber}\n                            onChange={(v) => setInvoiceNumber(v.target.value)}\n                          />\n                        </div>\n                      </div>\n                      {/*  */}\n                      <div className=\"md:w-1/2 w-full  md:pr-1 my-1\">\n                        <div className=\"text-[#B4B4B4] text-xs  mb-1\">\n                          Date Issued (Optional)\n                        </div>\n                        <div>\n                          <input\n                            className=\" outline-none border border-[#F1F3FF] px-3 py-2 w-full rounded text-sm\"\n                            type=\"date\"\n                            placeholder=\"Date Issued (Optional)\"\n                            value={dateIssued}\n                            onChange={(v) => setDateIssued(v.target.value)}\n                          />\n                        </div>\n                      </div>\n                    </div>\n                    {/*  */}\n                    <div className=\"flex md:flex-row flex-col  \">\n                      <div className=\"w-full  md:pr-1 my-1\">\n                        <div className=\"text-[#B4B4B4] text-xs  mb-1\">\n                          Amount (Optional)\n                        </div>\n                        <div>\n                          <input\n                            className=\" outline-none border border-[#F1F3FF] px-3 py-2 w-full rounded text-sm\"\n                            type=\"number\"\n                            placeholder=\"Amount (Optional)\"\n                            value={amount}\n                            onChange={(v) => setAmount(v.target.value)}\n                          />\n                        </div>\n                      </div>\n                    </div>\n                  </div>\n                  <div className=\"text-xs font-medium mt-5 mb-2 text-black\">\n                    Upload Invoice\n                  </div>\n                  <div className=\"my-2 bg-white py-4 px-2 rounded-md\">\n                    <div\n                      {...getRootPropsUploadInvoice({ className: \"dropzone\" })}\n                      // style={dropzoneStyle}\n                      className=\"bg-[#F5F6FF] w-full min-h-50 flex flex-col items-center justify-center  cursor-pointer\"\n                    >\n                      <input {...getInputPropsUploadInvoice()} />\n                      <div className=\"my-2\">\n                        <svg\n                          xmlns=\"http://www.w3.org/2000/svg\"\n                          fill=\"none\"\n                          viewBox=\"0 0 24 24\"\n                          stroke-width=\"1.5\"\n                          stroke=\"currentColor\"\n                          className=\"size-8 p-2 bg-[#0388A6] rounded-full text-white\"\n                        >\n                          <path\n                            stroke-linecap=\"round\"\n                            stroke-linejoin=\"round\"\n                            d=\"M3 16.5v2.25A2.25 2.25 0 0 0 5.25 21h13.5A2.25 2.25 0 0 0 21 18.75V16.5m-13.5-9L12 3m0 0 4.5 4.5M12 3v13.5\"\n                          />\n                        </svg>\n                      </div>\n                      <div className=\"my-2\">\n                        Drag & Drop Image File or BROWSE\n                      </div>\n                    </div>\n                    <aside style={thumbsContainer}>\n                      <div className=\"w-full flex flex-col \">\n                        {filesUploadInvoice?.map((file, index) => (\n                          <div\n                            className=\"bg-[#F3F5F7] rounded-xl px-5 py-5 w-full my-2 flex flex-row items-center\"\n                            key={file.name}\n                          >\n                            <div className=\" rounded-full  bg-white text-[#81838E] text-center p-2 shadow-1 \">\n                              <svg\n                                xmlns=\"http://www.w3.org/2000/svg\"\n                                viewBox=\"0 0 24 24\"\n                                fill=\"currentColor\"\n                                class=\"size-4\"\n                              >\n                                <path d=\"M5.625 1.5c-1.036 0-1.875.84-1.875 1.875v17.25c0 1.035.84 1.875 1.875 1.875h12.75c1.035 0 1.875-.84 1.875-1.875V12.75A3.75 3.75 0 0 0 16.5 9h-1.875a1.875 1.875 0 0 1-1.875-1.875V5.25A3.75 3.75 0 0 0 9 1.5H5.625Z\" />\n                                <path d=\"M12.971 1.816A5.23 5.23 0 0 1 14.25 5.25v1.875c0 .207.168.375.375.375H16.5a5.23 5.23 0 0 1 3.434 1.279 9.768 9.768 0 0 0-6.963-6.963Z\" />\n                              </svg>\n                            </div>\n                            <div className=\"flex-1 px-5 text-[#303030] text-sm\">\n                              <div className=\"whitespace-nowrap overflow-hidden text-ellipsis max-w-full flex-shrink text-xs\">\n                                {file.name}\n                              </div>\n                              <div>\n                                {(file.size / (1024 * 1024)).toFixed(2)} mb\n                              </div>\n                            </div>\n                            <button\n                              onClick={() => {\n                                setFilesUploadInvoice((prevFiles) =>\n                                  prevFiles.filter(\n                                    (_, indexToRemove) =>\n                                      index !== indexToRemove\n                                  )\n                                );\n                              }}\n                              className=\"rounded-full p-1 bg-[#E6E7E8] font-bold text-[#9D9D9D]\"\n                            >\n                              <svg\n                                xmlns=\"http://www.w3.org/2000/svg\"\n                                fill=\"none\"\n                                viewBox=\"0 0 24 24\"\n                                stroke-width=\"1.5\"\n                                stroke=\"currentColor\"\n                                class=\"size-5\"\n                              >\n                                <path\n                                  stroke-linecap=\"round\"\n                                  stroke-linejoin=\"round\"\n                                  d=\"M6 18 18 6M6 6l12 12\"\n                                />\n                              </svg>\n                            </button>\n                          </div>\n                        ))}\n                      </div>\n                    </aside>\n                  </div>\n\n                  {/* Save & Continue - step 4 */}\n                  <div className=\"flex flex-row items-center justify-end my-3\">\n                    <button\n                      onClick={() => setStepSelect(2)}\n                      className=\"bg-white text-[#0388A6] text-sm px-5 py-3 rounded-full mr-3\"\n                    >\n                      Back\n                    </button>\n                    <button\n                      onClick={() => setStepSelect(4)}\n                      className=\"text-white bg-[#0388A6] text-sm px-5 py-3 rounded-full\"\n                    >\n                      Save & Continue\n                    </button>\n                  </div>\n                </div>\n              ) : null}\n              {/* step 5 */}\n              {stepSelect === 4 ? (\n                <div className=\"\">\n                  <div className=\"text-[#0388A6] font-semibold text-xl\">\n                    Insurance Authorization\n                  </div>\n                  {/* Insurance Details: */}\n                  <div className=\"text-xs font-medium mt-5 mb-2 text-black\">\n                    Insurance Details:\n                  </div>\n                  <div className=\"my-2 bg-white py-4 px-2 rounded-md\">\n                    <div className=\"flex md:flex-row flex-col  \">\n                      <div className=\"md:w-1/2 w-full  md:pr-1 my-1\">\n                        <div className=\"text-[#B4B4B4] text-xs  mb-1\">\n                          Insurance Company Name\n                        </div>\n                        <div>\n                          <Select\n                            value={insuranceCompany}\n                            onChange={(option) => {\n                              setInsuranceCompany(option);\n                            }}\n                            options={insurances?.map((assurance) => ({\n                              value: assurance.id,\n                              label: assurance.assurance_name || \"\",\n                            }))}\n                            filterOption={(option, inputValue) =>\n                              option.label\n                                .toLowerCase()\n                                .includes(inputValue.toLowerCase())\n                            }\n                            className=\"text-sm\"\n                            placeholder=\"Select Insurance...\"\n                            isSearchable\n                            styles={{\n                              control: (base, state) => ({\n                                ...base,\n                                background: \"#fff\",\n                                border: insuranceCompanyError\n                                  ? \"1px solid #d34053\"\n                                  : \"1px solid #F1F3FF\",\n                                boxShadow: state.isFocused ? \"none\" : \"none\",\n                                \"&:hover\": {\n                                  border: \"1px solid #F1F3FF\",\n                                },\n                              }),\n                              option: (base) => ({\n                                ...base,\n                                display: \"flex\",\n                                alignItems: \"center\",\n                              }),\n                              singleValue: (base) => ({\n                                ...base,\n                                display: \"flex\",\n                                alignItems: \"center\",\n                              }),\n                            }}\n                          />\n                        </div>\n                      </div>\n                      {/*  */}\n                      <div className=\"md:w-1/2 w-full  md:pr-1 my-1\">\n                        <div className=\"text-[#B4B4B4] text-xs  mb-1\">\n                          Policy Number\n                        </div>\n                        <div>\n                          <input\n                            className=\" outline-none border border-[#F1F3FF] px-3 py-2 w-full rounded text-sm\"\n                            type=\"text\"\n                            placeholder=\"Policy Number\"\n                            value={policyNumber}\n                            onChange={(v) => setPolicyNumber(v.target.value)}\n                          />\n                        </div>\n                      </div>\n                    </div>\n                  </div>\n                  {/* Authorization Status: */}\n                  <div className=\"text-xs font-medium mt-5 mb-2 text-black\">\n                    Authorization Status:\n                  </div>\n                  <div className=\"my-2 bg-white py-4 px-2 rounded-md\">\n                    <div className=\"flex md:flex-row flex-col  \">\n                      <div className=\"w-full  md:pr-1 my-1\">\n                        <div className=\"text-[#B4B4B4] text-xs  mb-1\">\n                          Initial Status\n                        </div>\n                        <div>\n                          <select\n                            value={initialStatus}\n                            onChange={(v) => setInitialStatus(v.target.value)}\n                            className=\" outline-none border border-[#F1F3FF] px-3 py-2 w-full rounded text-sm\"\n                          >\n                            <option value={\"\"}>Select Status</option>\n                            <option value={\"Pending\"}>Pending</option>\n                            <option value={\"Approved\"}>Approved</option>\n                            <option value={\"Denied\"}>Denied</option>\n                          </select>\n                        </div>\n                      </div>\n                    </div>\n                  </div>\n                  {/* Upload Authorization Documents */}\n                  <div className=\"text-xs font-medium mt-5 mb-2 text-black\">\n                    Upload Authorization Documents\n                  </div>\n                  <div className=\"my-2 bg-white py-4 px-2 rounded-md\">\n                    <div\n                      {...getRootPropsUploadAuthorizationDocuments({\n                        className: \"dropzone\",\n                      })}\n                      // style={dropzoneStyle}\n                      className=\"bg-[#F5F6FF] w-full min-h-50 flex flex-col items-center justify-center  cursor-pointer\"\n                    >\n                      <input {...getInputPropsUploadAuthorizationDocuments()} />\n                      <div className=\"my-2\">\n                        <svg\n                          xmlns=\"http://www.w3.org/2000/svg\"\n                          fill=\"none\"\n                          viewBox=\"0 0 24 24\"\n                          stroke-width=\"1.5\"\n                          stroke=\"currentColor\"\n                          className=\"size-8 p-2 bg-[#0388A6] rounded-full text-white\"\n                        >\n                          <path\n                            stroke-linecap=\"round\"\n                            stroke-linejoin=\"round\"\n                            d=\"M3 16.5v2.25A2.25 2.25 0 0 0 5.25 21h13.5A2.25 2.25 0 0 0 21 18.75V16.5m-13.5-9L12 3m0 0 4.5 4.5M12 3v13.5\"\n                          />\n                        </svg>\n                      </div>\n                      <div className=\"my-2\">\n                        Drag & Drop Image File or BROWSE\n                      </div>\n                    </div>\n                    <aside style={thumbsContainer}>\n                      <div className=\"w-full flex flex-col \">\n                        {filesUploadAuthorizationDocuments?.map(\n                          (file, index) => (\n                            <div\n                              className=\"bg-[#F3F5F7] rounded-xl px-5 py-5 w-full my-2 flex flex-row items-center\"\n                              key={file.name}\n                            >\n                              <div className=\" rounded-full  bg-white text-[#81838E] text-center p-2 shadow-1 \">\n                                <svg\n                                  xmlns=\"http://www.w3.org/2000/svg\"\n                                  viewBox=\"0 0 24 24\"\n                                  fill=\"currentColor\"\n                                  class=\"size-4\"\n                                >\n                                  <path d=\"M5.625 1.5c-1.036 0-1.875.84-1.875 1.875v17.25c0 1.035.84 1.875 1.875 1.875h12.75c1.035 0 1.875-.84 1.875-1.875V12.75A3.75 3.75 0 0 0 16.5 9h-1.875a1.875 1.875 0 0 1-1.875-1.875V5.25A3.75 3.75 0 0 0 9 1.5H5.625Z\" />\n                                  <path d=\"M12.971 1.816A5.23 5.23 0 0 1 14.25 5.25v1.875c0 .207.168.375.375.375H16.5a5.23 5.23 0 0 1 3.434 1.279 9.768 9.768 0 0 0-6.963-6.963Z\" />\n                                </svg>\n                              </div>\n                              <div className=\"flex-1 px-5 text-[#303030] text-sm\">\n                                <div className=\"whitespace-nowrap overflow-hidden text-ellipsis max-w-full flex-shrink text-xs\">\n                                  {file.name}\n                                </div>\n                                <div>\n                                  {(file.size / (1024 * 1024)).toFixed(2)} mb\n                                </div>\n                              </div>\n                              <button\n                                onClick={() => {\n                                  setFilesUploadAuthorizationDocuments(\n                                    (prevFiles) =>\n                                      prevFiles.filter(\n                                        (_, indexToRemove) =>\n                                          index !== indexToRemove\n                                      )\n                                  );\n                                }}\n                                className=\"rounded-full p-1 bg-[#E6E7E8] font-bold text-[#9D9D9D]\"\n                              >\n                                <svg\n                                  xmlns=\"http://www.w3.org/2000/svg\"\n                                  fill=\"none\"\n                                  viewBox=\"0 0 24 24\"\n                                  stroke-width=\"1.5\"\n                                  stroke=\"currentColor\"\n                                  class=\"size-5\"\n                                >\n                                  <path\n                                    stroke-linecap=\"round\"\n                                    stroke-linejoin=\"round\"\n                                    d=\"M6 18 18 6M6 6l12 12\"\n                                  />\n                                </svg>\n                              </button>\n                            </div>\n                          )\n                        )}\n                      </div>\n                    </aside>\n                  </div>\n                  {/* Save & Continue - step 5 */}\n                  <div className=\"flex flex-row items-center justify-end my-3\">\n                    <button\n                      onClick={() => setStepSelect(3)}\n                      className=\"bg-white text-[#0388A6] text-sm px-5 py-3 rounded-full mr-3\"\n                    >\n                      Back\n                    </button>\n                    <button\n                      disabled={loadingCaseAdd}\n                      onClick={async () => {\n                        const providerItems = providerMultiSelect?.map(\n                          (item) => ({\n                            service: item.service?.id,\n                            provider: item.provider?.id,\n                            date: item.date,\n                          })\n                        );\n                        await dispatch(\n                          addNewCase({\n                            first_name: firstName,\n                            last_name: lastName,\n                            full_name: firstName + \" \" + lastName,\n                            birth_day: birthDate,\n                            patient_phone: phone,\n                            patient_email: email,\n                            patient_address: address,\n                            patient_city: city,\n                            patient_country: country.value,\n                            //\n                            coordinator: coordinator.value,\n                            case_date: caseDate,\n                            case_type: caseType,\n                            case_type_item:\n                              caseType === \"Medical\" ? caseTypeItem : \"\",\n                            case_description: caseDescription,\n                            //\n                            status_coordination: coordinatStatus,\n                            case_status: coordinatStatusList,\n                            appointment_date:\n                              caseTypeItem === \"Inpatient\"\n                                ? \"\"\n                                : appointmentDate,\n                            start_date:\n                              caseTypeItem === \"Inpatient\" ? startDate : \"\",\n                            end_date:\n                              caseTypeItem === \"Inpatient\" ? endDate : \"\",\n                            service_location: serviceLocation,\n                            provider: providerName.value,\n                            //\n                            invoice_number: invoiceNumber,\n                            date_issued: dateIssued,\n                            invoice_amount: amount,\n                            assurance: insuranceCompany.value,\n                            assurance_number: insuranceNumber,\n                            policy_number: policyNumber,\n                            assurance_status: initialStatus,\n                            // files\n                            initial_medical_reports: filesInitialMedicalReports,\n                            upload_invoice: filesUploadInvoice,\n                            upload_authorization_documents:\n                              filesUploadAuthorizationDocuments,\n                            //\n                            providers: providerItems ?? [],\n                            //\n                            is_pay: isPay ? \"True\" : \"False\",\n                            price_tatal: priceTotal,\n                            currency_price: currencyCode.value ?? \"\",\n                          })\n                        );\n                      }}\n                      className=\"text-white bg-[#0388A6] text-sm px-5 py-3 rounded-full\"\n                    >\n                      {loadingCaseAdd ? \"Loading..\" : \"Submit\"}\n                    </button>\n                  </div>\n                </div>\n              ) : null}\n              {/* step 6 */}\n              {stepSelect === 5 ? (\n                <div className=\"\">\n                  <div className=\"my-2 bg-white py-4 px-2 rounded-md\">\n                    <div className=\"min-h-30 flex flex-col items-center justify-center\">\n                      <svg\n                        xmlns=\"http://www.w3.org/2000/svg\"\n                        fill=\"none\"\n                        viewBox=\"0 0 24 24\"\n                        stroke-width=\"1.5\"\n                        stroke=\"currentColor\"\n                        className=\"size-13 p-3 rounded-full text-[#0388A6] bg-[#0388A6] bg-opacity-10\"\n                      >\n                        <path\n                          stroke-linecap=\"round\"\n                          stroke-linejoin=\"round\"\n                          d=\"m4.5 12.75 6 6 9-13.5\"\n                        />\n                      </svg>\n                      <div className=\"my-5 font-semibold text-2xl text-black\">\n                        Case Created Successfully!\n                      </div>\n                      <div className=\"text-base text-center md:w-2/3 mx-auto w-full px-3\">\n                        Your case has been successfully created and saved. You\n                        can now view the case details or create another case.\n                      </div>\n                      <div className=\"flex flex-row items-center justify-end my-3\">\n                        {/* <button\n                          onClick={() => {\n                            setStepSelect(4);\n                          }}\n                          className=\"text-white bg-[#0388A6] text-sm px-5 py-3 rounded-full\"\n                        >\n                          Go to Dahboard\n                        </button> */}\n                        <a\n                          href=\"/dashboard\"\n                          className=\"text-white bg-[#0388A6] text-sm px-5 py-3 rounded-full\"\n                        >\n                          Go to Dahboard\n                        </a>\n                      </div>\n                    </div>\n                  </div>\n                </div>\n              ) : null}\n            </div>\n          </div>\n        </div>\n      </div>\n    </DefaultLayout>\n  );\n}\n\nexport default AddCaseScreen;\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,EAAIC,SAAS,CAAEC,QAAQ,KAAQ,OAAO,CAClD,OAASC,WAAW,CAAEC,WAAW,KAAQ,aAAa,CACtD,OAASC,WAAW,CAAEC,WAAW,KAAQ,kBAAkB,CAC3D,MAAO,CAAAC,aAAa,KAAM,6BAA6B,CACvD,MAAO,CAAAC,eAAe,KAAM,oCAAoC,CAChE,OAASC,KAAK,KAAQ,gBAAgB,CACtC,OAASC,sBAAsB,KAAQ,qCAAqC,CAC5E,OAASC,UAAU,KAAQ,iCAAiC,CAE5D,MAAO,CAAAC,MAAM,KAAM,cAAc,CAEjC,OAASC,WAAW,KAAQ,gBAAgB,CAC5C,OAASC,uBAAuB,KAAQ,sCAAsC,CAC9E,OAASC,yBAAyB,KAAQ,iCAAiC,CAC3E,OAASC,SAAS,CAAEC,aAAa,KAAQ,iBAAiB,CAC1D,MAAO,CAAAC,eAAe,KAAM,2BAA2B,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAExD,KAAM,CAAAC,SAAS,CAAG,CAChB,CACEC,KAAK,CAAE,CAAC,CACRC,KAAK,CAAE,qBAAqB,CAC5BC,WAAW,CACT,sEACJ,CAAC,CACD,CACEF,KAAK,CAAE,CAAC,CACRC,KAAK,CAAE,sBAAsB,CAC7BC,WAAW,CACT,wFACJ,CAAC,CACD,CACEF,KAAK,CAAE,CAAC,CACRC,KAAK,CAAE,iBAAiB,CACxBC,WAAW,CAAE,yDACf,CAAC,CACD,CACEF,KAAK,CAAE,CAAC,CACRC,KAAK,CAAE,UAAU,CACjBC,WAAW,CACT,6GACJ,CAAC,CACD,CACEF,KAAK,CAAE,CAAC,CACRC,KAAK,CAAE,yBAAyB,CAChCC,WAAW,CACT,6GACJ,CAAC,CACD,CACEF,KAAK,CAAE,CAAC,CACRC,KAAK,CAAE,QAAQ,CACfC,WAAW,CAAE,8CACf,CAAC,CACF,CAED,KAAM,CAAAC,eAAe,CAAG,CACtBC,OAAO,CAAE,MAAM,CACfC,aAAa,CAAE,KAAK,CACpBC,QAAQ,CAAE,MAAM,CAChBC,SAAS,CAAE,EACb,CAAC,CAED,QAAS,CAAAC,aAAaA,CAAA,CAAG,CACvB,KAAM,CAAAC,QAAQ,CAAG3B,WAAW,CAAC,CAAC,CAC9B,KAAM,CAAA4B,QAAQ,CAAG7B,WAAW,CAAC,CAAC,CAC9B,KAAM,CAAA8B,QAAQ,CAAGhC,WAAW,CAAC,CAAC,CAE9B;AACA,KAAM,CAACiC,SAAS,CAAEC,YAAY,CAAC,CAAGnC,QAAQ,CAAC,EAAE,CAAC,CAC9C,KAAM,CAACoC,cAAc,CAAEC,iBAAiB,CAAC,CAAGrC,QAAQ,CAAC,EAAE,CAAC,CAExD,KAAM,CAACsC,QAAQ,CAAEC,WAAW,CAAC,CAAGvC,QAAQ,CAAC,EAAE,CAAC,CAC5C,KAAM,CAACwC,aAAa,CAAEC,gBAAgB,CAAC,CAAGzC,QAAQ,CAAC,EAAE,CAAC,CAEtD,KAAM,CAAC0C,KAAK,CAAEC,QAAQ,CAAC,CAAG3C,QAAQ,CAAC,EAAE,CAAC,CACtC,KAAM,CAAC4C,UAAU,CAAEC,aAAa,CAAC,CAAG7C,QAAQ,CAAC,EAAE,CAAC,CAEhD,KAAM,CAAC8C,SAAS,CAAEC,YAAY,CAAC,CAAG/C,QAAQ,CAAC,EAAE,CAAC,CAC9C,KAAM,CAACgD,cAAc,CAAEC,iBAAiB,CAAC,CAAGjD,QAAQ,CAAC,EAAE,CAAC,CAExD,KAAM,CAACkD,KAAK,CAAEC,QAAQ,CAAC,CAAGnD,QAAQ,CAAC,EAAE,CAAC,CACtC,KAAM,CAACoD,UAAU,CAAEC,aAAa,CAAC,CAAGrD,QAAQ,CAAC,EAAE,CAAC,CAEhD,KAAM,CAACsD,OAAO,CAAEC,UAAU,CAAC,CAAGvD,QAAQ,CAAC,EAAE,CAAC,CAC1C,KAAM,CAACwD,YAAY,CAAEC,eAAe,CAAC,CAAGzD,QAAQ,CAAC,EAAE,CAAC,CAEpD,KAAM,CAAC0D,IAAI,CAAEC,OAAO,CAAC,CAAG3D,QAAQ,CAAC,EAAE,CAAC,CACpC,KAAM,CAAC4D,SAAS,CAAEC,YAAY,CAAC,CAAG7D,QAAQ,CAAC,EAAE,CAAC,CAE9C,KAAM,CAAC8D,OAAO,CAAEC,UAAU,CAAC,CAAG/D,QAAQ,CAAC,EAAE,CAAC,CAC1C,KAAM,CAACgE,YAAY,CAAEC,eAAe,CAAC,CAAGjE,QAAQ,CAAC,EAAE,CAAC,CAEpD,KAAM,CAACkE,KAAK,CAAEC,QAAQ,CAAC,CAAGnE,QAAQ,CAAC,KAAK,CAAC,CAEzC,KAAM,CAACoE,YAAY,CAAEC,eAAe,CAAC,CAAGrE,QAAQ,CAAC,EAAE,CAAC,CACpD,KAAM,CAACsE,iBAAiB,CAAEC,oBAAoB,CAAC,CAAGvE,QAAQ,CAAC,EAAE,CAAC,CAE9D,KAAM,CAACwE,UAAU,CAAEC,aAAa,CAAC,CAAGzE,QAAQ,CAAC,CAAC,CAAC,CAC/C,KAAM,CAAC0E,eAAe,CAAEC,kBAAkB,CAAC,CAAG3E,QAAQ,CAAC,EAAE,CAAC,CAC1D;AACA,KAAM,CAAC4E,WAAW,CAAEC,cAAc,CAAC,CAAG7E,QAAQ,CAAC,EAAE,CAAC,CAClD,KAAM,CAAC8E,gBAAgB,CAAEC,mBAAmB,CAAC,CAAG/E,QAAQ,CAAC,EAAE,CAAC,CAE5D,KAAM,CAACgF,gBAAgB,CAAEC,mBAAmB,CAAC,CAAGjF,QAAQ,CAAC,EAAE,CAAC,CAC5D,KAAM,CAACkF,mBAAmB,CAAEC,sBAAsB,CAAC,CAAGnF,QAAQ,CAAC,EAAE,CAAC,CAElE,KAAM,CAACoF,eAAe,CAAEC,kBAAkB,CAAC,CAAGrF,QAAQ,CAAC,EAAE,CAAC,CAC1D,KAAM,CAACsF,oBAAoB,CAAEC,uBAAuB,CAAC,CAAGvF,QAAQ,CAAC,EAAE,CAAC,CAEpE,KAAM,CAACwF,YAAY,CAAEC,eAAe,CAAC,CAAGzF,QAAQ,CAAC,EAAE,CAAC,CACpD,KAAM,CAAC0F,iBAAiB,CAAEC,oBAAoB,CAAC,CAAG3F,QAAQ,CAAC,EAAE,CAAC,CAE9D,KAAM,CAAC4F,QAAQ,CAAEC,WAAW,CAAC,CAAG7F,QAAQ,CACtC,GAAI,CAAA8F,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CACvC,CAAC,CACD,KAAM,CAACC,aAAa,CAAEC,gBAAgB,CAAC,CAAGlG,QAAQ,CAAC,EAAE,CAAC,CAEtD,KAAM,CAACmG,QAAQ,CAAEC,WAAW,CAAC,CAAGpG,QAAQ,CAAC,EAAE,CAAC,CAC5C,KAAM,CAACqG,aAAa,CAAEC,gBAAgB,CAAC,CAAGtG,QAAQ,CAAC,EAAE,CAAC,CAEtD,KAAM,CAACuG,YAAY,CAAEC,eAAe,CAAC,CAAGxG,QAAQ,CAAC,EAAE,CAAC,CACpD,KAAM,CAACyG,iBAAiB,CAAEC,oBAAoB,CAAC,CAAG1G,QAAQ,CAAC,EAAE,CAAC,CAE9D,KAAM,CAAC2G,eAAe,CAAEC,kBAAkB,CAAC,CAAG5G,QAAQ,CAAC,EAAE,CAAC,CAC1D,KAAM,CAAC6G,oBAAoB,CAAEC,uBAAuB,CAAC,CAAG9G,QAAQ,CAAC,EAAE,CAAC,CACpE;AACA,KAAM,CAAC+G,eAAe,CAAEC,kBAAkB,CAAC,CAAGhH,QAAQ,CAAC,EAAE,CAAC,CAC1D,KAAM,CAACiH,oBAAoB,CAAEC,uBAAuB,CAAC,CAAGlH,QAAQ,CAAC,EAAE,CAAC,CAEpE,KAAM,CAACmH,mBAAmB,CAAEC,sBAAsB,CAAC,CAAGpH,QAAQ,CAAC,EAAE,CAAC,CAClE,KAAM,CAACqH,wBAAwB,CAAEC,2BAA2B,CAAC,CAAGtH,QAAQ,CAAC,EAAE,CAAC,CAE5E,KAAM,CAACuH,eAAe,CAAEC,kBAAkB,CAAC,CAAGxH,QAAQ,CAAC,EAAE,CAAC,CAC1D,KAAM,CAACyH,oBAAoB,CAAEC,uBAAuB,CAAC,CAAG1H,QAAQ,CAAC,EAAE,CAAC,CAEpE,KAAM,CAAC2H,SAAS,CAAEC,YAAY,CAAC,CAAG5H,QAAQ,CAAC,EAAE,CAAC,CAC9C,KAAM,CAAC6H,cAAc,CAAEC,iBAAiB,CAAC,CAAG9H,QAAQ,CAAC,EAAE,CAAC,CAExD,KAAM,CAAC+H,OAAO,CAAEC,UAAU,CAAC,CAAGhI,QAAQ,CAAC,EAAE,CAAC,CAC1C,KAAM,CAACiI,YAAY,CAAEC,eAAe,CAAC,CAAGlI,QAAQ,CAAC,EAAE,CAAC,CAEpD,KAAM,CAACmI,eAAe,CAAEC,kBAAkB,CAAC,CAAGpI,QAAQ,CAAC,EAAE,CAAC,CAC1D,KAAM,CAACqI,oBAAoB,CAAEC,uBAAuB,CAAC,CAAGtI,QAAQ,CAAC,EAAE,CAAC,CACpE;AACA,KAAM,CAACuI,YAAY,CAAEC,eAAe,CAAC,CAAGxI,QAAQ,CAAC,EAAE,CAAC,CACpD,KAAM,CAACyI,iBAAiB,CAAEC,oBAAoB,CAAC,CAAG1I,QAAQ,CAAC,EAAE,CAAC,CAE9D,KAAM,CAAC2I,aAAa,CAAEC,gBAAgB,CAAC,CAAG5I,QAAQ,CAAC,EAAE,CAAC,CACtD,KAAM,CAAC6I,kBAAkB,CAAEC,qBAAqB,CAAC,CAAG9I,QAAQ,CAAC,EAAE,CAAC,CAEhE,KAAM,CAAC+I,aAAa,CAAEC,gBAAgB,CAAC,CAAGhJ,QAAQ,CAAC,EAAE,CAAC,CACtD,KAAM,CAACiJ,kBAAkB,CAAEC,qBAAqB,CAAC,CAAGlJ,QAAQ,CAAC,EAAE,CAAC,CAEhE,KAAM,CAACmJ,eAAe,CAAEC,kBAAkB,CAAC,CAAGpJ,QAAQ,CAAC,EAAE,CAAC,CAC1D,KAAM,CAACqJ,oBAAoB,CAAEC,uBAAuB,CAAC,CAAGtJ,QAAQ,CAAC,EAAE,CAAC,CACpE;AACA,KAAM,CAACuJ,aAAa,CAAEC,gBAAgB,CAAC,CAAGxJ,QAAQ,CAAC,EAAE,CAAC,CACtD,KAAM,CAACyJ,kBAAkB,CAAEC,qBAAqB,CAAC,CAAG1J,QAAQ,CAAC,EAAE,CAAC,CAEhE,KAAM,CAAC2J,UAAU,CAAEC,aAAa,CAAC,CAAG5J,QAAQ,CAAC,EAAE,CAAC,CAChD,KAAM,CAAC6J,eAAe,CAAEC,kBAAkB,CAAC,CAAG9J,QAAQ,CAAC,EAAE,CAAC,CAE1D,KAAM,CAAC+J,MAAM,CAAEC,SAAS,CAAC,CAAGhK,QAAQ,CAAC,CAAC,CAAC,CACvC,KAAM,CAACiK,WAAW,CAAEC,cAAc,CAAC,CAAGlK,QAAQ,CAAC,EAAE,CAAC,CAClD;AACA,KAAM,CAACmK,gBAAgB,CAAEC,mBAAmB,CAAC,CAAGpK,QAAQ,CAAC,EAAE,CAAC,CAC5D,KAAM,CAACqK,qBAAqB,CAAEC,wBAAwB,CAAC,CAAGtK,QAAQ,CAAC,EAAE,CAAC,CAEtE,KAAM,CAACuK,eAAe,CAAEC,kBAAkB,CAAC,CAAGxK,QAAQ,CAAC,EAAE,CAAC,CAC1D,KAAM,CAACyK,oBAAoB,CAAEC,uBAAuB,CAAC,CAAG1K,QAAQ,CAAC,EAAE,CAAC,CAEpE,KAAM,CAAC2K,YAAY,CAAEC,eAAe,CAAC,CAAG5K,QAAQ,CAAC,EAAE,CAAC,CACpD,KAAM,CAAC6K,iBAAiB,CAAEC,oBAAoB,CAAC,CAAG9K,QAAQ,CAAC,EAAE,CAAC,CAE9D,KAAM,CAAC+K,aAAa,CAAEC,gBAAgB,CAAC,CAAGhL,QAAQ,CAAC,EAAE,CAAC,CACtD,KAAM,CAACiL,kBAAkB,CAAEC,qBAAqB,CAAC,CAAGlL,QAAQ,CAAC,EAAE,CAAC,CAEhE;AACA;AACA,KAAM,CAACmL,0BAA0B,CAAEC,6BAA6B,CAAC,CAAGpL,QAAQ,CAC1E,EACF,CAAC,CACD,KAAM,CACJqL,YAAY,CAAEC,0BAA0B,CACxCC,aAAa,CAAEC,2BACjB,CAAC,CAAG7K,WAAW,CAAC,CACd8K,MAAM,CAAE,CACN,GAAG,CAAE,EACP,CAAC,CACDC,MAAM,CAAGC,aAAa,EAAK,CACzBP,6BAA6B,CAAEQ,SAAS,EAAK,CAC3C,GAAGA,SAAS,CACZ,GAAGD,aAAa,CAACE,GAAG,CAAEC,IAAI,EACxBC,MAAM,CAACC,MAAM,CAACF,IAAI,CAAE,CAClBG,OAAO,CAAEC,GAAG,CAACC,eAAe,CAACL,IAAI,CACnC,CAAC,CACH,CAAC,CACF,CAAC,CACJ,CACF,CAAC,CAAC,CAEF/L,SAAS,CAAC,IAAM,CACd,MAAO,IACLoL,0BAA0B,CAACiB,OAAO,CAAEN,IAAI,EACtCI,GAAG,CAACG,eAAe,CAACP,IAAI,CAACG,OAAO,CAClC,CAAC,CACL,CAAC,CAAE,EAAE,CAAC,CAEN;AACA,KAAM,CAACK,kBAAkB,CAAEC,qBAAqB,CAAC,CAAGvM,QAAQ,CAAC,EAAE,CAAC,CAChE,KAAM,CACJqL,YAAY,CAAEmB,yBAAyB,CACvCjB,aAAa,CAAEkB,0BACjB,CAAC,CAAG9L,WAAW,CAAC,CACd8K,MAAM,CAAE,CACN,GAAG,CAAE,EACP,CAAC,CACDC,MAAM,CAAGC,aAAa,EAAK,CACzBY,qBAAqB,CAAEX,SAAS,EAAK,CACnC,GAAGA,SAAS,CACZ,GAAGD,aAAa,CAACE,GAAG,CAAEC,IAAI,EACxBC,MAAM,CAACC,MAAM,CAACF,IAAI,CAAE,CAClBG,OAAO,CAAEC,GAAG,CAACC,eAAe,CAACL,IAAI,CACnC,CAAC,CACH,CAAC,CACF,CAAC,CACJ,CACF,CAAC,CAAC,CAEF/L,SAAS,CAAC,IAAM,CACd,MAAO,IACLuM,kBAAkB,CAACF,OAAO,CAAEN,IAAI,EAAKI,GAAG,CAACG,eAAe,CAACP,IAAI,CAACG,OAAO,CAAC,CAAC,CAC3E,CAAC,CAAE,EAAE,CAAC,CACN;AACA,KAAM,CACJS,iCAAiC,CACjCC,oCAAoC,CACrC,CAAG3M,QAAQ,CAAC,EAAE,CAAC,CAChB,KAAM,CACJqL,YAAY,CAAEuB,wCAAwC,CACtDrB,aAAa,CAAEsB,yCACjB,CAAC,CAAGlM,WAAW,CAAC,CACd8K,MAAM,CAAE,CACN,GAAG,CAAE,EACP,CAAC,CACDC,MAAM,CAAGC,aAAa,EAAK,CACzBgB,oCAAoC,CAAEf,SAAS,EAAK,CAClD,GAAGA,SAAS,CACZ,GAAGD,aAAa,CAACE,GAAG,CAAEC,IAAI,EACxBC,MAAM,CAACC,MAAM,CAACF,IAAI,CAAE,CAClBG,OAAO,CAAEC,GAAG,CAACC,eAAe,CAACL,IAAI,CACnC,CAAC,CACH,CAAC,CACF,CAAC,CACJ,CACF,CAAC,CAAC,CAEF/L,SAAS,CAAC,IAAM,CACd,MAAO,IACL2M,iCAAiC,CAACN,OAAO,CAAEN,IAAI,EAC7CI,GAAG,CAACG,eAAe,CAACP,IAAI,CAACG,OAAO,CAClC,CAAC,CACL,CAAC,CAAE,EAAE,CAAC,CAEN;AAEA;AAEA,KAAM,CAACa,UAAU,CAAEC,aAAa,CAAC,CAAG/M,QAAQ,CAAC,CAAC,CAAC,CAC/C,KAAM,CAACgN,SAAS,CAAEC,YAAY,CAAC,CAAGjN,QAAQ,CAAC,IAAI,CAAC,CAEhD,KAAM,CAAAkN,SAAS,CAAGhN,WAAW,CAAEiN,KAAK,EAAKA,KAAK,CAACD,SAAS,CAAC,CACzD,KAAM,CAAEE,QAAS,CAAC,CAAGF,SAAS,CAE9B,KAAM,CAAAG,aAAa,CAAGnN,WAAW,CAAEiN,KAAK,EAAKA,KAAK,CAACG,YAAY,CAAC,CAChE,KAAM,CAAEC,SAAS,CAAEC,gBAAgB,CAAEC,cAAe,CAAC,CAAGJ,aAAa,CAErE,KAAM,CAAAK,UAAU,CAAGxN,WAAW,CAAEiN,KAAK,EAAKA,KAAK,CAACQ,aAAa,CAAC,CAC9D,KAAM,CAAEC,cAAc,CAAEC,cAAc,CAAEC,YAAa,CAAC,CAAGJ,UAAU,CAEnE,KAAM,CAAAK,cAAc,CAAG7N,WAAW,CAAEiN,KAAK,EAAKA,KAAK,CAACa,aAAa,CAAC,CAClE,KAAM,CAAEC,UAAU,CAAEC,iBAAiB,CAAEC,eAAgB,CAAC,CAAGJ,cAAc,CAEzE,KAAM,CAAAK,gBAAgB,CAAGlO,WAAW,CAAEiN,KAAK,EAAKA,KAAK,CAACkB,gBAAgB,CAAC,CACvE,KAAM,CAAEC,YAAY,CAAEC,mBAAmB,CAAEC,iBAAkB,CAAC,CAC5DJ,gBAAgB,CAElB,KAAM,CAAAK,QAAQ,CAAG,GAAG,CACpB1O,SAAS,CAAC,IAAM,CACd,GAAI,CAACqN,QAAQ,CAAE,CACbrL,QAAQ,CAAC0M,QAAQ,CAAC,CACpB,CAAC,IAAM,CACL;AACAxB,YAAY,CAAC,IAAI,CAAC,CAElBF,aAAa,CAAC,CAAC,CAAC,CAChB9K,QAAQ,CAACpB,yBAAyB,CAAC,GAAG,CAAC,CAAC,CACxCoB,QAAQ,CAACzB,sBAAsB,CAAC,GAAG,CAAC,CAAC,CACrCyB,QAAQ,CAACrB,uBAAuB,CAAC,GAAG,CAAC,CAAC,CACtC;AAEA;AACA,KAAM,CAAA8N,SAAS,CAAGC,UAAU,CAAC,IAAM,CACjC1B,YAAY,CAAC,KAAK,CAAC,CACnB2B,OAAO,CAACC,GAAG,CAAC,wDAAwD,CAAC,CACvE,CAAC,CAAE,IAAI,CAAC,CAER;AACA,MAAO,IAAMC,YAAY,CAACJ,SAAS,CAAC,CACtC,CACF,CAAC,CAAE,CAAC3M,QAAQ,CAAEqL,QAAQ,CAAEnL,QAAQ,CAAC,CAAC,CAElClC,SAAS,CAAC,IAAM,CACd,GAAI8N,cAAc,CAAE,CAClBd,aAAa,CAAC,CAAC,CAAC,CAChBE,YAAY,CAAC,KAAK,CAAC,CACrB,CACF,CAAC,CAAE,CAACY,cAAc,CAAC,CAAC,CAEpB;AACA9N,SAAS,CAAC,IAAM,CACd,GAAI6N,cAAc,CAAE,CAClBX,YAAY,CAAC,IAAI,CAAC,CACpB,CACF,CAAC,CAAE,CAACW,cAAc,CAAC,CAAC,CAEpB;AACA7N,SAAS,CAAC,IAAM,CACd;AACA,GACE,CAACyN,gBAAgB,EACjB,CAACU,iBAAiB,EAClB,CAACK,mBAAmB,EACpBhB,SAAS,EACTA,SAAS,CAACwB,MAAM,CAAG,CAAC,EACpBT,YAAY,EACZA,YAAY,CAACS,MAAM,CAAG,CAAC,CACvB,CACA;AACA9B,YAAY,CAAC,KAAK,CAAC,CACrB,CACF,CAAC,CAAE,CACDO,gBAAgB,CAChBU,iBAAiB,CACjBK,mBAAmB,CACnBhB,SAAS,CACTe,YAAY,CACb,CAAC,CAEF,mBACElN,KAAA,CAACf,aAAa,EAAA2O,QAAA,EAEXhC,SAAS,eACR9L,IAAA,QAAK+N,SAAS,CAAC,+FAA+F,CAAAD,QAAA,cAC5G5N,KAAA,QAAK6N,SAAS,CAAC,8DAA8D,CAAAD,QAAA,eAC3E9N,IAAA,QAAK+N,SAAS,CAAC,iFAAiF,CAAM,CAAC,cACvG/N,IAAA,QAAK+N,SAAS,CAAC,2BAA2B,CAAAD,QAAA,CAAC,iBAAe,CAAK,CAAC,EAC7D,CAAC,CACH,CACN,cAED5N,KAAA,QAAK6N,SAAS,CAAC,EAAE,CAAAD,QAAA,eACf5N,KAAA,QAAK6N,SAAS,CAAC,yCAAyC,CAAAD,QAAA,eAEtD9N,IAAA,MAAGgO,IAAI,CAAC,YAAY,CAAAF,QAAA,cAClB5N,KAAA,QAAK6N,SAAS,CAAC,+CAA+C,CAAAD,QAAA,eAC5D9N,IAAA,QACEiO,KAAK,CAAC,4BAA4B,CAClCC,IAAI,CAAC,MAAM,CACXC,OAAO,CAAC,WAAW,CACnB,eAAa,KAAK,CAClBC,MAAM,CAAC,cAAc,CACrBL,SAAS,CAAC,SAAS,CAAAD,QAAA,cAEnB9N,IAAA,SACEqO,aAAa,CAAC,OAAO,CACrBC,cAAc,CAAC,OAAO,CACtBC,CAAC,CAAC,4OAA4O,CAC/O,CAAC,CACC,CAAC,cACNvO,IAAA,SAAM+N,SAAS,CAAC,MAAM,CAAAD,QAAA,CAAC,WAAS,CAAM,CAAC,EACpC,CAAC,CACL,CAAC,cACJ9N,IAAA,SAAA8N,QAAA,cACE9N,IAAA,QACEiO,KAAK,CAAC,4BAA4B,CAClCC,IAAI,CAAC,MAAM,CACXC,OAAO,CAAC,WAAW,CACnB,eAAa,KAAK,CAClBC,MAAM,CAAC,cAAc,CACrBL,SAAS,CAAC,SAAS,CAAAD,QAAA,cAEnB9N,IAAA,SACEqO,aAAa,CAAC,OAAO,CACrBC,cAAc,CAAC,OAAO,CACtBC,CAAC,CAAC,2BAA2B,CAC9B,CAAC,CACC,CAAC,CACF,CAAC,cACPvO,IAAA,QAAK+N,SAAS,CAAC,EAAE,CAAAD,QAAA,CAAC,iBAAe,CAAK,CAAC,EACpC,CAAC,cAEN9N,IAAA,QAAK+N,SAAS,CAAC,gCAAgC,CAAAD,QAAA,cAC7C9N,IAAA,OAAI+N,SAAS,CAAC,qDAAqD,CAAAD,QAAA,CAAC,UAEpE,CAAI,CAAC,CACF,CAAC,cAEN9N,IAAA,QAAK+N,SAAS,CAAC,mIAAmI,CAAAD,QAAA,cAChJ5N,KAAA,QAAK6N,SAAS,CAAC,2BAA2B,CAAAD,QAAA,eACxC5N,KAAA,QAAK6N,SAAS,CAAC,2DAA2D,CAAAD,QAAA,eACxE9N,IAAA,QAAK+N,SAAS,CAAC,wFAAwF,CAAM,CAAC,CAC7G5N,SAAS,SAATA,SAAS,iBAATA,SAAS,CAAEwK,GAAG,CAAC,CAAC6D,IAAI,CAAEpO,KAAK,gBAC1BF,KAAA,QACEuO,OAAO,CAAEA,CAAA,GAAM,CACb,GAAI7C,UAAU,CAAG4C,IAAI,CAACpO,KAAK,EAAIwL,UAAU,GAAK,CAAC,CAAE,CAC/CC,aAAa,CAAC2C,IAAI,CAACpO,KAAK,CAAC,CAC3B,CACF,CAAE,CACF2N,SAAS,CAAG,kCACVnC,UAAU,CAAG4C,IAAI,CAACpO,KAAK,EAAIwL,UAAU,GAAK,CAAC,CACvC,gBAAgB,CAChB,EACL,8BAA8B,CAAAkC,QAAA,EAE9BlC,UAAU,CAAG4C,IAAI,CAACpO,KAAK,cACtBJ,IAAA,QAAK+N,SAAS,CAAC,oGAAoG,CAAAD,QAAA,cACjH9N,IAAA,QACE0O,GAAG,CAAEtP,eAAgB,CACrB2O,SAAS,CAAC,QAAQ,CAClBY,OAAO,CAAGC,CAAC,EAAK,CACdA,CAAC,CAACC,MAAM,CAACC,OAAO,CAAG,IAAI,CACvBF,CAAC,CAACC,MAAM,CAACH,GAAG,CAAG,yBAAyB,CAC1C,CAAE,CACH,CAAC,CACC,CAAC,CACJ9C,UAAU,GAAK4C,IAAI,CAACpO,KAAK,cAC3BJ,IAAA,QAAK+N,SAAS,CAAC,kDAAkD,CAAM,CAAC,cAExE/N,IAAA,QAAK+N,SAAS,CAAC,oGAAoG,CAAAD,QAAA,cACjH9N,IAAA,QACEiO,KAAK,CAAC,4BAA4B,CAClCC,IAAI,CAAC,MAAM,CACXC,OAAO,CAAC,WAAW,CACnB,eAAa,KAAK,CAClBC,MAAM,CAAC,cAAc,CACrBL,SAAS,CAAC,QAAQ,CAAAD,QAAA,cAElB9N,IAAA,SACE,iBAAe,OAAO,CACtB,kBAAgB,OAAO,CACvBuO,CAAC,CAAC,uBAAuB,CAC1B,CAAC,CACC,CAAC,CACH,CACN,cAEDrO,KAAA,QAAK6N,SAAS,CAAC,wBAAwB,CAAAD,QAAA,eACrC9N,IAAA,QAAK+N,SAAS,CAAC,qBAAqB,CAAAD,QAAA,CAAEU,IAAI,CAACnO,KAAK,CAAM,CAAC,CACtDuL,UAAU,GAAK4C,IAAI,CAACpO,KAAK,cACxBJ,IAAA,QAAK+N,SAAS,CAAC,oCAAoC,CAAAD,QAAA,CAChDU,IAAI,CAAClO,WAAW,CACd,CAAC,CACJ,IAAI,EACL,CAAC,EACH,CACN,CAAC,EACC,CAAC,cACNJ,KAAA,QAAK6N,SAAS,CAAC,0CAA0C,CAAAD,QAAA,EAEtDlC,UAAU,GAAK,CAAC,cACf1L,KAAA,QAAK6N,SAAS,CAAC,EAAE,CAAAD,QAAA,eACf9N,IAAA,QAAK+N,SAAS,CAAC,sCAAsC,CAAAD,QAAA,CAAC,qBAEtD,CAAK,CAAC,cAEN9N,IAAA,QAAK+N,SAAS,CAAC,0CAA0C,CAAAD,QAAA,CAAC,kBAE1D,CAAK,CAAC,cACN5N,KAAA,QAAK6N,SAAS,CAAC,oCAAoC,CAAAD,QAAA,eACjD5N,KAAA,QAAK6N,SAAS,CAAC,6BAA6B,CAAAD,QAAA,eAC1C5N,KAAA,QAAK6N,SAAS,CAAC,+BAA+B,CAAAD,QAAA,eAC5C5N,KAAA,QAAK6N,SAAS,CAAC,8BAA8B,CAAAD,QAAA,EAAC,aACjC,cAAA9N,IAAA,WAAQ+N,SAAS,CAAC,aAAa,CAAAD,QAAA,CAAC,GAAC,CAAQ,CAAC,EAClD,CAAC,cACN5N,KAAA,QAAA4N,QAAA,eACE9N,IAAA,UACE+N,SAAS,CAAG,wBACV7M,cAAc,CACV,eAAe,CACf,kBACL,mCAAmC,CACpC6N,IAAI,CAAC,MAAM,CACXC,WAAW,CAAC,YAAY,CACxBC,KAAK,CAAEjO,SAAU,CACjBkO,QAAQ,CAAGC,CAAC,EAAKlO,YAAY,CAACkO,CAAC,CAACN,MAAM,CAACI,KAAK,CAAE,CAC/C,CAAC,cACFjP,IAAA,QAAK+N,SAAS,CAAC,yBAAyB,CAAAD,QAAA,CACrC5M,cAAc,CAAGA,cAAc,CAAG,EAAE,CAClC,CAAC,EACH,CAAC,EACH,CAAC,cAENhB,KAAA,QAAK6N,SAAS,CAAC,+BAA+B,CAAAD,QAAA,eAC5C9N,IAAA,QAAK+N,SAAS,CAAC,6BAA6B,CAAAD,QAAA,CAAC,WAE7C,CAAK,CAAC,cACN9N,IAAA,QAAA8N,QAAA,cACE9N,IAAA,UACE+N,SAAS,CAAC,wEAAwE,CAClFgB,IAAI,CAAC,MAAM,CACXC,WAAW,CAAC,WAAW,CACvBC,KAAK,CAAE7N,QAAS,CAChB8N,QAAQ,CAAGC,CAAC,EAAK9N,WAAW,CAAC8N,CAAC,CAACN,MAAM,CAACI,KAAK,CAAE,CAC9C,CAAC,CACC,CAAC,EACH,CAAC,EACH,CAAC,cAEN/O,KAAA,QAAK6N,SAAS,CAAC,4BAA4B,CAAAD,QAAA,eACzC5N,KAAA,QAAK6N,SAAS,CAAC,8BAA8B,CAAAD,QAAA,eAC3C9N,IAAA,QAAK+N,SAAS,CAAC,8BAA8B,CAAAD,QAAA,CAAC,OAE9C,CAAK,CAAC,cACN5N,KAAA,QAAA4N,QAAA,eACE9N,IAAA,UACE+N,SAAS,CAAG,wBACVrM,UAAU,CAAG,eAAe,CAAG,kBAChC,mCAAmC,CACpCqN,IAAI,CAAC,OAAO,CACZC,WAAW,CAAC,eAAe,CAC3BC,KAAK,CAAEzN,KAAM,CACb0N,QAAQ,CAAGC,CAAC,EAAK1N,QAAQ,CAAC0N,CAAC,CAACN,MAAM,CAACI,KAAK,CAAE,CAC3C,CAAC,cACFjP,IAAA,QAAK+N,SAAS,CAAC,yBAAyB,CAAAD,QAAA,CACrCpM,UAAU,CAAGA,UAAU,CAAG,EAAE,CAC1B,CAAC,EACH,CAAC,EACH,CAAC,cAENxB,KAAA,QAAK6N,SAAS,CAAC,+BAA+B,CAAAD,QAAA,eAC5C5N,KAAA,QAAK6N,SAAS,CAAC,6BAA6B,CAAAD,QAAA,EAAC,QACrC,cAAA9N,IAAA,WAAQ+N,SAAS,CAAC,aAAa,CAAAD,QAAA,CAAC,GAAC,CAAQ,CAAC,EAC7C,CAAC,cACN5N,KAAA,QAAA4N,QAAA,eACE9N,IAAA,UACE+N,SAAS,CAAG,uBACV7L,UAAU,CAAG,eAAe,CAAG,kBAChC,mCAAmC,CACpC6M,IAAI,CAAC,MAAM,CACXC,WAAW,CAAC,UAAU,CACtBC,KAAK,CAAEjN,KAAM,CACbkN,QAAQ,CAAGC,CAAC,EAAKlN,QAAQ,CAACkN,CAAC,CAACN,MAAM,CAACI,KAAK,CAAE,CAC3C,CAAC,cACFjP,IAAA,QAAK+N,SAAS,CAAC,yBAAyB,CAAAD,QAAA,CACrC5L,UAAU,CAAGA,UAAU,CAAG,EAAE,CAC1B,CAAC,EACH,CAAC,EACH,CAAC,EACH,CAAC,cAGNhC,KAAA,QAAK6N,SAAS,CAAC,4BAA4B,CAAAD,QAAA,eACzC5N,KAAA,QAAK6N,SAAS,CAAC,qBAAqB,CAAAD,QAAA,eAClC5N,KAAA,QAAK6N,SAAS,CAAC,8BAA8B,CAAAD,QAAA,EAAC,UACpC,cAAA9N,IAAA,WAAQ+N,SAAS,CAAC,aAAa,CAAAD,QAAA,CAAC,GAAC,CAAQ,CAAC,EAC/C,CAAC,cACN5N,KAAA,QAAA4N,QAAA,eACE9N,IAAA,CAACR,MAAM,EACLyP,KAAK,CAAErM,OAAQ,CACfsM,QAAQ,CAAGE,MAAM,EAAK,CACpBvM,UAAU,CAACuM,MAAM,CAAC,CACpB,CAAE,CACFC,OAAO,CAAEzP,SAAS,CAAC+K,GAAG,CAAE/H,OAAO,GAAM,CACnCqM,KAAK,CAAErM,OAAO,CAACvC,KAAK,CACpBiP,KAAK,cACHpP,KAAA,QACE6N,SAAS,CAAG,GACVnL,OAAO,CAACvC,KAAK,GAAK,EAAE,CAAG,MAAM,CAAG,EACjC,6BAA6B,CAAAyN,QAAA,eAE9B9N,IAAA,SAAM+N,SAAS,CAAC,MAAM,CAAAD,QAAA,CAAElL,OAAO,CAAC2M,IAAI,CAAO,CAAC,cAC5CvP,IAAA,SAAA8N,QAAA,CAAOlL,OAAO,CAACvC,KAAK,CAAO,CAAC,EACzB,CAET,CAAC,CAAC,CAAE,CACJ0N,SAAS,CAAC,SAAS,CACnBiB,WAAW,CAAC,qBAAqB,CACjCQ,YAAY,MACZC,MAAM,CAAE,CACNC,OAAO,CAAEA,CAACC,IAAI,CAAE1D,KAAK,IAAM,CACzB,GAAG0D,IAAI,CACPC,UAAU,CAAE,MAAM,CAClBC,MAAM,CAAE/M,YAAY,CAChB,mBAAmB,CACnB,mBAAmB,CACvBgN,SAAS,CAAE7D,KAAK,CAAC8D,SAAS,CAAG,MAAM,CAAG,MAAM,CAC5C,SAAS,CAAE,CACTF,MAAM,CAAE,mBACV,CACF,CAAC,CAAC,CACFT,MAAM,CAAGO,IAAI,GAAM,CACjB,GAAGA,IAAI,CACPnP,OAAO,CAAE,MAAM,CACfwP,UAAU,CAAE,QACd,CAAC,CAAC,CACFC,WAAW,CAAGN,IAAI,GAAM,CACtB,GAAGA,IAAI,CACPnP,OAAO,CAAE,MAAM,CACfwP,UAAU,CAAE,QACd,CAAC,CACH,CAAE,CACH,CAAC,cAEFhQ,IAAA,QAAK+N,SAAS,CAAC,yBAAyB,CAAAD,QAAA,CACrChL,YAAY,CAAGA,YAAY,CAAG,EAAE,CAC9B,CAAC,EACH,CAAC,EACH,CAAC,cACN5C,KAAA,QAAK6N,SAAS,CAAC,qBAAqB,CAAAD,QAAA,eAClC5N,KAAA,QAAK6N,SAAS,CAAC,8BAA8B,CAAAD,QAAA,EAAC,OACvC,cAAA9N,IAAA,WAAQ+N,SAAS,CAAC,aAAa,CAAAD,QAAA,CAAC,GAAC,CAAQ,CAAC,EAC5C,CAAC,cACN5N,KAAA,QAAA4N,QAAA,eACE9N,IAAA,CAACF,eAAe,EACdoQ,MAAM,CAAC,yCAAyC,CAChDnC,SAAS,CAAG,wBACVrL,SAAS,CAAG,eAAe,CAAG,kBAC/B,mCAAmC,CACpCwM,QAAQ,CAAGC,CAAC,EAAK,CACf1M,OAAO,CAAC0M,CAAC,CAACN,MAAM,CAACI,KAAK,CAAC,CACzB,CAAE,CACFkB,eAAe,CAAGC,KAAK,EAAK,CAC1B,GAAIA,KAAK,EAAIA,KAAK,CAACC,QAAQ,CAAE,KAAAC,qBAAA,CAC3B7N,OAAO,EAAA6N,qBAAA,CAACF,KAAK,CAACG,iBAAiB,UAAAD,qBAAA,UAAAA,qBAAA,CAAI,EAAE,CAAC,CACtC;AACA;AACA;AACA;AACA;AACF,CACF,CAAE,CACFE,YAAY,CAAEhO,IAAK,CACnBiO,KAAK,CAAE,CAAC,MAAM,CAAE,CAChBC,QAAQ,CAAC,IAAI,CACd,CAAC,cAUF1Q,IAAA,QAAK+N,SAAS,CAAC,yBAAyB,CAAAD,QAAA,CACrCpL,SAAS,CAAGA,SAAS,CAAG,EAAE,CACxB,CAAC,EACH,CAAC,EACH,CAAC,EACH,CAAC,cAENxC,KAAA,QAAK6N,SAAS,CAAC,4BAA4B,CAAAD,QAAA,eACzC5N,KAAA,QAAK6N,SAAS,CAAC,+BAA+B,CAAAD,QAAA,eAC5C9N,IAAA,QAAK+N,SAAS,CAAC,8BAA8B,CAAAD,QAAA,CAAC,KAAG,CAAK,CAAC,cACvD5N,KAAA,QAAA4N,QAAA,eACE9N,IAAA,CAACR,MAAM,EACLyP,KAAK,CAAEhG,gBAAiB,CACxBiG,QAAQ,CAAGE,MAAM,EAAK,CACpBlG,mBAAmB,CAACkG,MAAM,CAAC,CAC7B,CAAE,CACFC,OAAO,CAAEtC,UAAU,SAAVA,UAAU,iBAAVA,UAAU,CAAEpC,GAAG,CAAEgG,SAAS,GAAM,CACvC1B,KAAK,CAAE0B,SAAS,CAACC,EAAE,CACnBtB,KAAK,CAAEqB,SAAS,CAACE,cAAc,EAAI,EACrC,CAAC,CAAC,CAAE,CACJC,YAAY,CAAEA,CAAC1B,MAAM,CAAE2B,UAAU,GAC/B3B,MAAM,CAACE,KAAK,CACT0B,WAAW,CAAC,CAAC,CACbC,QAAQ,CAACF,UAAU,CAACC,WAAW,CAAC,CAAC,CACrC,CACDjD,SAAS,CAAC,SAAS,CACnBiB,WAAW,CAAC,qBAAqB,CACjCQ,YAAY,MACZC,MAAM,CAAE,CACNC,OAAO,CAAEA,CAACC,IAAI,CAAE1D,KAAK,IAAM,CACzB,GAAG0D,IAAI,CACPC,UAAU,CAAE,MAAM,CAClBC,MAAM,CAAE1G,qBAAqB,CACzB,mBAAmB,CACnB,mBAAmB,CACvB2G,SAAS,CAAE7D,KAAK,CAAC8D,SAAS,CAAG,MAAM,CAAG,MAAM,CAC5C,SAAS,CAAE,CACTF,MAAM,CAAE,mBACV,CACF,CAAC,CAAC,CACFT,MAAM,CAAGO,IAAI,GAAM,CACjB,GAAGA,IAAI,CACPnP,OAAO,CAAE,MAAM,CACfwP,UAAU,CAAE,QACd,CAAC,CAAC,CACFC,WAAW,CAAGN,IAAI,GAAM,CACtB,GAAGA,IAAI,CACPnP,OAAO,CAAE,MAAM,CACfwP,UAAU,CAAE,QACd,CAAC,CACH,CAAE,CACH,CAAC,cACFhQ,IAAA,QAAK+N,SAAS,CAAC,yBAAyB,CAAAD,QAAA,CACrC3E,qBAAqB,CAAGA,qBAAqB,CAAG,EAAE,CAChD,CAAC,EACH,CAAC,EACH,CAAC,cACNjJ,KAAA,QAAK6N,SAAS,CAAC,+BAA+B,CAAAD,QAAA,eAC5C9N,IAAA,QAAK+N,SAAS,CAAC,8BAA8B,CAAAD,QAAA,CAAC,eAE9C,CAAK,CAAC,cACN5N,KAAA,QAAA4N,QAAA,eACE9N,IAAA,UACE+N,SAAS,CAAG,wBACVxE,oBAAoB,CAChB,eAAe,CACf,kBACL,oCAAoC,CACrCwF,IAAI,CAAC,MAAM,CACXC,WAAW,CAAC,eAAe,CAC3BC,KAAK,CAAE5F,eAAgB,CACvB6F,QAAQ,CAAGC,CAAC,EAAK7F,kBAAkB,CAAC6F,CAAC,CAACN,MAAM,CAACI,KAAK,CAAE,CACrD,CAAC,cACFjP,IAAA,QAAK+N,SAAS,CAAC,yBAAyB,CAAAD,QAAA,CACrCvE,oBAAoB,CAAGA,oBAAoB,CAAG,EAAE,CAC9C,CAAC,EACH,CAAC,EACH,CAAC,EACH,CAAC,EAEH,CAAC,cAENvJ,IAAA,QAAK+N,SAAS,CAAC,0CAA0C,CAAAD,QAAA,CAAC,eAE1D,CAAK,CAAC,cACN5N,KAAA,QAAK6N,SAAS,CAAC,oCAAoC,CAAAD,QAAA,eACjD9N,IAAA,QAAK+N,SAAS,CAAC,6BAA6B,CAAAD,QAAA,cAC1C5N,KAAA,QAAK6N,SAAS,CAAC,uBAAuB,CAAAD,QAAA,eACpC5N,KAAA,QAAK6N,SAAS,CAAC,8BAA8B,CAAAD,QAAA,EAAC,sBACxB,CAAC,GAAG,cACxB9N,IAAA,WAAQ+N,SAAS,CAAC,aAAa,CAAAD,QAAA,CAAC,GAAC,CAAQ,CAAC,EACvC,CAAC,cACN5N,KAAA,QAAA4N,QAAA,eACE9N,IAAA,CAACR,MAAM,EACLyP,KAAK,CAAEvL,WAAY,CACnBwL,QAAQ,CAAGE,MAAM,EAAK,CACpBzL,cAAc,CAACyL,MAAM,CAAC,CACxB,CAAE,CACFrB,SAAS,CAAC,SAAS,CACnBsB,OAAO,CAAEjC,YAAY,SAAZA,YAAY,iBAAZA,YAAY,CAAEzC,GAAG,CAAEuG,IAAI,GAAM,CACpCjC,KAAK,CAAEiC,IAAI,CAACN,EAAE,CACdtB,KAAK,CAAE4B,IAAI,CAACC,SAAS,EAAI,EAC3B,CAAC,CAAC,CAAE,CACJL,YAAY,CAAEA,CAAC1B,MAAM,CAAE2B,UAAU,GAC/B3B,MAAM,CAACE,KAAK,CACT0B,WAAW,CAAC,CAAC,CACbC,QAAQ,CAACF,UAAU,CAACC,WAAW,CAAC,CAAC,CACrC,CACDhC,WAAW,CAAC,uBAAuB,CACnCQ,YAAY,MACZC,MAAM,CAAE,CACNC,OAAO,CAAEA,CAACC,IAAI,CAAE1D,KAAK,IAAM,CACzB,GAAG0D,IAAI,CACPC,UAAU,CAAE,MAAM,CAClBC,MAAM,CAAEjM,gBAAgB,CACpB,mBAAmB,CACnB,mBAAmB,CACvBkM,SAAS,CAAE7D,KAAK,CAAC8D,SAAS,CAAG,MAAM,CAAG,MAAM,CAC5C,SAAS,CAAE,CACTF,MAAM,CAAE,mBACV,CACF,CAAC,CAAC,CACFT,MAAM,CAAGO,IAAI,GAAM,CACjB,GAAGA,IAAI,CACPnP,OAAO,CAAE,MAAM,CACfwP,UAAU,CAAE,QACd,CAAC,CAAC,CACFC,WAAW,CAAGN,IAAI,GAAM,CACtB,GAAGA,IAAI,CACPnP,OAAO,CAAE,MAAM,CACfwP,UAAU,CAAE,QACd,CAAC,CACH,CAAE,CACH,CAAC,cACFhQ,IAAA,QAAK+N,SAAS,CAAC,yBAAyB,CAAAD,QAAA,CACrClK,gBAAgB,CAAGA,gBAAgB,CAAG,EAAE,CACtC,CAAC,EACH,CAAC,EACH,CAAC,CACH,CAAC,cAEN1D,KAAA,QAAK6N,SAAS,CAAC,6BAA6B,CAAAD,QAAA,eAC1C5N,KAAA,QAAK6N,SAAS,CAAC,+BAA+B,CAAAD,QAAA,eAC5C5N,KAAA,QAAK6N,SAAS,CAAC,6BAA6B,CAAAD,QAAA,EAAC,oBACzB,CAAC,GAAG,cACtB9N,IAAA,WAAQ+N,SAAS,CAAC,aAAa,CAAAD,QAAA,CAAC,GAAC,CAAQ,CAAC,EACvC,CAAC,cACN5N,KAAA,QAAA4N,QAAA,eACE9N,IAAA,UACE+N,SAAS,CAAG,wBACVhJ,aAAa,CACT,eAAe,CACf,kBACL,mCAAmC,CACpCgK,IAAI,CAAC,MAAM,CACXC,WAAW,CAAC,oBAAoB,CAChCC,KAAK,CAAEvK,QAAS,CAChBwK,QAAQ,CAAGC,CAAC,EAAKxK,WAAW,CAACwK,CAAC,CAACN,MAAM,CAACI,KAAK,CAAE,CAC9C,CAAC,cACFjP,IAAA,QAAK+N,SAAS,CAAC,yBAAyB,CAAAD,QAAA,CACrC/I,aAAa,CAAGA,aAAa,CAAG,EAAE,CAChC,CAAC,EACH,CAAC,EACH,CAAC,cAEN7E,KAAA,QAAK6N,SAAS,CAAC,+BAA+B,CAAAD,QAAA,eAC5C5N,KAAA,QAAK6N,SAAS,CAAC,8BAA8B,CAAAD,QAAA,EAAC,OACvC,cAAA9N,IAAA,WAAQ+N,SAAS,CAAC,aAAa,CAAAD,QAAA,CAAC,GAAC,CAAQ,CAAC,EAC5C,CAAC,cACN5N,KAAA,QAAA4N,QAAA,eACE5N,KAAA,WACE+O,KAAK,CAAEhK,QAAS,CAChBiK,QAAQ,CAAGC,CAAC,EAAKjK,WAAW,CAACiK,CAAC,CAACN,MAAM,CAACI,KAAK,CAAE,CAC7ClB,SAAS,CAAG,wBACV5I,aAAa,CACT,eAAe,CACf,kBACL,mCAAmC,CAAA2I,QAAA,eAEpC9N,IAAA,WAAQiP,KAAK,CAAE,EAAG,CAAAnB,QAAA,CAAC,aAAW,CAAQ,CAAC,cACvC9N,IAAA,WAAQiP,KAAK,CAAE,SAAU,CAAAnB,QAAA,CAAC,SAAO,CAAQ,CAAC,cAC1C9N,IAAA,WAAQiP,KAAK,CAAE,WAAY,CAAAnB,QAAA,CAAC,WAAS,CAAQ,CAAC,EACxC,CAAC,cACT9N,IAAA,QAAK+N,SAAS,CAAC,yBAAyB,CAAAD,QAAA,CACrC3I,aAAa,CAAGA,aAAa,CAAG,EAAE,CAChC,CAAC,EACH,CAAC,EACH,CAAC,EACH,CAAC,CAELF,QAAQ,GAAK,SAAS,eACrB/E,KAAA,QAAK6N,SAAS,CAAC,gCAAgC,CAAAD,QAAA,eAC7C5N,KAAA,QAAK6N,SAAS,CAAC,8BAA8B,CAAAD,QAAA,EAAC,YAClC,cAAA9N,IAAA,WAAQ+N,SAAS,CAAC,aAAa,CAAAD,QAAA,CAAC,GAAC,CAAQ,CAAC,EACjD,CAAC,cACN5N,KAAA,QAAA4N,QAAA,eACE5N,KAAA,WACE+O,KAAK,CAAE5J,YAAa,CACpB6J,QAAQ,CAAGC,CAAC,EAAK7J,eAAe,CAAC6J,CAAC,CAACN,MAAM,CAACI,KAAK,CAAE,CACjDlB,SAAS,CAAG,wBACVxI,iBAAiB,CACb,eAAe,CACf,kBACL,mCAAmC,CAAAuI,QAAA,eAEpC9N,IAAA,WAAQiP,KAAK,CAAE,EAAG,CAAAnB,QAAA,CAAC,kBAAgB,CAAQ,CAAC,cAC5C9N,IAAA,WAAQiP,KAAK,CAAE,YAAa,CAAAnB,QAAA,CAAC,YAAU,CAAQ,CAAC,cAChD9N,IAAA,WAAQiP,KAAK,CAAE,WAAY,CAAAnB,QAAA,CAAC,WAAS,CAAQ,CAAC,EACxC,CAAC,cACT9N,IAAA,QAAK+N,SAAS,CAAC,yBAAyB,CAAAD,QAAA,CACrCvI,iBAAiB,CAAGA,iBAAiB,CAAG,EAAE,CACxC,CAAC,EACH,CAAC,EACH,CACN,cAEDrF,KAAA,QAAK6N,SAAS,CAAC,4BAA4B,CAAAD,QAAA,eACzC5N,KAAA,QAAK6N,SAAS,CAAC,+BAA+B,CAAAD,QAAA,eAC5C5N,KAAA,QAAK6N,SAAS,CAAC,8BAA8B,CAAAD,QAAA,EAAC,eAC/B,CAAC,GAAG,cACjB9N,IAAA,WAAQ+N,SAAS,CAAC,aAAa,CAAAD,QAAA,CAAC,GAAC,CAAQ,CAAC,EACvC,CAAC,cACN5N,KAAA,QAAA4N,QAAA,eACE9N,IAAA,CAACR,MAAM,EACLyP,KAAK,CAAE/L,YAAa,CACpBgM,QAAQ,CAAGE,MAAM,EAAK,CACpBjM,eAAe,CAACiM,MAAM,CAAC,CACzB,CAAE,CACFC,OAAO,CAAExP,aAAa,SAAbA,aAAa,iBAAbA,aAAa,CAAE8K,GAAG,CAAEyG,QAAQ,GAAM,CACzCnC,KAAK,CAAEmC,QAAQ,CAACC,IAAI,CACpB/B,KAAK,CACH8B,QAAQ,CAACE,IAAI,GAAK,EAAE,CAChBF,QAAQ,CAACE,IAAI,CACX,IAAI,CACJF,QAAQ,CAACC,IAAI,CACb,IAAI,EAAI,EAAE,CACZ,EACR,CAAC,CAAC,CAAE,CACJP,YAAY,CAAEA,CAAC1B,MAAM,CAAE2B,UAAU,GAC/B3B,MAAM,CAACE,KAAK,CACT0B,WAAW,CAAC,CAAC,CACbC,QAAQ,CAACF,UAAU,CAACC,WAAW,CAAC,CAAC,CACrC,CACDjD,SAAS,CAAC,SAAS,CACnBiB,WAAW,CAAC,0BAA0B,CACtCQ,YAAY,MACZC,MAAM,CAAE,CACNC,OAAO,CAAEA,CAACC,IAAI,CAAE1D,KAAK,IAAM,CACzB,GAAG0D,IAAI,CACPC,UAAU,CAAE,MAAM,CAClBC,MAAM,CAAEzM,iBAAiB,CACrB,mBAAmB,CACnB,mBAAmB,CACvB0M,SAAS,CAAE7D,KAAK,CAAC8D,SAAS,CAAG,MAAM,CAAG,MAAM,CAC5C,SAAS,CAAE,CACTF,MAAM,CAAE,mBACV,CACF,CAAC,CAAC,CACFT,MAAM,CAAGO,IAAI,GAAM,CACjB,GAAGA,IAAI,CACPnP,OAAO,CAAE,MAAM,CACfwP,UAAU,CAAE,QACd,CAAC,CAAC,CACFC,WAAW,CAAGN,IAAI,GAAM,CACtB,GAAGA,IAAI,CACPnP,OAAO,CAAE,MAAM,CACfwP,UAAU,CAAE,QACd,CAAC,CACH,CAAE,CACH,CAAC,cACFhQ,IAAA,QAAK+N,SAAS,CAAC,yBAAyB,CAAAD,QAAA,CACrC1K,iBAAiB,CAAGA,iBAAiB,CAAG,EAAE,CACxC,CAAC,EACH,CAAC,EACH,CAAC,cACNlD,KAAA,QAAK6N,SAAS,CAAC,+BAA+B,CAAAD,QAAA,eAC5C5N,KAAA,QAAK6N,SAAS,CAAC,8BAA8B,CAAAD,QAAA,EAAC,kBAC5B,CAAC,GAAG,cACpB9N,IAAA,WAAQ+N,SAAS,CAAC,aAAa,CAAAD,QAAA,CAAC,GAAC,CAAQ,CAAC,EACvC,CAAC,cACN5N,KAAA,QAAA4N,QAAA,eACE9N,IAAA,UACE+N,SAAS,CAAG,wBACVvK,eAAe,CACX,eAAe,CACf,kBACL,oCAAoC,CACrCuL,IAAI,CAAC,QAAQ,CACbwC,GAAG,CAAE,CAAE,CACP/C,IAAI,CAAE,IAAK,CACXQ,WAAW,CAAC,MAAM,CAClBC,KAAK,CAAE3L,UAAW,CAClB4L,QAAQ,CAAGC,CAAC,EAAK5L,aAAa,CAAC4L,CAAC,CAACN,MAAM,CAACI,KAAK,CAAE,CAChD,CAAC,cACFjP,IAAA,QAAK+N,SAAS,CAAC,yBAAyB,CAAAD,QAAA,CACrCtK,eAAe,CAAGA,eAAe,CAAG,EAAE,CACpC,CAAC,EACH,CAAC,EACH,CAAC,EACH,CAAC,cACNtD,KAAA,QAAK6N,SAAS,CAAC,4BAA4B,CAAAD,QAAA,eACzC9N,IAAA,QAAK+N,SAAS,CAAC,+BAA+B,CAAAD,QAAA,cAC5C5N,KAAA,QAAA4N,QAAA,eACE9N,IAAA,UACE+O,IAAI,CAAE,UAAW,CACjBuC,IAAI,CAAC,OAAO,CACZV,EAAE,CAAC,OAAO,CACVY,OAAO,CAAExO,KAAK,GAAK,IAAK,CACxBkM,QAAQ,CAAGC,CAAC,EAAK,CACflM,QAAQ,CAAC,IAAI,CAAC,CAChB,CAAE,CACH,CAAC,cACFjD,IAAA,UACE+N,SAAS,CAAC,6CAA6C,CACvD0D,GAAG,CAAC,OAAO,CAAA3D,QAAA,CACZ,MAED,CAAO,CAAC,EACL,CAAC,CACH,CAAC,cACN9N,IAAA,QAAK+N,SAAS,CAAC,+BAA+B,CAAAD,QAAA,cAC5C5N,KAAA,QAAA4N,QAAA,eACE9N,IAAA,UACE+O,IAAI,CAAE,UAAW,CACjBuC,IAAI,CAAC,QAAQ,CACbV,EAAE,CAAC,QAAQ,CACXY,OAAO,CAAExO,KAAK,GAAK,KAAM,CACzBkM,QAAQ,CAAGC,CAAC,EAAK,CACflM,QAAQ,CAAC,KAAK,CAAC,CACjB,CAAE,CACH,CAAC,cACFjD,IAAA,UACE+N,SAAS,CAAC,6CAA6C,CACvD0D,GAAG,CAAC,QAAQ,CAAA3D,QAAA,CACb,QAED,CAAO,CAAC,EACL,CAAC,CACH,CAAC,EACH,CAAC,cAGN9N,IAAA,QAAK+N,SAAS,CAAC,6BAA6B,CAAAD,QAAA,cAC1C5N,KAAA,QAAK6N,SAAS,CAAC,uBAAuB,CAAAD,QAAA,eACpC9N,IAAA,QAAK+N,SAAS,CAAC,8BAA8B,CAAAD,QAAA,CAAC,aAE9C,CAAK,CAAC,cACN9N,IAAA,QAAA8N,QAAA,cACE9N,IAAA,aACEiP,KAAK,CAAExJ,eAAgB,CACvBiM,IAAI,CAAE,CAAE,CACRxC,QAAQ,CAAGC,CAAC,EAAKzJ,kBAAkB,CAACyJ,CAAC,CAACN,MAAM,CAACI,KAAK,CAAE,CACpDlB,SAAS,CAAC,wEAAwE,CACzE,CAAC,CACT,CAAC,EACH,CAAC,CACH,CAAC,EACH,CAAC,cAGN/N,IAAA,QAAK+N,SAAS,CAAC,6CAA6C,CAAAD,QAAA,cAC1D9N,IAAA,WACEyO,OAAO,CAAEA,CAAA,GAAM,CACb,GAAI,CAAAkD,KAAK,CAAG,IAAI,CAChBxQ,iBAAiB,CAAC,EAAE,CAAC,CACrBI,gBAAgB,CAAC,EAAE,CAAC,CACpBQ,iBAAiB,CAAC,EAAE,CAAC,CACrBI,aAAa,CAAC,EAAE,CAAC,CACjBR,aAAa,CAAC,EAAE,CAAC,CACjBY,eAAe,CAAC,EAAE,CAAC,CACnB6C,gBAAgB,CAAC,EAAE,CAAC,CACpBI,oBAAoB,CAAC,EAAE,CAAC,CACxBR,gBAAgB,CAAC,EAAE,CAAC,CACpBnB,mBAAmB,CAAC,EAAE,CAAC,CACvBlB,YAAY,CAAC,EAAE,CAAC,CAChBI,eAAe,CAAC,EAAE,CAAC,CACnBM,oBAAoB,CAAC,EAAE,CAAC,CACxBI,kBAAkB,CAAC,EAAE,CAAC,CAEtB,GAAIzC,SAAS,GAAK,EAAE,CAAE,CACpBG,iBAAiB,CAAC,yBAAyB,CAAC,CAC5CwQ,KAAK,CAAG,KAAK,CACf,CAEA,GAAI3P,KAAK,GAAK,EAAE,CAAE,CAChBG,aAAa,CAAC,yBAAyB,CAAC,CACxCwP,KAAK,CAAG,KAAK,CACf,CAEA,GAAI/O,OAAO,GAAK,EAAE,EAAIA,OAAO,CAACqM,KAAK,GAAK,EAAE,CAAE,CAC1ClM,eAAe,CAAC,yBAAyB,CAAC,CAC1C4O,KAAK,CAAG,KAAK,CACf,CAEA,GAAInP,IAAI,GAAK,EAAE,CAAE,CACfG,YAAY,CAAC,yBAAyB,CAAC,CACvCgP,KAAK,CAAG,KAAK,CACf,CAEA,GAAIzO,YAAY,GAAK,EAAE,EAAIA,YAAY,CAAC+L,KAAK,GAAK,EAAE,CAAE,CACpD5L,oBAAoB,CAAC,yBAAyB,CAAC,CAC/CsO,KAAK,CAAG,KAAK,CACf,CACA,GAAIrO,UAAU,GAAK,EAAE,CAAE,CACrBG,kBAAkB,CAAC,yBAAyB,CAAC,CAC7CkO,KAAK,CAAG,KAAK,CACf,CAEA,GAAIjO,WAAW,GAAK,EAAE,EAAIA,WAAW,CAACuL,KAAK,GAAK,EAAE,CAAE,CAClDpL,mBAAmB,CAAC,yBAAyB,CAAC,CAC9C8N,KAAK,CAAG,KAAK,CACf,CAEA,GAAIjN,QAAQ,GAAK,EAAE,CAAE,CACnBM,gBAAgB,CAAC,yBAAyB,CAAC,CAC3C2M,KAAK,CAAG,KAAK,CACf,CAEA,GAAI1M,QAAQ,GAAK,EAAE,CAAE,CACnBG,gBAAgB,CAAC,yBAAyB,CAAC,CAC3CuM,KAAK,CAAG,KAAK,CACf,CAAC,IAAM,IACL1M,QAAQ,GAAK,SAAS,EACtBI,YAAY,GAAK,EAAE,CACnB,CACAG,oBAAoB,CAAC,yBAAyB,CAAC,CAC/CmM,KAAK,CAAG,KAAK,CACf,CAEA,GAAIA,KAAK,CAAE,CACT9F,aAAa,CAAC,CAAC,CAAC,CAClB,CAAC,IAAM,CACLxM,KAAK,CAACuS,KAAK,CACT,oDACF,CAAC,CACH,CACF,CAAE,CACF7D,SAAS,CAAC,wDAAwD,CAAAD,QAAA,CACnE,iBAED,CAAQ,CAAC,CACN,CAAC,EACH,CAAC,CACJ,IAAI,CAEPlC,UAAU,GAAK,CAAC,cACf1L,KAAA,QAAK6N,SAAS,CAAC,EAAE,CAAAD,QAAA,eACf9N,IAAA,QAAK+N,SAAS,CAAC,sCAAsC,CAAAD,QAAA,CAAC,sBAEtD,CAAK,CAAC,cAEN9N,IAAA,QAAK+N,SAAS,CAAC,0CAA0C,CAAAD,QAAA,CAAC,8BAE1D,CAAK,CAAC,cACN9N,IAAA,QAAK+N,SAAS,CAAC,oCAAoC,CAAAD,QAAA,cACjD9N,IAAA,QAAK+N,SAAS,CAAC,6BAA6B,CAAAD,QAAA,cAC1C5N,KAAA,QAAK6N,SAAS,CAAC,sBAAsB,CAAAD,QAAA,eACnC5N,KAAA,QAAK6N,SAAS,CAAC,8BAA8B,CAAAD,QAAA,EAAC,SACrC,cAAA9N,IAAA,WAAQ+N,SAAS,CAAC,aAAa,CAAAD,QAAA,CAAC,GAAC,CAAQ,CAAC,EAC9C,CAAC,cACN5N,KAAA,QAAA4N,QAAA,eACE5N,KAAA,QAAK6N,SAAS,CAAC,gBAAgB,CAAAD,QAAA,eAC7B5N,KAAA,QAAK6N,SAAS,CAAC,qDAAqD,CAAAD,QAAA,eAClE9N,IAAA,UACEkP,QAAQ,CAAGC,CAAC,EAAK,CACf,GACE,CAAClJ,mBAAmB,CAACgL,QAAQ,CAC3B,sBACF,CAAC,CACD,CACA/K,sBAAsB,CAAC,CACrB,GAAGD,mBAAmB,CACtB,sBAAsB,CACvB,CAAC,CACJ,CAAC,IAAM,CACLC,sBAAsB,CACpBD,mBAAmB,CAAC4L,MAAM,CACvBC,MAAM,EACLA,MAAM,GAAK,sBACf,CACF,CAAC,CACH,CACF,CAAE,CACFlB,EAAE,CAAC,sBAAsB,CACzB7B,IAAI,CAAE,UAAW,CACjByC,OAAO,CAAEvL,mBAAmB,CAACgL,QAAQ,CACnC,sBACF,CAAE,CACFlD,SAAS,CAAC,MAAM,CACjB,CAAC,cACF/N,IAAA,UACEyR,GAAG,CAAC,sBAAsB,CAC1B1D,SAAS,CAAC,8BAA8B,CAAAD,QAAA,CACzC,sBAED,CAAO,CAAC,EACL,CAAC,cACN5N,KAAA,QAAK6N,SAAS,CAAC,wDAAwD,CAAAD,QAAA,eACrE9N,IAAA,UACEkP,QAAQ,CAAGC,CAAC,EAAK,CACf,GACE,CAAClJ,mBAAmB,CAACgL,QAAQ,CAC3B,yBACF,CAAC,CACD,CACA/K,sBAAsB,CAAC,CACrB,GAAGD,mBAAmB,CACtB,yBAAyB,CAC1B,CAAC,CACJ,CAAC,IAAM,CACLC,sBAAsB,CACpBD,mBAAmB,CAAC4L,MAAM,CACvBC,MAAM,EACLA,MAAM,GAAK,yBACf,CACF,CAAC,CACH,CACF,CAAE,CACFN,OAAO,CAAEvL,mBAAmB,CAACgL,QAAQ,CACnC,yBACF,CAAE,CACFL,EAAE,CAAC,yBAAyB,CAC5B7B,IAAI,CAAE,UAAW,CACjBhB,SAAS,CAAC,MAAM,CACjB,CAAC,cACF/N,IAAA,UACEyR,GAAG,CAAC,yBAAyB,CAC7B1D,SAAS,CAAC,8BAA8B,CAAAD,QAAA,CACzC,2BAED,CAAO,CAAC,EACL,CAAC,cACN5N,KAAA,QAAK6N,SAAS,CAAC,wDAAwD,CAAAD,QAAA,eACrE9N,IAAA,UACEkP,QAAQ,CAAGC,CAAC,EAAK,CACf,GACE,CAAClJ,mBAAmB,CAACgL,QAAQ,CAC3B,6BACF,CAAC,CACD,CACA/K,sBAAsB,CAAC,CACrB,GAAGD,mBAAmB,CACtB,6BAA6B,CAC9B,CAAC,CACJ,CAAC,IAAM,CACLC,sBAAsB,CACpBD,mBAAmB,CAAC4L,MAAM,CACvBC,MAAM,EACLA,MAAM,GACN,6BACJ,CACF,CAAC,CACH,CACF,CAAE,CACFN,OAAO,CAAEvL,mBAAmB,CAACgL,QAAQ,CACnC,6BACF,CAAE,CACFL,EAAE,CAAC,6BAA6B,CAChC7B,IAAI,CAAE,UAAW,CACjBhB,SAAS,CAAC,MAAM,CACjB,CAAC,cACF/N,IAAA,UACEyR,GAAG,CAAC,6BAA6B,CACjC1D,SAAS,CAAC,8BAA8B,CAAAD,QAAA,CACzC,8BAED,CAAO,CAAC,EACL,CAAC,cACN5N,KAAA,QAAK6N,SAAS,CAAC,sDAAsD,CAAAD,QAAA,eACnE9N,IAAA,UACEkP,QAAQ,CAAGC,CAAC,EAAK,CACf,GACE,CAAClJ,mBAAmB,CAACgL,QAAQ,CAC3B,qCACF,CAAC,CACD,CACA/K,sBAAsB,CAAC,CACrB,GAAGD,mBAAmB,CACtB,qCAAqC,CACtC,CAAC,CACJ,CAAC,IAAM,CACLC,sBAAsB,CACpBD,mBAAmB,CAAC4L,MAAM,CACvBC,MAAM,EACLA,MAAM,GACN,qCACJ,CACF,CAAC,CACH,CACF,CAAE,CACFN,OAAO,CAAEvL,mBAAmB,CAACgL,QAAQ,CACnC,qCACF,CAAE,CACFL,EAAE,CAAC,qCAAqC,CACxC7B,IAAI,CAAE,UAAW,CACjBhB,SAAS,CAAC,MAAM,CACjB,CAAC,cACF/N,IAAA,UACEyR,GAAG,CAAC,qCAAqC,CACzC1D,SAAS,CAAC,8BAA8B,CAAAD,QAAA,CACzC,qCAED,CAAO,CAAC,EACL,CAAC,cACN5N,KAAA,QAAK6N,SAAS,CAAC,sDAAsD,CAAAD,QAAA,eACnE9N,IAAA,UACEkP,QAAQ,CAAGC,CAAC,EAAK,CACf,GACE,CAAClJ,mBAAmB,CAACgL,QAAQ,CAC3B,kCACF,CAAC,CACD,CACA/K,sBAAsB,CAAC,CACrB,GAAGD,mBAAmB,CACtB,kCAAkC,CACnC,CAAC,CACJ,CAAC,IAAM,CACLC,sBAAsB,CACpBD,mBAAmB,CAAC4L,MAAM,CACvBC,MAAM,EACLA,MAAM,GACN,kCACJ,CACF,CAAC,CACH,CACF,CAAE,CACFN,OAAO,CAAEvL,mBAAmB,CAACgL,QAAQ,CACnC,kCACF,CAAE,CACFL,EAAE,CAAC,kCAAkC,CACrC7B,IAAI,CAAE,UAAW,CACjBhB,SAAS,CAAC,MAAM,CACjB,CAAC,cACF/N,IAAA,UACEyR,GAAG,CAAC,kCAAkC,CACtC1D,SAAS,CAAC,8BAA8B,CAAAD,QAAA,CACzC,mCAED,CAAO,CAAC,EACL,CAAC,cAEN5N,KAAA,QAAK6N,SAAS,CAAC,sDAAsD,CAAAD,QAAA,eACnE9N,IAAA,UACEkP,QAAQ,CAAGC,CAAC,EAAK,CACf,GACE,CAAClJ,mBAAmB,CAACgL,QAAQ,CAC3B,kBACF,CAAC,CACD,CACA/K,sBAAsB,CAAC,CACrB,GAAGD,mBAAmB,CACtB,kBAAkB,CACnB,CAAC,CACJ,CAAC,IAAM,CACLC,sBAAsB,CACpBD,mBAAmB,CAAC4L,MAAM,CACvBC,MAAM,EACLA,MAAM,GAAK,kBACf,CACF,CAAC,CACH,CACF,CAAE,CACFN,OAAO,CAAEvL,mBAAmB,CAACgL,QAAQ,CACnC,kBACF,CAAE,CACFL,EAAE,CAAC,kBAAkB,CACrB7B,IAAI,CAAE,UAAW,CACjBhB,SAAS,CAAC,MAAM,CACjB,CAAC,cACF/N,IAAA,UACEyR,GAAG,CAAC,kBAAkB,CACtB1D,SAAS,CAAC,8BAA8B,CAAAD,QAAA,CACzC,kBAED,CAAO,CAAC,EACL,CAAC,cAEN5N,KAAA,QAAK6N,SAAS,CAAC,sDAAsD,CAAAD,QAAA,eACnE9N,IAAA,UACEkP,QAAQ,CAAGC,CAAC,EAAK,CACf,GACE,CAAClJ,mBAAmB,CAACgL,QAAQ,CAC3B,6BACF,CAAC,CACD,CACA/K,sBAAsB,CAAC,CACrB,GAAGD,mBAAmB,CACtB,6BAA6B,CAC9B,CAAC,CACJ,CAAC,IAAM,CACLC,sBAAsB,CACpBD,mBAAmB,CAAC4L,MAAM,CACvBC,MAAM,EACLA,MAAM,GACN,6BACJ,CACF,CAAC,CACH,CACF,CAAE,CACFN,OAAO,CAAEvL,mBAAmB,CAACgL,QAAQ,CACnC,6BACF,CAAE,CACFL,EAAE,CAAC,6BAA6B,CAChC7B,IAAI,CAAE,UAAW,CACjBhB,SAAS,CAAC,MAAM,CACjB,CAAC,cACF/N,IAAA,UACEyR,GAAG,CAAC,6BAA6B,CACjC1D,SAAS,CAAC,8BAA8B,CAAAD,QAAA,CACzC,8BAED,CAAO,CAAC,EACL,CAAC,cAGN5N,KAAA,QAAK6N,SAAS,CAAC,wDAAwD,CAAAD,QAAA,eACrE9N,IAAA,UACEkP,QAAQ,CAAGC,CAAC,EAAK,CACf,GACE,CAAClJ,mBAAmB,CAACgL,QAAQ,CAC3B,mBACF,CAAC,CACD,CACA/K,sBAAsB,CAAC,CACrB,GAAGD,mBAAmB,CACtB,mBAAmB,CACpB,CAAC,CACJ,CAAC,IAAM,CACLC,sBAAsB,CACpBD,mBAAmB,CAAC4L,MAAM,CACvBC,MAAM,EACLA,MAAM,GAAK,mBACf,CACF,CAAC,CACH,CACF,CAAE,CACFN,OAAO,CAAEvL,mBAAmB,CAACgL,QAAQ,CACnC,mBACF,CAAE,CACFL,EAAE,CAAC,mBAAmB,CACtB7B,IAAI,CAAE,UAAW,CACjBhB,SAAS,CAAC,MAAM,CACjB,CAAC,cACF/N,IAAA,UACEyR,GAAG,CAAC,mBAAmB,CACvB1D,SAAS,CAAC,8BAA8B,CAAAD,QAAA,CACzC,mBAED,CAAO,CAAC,EACL,CAAC,cACN5N,KAAA,QAAK6N,SAAS,CAAC,wDAAwD,CAAAD,QAAA,eACrE9N,IAAA,UACEkP,QAAQ,CAAGC,CAAC,EAAK,CACf,GAAI,CAAClJ,mBAAmB,CAACgL,QAAQ,CAAC,QAAQ,CAAC,CAAE,CAC3C/K,sBAAsB,CAAC,CACrB,GAAGD,mBAAmB,CACtB,QAAQ,CACT,CAAC,CACJ,CAAC,IAAM,CACLC,sBAAsB,CACpBD,mBAAmB,CAAC4L,MAAM,CACvBC,MAAM,EAAKA,MAAM,GAAK,QACzB,CACF,CAAC,CACH,CACF,CAAE,CACFN,OAAO,CAAEvL,mBAAmB,CAACgL,QAAQ,CAAC,QAAQ,CAAE,CAChDL,EAAE,CAAC,QAAQ,CACX7B,IAAI,CAAE,UAAW,CACjBhB,SAAS,CAAC,MAAM,CACjB,CAAC,cACF/N,IAAA,UACEyR,GAAG,CAAC,QAAQ,CACZ1D,SAAS,CAAC,8BAA8B,CAAAD,QAAA,CACzC,QAED,CAAO,CAAC,EACL,CAAC,EACH,CAAC,cAiCN9N,IAAA,QAAK+N,SAAS,CAAC,yBAAyB,CAAAD,QAAA,CACrC3H,wBAAwB,CACrBA,wBAAwB,CACxB,EAAE,CACH,CAAC,EACH,CAAC,EACH,CAAC,CACH,CAAC,CACH,CAAC,cAENnG,IAAA,QAAK+N,SAAS,CAAC,0CAA0C,CAAAD,QAAA,CAAC,qBAE1D,CAAK,CAAC,cACN5N,KAAA,QAAK6N,SAAS,CAAC,oCAAoC,CAAAD,QAAA,eAEjD9N,IAAA,QAAK+N,SAAS,CAAC,0CAA0C,CAAAD,QAAA,CAAC,sBAE1D,CAAK,CAAC,cACN9N,IAAA,QAAK+N,SAAS,CAAC,mCAAmC,CAAAD,QAAA,CAC/C7I,QAAQ,GAAK,SAAS,EACvBI,YAAY,GAAK,WAAW,cAC1BnF,KAAA,QAAK6N,SAAS,CAAC,kCAAkC,CAAAD,QAAA,eAC/C5N,KAAA,QAAK6N,SAAS,CAAC,+BAA+B,CAAAD,QAAA,eAC5C9N,IAAA,QAAK+N,SAAS,CAAC,8BAA8B,CAAAD,QAAA,CAAC,wBAE9C,CAAK,CAAC,cACN9N,IAAA,QAAA8N,QAAA,cACE9N,IAAA,UACE+N,SAAS,CAAC,wEAAwE,CAClFgB,IAAI,CAAC,MAAM,CACXC,WAAW,CAAC,wBAAwB,CACpCC,KAAK,CAAExI,SAAU,CACjByI,QAAQ,CAAGC,CAAC,EAAK,CACfzI,YAAY,CAACyI,CAAC,CAACN,MAAM,CAACI,KAAK,CAAC,CAC5B;AACA,GAAIpI,OAAO,EAAIA,OAAO,CAAGsI,CAAC,CAACN,MAAM,CAACI,KAAK,CAAE,CACvCnI,UAAU,CAACqI,CAAC,CAACN,MAAM,CAACI,KAAK,CAAC,CAC5B,CACF,CAAE,CACH,CAAC,CACC,CAAC,EACH,CAAC,cACN/O,KAAA,QAAK6N,SAAS,CAAC,+BAA+B,CAAAD,QAAA,eAC5C9N,IAAA,QAAK+N,SAAS,CAAC,8BAA8B,CAAAD,QAAA,CAAC,sBAE9C,CAAK,CAAC,cACN9N,IAAA,QAAA8N,QAAA,cACE9N,IAAA,UACE+N,SAAS,CAAC,wEAAwE,CAClFgB,IAAI,CAAC,MAAM,CACXC,WAAW,CAAC,sBAAsB,CAClCC,KAAK,CAAEpI,OAAQ,CACfqI,QAAQ,CAAGC,CAAC,EAAKrI,UAAU,CAACqI,CAAC,CAACN,MAAM,CAACI,KAAK,CAAE,CAC5C8C,QAAQ,CAAE,CAACtL,SAAU,CACrB8K,GAAG,CAAE9K,SAAU,CAChB,CAAC,CACC,CAAC,EACH,CAAC,EACH,CAAC,cAENvG,KAAA,QAAK6N,SAAS,CAAC,uBAAuB,CAAAD,QAAA,eACpC9N,IAAA,QAAK+N,SAAS,CAAC,8BAA8B,CAAAD,QAAA,CAAC,kBAE9C,CAAK,CAAC,cACN9N,IAAA,QAAA8N,QAAA,cACE9N,IAAA,UACE+N,SAAS,CAAC,wEAAwE,CAClFgB,IAAI,CAAC,MAAM,CACXC,WAAW,CAAC,kBAAkB,CAC9BC,KAAK,CAAE5I,eAAgB,CACvB6I,QAAQ,CAAGC,CAAC,EACV7I,kBAAkB,CAAC6I,CAAC,CAACN,MAAM,CAACI,KAAK,CAClC,CACF,CAAC,CACC,CAAC,EACH,CACN,CACE,CAAC,cAENjP,IAAA,QAAK+N,SAAS,CAAC,6BAA6B,CAAAD,QAAA,cAE1C5N,KAAA,QAAK6N,SAAS,CAAC,+BAA+B,CAAAD,QAAA,eAC5C9N,IAAA,QAAK+N,SAAS,CAAC,8BAA8B,CAAAD,QAAA,CAAC,kBAE9C,CAAK,CAAC,cACN9N,IAAA,QAAA8N,QAAA,cACE9N,IAAA,UACE+N,SAAS,CAAC,wEAAwE,CAClFgB,IAAI,CAAC,MAAM,CACXC,WAAW,CAAC,mBAAmB,CAC/BC,KAAK,CAAEhI,eAAgB,CACvBiI,QAAQ,CAAGC,CAAC,EAAKjI,kBAAkB,CAACiI,CAAC,CAACN,MAAM,CAACI,KAAK,CAAE,CACrD,CAAC,CACC,CAAC,EACH,CAAC,CACH,CAAC,cAGNjP,IAAA,QAAK+N,SAAS,CAAC,0CAA0C,CAAAD,QAAA,CAAC,uBAE1D,CAAK,CAAC,cACN5N,KAAA,QAAK6N,SAAS,CAAC,6BAA6B,CAAAD,QAAA,eAC1C5N,KAAA,QAAK6N,SAAS,CAAC,gCAAgC,CAAAD,QAAA,eAC7C9N,IAAA,QAAK+N,SAAS,CAAC,8BAA8B,CAAAD,QAAA,CAAC,eAE9C,CAAK,CAAC,cACN5N,KAAA,QAAA4N,QAAA,eACE9N,IAAA,CAACR,MAAM,EACLyP,KAAK,CAAE5H,YAAa,CACpB6H,QAAQ,CAAGE,MAAM,EAAK,KAAA4C,aAAA,CACpB1K,eAAe,CAAC8H,MAAM,CAAC,CACvB;AACA,GAAI,CAAA6C,eAAe,EAAAD,aAAA,CAAG5C,MAAM,SAANA,MAAM,iBAANA,MAAM,CAAEH,KAAK,UAAA+C,aAAA,UAAAA,aAAA,CAAI,EAAE,CACzC;AACAjG,YAAY,CAAC,IAAI,CAAC,CAElB,KAAM,CAAAmG,aAAa,CAAG7F,SAAS,SAATA,SAAS,iBAATA,SAAS,CAAE8F,IAAI,CAClCjB,IAAI,EAAKA,IAAI,CAACN,EAAE,GAAKqB,eACxB,CAAC,CACD,GAAIC,aAAa,CAAE,KAAAE,qBAAA,CACjBrO,mBAAmB,EAAAqO,qBAAA,CACjBF,aAAa,CAACG,QAAQ,UAAAD,qBAAA,UAAAA,qBAAA,CAAI,EAC5B,CAAC,CACD;AACA3E,UAAU,CAAC,IAAM,CACf1B,YAAY,CAAC,KAAK,CAAC,CACrB,CAAC,CAAE,GAAG,CAAC,CACT,CAAC,IAAM,CACLhI,mBAAmB,CAAC,EAAE,CAAC,CACvBgI,YAAY,CAAC,KAAK,CAAC,CACrB,CACF,CAAE,CACFgC,SAAS,CAAC,SAAS,CACnBsB,OAAO,CAAEhD,SAAS,SAATA,SAAS,iBAATA,SAAS,CAAE1B,GAAG,CAAEuG,IAAI,GAAM,CACjCjC,KAAK,CAAEiC,IAAI,CAACN,EAAE,CACdtB,KAAK,CAAE4B,IAAI,CAACC,SAAS,EAAI,EAAE,CAC3B3O,IAAI,CAAE0O,IAAI,CAAC1O,IAAI,EAAI,EAAE,CACrBI,OAAO,CAAEsO,IAAI,CAACtO,OAAO,EAAI,EAC3B,CAAC,CAAC,CAAE,CACJkO,YAAY,CAAEA,CAAC1B,MAAM,CAAE2B,UAAU,GAAK,KAAAuB,aAAA,CAAAC,YAAA,CAAAC,eAAA,CACpC;AACA,KAAM,CAAAC,UAAU,CAAG1B,UAAU,SAAVA,UAAU,iBAAVA,UAAU,CAAEC,WAAW,CAAC,CAAC,CAC5C,MACE,EAAAsB,aAAA,CAAAlD,MAAM,CAACE,KAAK,UAAAgD,aAAA,iBAAZA,aAAA,CACItB,WAAW,CAAC,CAAC,CACdC,QAAQ,CAACwB,UAAU,CAAC,KAAAF,YAAA,CACvBnD,MAAM,CAAC5M,IAAI,UAAA+P,YAAA,iBAAXA,YAAA,CACIvB,WAAW,CAAC,CAAC,CACdC,QAAQ,CAACwB,UAAU,CAAC,KAAAD,eAAA,CACvBpD,MAAM,CAACxM,OAAO,UAAA4P,eAAA,iBAAdA,eAAA,CACIxB,WAAW,CAAC,CAAC,CACdC,QAAQ,CAACwB,UAAU,CAAC,EAE3B,CAAE,CACFzD,WAAW,CAAC,oBAAoB,CAChCQ,YAAY,KACZ;AAAA,CACA1D,SAAS,CAAEQ,gBAAiB,CAC5BmD,MAAM,CAAE,CACNC,OAAO,CAAEA,CAACC,IAAI,CAAE1D,KAAK,IAAM,CACzB,GAAG0D,IAAI,CACPC,UAAU,CAAE,MAAM,CAClBC,MAAM,CAAEtI,iBAAiB,CACrB,mBAAmB,CACnB,mBAAmB,CACvBuI,SAAS,CAAE7D,KAAK,CAAC8D,SAAS,CAAG,MAAM,CAAG,MAAM,CAC5C,SAAS,CAAE,CACTF,MAAM,CAAE,mBACV,CACF,CAAC,CAAC,CACFT,MAAM,CAAGO,IAAI,GAAM,CACjB,GAAGA,IAAI,CACPnP,OAAO,CAAE,MAAM,CACfwP,UAAU,CAAE,QACd,CAAC,CAAC,CACFC,WAAW,CAAGN,IAAI,GAAM,CACtB,GAAGA,IAAI,CACPnP,OAAO,CAAE,MAAM,CACfwP,UAAU,CAAE,QACd,CAAC,CACH,CAAE,CACH,CAAC,cACFhQ,IAAA,QAAK+N,SAAS,CAAC,yBAAyB,CAAAD,QAAA,CACrCvG,iBAAiB,CAAGA,iBAAiB,CAAG,EAAE,CACxC,CAAC,EACH,CAAC,EACH,CAAC,cAENrH,KAAA,QAAK6N,SAAS,CAAC,gCAAgC,CAAAD,QAAA,eAC7C9N,IAAA,QAAK+N,SAAS,CAAC,8BAA8B,CAAAD,QAAA,CAAC,kBAE9C,CAAK,CAAC,cACN5N,KAAA,QAAA4N,QAAA,eACE5N,KAAA,WACE6N,SAAS,CAAG,uBACV3J,oBAAoB,CAChB,eAAe,CACf,kBACL,oCAAoC,CACrC8K,QAAQ,CAAGC,CAAC,EAAK,CACfhL,kBAAkB,CAACgL,CAAC,CAACN,MAAM,CAACI,KAAK,CAAC,CACpC,CAAE,CACFA,KAAK,CAAE/K,eAAgB,CAAA4J,QAAA,eAEvB9N,IAAA,WAAQiP,KAAK,CAAE,EAAG,CAAS,CAAC,CAC3BnL,gBAAgB,SAAhBA,gBAAgB,iBAAhBA,gBAAgB,CAAE6G,GAAG,CAAC,CAAC+H,OAAO,CAAEtS,KAAK,QAAAuS,qBAAA,oBACpCzS,KAAA,WAAQ+O,KAAK,CAAEyD,OAAO,CAAC9B,EAAG,CAAA9C,QAAA,GAAA6E,qBAAA,CACvBD,OAAO,CAACE,YAAY,UAAAD,qBAAA,UAAAA,qBAAA,CAAI,EAAE,CAC1BD,OAAO,CAACG,kBAAkB,GAAK,EAAE,CAC9B,KAAK,CAAGH,OAAO,CAACG,kBAAkB,CAClC,EAAE,EACA,CAAC,EACV,CAAC,EACI,CAAC,cACT7S,IAAA,QAAK+N,SAAS,CAAC,yBAAyB,CAAAD,QAAA,CACrC1J,oBAAoB,CAAGA,oBAAoB,CAAG,EAAE,CAC9C,CAAC,EACH,CAAC,EACH,CAAC,EACH,CAAC,cACNpE,IAAA,QAAK+N,SAAS,CAAC,6BAA6B,CAAAD,QAAA,cAC1C5N,KAAA,QAAK6N,SAAS,CAAC,uBAAuB,CAAAD,QAAA,eACpC9N,IAAA,QAAK+N,SAAS,CAAC,8BAA8B,CAAAD,QAAA,CAAC,YAE9C,CAAK,CAAC,cACN5N,KAAA,QAAA4N,QAAA,eACE9N,IAAA,UACE+N,SAAS,CAAG,uBACVvJ,iBAAiB,CACb,eAAe,CACf,kBACL,oCAAoC,CACrCuK,IAAI,CAAC,MAAM,CACXC,WAAW,CAAC,YAAY,CACxBC,KAAK,CAAE3K,YAAa,CACpB4K,QAAQ,CAAGC,CAAC,EAAK5K,eAAe,CAAC4K,CAAC,CAACN,MAAM,CAACI,KAAK,CAAE,CAClD,CAAC,cACFjP,IAAA,QAAK+N,SAAS,CAAC,yBAAyB,CAAAD,QAAA,CACrCtJ,iBAAiB,CAAGA,iBAAiB,CAAG,EAAE,CACxC,CAAC,EACH,CAAC,EACH,CAAC,CACH,CAAC,cAENtE,KAAA,QAAK6N,SAAS,CAAC,iBAAiB,CAAAD,QAAA,eAC9B5N,KAAA,WACEuO,OAAO,CAAEA,CAAA,GAAM,CACb;AACA,GAAI,CAAAkD,KAAK,CAAG,IAAI,CAChBnK,oBAAoB,CAAC,EAAE,CAAC,CACxBnD,uBAAuB,CAAC,EAAE,CAAC,CAC3BI,oBAAoB,CAAC,EAAE,CAAC,CACxB,GACE4C,YAAY,GAAK,EAAE,EACnBA,YAAY,CAAC4H,KAAK,GAAK,EAAE,CACzB,CACAzH,oBAAoB,CAAC,4BAA4B,CAAC,CAClDnI,KAAK,CAACuS,KAAK,CAAC,sBAAsB,CAAC,CACnCD,KAAK,CAAG,KAAK,CACf,CACA,GAAIzN,eAAe,GAAK,EAAE,CAAE,CAC1BG,uBAAuB,CACrB,4BACF,CAAC,CACDhF,KAAK,CAACuS,KAAK,CAAC,8BAA8B,CAAC,CAC3CD,KAAK,CAAG,KAAK,CACf,CACA,GAAIrN,YAAY,GAAK,EAAE,CAAE,CACvBG,oBAAoB,CAAC,4BAA4B,CAAC,CAClDpF,KAAK,CAACuS,KAAK,CAAC,wBAAwB,CAAC,CACrCD,KAAK,CAAG,KAAK,CACf,CACA,GAAIA,KAAK,CAAE,CACT,KAAM,CAAAmB,MAAM,CAAG,KAAK,CACpB;AACA;AACA;AACA;AACA;AACA;AACA;AAEA,GAAI,CAACA,MAAM,CAAE,KAAAC,mBAAA,CACX;AACA,GAAI,CAAAd,eAAe,EAAAc,mBAAA,CAAG1L,YAAY,CAAC4H,KAAK,UAAA8D,mBAAA,UAAAA,mBAAA,CAAI,EAAE,CAC9C,KAAM,CAAAb,aAAa,CAAG7F,SAAS,SAATA,SAAS,iBAATA,SAAS,CAAE8F,IAAI,CAClCjB,IAAI,EACH8B,MAAM,CAAC9B,IAAI,CAACN,EAAE,CAAC,GAAKoC,MAAM,CAACf,eAAe,CAC9C,CAAC,CACDvE,OAAO,CAACC,GAAG,CAACuE,aAAa,CAAC,CAE1B,GAAIA,aAAa,CAAE,KAAAe,sBAAA,CAAAC,sBAAA,CACjB;AACA,GAAI,CAAAC,cAAc,CAAGjP,eAAe,SAAfA,eAAe,UAAfA,eAAe,CAAI,EAAE,CAE1CgO,aAAa,SAAbA,aAAa,kBAAAe,sBAAA,CAAbf,aAAa,CAAEG,QAAQ,UAAAY,sBAAA,iBAAvBA,sBAAA,CAAyB/H,OAAO,CAAEkI,OAAO,EAAK,CAC5C1F,OAAO,CAACC,GAAG,CAACyF,OAAO,CAACxC,EAAE,CAAC,CACzB,CAAC,CAAC,CAEF,KAAM,CAAAyC,YAAY,CAChBnB,aAAa,SAAbA,aAAa,kBAAAgB,sBAAA,CAAbhB,aAAa,CAAEG,QAAQ,UAAAa,sBAAA,iBAAvBA,sBAAA,CAAyBf,IAAI,CAC1BjB,IAAI,EACH8B,MAAM,CAAC9B,IAAI,CAACN,EAAE,CAAC,GAAKoC,MAAM,CAACG,cAAc,CAC7C,CAAC,CAEH,GAAIE,YAAY,CAAE,CAChB;AACApP,sBAAsB,CAAC,CACrB,GAAGD,mBAAmB,CACtB,CACEsP,QAAQ,CAAEpB,aAAa,CACvBQ,OAAO,CAAEW,YAAY,CACrBE,IAAI,CAAEjP,YACR,CAAC,CACF,CAAC,CACFgD,eAAe,CAAC,EAAE,CAAC,CACnBnD,kBAAkB,CAAC,EAAE,CAAC,CACtBI,eAAe,CAAC,EAAE,CAAC,CACnBmJ,OAAO,CAACC,GAAG,CAAC3J,mBAAmB,CAAC,CAClC,CAAC,IAAM,CACLwD,oBAAoB,CAClB,kCACF,CAAC,CACDnI,KAAK,CAACuS,KAAK,CACT,kCACF,CAAC,CACH,CACF,CAAC,IAAM,CACLpK,oBAAoB,CAClB,0BACF,CAAC,CACDnI,KAAK,CAACuS,KAAK,CAAC,0BAA0B,CAAC,CACzC,CACF,CAAC,IAAM,CACLpK,oBAAoB,CAClB,4CACF,CAAC,CACDnI,KAAK,CAACuS,KAAK,CACT,4CACF,CAAC,CACH,CACF,CACF,CAAE,CACF7D,SAAS,CAAC,uDAAuD,CAAAD,QAAA,eAEjE9N,IAAA,QACEiO,KAAK,CAAC,4BAA4B,CAClCC,IAAI,CAAC,MAAM,CACXC,OAAO,CAAC,WAAW,CACnB,eAAa,KAAK,CAClBC,MAAM,CAAC,cAAc,CACrBoF,KAAK,CAAC,QAAQ,CAAA1F,QAAA,cAEd9N,IAAA,SACE,iBAAe,OAAO,CACtB,kBAAgB,OAAO,CACvBuO,CAAC,CAAC,mDAAmD,CACtD,CAAC,CACC,CAAC,cACNvO,IAAA,SAAA8N,QAAA,CAAM,gBAAc,CAAM,CAAC,EACrB,CAAC,cACT5N,KAAA,QAAK6N,SAAS,CAAC,uBAAuB,CAAAD,QAAA,eACpC9N,IAAA,QAAK+N,SAAS,CAAC,0CAA0C,CAAAD,QAAA,CAAC,WAE1D,CAAK,CAAC,cACN9N,IAAA,QAAK+N,SAAS,CAAC,yBAAyB,CAAAD,QAAA,CACrC9J,mBAAmB,SAAnBA,mBAAmB,iBAAnBA,mBAAmB,CAAE2G,GAAG,CAAC,CAAC8I,YAAY,CAAErT,KAAK,QAAAsT,qBAAA,CAAAC,sBAAA,CAAAC,qBAAA,CAAAC,sBAAA,CAAAC,sBAAA,CAAAC,sBAAA,CAAAC,kBAAA,oBAC5C9T,KAAA,QAEE6N,SAAS,CAAC,iCAAiC,CAAAD,QAAA,eAE3C9N,IAAA,QAAK+N,SAAS,CAAC,qBAAqB,CAAAD,QAAA,cAClC9N,IAAA,WACEyO,OAAO,CAAEA,CAAA,GAAM,CACb,KAAM,CAAAwF,eAAe,CACnBjQ,mBAAmB,CAAC6N,MAAM,CACxB,CAACqC,CAAC,CAAEC,MAAM,GAAKA,MAAM,GAAK/T,KAC5B,CAAC,CACH6D,sBAAsB,CAACgQ,eAAe,CAAC,CACzC,CAAE,CAAAnG,QAAA,cAEF9N,IAAA,QACEiO,KAAK,CAAC,4BAA4B,CAClCC,IAAI,CAAC,MAAM,CACXC,OAAO,CAAC,WAAW,CACnB,eAAa,KAAK,CAClBC,MAAM,CAAC,cAAc,CACrBoF,KAAK,CAAC,QAAQ,CAAA1F,QAAA,cAEd9N,IAAA,SACE,iBAAe,OAAO,CACtB,kBAAgB,OAAO,CACvBuO,CAAC,CAAC,uEAAuE,CAC1E,CAAC,CACC,CAAC,CACA,CAAC,CACN,CAAC,cACNrO,KAAA,QAAK6N,SAAS,CAAC,2BAA2B,CAAAD,QAAA,eACxC5N,KAAA,QAAA4N,QAAA,eACE9N,IAAA,MAAA8N,QAAA,CAAG,WAAS,CAAG,CAAC,CAAC,GAAG,EAAA4F,qBAAA,EAAAC,sBAAA,CACnBF,YAAY,CAACH,QAAQ,UAAAK,sBAAA,iBAArBA,sBAAA,CAAuBxC,SAAS,UAAAuC,qBAAA,UAAAA,qBAAA,CAAI,KAAK,EACvC,CAAC,cACNxT,KAAA,QAAA4N,QAAA,eACE9N,IAAA,MAAA8N,QAAA,CAAG,UAAQ,CAAG,CAAC,CAAC,GAAG,EAAA8F,qBAAA,EAAAC,sBAAA,CAClBJ,YAAY,CAACf,OAAO,UAAAmB,sBAAA,iBAApBA,sBAAA,CAAsBjB,YAAY,UAAAgB,qBAAA,UAAAA,qBAAA,CAAI,IAAI,EACxC,CAAC,cACN1T,KAAA,QAAA4N,QAAA,eACE9N,IAAA,MAAA8N,QAAA,CAAG,aAAW,CAAG,CAAC,CAAC,GAAG,EAAAgG,sBAAA,EAAAC,sBAAA,CACrBN,YAAY,CAACf,OAAO,UAAAqB,sBAAA,iBAApBA,sBAAA,CAAsBlB,kBAAkB,UAAAiB,sBAAA,UAAAA,sBAAA,CACvC,KAAK,EACJ,CAAC,cACN5T,KAAA,QAAA4N,QAAA,eACE9N,IAAA,MAAA8N,QAAA,CAAG,OAAK,CAAG,CAAC,IAAC,EAAAkG,kBAAA,CAACP,YAAY,CAACF,IAAI,UAAAS,kBAAA,UAAAA,kBAAA,CAAI,KAAK,EACrC,CAAC,EACH,CAAC,GA9CD5T,KA+CF,CAAC,EACP,CAAC,CACC,CAAC,EACH,CAAC,EACH,CAAC,EACH,CAAC,cAENF,KAAA,QAAK6N,SAAS,CAAC,6CAA6C,CAAAD,QAAA,eAC1D9N,IAAA,WACEyO,OAAO,CAAEA,CAAA,GAAM5C,aAAa,CAAC,CAAC,CAAE,CAChCkC,SAAS,CAAC,6DAA6D,CAAAD,QAAA,CACxE,MAED,CAAQ,CAAC,cACT9N,IAAA,WACEyO,OAAO,CAAEA,CAAA,GAAM,CACb,GAAI,CAAAkD,KAAK,CAAG,IAAI,CAChBvL,2BAA2B,CAAC,EAAE,CAAC,CAC/BoB,oBAAoB,CAAC,EAAE,CAAC,CAExB;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AAEA,GAAImK,KAAK,CAAE,CACT9F,aAAa,CAAC,CAAC,CAAC,CAClB,CAAC,IAAM,CACLxM,KAAK,CAACuS,KAAK,CACT,oDACF,CAAC,CACH,CACF,CAAE,CACF7D,SAAS,CAAC,wDAAwD,CAAAD,QAAA,CACnE,iBAED,CAAQ,CAAC,EACN,CAAC,EACH,CAAC,CACJ,IAAI,CAEPlC,UAAU,GAAK,CAAC,cACf1L,KAAA,QAAK6N,SAAS,CAAC,EAAE,CAAAD,QAAA,eACf9N,IAAA,QAAK+N,SAAS,CAAC,sCAAsC,CAAAD,QAAA,CAAC,iBAEtD,CAAK,CAAC,cAEN9N,IAAA,QAAK+N,SAAS,CAAC,0CAA0C,CAAAD,QAAA,CAAC,0BAE1D,CAAK,CAAC,cACN5N,KAAA,QAAK6N,SAAS,CAAC,oCAAoC,CAAAD,QAAA,eACjD5N,KAAA,WACMkK,0BAA0B,CAAC,CAAE2D,SAAS,CAAE,UAAW,CAAC,CAAC,CACzD;AACAA,SAAS,CAAC,wFAAwF,CAAAD,QAAA,eAElG9N,IAAA,aAAWsK,2BAA2B,CAAC,CAAC,CAAG,CAAC,cAC5CtK,IAAA,QAAK+N,SAAS,CAAC,MAAM,CAAAD,QAAA,cACnB9N,IAAA,QACEiO,KAAK,CAAC,4BAA4B,CAClCC,IAAI,CAAC,MAAM,CACXC,OAAO,CAAC,WAAW,CACnB,eAAa,KAAK,CAClBC,MAAM,CAAC,cAAc,CACrBL,SAAS,CAAC,iDAAiD,CAAAD,QAAA,cAE3D9N,IAAA,SACE,iBAAe,OAAO,CACtB,kBAAgB,OAAO,CACvBuO,CAAC,CAAC,4GAA4G,CAC/G,CAAC,CACC,CAAC,CACH,CAAC,cACNvO,IAAA,QAAK+N,SAAS,CAAC,MAAM,CAAAD,QAAA,CAAC,kCAEtB,CAAK,CAAC,EACH,CAAC,cACN9N,IAAA,UAAOoU,KAAK,CAAE7T,eAAgB,CAAAuN,QAAA,cAC5B9N,IAAA,QAAK+N,SAAS,CAAC,uBAAuB,CAAAD,QAAA,CACnC7D,0BAA0B,SAA1BA,0BAA0B,iBAA1BA,0BAA0B,CAAEU,GAAG,CAAC,CAACC,IAAI,CAAExK,KAAK,gBAC3CF,KAAA,QACE6N,SAAS,CAAC,0EAA0E,CAAAD,QAAA,eAGpF9N,IAAA,QAAK+N,SAAS,CAAC,kEAAkE,CAAAD,QAAA,cAC/E5N,KAAA,QACE+N,KAAK,CAAC,4BAA4B,CAClCE,OAAO,CAAC,WAAW,CACnBD,IAAI,CAAC,cAAc,CACnBsF,KAAK,CAAC,QAAQ,CAAA1F,QAAA,eAEd9N,IAAA,SAAMuO,CAAC,CAAC,qNAAqN,CAAE,CAAC,cAChOvO,IAAA,SAAMuO,CAAC,CAAC,uIAAuI,CAAE,CAAC,EAC/I,CAAC,CACH,CAAC,cACNrO,KAAA,QAAK6N,SAAS,CAAC,oCAAoC,CAAAD,QAAA,eACjD9N,IAAA,QAAK+N,SAAS,CAAC,gFAAgF,CAAAD,QAAA,CAC5FlD,IAAI,CAAC0G,IAAI,CACP,CAAC,cACNpR,KAAA,QAAA4N,QAAA,EACG,CAAClD,IAAI,CAACyJ,IAAI,EAAI,IAAI,CAAG,IAAI,CAAC,EAAEC,OAAO,CAAC,CAAC,CAAC,CAAC,KAC1C,EAAK,CAAC,EACH,CAAC,cACNtU,IAAA,WACEyO,OAAO,CAAEA,CAAA,GAAM,CACbvE,6BAA6B,CAAEQ,SAAS,EACtCA,SAAS,CAACmH,MAAM,CACd,CAACqC,CAAC,CAAEK,aAAa,GACfnU,KAAK,GAAKmU,aACd,CACF,CAAC,CACH,CAAE,CACFxG,SAAS,CAAC,wDAAwD,CAAAD,QAAA,cAElE9N,IAAA,QACEiO,KAAK,CAAC,4BAA4B,CAClCC,IAAI,CAAC,MAAM,CACXC,OAAO,CAAC,WAAW,CACnB,eAAa,KAAK,CAClBC,MAAM,CAAC,cAAc,CACrBoF,KAAK,CAAC,QAAQ,CAAA1F,QAAA,cAEd9N,IAAA,SACE,iBAAe,OAAO,CACtB,kBAAgB,OAAO,CACvBuO,CAAC,CAAC,sBAAsB,CACzB,CAAC,CACC,CAAC,CACA,CAAC,GA9CJ3D,IAAI,CAAC0G,IA+CP,CACN,CAAC,CACC,CAAC,CACD,CAAC,EACL,CAAC,cAENpR,KAAA,QAAK6N,SAAS,CAAC,6CAA6C,CAAAD,QAAA,eAC1D9N,IAAA,WACEyO,OAAO,CAAEA,CAAA,GAAM5C,aAAa,CAAC,CAAC,CAAE,CAChCkC,SAAS,CAAC,6DAA6D,CAAAD,QAAA,CACxE,MAED,CAAQ,CAAC,cACT9N,IAAA,WACEyO,OAAO,CAAEA,CAAA,GAAM5C,aAAa,CAAC,CAAC,CAAE,CAChCkC,SAAS,CAAC,wDAAwD,CAAAD,QAAA,CACnE,iBAED,CAAQ,CAAC,EACN,CAAC,EACH,CAAC,CACJ,IAAI,CAEPlC,UAAU,GAAK,CAAC,cACf1L,KAAA,QAAK6N,SAAS,CAAC,EAAE,CAAAD,QAAA,eACf9N,IAAA,QAAK+N,SAAS,CAAC,sCAAsC,CAAAD,QAAA,CAAC,UAEtD,CAAK,CAAC,cAEN9N,IAAA,QAAK+N,SAAS,CAAC,0CAA0C,CAAAD,QAAA,CAAC,sBAE1D,CAAK,CAAC,cACN5N,KAAA,QAAK6N,SAAS,CAAC,oCAAoC,CAAAD,QAAA,eACjD5N,KAAA,QAAK6N,SAAS,CAAC,6BAA6B,CAAAD,QAAA,eAC1C5N,KAAA,QAAK6N,SAAS,CAAC,+BAA+B,CAAAD,QAAA,eAC5C9N,IAAA,QAAK+N,SAAS,CAAC,8BAA8B,CAAAD,QAAA,CAAC,2BAE9C,CAAK,CAAC,cACN9N,IAAA,QAAA8N,QAAA,cACE9N,IAAA,UACE+N,SAAS,CAAC,wEAAwE,CAClFgB,IAAI,CAAC,MAAM,CACXC,WAAW,CAAC,2BAA2B,CACvCC,KAAK,CAAE5G,aAAc,CACrB6G,QAAQ,CAAGC,CAAC,EAAK7G,gBAAgB,CAAC6G,CAAC,CAACN,MAAM,CAACI,KAAK,CAAE,CACnD,CAAC,CACC,CAAC,EACH,CAAC,cAEN/O,KAAA,QAAK6N,SAAS,CAAC,+BAA+B,CAAAD,QAAA,eAC5C9N,IAAA,QAAK+N,SAAS,CAAC,8BAA8B,CAAAD,QAAA,CAAC,wBAE9C,CAAK,CAAC,cACN9N,IAAA,QAAA8N,QAAA,cACE9N,IAAA,UACE+N,SAAS,CAAC,wEAAwE,CAClFgB,IAAI,CAAC,MAAM,CACXC,WAAW,CAAC,wBAAwB,CACpCC,KAAK,CAAExG,UAAW,CAClByG,QAAQ,CAAGC,CAAC,EAAKzG,aAAa,CAACyG,CAAC,CAACN,MAAM,CAACI,KAAK,CAAE,CAChD,CAAC,CACC,CAAC,EACH,CAAC,EACH,CAAC,cAENjP,IAAA,QAAK+N,SAAS,CAAC,6BAA6B,CAAAD,QAAA,cAC1C5N,KAAA,QAAK6N,SAAS,CAAC,sBAAsB,CAAAD,QAAA,eACnC9N,IAAA,QAAK+N,SAAS,CAAC,8BAA8B,CAAAD,QAAA,CAAC,mBAE9C,CAAK,CAAC,cACN9N,IAAA,QAAA8N,QAAA,cACE9N,IAAA,UACE+N,SAAS,CAAC,wEAAwE,CAClFgB,IAAI,CAAC,QAAQ,CACbC,WAAW,CAAC,mBAAmB,CAC/BC,KAAK,CAAEpG,MAAO,CACdqG,QAAQ,CAAGC,CAAC,EAAKrG,SAAS,CAACqG,CAAC,CAACN,MAAM,CAACI,KAAK,CAAE,CAC5C,CAAC,CACC,CAAC,EACH,CAAC,CACH,CAAC,EACH,CAAC,cACNjP,IAAA,QAAK+N,SAAS,CAAC,0CAA0C,CAAAD,QAAA,CAAC,gBAE1D,CAAK,CAAC,cACN5N,KAAA,QAAK6N,SAAS,CAAC,oCAAoC,CAAAD,QAAA,eACjD5N,KAAA,WACMoL,yBAAyB,CAAC,CAAEyC,SAAS,CAAE,UAAW,CAAC,CAAC,CACxD;AACAA,SAAS,CAAC,wFAAwF,CAAAD,QAAA,eAElG9N,IAAA,aAAWuL,0BAA0B,CAAC,CAAC,CAAG,CAAC,cAC3CvL,IAAA,QAAK+N,SAAS,CAAC,MAAM,CAAAD,QAAA,cACnB9N,IAAA,QACEiO,KAAK,CAAC,4BAA4B,CAClCC,IAAI,CAAC,MAAM,CACXC,OAAO,CAAC,WAAW,CACnB,eAAa,KAAK,CAClBC,MAAM,CAAC,cAAc,CACrBL,SAAS,CAAC,iDAAiD,CAAAD,QAAA,cAE3D9N,IAAA,SACE,iBAAe,OAAO,CACtB,kBAAgB,OAAO,CACvBuO,CAAC,CAAC,4GAA4G,CAC/G,CAAC,CACC,CAAC,CACH,CAAC,cACNvO,IAAA,QAAK+N,SAAS,CAAC,MAAM,CAAAD,QAAA,CAAC,kCAEtB,CAAK,CAAC,EACH,CAAC,cACN9N,IAAA,UAAOoU,KAAK,CAAE7T,eAAgB,CAAAuN,QAAA,cAC5B9N,IAAA,QAAK+N,SAAS,CAAC,uBAAuB,CAAAD,QAAA,CACnC1C,kBAAkB,SAAlBA,kBAAkB,iBAAlBA,kBAAkB,CAAET,GAAG,CAAC,CAACC,IAAI,CAAExK,KAAK,gBACnCF,KAAA,QACE6N,SAAS,CAAC,0EAA0E,CAAAD,QAAA,eAGpF9N,IAAA,QAAK+N,SAAS,CAAC,kEAAkE,CAAAD,QAAA,cAC/E5N,KAAA,QACE+N,KAAK,CAAC,4BAA4B,CAClCE,OAAO,CAAC,WAAW,CACnBD,IAAI,CAAC,cAAc,CACnBsF,KAAK,CAAC,QAAQ,CAAA1F,QAAA,eAEd9N,IAAA,SAAMuO,CAAC,CAAC,qNAAqN,CAAE,CAAC,cAChOvO,IAAA,SAAMuO,CAAC,CAAC,uIAAuI,CAAE,CAAC,EAC/I,CAAC,CACH,CAAC,cACNrO,KAAA,QAAK6N,SAAS,CAAC,oCAAoC,CAAAD,QAAA,eACjD9N,IAAA,QAAK+N,SAAS,CAAC,gFAAgF,CAAAD,QAAA,CAC5FlD,IAAI,CAAC0G,IAAI,CACP,CAAC,cACNpR,KAAA,QAAA4N,QAAA,EACG,CAAClD,IAAI,CAACyJ,IAAI,EAAI,IAAI,CAAG,IAAI,CAAC,EAAEC,OAAO,CAAC,CAAC,CAAC,CAAC,KAC1C,EAAK,CAAC,EACH,CAAC,cACNtU,IAAA,WACEyO,OAAO,CAAEA,CAAA,GAAM,CACbpD,qBAAqB,CAAEX,SAAS,EAC9BA,SAAS,CAACmH,MAAM,CACd,CAACqC,CAAC,CAAEK,aAAa,GACfnU,KAAK,GAAKmU,aACd,CACF,CAAC,CACH,CAAE,CACFxG,SAAS,CAAC,wDAAwD,CAAAD,QAAA,cAElE9N,IAAA,QACEiO,KAAK,CAAC,4BAA4B,CAClCC,IAAI,CAAC,MAAM,CACXC,OAAO,CAAC,WAAW,CACnB,eAAa,KAAK,CAClBC,MAAM,CAAC,cAAc,CACrBoF,KAAK,CAAC,QAAQ,CAAA1F,QAAA,cAEd9N,IAAA,SACE,iBAAe,OAAO,CACtB,kBAAgB,OAAO,CACvBuO,CAAC,CAAC,sBAAsB,CACzB,CAAC,CACC,CAAC,CACA,CAAC,GA9CJ3D,IAAI,CAAC0G,IA+CP,CACN,CAAC,CACC,CAAC,CACD,CAAC,EACL,CAAC,cAGNpR,KAAA,QAAK6N,SAAS,CAAC,6CAA6C,CAAAD,QAAA,eAC1D9N,IAAA,WACEyO,OAAO,CAAEA,CAAA,GAAM5C,aAAa,CAAC,CAAC,CAAE,CAChCkC,SAAS,CAAC,6DAA6D,CAAAD,QAAA,CACxE,MAED,CAAQ,CAAC,cACT9N,IAAA,WACEyO,OAAO,CAAEA,CAAA,GAAM5C,aAAa,CAAC,CAAC,CAAE,CAChCkC,SAAS,CAAC,wDAAwD,CAAAD,QAAA,CACnE,iBAED,CAAQ,CAAC,EACN,CAAC,EACH,CAAC,CACJ,IAAI,CAEPlC,UAAU,GAAK,CAAC,cACf1L,KAAA,QAAK6N,SAAS,CAAC,EAAE,CAAAD,QAAA,eACf9N,IAAA,QAAK+N,SAAS,CAAC,sCAAsC,CAAAD,QAAA,CAAC,yBAEtD,CAAK,CAAC,cAEN9N,IAAA,QAAK+N,SAAS,CAAC,0CAA0C,CAAAD,QAAA,CAAC,oBAE1D,CAAK,CAAC,cACN9N,IAAA,QAAK+N,SAAS,CAAC,oCAAoC,CAAAD,QAAA,cACjD5N,KAAA,QAAK6N,SAAS,CAAC,6BAA6B,CAAAD,QAAA,eAC1C5N,KAAA,QAAK6N,SAAS,CAAC,+BAA+B,CAAAD,QAAA,eAC5C9N,IAAA,QAAK+N,SAAS,CAAC,8BAA8B,CAAAD,QAAA,CAAC,wBAE9C,CAAK,CAAC,cACN9N,IAAA,QAAA8N,QAAA,cACE9N,IAAA,CAACR,MAAM,EACLyP,KAAK,CAAEhG,gBAAiB,CACxBiG,QAAQ,CAAGE,MAAM,EAAK,CACpBlG,mBAAmB,CAACkG,MAAM,CAAC,CAC7B,CAAE,CACFC,OAAO,CAAEtC,UAAU,SAAVA,UAAU,iBAAVA,UAAU,CAAEpC,GAAG,CAAEgG,SAAS,GAAM,CACvC1B,KAAK,CAAE0B,SAAS,CAACC,EAAE,CACnBtB,KAAK,CAAEqB,SAAS,CAACE,cAAc,EAAI,EACrC,CAAC,CAAC,CAAE,CACJC,YAAY,CAAEA,CAAC1B,MAAM,CAAE2B,UAAU,GAC/B3B,MAAM,CAACE,KAAK,CACT0B,WAAW,CAAC,CAAC,CACbC,QAAQ,CAACF,UAAU,CAACC,WAAW,CAAC,CAAC,CACrC,CACDjD,SAAS,CAAC,SAAS,CACnBiB,WAAW,CAAC,qBAAqB,CACjCQ,YAAY,MACZC,MAAM,CAAE,CACNC,OAAO,CAAEA,CAACC,IAAI,CAAE1D,KAAK,IAAM,CACzB,GAAG0D,IAAI,CACPC,UAAU,CAAE,MAAM,CAClBC,MAAM,CAAE1G,qBAAqB,CACzB,mBAAmB,CACnB,mBAAmB,CACvB2G,SAAS,CAAE7D,KAAK,CAAC8D,SAAS,CAAG,MAAM,CAAG,MAAM,CAC5C,SAAS,CAAE,CACTF,MAAM,CAAE,mBACV,CACF,CAAC,CAAC,CACFT,MAAM,CAAGO,IAAI,GAAM,CACjB,GAAGA,IAAI,CACPnP,OAAO,CAAE,MAAM,CACfwP,UAAU,CAAE,QACd,CAAC,CAAC,CACFC,WAAW,CAAGN,IAAI,GAAM,CACtB,GAAGA,IAAI,CACPnP,OAAO,CAAE,MAAM,CACfwP,UAAU,CAAE,QACd,CAAC,CACH,CAAE,CACH,CAAC,CACC,CAAC,EACH,CAAC,cAEN9P,KAAA,QAAK6N,SAAS,CAAC,+BAA+B,CAAAD,QAAA,eAC5C9N,IAAA,QAAK+N,SAAS,CAAC,8BAA8B,CAAAD,QAAA,CAAC,eAE9C,CAAK,CAAC,cACN9N,IAAA,QAAA8N,QAAA,cACE9N,IAAA,UACE+N,SAAS,CAAC,wEAAwE,CAClFgB,IAAI,CAAC,MAAM,CACXC,WAAW,CAAC,eAAe,CAC3BC,KAAK,CAAExF,YAAa,CACpByF,QAAQ,CAAGC,CAAC,EAAKzF,eAAe,CAACyF,CAAC,CAACN,MAAM,CAACI,KAAK,CAAE,CAClD,CAAC,CACC,CAAC,EACH,CAAC,EACH,CAAC,CACH,CAAC,cAENjP,IAAA,QAAK+N,SAAS,CAAC,0CAA0C,CAAAD,QAAA,CAAC,uBAE1D,CAAK,CAAC,cACN9N,IAAA,QAAK+N,SAAS,CAAC,oCAAoC,CAAAD,QAAA,cACjD9N,IAAA,QAAK+N,SAAS,CAAC,6BAA6B,CAAAD,QAAA,cAC1C5N,KAAA,QAAK6N,SAAS,CAAC,sBAAsB,CAAAD,QAAA,eACnC9N,IAAA,QAAK+N,SAAS,CAAC,8BAA8B,CAAAD,QAAA,CAAC,gBAE9C,CAAK,CAAC,cACN9N,IAAA,QAAA8N,QAAA,cACE5N,KAAA,WACE+O,KAAK,CAAEpF,aAAc,CACrBqF,QAAQ,CAAGC,CAAC,EAAKrF,gBAAgB,CAACqF,CAAC,CAACN,MAAM,CAACI,KAAK,CAAE,CAClDlB,SAAS,CAAC,wEAAwE,CAAAD,QAAA,eAElF9N,IAAA,WAAQiP,KAAK,CAAE,EAAG,CAAAnB,QAAA,CAAC,eAAa,CAAQ,CAAC,cACzC9N,IAAA,WAAQiP,KAAK,CAAE,SAAU,CAAAnB,QAAA,CAAC,SAAO,CAAQ,CAAC,cAC1C9N,IAAA,WAAQiP,KAAK,CAAE,UAAW,CAAAnB,QAAA,CAAC,UAAQ,CAAQ,CAAC,cAC5C9N,IAAA,WAAQiP,KAAK,CAAE,QAAS,CAAAnB,QAAA,CAAC,QAAM,CAAQ,CAAC,EAClC,CAAC,CACN,CAAC,EACH,CAAC,CACH,CAAC,CACH,CAAC,cAEN9N,IAAA,QAAK+N,SAAS,CAAC,0CAA0C,CAAAD,QAAA,CAAC,gCAE1D,CAAK,CAAC,cACN5N,KAAA,QAAK6N,SAAS,CAAC,oCAAoC,CAAAD,QAAA,eACjD5N,KAAA,WACMwL,wCAAwC,CAAC,CAC3CqC,SAAS,CAAE,UACb,CAAC,CAAC,CACF;AACAA,SAAS,CAAC,wFAAwF,CAAAD,QAAA,eAElG9N,IAAA,aAAW2L,yCAAyC,CAAC,CAAC,CAAG,CAAC,cAC1D3L,IAAA,QAAK+N,SAAS,CAAC,MAAM,CAAAD,QAAA,cACnB9N,IAAA,QACEiO,KAAK,CAAC,4BAA4B,CAClCC,IAAI,CAAC,MAAM,CACXC,OAAO,CAAC,WAAW,CACnB,eAAa,KAAK,CAClBC,MAAM,CAAC,cAAc,CACrBL,SAAS,CAAC,iDAAiD,CAAAD,QAAA,cAE3D9N,IAAA,SACE,iBAAe,OAAO,CACtB,kBAAgB,OAAO,CACvBuO,CAAC,CAAC,4GAA4G,CAC/G,CAAC,CACC,CAAC,CACH,CAAC,cACNvO,IAAA,QAAK+N,SAAS,CAAC,MAAM,CAAAD,QAAA,CAAC,kCAEtB,CAAK,CAAC,EACH,CAAC,cACN9N,IAAA,UAAOoU,KAAK,CAAE7T,eAAgB,CAAAuN,QAAA,cAC5B9N,IAAA,QAAK+N,SAAS,CAAC,uBAAuB,CAAAD,QAAA,CACnCtC,iCAAiC,SAAjCA,iCAAiC,iBAAjCA,iCAAiC,CAAEb,GAAG,CACrC,CAACC,IAAI,CAAExK,KAAK,gBACVF,KAAA,QACE6N,SAAS,CAAC,0EAA0E,CAAAD,QAAA,eAGpF9N,IAAA,QAAK+N,SAAS,CAAC,kEAAkE,CAAAD,QAAA,cAC/E5N,KAAA,QACE+N,KAAK,CAAC,4BAA4B,CAClCE,OAAO,CAAC,WAAW,CACnBD,IAAI,CAAC,cAAc,CACnBsF,KAAK,CAAC,QAAQ,CAAA1F,QAAA,eAEd9N,IAAA,SAAMuO,CAAC,CAAC,qNAAqN,CAAE,CAAC,cAChOvO,IAAA,SAAMuO,CAAC,CAAC,uIAAuI,CAAE,CAAC,EAC/I,CAAC,CACH,CAAC,cACNrO,KAAA,QAAK6N,SAAS,CAAC,oCAAoC,CAAAD,QAAA,eACjD9N,IAAA,QAAK+N,SAAS,CAAC,gFAAgF,CAAAD,QAAA,CAC5FlD,IAAI,CAAC0G,IAAI,CACP,CAAC,cACNpR,KAAA,QAAA4N,QAAA,EACG,CAAClD,IAAI,CAACyJ,IAAI,EAAI,IAAI,CAAG,IAAI,CAAC,EAAEC,OAAO,CAAC,CAAC,CAAC,CAAC,KAC1C,EAAK,CAAC,EACH,CAAC,cACNtU,IAAA,WACEyO,OAAO,CAAEA,CAAA,GAAM,CACbhD,oCAAoC,CACjCf,SAAS,EACRA,SAAS,CAACmH,MAAM,CACd,CAACqC,CAAC,CAAEK,aAAa,GACfnU,KAAK,GAAKmU,aACd,CACJ,CAAC,CACH,CAAE,CACFxG,SAAS,CAAC,wDAAwD,CAAAD,QAAA,cAElE9N,IAAA,QACEiO,KAAK,CAAC,4BAA4B,CAClCC,IAAI,CAAC,MAAM,CACXC,OAAO,CAAC,WAAW,CACnB,eAAa,KAAK,CAClBC,MAAM,CAAC,cAAc,CACrBoF,KAAK,CAAC,QAAQ,CAAA1F,QAAA,cAEd9N,IAAA,SACE,iBAAe,OAAO,CACtB,kBAAgB,OAAO,CACvBuO,CAAC,CAAC,sBAAsB,CACzB,CAAC,CACC,CAAC,CACA,CAAC,GA/CJ3D,IAAI,CAAC0G,IAgDP,CAET,CAAC,CACE,CAAC,CACD,CAAC,EACL,CAAC,cAENpR,KAAA,QAAK6N,SAAS,CAAC,6CAA6C,CAAAD,QAAA,eAC1D9N,IAAA,WACEyO,OAAO,CAAEA,CAAA,GAAM5C,aAAa,CAAC,CAAC,CAAE,CAChCkC,SAAS,CAAC,6DAA6D,CAAAD,QAAA,CACxE,MAED,CAAQ,CAAC,cACT9N,IAAA,WACE+R,QAAQ,CAAErF,cAAe,CACzB+B,OAAO,CAAE,KAAAA,CAAA,GAAY,KAAA+F,mBAAA,CACnB,KAAM,CAAAC,aAAa,CAAGzQ,mBAAmB,SAAnBA,mBAAmB,iBAAnBA,mBAAmB,CAAE2G,GAAG,CAC3CuG,IAAI,OAAAwD,aAAA,CAAAC,cAAA,OAAM,CACTjC,OAAO,EAAAgC,aAAA,CAAExD,IAAI,CAACwB,OAAO,UAAAgC,aAAA,iBAAZA,aAAA,CAAc9D,EAAE,CACzB0C,QAAQ,EAAAqB,cAAA,CAAEzD,IAAI,CAACoC,QAAQ,UAAAqB,cAAA,iBAAbA,cAAA,CAAe/D,EAAE,CAC3B2C,IAAI,CAAErC,IAAI,CAACqC,IACb,CAAC,EACH,CAAC,CACD,KAAM,CAAAxS,QAAQ,CACZxB,UAAU,CAAC,CACTqV,UAAU,CAAE5T,SAAS,CACrB6T,SAAS,CAAEzT,QAAQ,CACnB+P,SAAS,CAAEnQ,SAAS,CAAG,GAAG,CAAGI,QAAQ,CACrC0T,SAAS,CAAElT,SAAS,CACpBmT,aAAa,CAAE/S,KAAK,CACpBgT,aAAa,CAAExT,KAAK,CACpByT,eAAe,CAAE7S,OAAO,CACxB8S,YAAY,CAAE1S,IAAI,CAClB2S,eAAe,CAAEvS,OAAO,CAACqM,KAAK,CAC9B;AACAvL,WAAW,CAAEA,WAAW,CAACuL,KAAK,CAC9BmG,SAAS,CAAE1Q,QAAQ,CACnB2Q,SAAS,CAAEpQ,QAAQ,CACnBqQ,cAAc,CACZrQ,QAAQ,GAAK,SAAS,CAAGI,YAAY,CAAG,EAAE,CAC5CkQ,gBAAgB,CAAE9P,eAAe,CACjC;AACA+P,mBAAmB,CAAE3P,eAAe,CACpC4P,WAAW,CAAExP,mBAAmB,CAChCyP,gBAAgB,CACdrQ,YAAY,GAAK,WAAW,CACxB,EAAE,CACFgB,eAAe,CACrBsP,UAAU,CACRtQ,YAAY,GAAK,WAAW,CAAGoB,SAAS,CAAG,EAAE,CAC/CmP,QAAQ,CACNvQ,YAAY,GAAK,WAAW,CAAGwB,OAAO,CAAG,EAAE,CAC7CgP,gBAAgB,CAAE5O,eAAe,CACjCqM,QAAQ,CAAEjM,YAAY,CAAC4H,KAAK,CAC5B;AACA6G,cAAc,CAAEzN,aAAa,CAC7B0N,WAAW,CAAEtN,UAAU,CACvBuN,cAAc,CAAEnN,MAAM,CACtB8H,SAAS,CAAE1H,gBAAgB,CAACgG,KAAK,CACjCgH,gBAAgB,CAAE5M,eAAe,CACjC6M,aAAa,CAAEzM,YAAY,CAC3B0M,gBAAgB,CAAEtM,aAAa,CAC/B;AACAuM,uBAAuB,CAAEnM,0BAA0B,CACnDoM,cAAc,CAAEjL,kBAAkB,CAClCkL,8BAA8B,CAC5B9K,iCAAiC,CACnC;AACAa,SAAS,CAAEoI,aAAa,SAAbA,aAAa,UAAbA,aAAa,CAAI,EAAE,CAC9B;AACA8B,MAAM,CAAEvT,KAAK,CAAG,MAAM,CAAG,OAAO,CAChCwT,WAAW,CAAElT,UAAU,CACvBmT,cAAc,EAAAjC,mBAAA,CAAEtR,YAAY,CAAC+L,KAAK,UAAAuF,mBAAA,UAAAA,mBAAA,CAAI,EACxC,CAAC,CACH,CAAC,CACH,CAAE,CACFzG,SAAS,CAAC,wDAAwD,CAAAD,QAAA,CAEjEpB,cAAc,CAAG,WAAW,CAAG,QAAQ,CAClC,CAAC,EACN,CAAC,EACH,CAAC,CACJ,IAAI,CAEPd,UAAU,GAAK,CAAC,cACf5L,IAAA,QAAK+N,SAAS,CAAC,EAAE,CAAAD,QAAA,cACf9N,IAAA,QAAK+N,SAAS,CAAC,oCAAoC,CAAAD,QAAA,cACjD5N,KAAA,QAAK6N,SAAS,CAAC,oDAAoD,CAAAD,QAAA,eACjE9N,IAAA,QACEiO,KAAK,CAAC,4BAA4B,CAClCC,IAAI,CAAC,MAAM,CACXC,OAAO,CAAC,WAAW,CACnB,eAAa,KAAK,CAClBC,MAAM,CAAC,cAAc,CACrBL,SAAS,CAAC,oEAAoE,CAAAD,QAAA,cAE9E9N,IAAA,SACE,iBAAe,OAAO,CACtB,kBAAgB,OAAO,CACvBuO,CAAC,CAAC,uBAAuB,CAC1B,CAAC,CACC,CAAC,cACNvO,IAAA,QAAK+N,SAAS,CAAC,wCAAwC,CAAAD,QAAA,CAAC,4BAExD,CAAK,CAAC,cACN9N,IAAA,QAAK+N,SAAS,CAAC,oDAAoD,CAAAD,QAAA,CAAC,8GAGpE,CAAK,CAAC,cACN9N,IAAA,QAAK+N,SAAS,CAAC,6CAA6C,CAAAD,QAAA,cAS1D9N,IAAA,MACEgO,IAAI,CAAC,YAAY,CACjBD,SAAS,CAAC,wDAAwD,CAAAD,QAAA,CACnE,gBAED,CAAG,CAAC,CACD,CAAC,EACH,CAAC,CACH,CAAC,CACH,CAAC,CACJ,IAAI,EACL,CAAC,EACH,CAAC,CACH,CAAC,EACH,CAAC,EACO,CAAC,CAEpB,CAEA,cAAe,CAAAlN,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}