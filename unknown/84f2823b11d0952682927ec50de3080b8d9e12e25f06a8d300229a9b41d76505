{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Desktop/Freelances/Mohssin <PERSON>/UNIMEDCARE/web-front/src/screens/insurances/InsurancesScreen.js\",\n  _s = $RefreshSig$();\nimport React, { useEffect, useState } from \"react\";\nimport { useDispatch, useSelector } from \"react-redux\";\nimport { Link, useLocation, useNavigate, useSearchParams } from \"react-router-dom\";\nimport { deleteInsurance, getInsuranesList } from \"../../redux/actions/insuranceActions\";\nimport DefaultLayout from \"../../layouts/DefaultLayout\";\nimport Loader from \"../../components/Loader\";\nimport Alert from \"../../components/Alert\";\nimport { baseURLFile } from \"../../constants\";\nimport ConfirmationModal from \"../../components/ConfirmationModal\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction InsurancesScreen() {\n  _s();\n  const location = useLocation();\n  const navigate = useNavigate();\n  const [searchParams] = useSearchParams();\n  const page = searchParams.get(\"page\") || \"1\";\n  const dispatch = useDispatch();\n  const [isDelete, setIsDelete] = useState(false);\n  const [loadEvent, setLoadEvent] = useState(false);\n  const [eventType, setEventType] = useState(\"\");\n  const [insuranceId, setInsuranceId] = useState(\"\");\n  const userLogin = useSelector(state => state.userLogin);\n  const {\n    userInfo\n  } = userLogin;\n  const listInsurances = useSelector(state => state.insuranceList);\n  const {\n    insurances,\n    loadingInsurances,\n    errorInsurances,\n    pages\n  } = listInsurances;\n  const insuranceDelete = useSelector(state => state.deleteInsurance);\n  const {\n    loadingInsuranceDelete,\n    errorInsuranceDelete,\n    successInsuranceDelete\n  } = insuranceDelete;\n  const redirect = \"/\";\n  useEffect(() => {\n    if (!userInfo) {\n      navigate(redirect);\n    } else if (userInfo.role !== \"1\" && userInfo.role !== 1 && userInfo.role !== \"2\" && userInfo.role !== 2) {\n      navigate(redirect);\n    } else {\n      dispatch(getInsuranesList(page));\n    }\n  }, [navigate, userInfo, dispatch, page]);\n  useEffect(() => {\n    if (successInsuranceDelete) {\n      dispatch(getInsuranesList(\"0\"));\n    }\n  }, [successInsuranceDelete]);\n  return /*#__PURE__*/_jsxDEV(DefaultLayout, {\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex flex-row text-sm items-center my-1\",\n        children: [/*#__PURE__*/_jsxDEV(\"a\", {\n          href: \"/dashboard\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex flex-row  items-center hover:text-black \",\n            children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n              xmlns: \"http://www.w3.org/2000/svg\",\n              fill: \"none\",\n              viewBox: \"0 0 24 24\",\n              \"stroke-width\": \"1.5\",\n              stroke: \"currentColor\",\n              className: \"w-4 h-4\",\n              children: /*#__PURE__*/_jsxDEV(\"path\", {\n                strokeLinecap: \"round\",\n                strokeLinejoin: \"round\",\n                d: \"m2.25 12 8.954-8.955c.44-.439 1.152-.439 1.591 0L21.75 12M4.5 9.75v10.125c0 .621.504 1.125 1.125 1.125H9.75v-4.875c0-.621.504-1.125 1.125-1.125h2.25c.621 0 1.125.504 1.125 1.125V21h4.125c.621 0 1.125-.504 1.125-1.125V9.75M8.25 21h8.25\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 82,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 74,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"mx-1\",\n              children: \"Dashboard\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 88,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 73,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 72,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          children: /*#__PURE__*/_jsxDEV(\"svg\", {\n            xmlns: \"http://www.w3.org/2000/svg\",\n            fill: \"none\",\n            viewBox: \"0 0 24 24\",\n            \"stroke-width\": \"1.5\",\n            stroke: \"currentColor\",\n            className: \"w-4 h-4\",\n            children: /*#__PURE__*/_jsxDEV(\"path\", {\n              strokeLinecap: \"round\",\n              strokeLinejoin: \"round\",\n              d: \"m8.25 4.5 7.5 7.5-7.5 7.5\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 100,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 92,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 91,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"\",\n          children: \"Insurance Company\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 107,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 70,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex flex-row justify-between  items-center my-3\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"mx-1 font-bold text-black \",\n          children: \"Insurances Company\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 111,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex flex-row items-center justify-end\",\n          children: /*#__PURE__*/_jsxDEV(\"a\", {\n            href: \"/insurances-company/new-insurance\",\n            className: \"mx-2 flex flex-row bg-[#0388A6] text-white text-xs rounded-full px-5 py-3 items-center\",\n            children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n              xmlns: \"http://www.w3.org/2000/svg\",\n              fill: \"none\",\n              viewBox: \"0 0 24 24\",\n              \"stroke-width\": \"1.5\",\n              stroke: \"currentColor\",\n              className: \"size-4 mx-1\",\n              children: /*#__PURE__*/_jsxDEV(\"path\", {\n                \"stroke-linecap\": \"round\",\n                \"stroke-linejoin\": \"round\",\n                d: \"M12 4.5v15m7.5-7.5h-15\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 126,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 118,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: \"New Insurance\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 133,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 114,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 113,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 110,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"rounded-sm border border-stroke bg-white px-5 pt-6 pb-2.5 shadow-default   dark:bg-boxdark sm:px-7.5 xl:pb-1\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"container mx-auto flex flex-col\",\n          children: loadingInsurances ? /*#__PURE__*/_jsxDEV(Loader, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 141,\n            columnNumber: 15\n          }, this) : errorInsurances ? /*#__PURE__*/_jsxDEV(Alert, {\n            type: \"error\",\n            message: errorInsurances\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 143,\n            columnNumber: 15\n          }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"max-w-full overflow-x-auto \",\n            children: /*#__PURE__*/_jsxDEV(\"table\", {\n              className: \"w-full table-auto\",\n              children: [/*#__PURE__*/_jsxDEV(\"thead\", {\n                children: /*#__PURE__*/_jsxDEV(\"tr\", {\n                  className: \" bg-[#F3F5FB] text-left \",\n                  children: [/*#__PURE__*/_jsxDEV(\"th\", {\n                    className: \"min-w-[30px] py-4 px-4 font-bold text-[#8E8E8E] text-xs w-max\",\n                    children: \"#\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 149,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                    className: \"min-w-[60px] py-4 px-4 font-bold text-[#8E8E8E] text-xs w-max \",\n                    children: \"Logo\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 152,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                    className: \"min-w-[60px] py-4 px-4 font-bold text-[#8E8E8E] text-xs w-max \",\n                    children: \"Insurance Name\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 155,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                    className: \"min-w-[120px] py-4 px-4 font-bold text-[#8E8E8E] text-xs w-max\",\n                    children: \"Country\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 158,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                    className: \"min-w-[120px] py-4 px-4 font-bold text-[#8E8E8E] text-xs w-max\",\n                    children: \"Email\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 161,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                    className: \"min-w-[120px] py-4 px-4 font-bold text-[#8E8E8E] text-xs w-max\",\n                    children: \"Phone\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 164,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                    className: \"py-4 px-4 font-bold text-[#8E8E8E] text-xs w-max\",\n                    children: \"Operation\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 167,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 148,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 147,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"tbody\", {\n                children: insurances === null || insurances === void 0 ? void 0 : insurances.map((item, index) => {\n                  var _item$assurance_name, _item$assurance_count, _item$assurance_email, _item$assurance_phone;\n                  return /*#__PURE__*/_jsxDEV(\"tr\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"td\", {\n                      className: \" py-3 px-4 min-w-[120px]  \",\n                      children: /*#__PURE__*/_jsxDEV(\"p\", {\n                        className: \"text-black  text-xs w-max  \",\n                        children: [\"#\", item.id]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 177,\n                        columnNumber: 27\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 176,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                      className: \" py-3 px-4 min-w-[120px]  \",\n                      children: /*#__PURE__*/_jsxDEV(\"a\", {\n                        href: \"/insurances-company/profile/\" + item.id\n                        // href={baseURLFile + item.assurance_logo}\n                        ,\n                        className: \"text-black  text-xs w-max  \"\n                        // target=\"_blank\"\n                        // rel=\"noopener noreferrer\"\n                        ,\n                        children: item.assurance_logo ? /*#__PURE__*/_jsxDEV(\"img\", {\n                          className: \"size-11 rounded\",\n                          src: baseURLFile + item.assurance_logo,\n                          alt: item.assurance_name,\n                          onError: e => {\n                            e.target.onerror = null;\n                            e.target.src = \"/assets/placeholder.png\";\n                          }\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 190,\n                          columnNumber: 31\n                        }, this) : /*#__PURE__*/_jsxDEV(\"img\", {\n                          className: \"size-11 rounded\",\n                          alt: item.assurance_name,\n                          src: \"/assets/placeholder.png\",\n                          onError: e => {\n                            e.target.onerror = null;\n                            e.target.src = \"/assets/placeholder.png\";\n                          }\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 200,\n                          columnNumber: 31\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 182,\n                        columnNumber: 27\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 181,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                      className: \" py-3 px-4 min-w-[120px]  \",\n                      children: /*#__PURE__*/_jsxDEV(\"p\", {\n                        className: \"text-black  text-xs w-max  \",\n                        children: (_item$assurance_name = item.assurance_name) !== null && _item$assurance_name !== void 0 ? _item$assurance_name : \"---\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 213,\n                        columnNumber: 27\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 212,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                      className: \" py-3 px-4 min-w-[120px]  \",\n                      children: /*#__PURE__*/_jsxDEV(\"p\", {\n                        className: \"text-black  text-xs w-max  \",\n                        children: (_item$assurance_count = item.assurance_country) !== null && _item$assurance_count !== void 0 ? _item$assurance_count : \"---\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 218,\n                        columnNumber: 27\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 217,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                      className: \" py-3 px-4 min-w-[120px]  \",\n                      children: /*#__PURE__*/_jsxDEV(\"p\", {\n                        className: \"text-black  text-xs w-max  \",\n                        children: (_item$assurance_email = item.assurance_email) !== null && _item$assurance_email !== void 0 ? _item$assurance_email : \"---\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 223,\n                        columnNumber: 27\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 222,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                      className: \" py-3 px-4 min-w-[120px]  \",\n                      children: /*#__PURE__*/_jsxDEV(\"p\", {\n                        className: \"text-black  text-xs w-max  \",\n                        children: (_item$assurance_phone = item.assurance_phone) !== null && _item$assurance_phone !== void 0 ? _item$assurance_phone : \"---\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 228,\n                        columnNumber: 27\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 227,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                      className: \" py-3 px-4 min-w-[120px]  \",\n                      children: /*#__PURE__*/_jsxDEV(\"p\", {\n                        className: \"text-black  text-xs w-max flex flex-row  \",\n                        children: [/*#__PURE__*/_jsxDEV(Link, {\n                          className: \"mx-1 update-class\",\n                          to: \"/insurances-company/edit/\" + item.id,\n                          children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                            xmlns: \"http://www.w3.org/2000/svg\",\n                            fill: \"none\",\n                            viewBox: \"0 0 24 24\",\n                            strokeWidth: \"1.5\",\n                            stroke: \"currentColor\",\n                            className: \"w-5 h-5 bg-primary rounded p-1 text-white text-center text-xs\",\n                            children: /*#__PURE__*/_jsxDEV(\"path\", {\n                              strokeLinecap: \"round\",\n                              strokeLinejoin: \"round\",\n                              d: \"m16.862 4.487 1.687-1.688a1.875 1.875 0 1 1 2.652 2.652L10.582 16.07a4.5 4.5 0 0 1-1.897 1.13L6 18l.8-2.685a4.5 4.5 0 0 1 1.13-1.897l8.932-8.931Zm0 0L19.5 7.125M18 14v4.75A2.25 2.25 0 0 1 15.75 21H5.25A2.25 2.25 0 0 1 3 18.75V8.25A2.25 2.25 0 0 1 5.25 6H10\"\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 246,\n                              columnNumber: 33\n                            }, this)\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 238,\n                            columnNumber: 31\n                          }, this)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 234,\n                          columnNumber: 29\n                        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                          onClick: () => {\n                            setEventType(\"delete\");\n                            setInsuranceId(item.id);\n                            setIsDelete(true);\n                          },\n                          className: \"mx-1 delete-class cursor-pointer\",\n                          children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                            xmlns: \"http://www.w3.org/2000/svg\",\n                            fill: \"none\",\n                            viewBox: \"0 0 24 24\",\n                            \"stroke-width\": \"1.5\",\n                            stroke: \"currentColor\",\n                            className: \"w-5 h-5 bg-danger rounded p-1 text-white text-center text-xs\",\n                            children: /*#__PURE__*/_jsxDEV(\"path\", {\n                              \"stroke-linecap\": \"round\",\n                              \"stroke-linejoin\": \"round\",\n                              d: \"m20.25 7.5-.625 10.632a2.25 2.25 0 0 1-2.247 2.118H6.622a2.25 2.25 0 0 1-2.247-2.118L3.75 7.5m6 4.125 2.25 2.25m0 0 2.25 2.25M12 13.875l2.25-2.25M12 13.875l-2.25 2.25M3.375 7.5h17.25c.621 0 1.125-.504 1.125-1.125v-1.5c0-.621-.504-1.125-1.125-1.125H3.375c-.621 0-1.125.504-1.125 1.125v1.5c0 .621.504 1.125 1.125 1.125Z\"\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 269,\n                              columnNumber: 33\n                            }, this)\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 261,\n                            columnNumber: 31\n                          }, this)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 253,\n                          columnNumber: 29\n                        }, this), /*#__PURE__*/_jsxDEV(Link, {\n                          className: \"mx-1 profile-class\",\n                          to: \"/insurances-company/profile/\" + item.id,\n                          children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                            xmlns: \"http://www.w3.org/2000/svg\",\n                            fill: \"none\",\n                            viewBox: \"0 0 24 24\",\n                            \"stroke-width\": \"1.5\",\n                            stroke: \"currentColor\",\n                            className: \"w-5 h-5 bg-success rounded p-1 text-white text-center text-xs\",\n                            children: /*#__PURE__*/_jsxDEV(\"path\", {\n                              \"stroke-linecap\": \"round\",\n                              \"stroke-linejoin\": \"round\",\n                              d: \"M8.25 6.75h12M8.25 12h12m-12 5.25h12M3.75 6.75h.007v.008H3.75V6.75Zm.375 0a.375.375 0 1 1-.75 0 .375.375 0 0 1 .75 0ZM3.75 12h.007v.008H3.75V12Zm.375 0a.375.375 0 1 1-.75 0 .375.375 0 0 1 .75 0Zm-.375 5.25h.007v.008H3.75v-.008Zm.375 0a.375.375 0 1 1-.75 0 .375.375 0 0 1 .75 0Z\"\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 288,\n                              columnNumber: 33\n                            }, this)\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 280,\n                            columnNumber: 31\n                          }, this)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 276,\n                          columnNumber: 29\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 233,\n                        columnNumber: 27\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 232,\n                      columnNumber: 25\n                    }, this)]\n                  }, index, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 175,\n                    columnNumber: 23\n                  }, this);\n                })\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 173,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 146,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 145,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 139,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 138,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(ConfirmationModal, {\n        isOpen: isDelete,\n        message: eventType === \"delete\" ? \"Are you sure you want to delete this Insurance?\" : \"Are you sure ?\",\n        onConfirm: async () => {\n          if (eventType === \"cancel\") {\n            setIsDelete(false);\n            setEventType(\"\");\n            setLoadEvent(false);\n          } else if (eventType === \"delete\" && insuranceId !== \"\") {\n            setLoadEvent(true);\n            dispatch(deleteInsurance(insuranceId));\n            setIsDelete(false);\n            setEventType(\"\");\n            setLoadEvent(false);\n          } else {\n            setIsDelete(false);\n            setEventType(\"\");\n            setLoadEvent(false);\n          }\n        },\n        onCancel: () => {\n          setIsDelete(false);\n          setEventType(\"\");\n          setLoadEvent(false);\n        },\n        loadEvent: loadEvent\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 305,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"grid md:grid-cols-2 w-full container mt-5\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 336,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 69,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 68,\n    columnNumber: 5\n  }, this);\n}\n_s(InsurancesScreen, \"cABSHbJG1hO06h8Eh+4h3QP8kH8=\", false, function () {\n  return [useLocation, useNavigate, useSearchParams, useDispatch, useSelector, useSelector, useSelector];\n});\n_c = InsurancesScreen;\nexport default InsurancesScreen;\nvar _c;\n$RefreshReg$(_c, \"InsurancesScreen\");", "map": {"version": 3, "names": ["React", "useEffect", "useState", "useDispatch", "useSelector", "Link", "useLocation", "useNavigate", "useSearchParams", "deleteInsurance", "getInsuranesList", "DefaultLayout", "Loader", "<PERSON><PERSON>", "baseURLFile", "ConfirmationModal", "jsxDEV", "_jsxDEV", "InsurancesScreen", "_s", "location", "navigate", "searchParams", "page", "get", "dispatch", "isDelete", "setIsDelete", "loadEvent", "setLoadEvent", "eventType", "setEventType", "insuranceId", "setInsuranceId", "userLogin", "state", "userInfo", "listInsurances", "insuranceList", "insurances", "loadingInsurances", "errorInsurances", "pages", "insuranceDelete", "loadingInsuranceDelete", "errorInsuranceDelete", "successInsuranceDelete", "redirect", "role", "children", "className", "href", "xmlns", "fill", "viewBox", "stroke", "strokeLinecap", "strokeLinejoin", "d", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "type", "message", "map", "item", "index", "_item$assurance_name", "_item$assurance_count", "_item$assurance_email", "_item$assurance_phone", "id", "assurance_logo", "src", "alt", "assurance_name", "onError", "e", "target", "onerror", "assurance_country", "assurance_email", "assurance_phone", "to", "strokeWidth", "onClick", "isOpen", "onConfirm", "onCancel", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Desktop/Freelances/<PERSON><PERSON><PERSON>/UNIMEDCARE/web-front/src/screens/insurances/InsurancesScreen.js"], "sourcesContent": ["import React, { useEffect, useState } from \"react\";\nimport { useDispatch, useSelector } from \"react-redux\";\nimport {\n  Link,\n  useLocation,\n  useNavigate,\n  useSearchParams,\n} from \"react-router-dom\";\nimport {\n  deleteInsurance,\n  getInsuranesList,\n} from \"../../redux/actions/insuranceActions\";\nimport DefaultLayout from \"../../layouts/DefaultLayout\";\nimport Loader from \"../../components/Loader\";\nimport Alert from \"../../components/Alert\";\nimport { baseURLFile } from \"../../constants\";\nimport ConfirmationModal from \"../../components/ConfirmationModal\";\n\nfunction InsurancesScreen() {\n  const location = useLocation();\n  const navigate = useNavigate();\n  const [searchParams] = useSearchParams();\n  const page = searchParams.get(\"page\") || \"1\";\n  const dispatch = useDispatch();\n\n  const [isDelete, setIsDelete] = useState(false);\n  const [loadEvent, setLoadEvent] = useState(false);\n  const [eventType, setEventType] = useState(\"\");\n  const [insuranceId, setInsuranceId] = useState(\"\");\n\n  const userLogin = useSelector((state) => state.userLogin);\n  const { userInfo } = userLogin;\n\n  const listInsurances = useSelector((state) => state.insuranceList);\n  const { insurances, loadingInsurances, errorInsurances, pages } =\n    listInsurances;\n\n  const insuranceDelete = useSelector((state) => state.deleteInsurance);\n  const {\n    loadingInsuranceDelete,\n    errorInsuranceDelete,\n    successInsuranceDelete,\n  } = insuranceDelete;\n\n  const redirect = \"/\";\n  useEffect(() => {\n    if (!userInfo) {\n      navigate(redirect);\n    } else if (\n      userInfo.role !== \"1\" &&\n      userInfo.role !== 1 &&\n      userInfo.role !== \"2\" &&\n      userInfo.role !== 2\n    ) {\n      navigate(redirect);\n    } else {\n      dispatch(getInsuranesList(page));\n    }\n  }, [navigate, userInfo, dispatch, page]);\n\n  useEffect(() => {\n    if (successInsuranceDelete) {\n      dispatch(getInsuranesList(\"0\"));\n    }\n  }, [successInsuranceDelete]);\n\n  return (\n    <DefaultLayout>\n      <div>\n        <div className=\"flex flex-row text-sm items-center my-1\">\n          {/* home */}\n          <a href=\"/dashboard\">\n            <div className=\"flex flex-row  items-center hover:text-black \">\n              <svg\n                xmlns=\"http://www.w3.org/2000/svg\"\n                fill=\"none\"\n                viewBox=\"0 0 24 24\"\n                stroke-width=\"1.5\"\n                stroke=\"currentColor\"\n                className=\"w-4 h-4\"\n              >\n                <path\n                  strokeLinecap=\"round\"\n                  strokeLinejoin=\"round\"\n                  d=\"m2.25 12 8.954-8.955c.44-.439 1.152-.439 1.591 0L21.75 12M4.5 9.75v10.125c0 .621.504 1.125 1.125 1.125H9.75v-4.875c0-.621.504-1.125 1.125-1.125h2.25c.621 0 1.125.504 1.125 1.125V21h4.125c.621 0 1.125-.504 1.125-1.125V9.75M8.25 21h8.25\"\n                />\n              </svg>\n              <span className=\"mx-1\">Dashboard</span>\n            </div>\n          </a>\n          <span>\n            <svg\n              xmlns=\"http://www.w3.org/2000/svg\"\n              fill=\"none\"\n              viewBox=\"0 0 24 24\"\n              stroke-width=\"1.5\"\n              stroke=\"currentColor\"\n              className=\"w-4 h-4\"\n            >\n              <path\n                strokeLinecap=\"round\"\n                strokeLinejoin=\"round\"\n                d=\"m8.25 4.5 7.5 7.5-7.5 7.5\"\n              />\n            </svg>\n          </span>\n          <div className=\"\">Insurance Company</div>\n        </div>\n        {/*  */}\n        <div className=\"flex flex-row justify-between  items-center my-3\">\n          <div className=\"mx-1 font-bold text-black \">Insurances Company</div>\n\n          <div className=\"flex flex-row items-center justify-end\">\n            <a\n              href=\"/insurances-company/new-insurance\"\n              className=\"mx-2 flex flex-row bg-[#0388A6] text-white text-xs rounded-full px-5 py-3 items-center\"\n            >\n              <svg\n                xmlns=\"http://www.w3.org/2000/svg\"\n                fill=\"none\"\n                viewBox=\"0 0 24 24\"\n                stroke-width=\"1.5\"\n                stroke=\"currentColor\"\n                className=\"size-4 mx-1\"\n              >\n                <path\n                  stroke-linecap=\"round\"\n                  stroke-linejoin=\"round\"\n                  d=\"M12 4.5v15m7.5-7.5h-15\"\n                />\n              </svg>\n\n              <div>New Insurance</div>\n            </a>\n          </div>\n        </div>\n        {/*  */}\n        <div className=\"rounded-sm border border-stroke bg-white px-5 pt-6 pb-2.5 shadow-default   dark:bg-boxdark sm:px-7.5 xl:pb-1\">\n          <div className=\"container mx-auto flex flex-col\">\n            {loadingInsurances ? (\n              <Loader />\n            ) : errorInsurances ? (\n              <Alert type=\"error\" message={errorInsurances} />\n            ) : (\n              <div className=\"max-w-full overflow-x-auto \">\n                <table className=\"w-full table-auto\">\n                  <thead>\n                    <tr className=\" bg-[#F3F5FB] text-left \">\n                      <th className=\"min-w-[30px] py-4 px-4 font-bold text-[#8E8E8E] text-xs w-max\">\n                        #\n                      </th>\n                      <th className=\"min-w-[60px] py-4 px-4 font-bold text-[#8E8E8E] text-xs w-max \">\n                        Logo\n                      </th>\n                      <th className=\"min-w-[60px] py-4 px-4 font-bold text-[#8E8E8E] text-xs w-max \">\n                        Insurance Name\n                      </th>\n                      <th className=\"min-w-[120px] py-4 px-4 font-bold text-[#8E8E8E] text-xs w-max\">\n                        Country\n                      </th>\n                      <th className=\"min-w-[120px] py-4 px-4 font-bold text-[#8E8E8E] text-xs w-max\">\n                        Email\n                      </th>\n                      <th className=\"min-w-[120px] py-4 px-4 font-bold text-[#8E8E8E] text-xs w-max\">\n                        Phone\n                      </th>\n                      <th className=\"py-4 px-4 font-bold text-[#8E8E8E] text-xs w-max\">\n                        Operation\n                      </th>\n                    </tr>\n                  </thead>\n                  {/*  */}\n                  <tbody>\n                    {insurances?.map((item, index) => (\n                      <tr key={index}>\n                        <td className=\" py-3 px-4 min-w-[120px]  \">\n                          <p className=\"text-black  text-xs w-max  \">\n                            #{item.id}\n                          </p>\n                        </td>\n                        <td className=\" py-3 px-4 min-w-[120px]  \">\n                          <a\n                            href={\"/insurances-company/profile/\" + item.id}\n                            // href={baseURLFile + item.assurance_logo}\n                            className=\"text-black  text-xs w-max  \"\n                            // target=\"_blank\"\n                            // rel=\"noopener noreferrer\"\n                          >\n                            {item.assurance_logo ? (\n                              <img\n                                className=\"size-11 rounded\"\n                                src={baseURLFile + item.assurance_logo}\n                                alt={item.assurance_name}\n                                onError={(e) => {\n                                  e.target.onerror = null;\n                                  e.target.src = \"/assets/placeholder.png\";\n                                }}\n                              />\n                            ) : (\n                              <img\n                                className=\"size-11 rounded\"\n                                alt={item.assurance_name}\n                                src={\"/assets/placeholder.png\"}\n                                onError={(e) => {\n                                  e.target.onerror = null;\n                                  e.target.src = \"/assets/placeholder.png\";\n                                }}\n                              />\n                            )}\n                          </a>\n                        </td>\n                        <td className=\" py-3 px-4 min-w-[120px]  \">\n                          <p className=\"text-black  text-xs w-max  \">\n                            {item.assurance_name ?? \"---\"}\n                          </p>\n                        </td>\n                        <td className=\" py-3 px-4 min-w-[120px]  \">\n                          <p className=\"text-black  text-xs w-max  \">\n                            {item.assurance_country ?? \"---\"}\n                          </p>\n                        </td>\n                        <td className=\" py-3 px-4 min-w-[120px]  \">\n                          <p className=\"text-black  text-xs w-max  \">\n                            {item.assurance_email ?? \"---\"}\n                          </p>\n                        </td>\n                        <td className=\" py-3 px-4 min-w-[120px]  \">\n                          <p className=\"text-black  text-xs w-max  \">\n                            {item.assurance_phone ?? \"---\"}\n                          </p>\n                        </td>\n                        <td className=\" py-3 px-4 min-w-[120px]  \">\n                          <p className=\"text-black  text-xs w-max flex flex-row  \">\n                            <Link\n                              className=\"mx-1 update-class\"\n                              to={\"/insurances-company/edit/\" + item.id}\n                            >\n                              <svg\n                                xmlns=\"http://www.w3.org/2000/svg\"\n                                fill=\"none\"\n                                viewBox=\"0 0 24 24\"\n                                strokeWidth=\"1.5\"\n                                stroke=\"currentColor\"\n                                className=\"w-5 h-5 bg-primary rounded p-1 text-white text-center text-xs\"\n                              >\n                                <path\n                                  strokeLinecap=\"round\"\n                                  strokeLinejoin=\"round\"\n                                  d=\"m16.862 4.487 1.687-1.688a1.875 1.875 0 1 1 2.652 2.652L10.582 16.07a4.5 4.5 0 0 1-1.897 1.13L6 18l.8-2.685a4.5 4.5 0 0 1 1.13-1.897l8.932-8.931Zm0 0L19.5 7.125M18 14v4.75A2.25 2.25 0 0 1 15.75 21H5.25A2.25 2.25 0 0 1 3 18.75V8.25A2.25 2.25 0 0 1 5.25 6H10\"\n                                />\n                              </svg>\n                            </Link>\n                            <div\n                              onClick={() => {\n                                setEventType(\"delete\");\n                                setInsuranceId(item.id);\n                                setIsDelete(true);\n                              }}\n                              className=\"mx-1 delete-class cursor-pointer\"\n                            >\n                              <svg\n                                xmlns=\"http://www.w3.org/2000/svg\"\n                                fill=\"none\"\n                                viewBox=\"0 0 24 24\"\n                                stroke-width=\"1.5\"\n                                stroke=\"currentColor\"\n                                className=\"w-5 h-5 bg-danger rounded p-1 text-white text-center text-xs\"\n                              >\n                                <path\n                                  stroke-linecap=\"round\"\n                                  stroke-linejoin=\"round\"\n                                  d=\"m20.25 7.5-.625 10.632a2.25 2.25 0 0 1-2.247 2.118H6.622a2.25 2.25 0 0 1-2.247-2.118L3.75 7.5m6 4.125 2.25 2.25m0 0 2.25 2.25M12 13.875l2.25-2.25M12 13.875l-2.25 2.25M3.375 7.5h17.25c.621 0 1.125-.504 1.125-1.125v-1.5c0-.621-.504-1.125-1.125-1.125H3.375c-.621 0-1.125.504-1.125 1.125v1.5c0 .621.504 1.125 1.125 1.125Z\"\n                                />\n                              </svg>\n                            </div>\n                            <Link\n                              className=\"mx-1 profile-class\"\n                              to={\"/insurances-company/profile/\" + item.id}\n                            >\n                              <svg\n                                xmlns=\"http://www.w3.org/2000/svg\"\n                                fill=\"none\"\n                                viewBox=\"0 0 24 24\"\n                                stroke-width=\"1.5\"\n                                stroke=\"currentColor\"\n                                className=\"w-5 h-5 bg-success rounded p-1 text-white text-center text-xs\"\n                              >\n                                <path\n                                  stroke-linecap=\"round\"\n                                  stroke-linejoin=\"round\"\n                                  d=\"M8.25 6.75h12M8.25 12h12m-12 5.25h12M3.75 6.75h.007v.008H3.75V6.75Zm.375 0a.375.375 0 1 1-.75 0 .375.375 0 0 1 .75 0ZM3.75 12h.007v.008H3.75V12Zm.375 0a.375.375 0 1 1-.75 0 .375.375 0 0 1 .75 0Zm-.375 5.25h.007v.008H3.75v-.008Zm.375 0a.375.375 0 1 1-.75 0 .375.375 0 0 1 .75 0Z\"\n                                />\n                              </svg>\n                            </Link>\n                          </p>\n                        </td>\n                      </tr>\n                    ))}\n                  </tbody>\n                </table>\n              </div>\n            )}\n          </div>\n        </div>\n        <ConfirmationModal\n          isOpen={isDelete}\n          message={\n            eventType === \"delete\"\n              ? \"Are you sure you want to delete this Insurance?\"\n              : \"Are you sure ?\"\n          }\n          onConfirm={async () => {\n            if (eventType === \"cancel\") {\n              setIsDelete(false);\n              setEventType(\"\");\n              setLoadEvent(false);\n            } else if (eventType === \"delete\" && insuranceId !== \"\") {\n              setLoadEvent(true);\n              dispatch(deleteInsurance(insuranceId));\n              setIsDelete(false);\n              setEventType(\"\");\n              setLoadEvent(false);\n            } else {\n              setIsDelete(false);\n              setEventType(\"\");\n              setLoadEvent(false);\n            }\n          }}\n          onCancel={() => {\n            setIsDelete(false);\n            setEventType(\"\");\n            setLoadEvent(false);\n          }}\n          loadEvent={loadEvent}\n        />\n        <div className=\"grid md:grid-cols-2 w-full container mt-5\"></div>\n      </div>\n    </DefaultLayout>\n  );\n}\n\nexport default InsurancesScreen;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,SAAS,EAAEC,QAAQ,QAAQ,OAAO;AAClD,SAASC,WAAW,EAAEC,WAAW,QAAQ,aAAa;AACtD,SACEC,IAAI,EACJC,WAAW,EACXC,WAAW,EACXC,eAAe,QACV,kBAAkB;AACzB,SACEC,eAAe,EACfC,gBAAgB,QACX,sCAAsC;AAC7C,OAAOC,aAAa,MAAM,6BAA6B;AACvD,OAAOC,MAAM,MAAM,yBAAyB;AAC5C,OAAOC,KAAK,MAAM,wBAAwB;AAC1C,SAASC,WAAW,QAAQ,iBAAiB;AAC7C,OAAOC,iBAAiB,MAAM,oCAAoC;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEnE,SAASC,gBAAgBA,CAAA,EAAG;EAAAC,EAAA;EAC1B,MAAMC,QAAQ,GAAGd,WAAW,CAAC,CAAC;EAC9B,MAAMe,QAAQ,GAAGd,WAAW,CAAC,CAAC;EAC9B,MAAM,CAACe,YAAY,CAAC,GAAGd,eAAe,CAAC,CAAC;EACxC,MAAMe,IAAI,GAAGD,YAAY,CAACE,GAAG,CAAC,MAAM,CAAC,IAAI,GAAG;EAC5C,MAAMC,QAAQ,GAAGtB,WAAW,CAAC,CAAC;EAE9B,MAAM,CAACuB,QAAQ,EAAEC,WAAW,CAAC,GAAGzB,QAAQ,CAAC,KAAK,CAAC;EAC/C,MAAM,CAAC0B,SAAS,EAAEC,YAAY,CAAC,GAAG3B,QAAQ,CAAC,KAAK,CAAC;EACjD,MAAM,CAAC4B,SAAS,EAAEC,YAAY,CAAC,GAAG7B,QAAQ,CAAC,EAAE,CAAC;EAC9C,MAAM,CAAC8B,WAAW,EAAEC,cAAc,CAAC,GAAG/B,QAAQ,CAAC,EAAE,CAAC;EAElD,MAAMgC,SAAS,GAAG9B,WAAW,CAAE+B,KAAK,IAAKA,KAAK,CAACD,SAAS,CAAC;EACzD,MAAM;IAAEE;EAAS,CAAC,GAAGF,SAAS;EAE9B,MAAMG,cAAc,GAAGjC,WAAW,CAAE+B,KAAK,IAAKA,KAAK,CAACG,aAAa,CAAC;EAClE,MAAM;IAAEC,UAAU;IAAEC,iBAAiB;IAAEC,eAAe;IAAEC;EAAM,CAAC,GAC7DL,cAAc;EAEhB,MAAMM,eAAe,GAAGvC,WAAW,CAAE+B,KAAK,IAAKA,KAAK,CAAC1B,eAAe,CAAC;EACrE,MAAM;IACJmC,sBAAsB;IACtBC,oBAAoB;IACpBC;EACF,CAAC,GAAGH,eAAe;EAEnB,MAAMI,QAAQ,GAAG,GAAG;EACpB9C,SAAS,CAAC,MAAM;IACd,IAAI,CAACmC,QAAQ,EAAE;MACbf,QAAQ,CAAC0B,QAAQ,CAAC;IACpB,CAAC,MAAM,IACLX,QAAQ,CAACY,IAAI,KAAK,GAAG,IACrBZ,QAAQ,CAACY,IAAI,KAAK,CAAC,IACnBZ,QAAQ,CAACY,IAAI,KAAK,GAAG,IACrBZ,QAAQ,CAACY,IAAI,KAAK,CAAC,EACnB;MACA3B,QAAQ,CAAC0B,QAAQ,CAAC;IACpB,CAAC,MAAM;MACLtB,QAAQ,CAACf,gBAAgB,CAACa,IAAI,CAAC,CAAC;IAClC;EACF,CAAC,EAAE,CAACF,QAAQ,EAAEe,QAAQ,EAAEX,QAAQ,EAAEF,IAAI,CAAC,CAAC;EAExCtB,SAAS,CAAC,MAAM;IACd,IAAI6C,sBAAsB,EAAE;MAC1BrB,QAAQ,CAACf,gBAAgB,CAAC,GAAG,CAAC,CAAC;IACjC;EACF,CAAC,EAAE,CAACoC,sBAAsB,CAAC,CAAC;EAE5B,oBACE7B,OAAA,CAACN,aAAa;IAAAsC,QAAA,eACZhC,OAAA;MAAAgC,QAAA,gBACEhC,OAAA;QAAKiC,SAAS,EAAC,yCAAyC;QAAAD,QAAA,gBAEtDhC,OAAA;UAAGkC,IAAI,EAAC,YAAY;UAAAF,QAAA,eAClBhC,OAAA;YAAKiC,SAAS,EAAC,+CAA+C;YAAAD,QAAA,gBAC5DhC,OAAA;cACEmC,KAAK,EAAC,4BAA4B;cAClCC,IAAI,EAAC,MAAM;cACXC,OAAO,EAAC,WAAW;cACnB,gBAAa,KAAK;cAClBC,MAAM,EAAC,cAAc;cACrBL,SAAS,EAAC,SAAS;cAAAD,QAAA,eAEnBhC,OAAA;gBACEuC,aAAa,EAAC,OAAO;gBACrBC,cAAc,EAAC,OAAO;gBACtBC,CAAC,EAAC;cAA4O;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC/O;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eACN7C,OAAA;cAAMiC,SAAS,EAAC,MAAM;cAAAD,QAAA,EAAC;YAAS;cAAAU,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACpC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC,eACJ7C,OAAA;UAAAgC,QAAA,eACEhC,OAAA;YACEmC,KAAK,EAAC,4BAA4B;YAClCC,IAAI,EAAC,MAAM;YACXC,OAAO,EAAC,WAAW;YACnB,gBAAa,KAAK;YAClBC,MAAM,EAAC,cAAc;YACrBL,SAAS,EAAC,SAAS;YAAAD,QAAA,eAEnBhC,OAAA;cACEuC,aAAa,EAAC,OAAO;cACrBC,cAAc,EAAC,OAAO;cACtBC,CAAC,EAAC;YAA2B;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC9B;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC,eACP7C,OAAA;UAAKiC,SAAS,EAAC,EAAE;UAAAD,QAAA,EAAC;QAAiB;UAAAU,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACtC,CAAC,eAEN7C,OAAA;QAAKiC,SAAS,EAAC,kDAAkD;QAAAD,QAAA,gBAC/DhC,OAAA;UAAKiC,SAAS,EAAC,4BAA4B;UAAAD,QAAA,EAAC;QAAkB;UAAAU,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eAEpE7C,OAAA;UAAKiC,SAAS,EAAC,wCAAwC;UAAAD,QAAA,eACrDhC,OAAA;YACEkC,IAAI,EAAC,mCAAmC;YACxCD,SAAS,EAAC,wFAAwF;YAAAD,QAAA,gBAElGhC,OAAA;cACEmC,KAAK,EAAC,4BAA4B;cAClCC,IAAI,EAAC,MAAM;cACXC,OAAO,EAAC,WAAW;cACnB,gBAAa,KAAK;cAClBC,MAAM,EAAC,cAAc;cACrBL,SAAS,EAAC,aAAa;cAAAD,QAAA,eAEvBhC,OAAA;gBACE,kBAAe,OAAO;gBACtB,mBAAgB,OAAO;gBACvByC,CAAC,EAAC;cAAwB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC3B;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eAEN7C,OAAA;cAAAgC,QAAA,EAAK;YAAa;cAAAU,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACvB;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAEN7C,OAAA;QAAKiC,SAAS,EAAC,8GAA8G;QAAAD,QAAA,eAC3HhC,OAAA;UAAKiC,SAAS,EAAC,iCAAiC;UAAAD,QAAA,EAC7CT,iBAAiB,gBAChBvB,OAAA,CAACL,MAAM;YAAA+C,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,GACRrB,eAAe,gBACjBxB,OAAA,CAACJ,KAAK;YAACkD,IAAI,EAAC,OAAO;YAACC,OAAO,EAAEvB;UAAgB;YAAAkB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,gBAEhD7C,OAAA;YAAKiC,SAAS,EAAC,6BAA6B;YAAAD,QAAA,eAC1ChC,OAAA;cAAOiC,SAAS,EAAC,mBAAmB;cAAAD,QAAA,gBAClChC,OAAA;gBAAAgC,QAAA,eACEhC,OAAA;kBAAIiC,SAAS,EAAC,0BAA0B;kBAAAD,QAAA,gBACtChC,OAAA;oBAAIiC,SAAS,EAAC,+DAA+D;oBAAAD,QAAA,EAAC;kBAE9E;oBAAAU,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACL7C,OAAA;oBAAIiC,SAAS,EAAC,gEAAgE;oBAAAD,QAAA,EAAC;kBAE/E;oBAAAU,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACL7C,OAAA;oBAAIiC,SAAS,EAAC,gEAAgE;oBAAAD,QAAA,EAAC;kBAE/E;oBAAAU,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACL7C,OAAA;oBAAIiC,SAAS,EAAC,gEAAgE;oBAAAD,QAAA,EAAC;kBAE/E;oBAAAU,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACL7C,OAAA;oBAAIiC,SAAS,EAAC,gEAAgE;oBAAAD,QAAA,EAAC;kBAE/E;oBAAAU,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACL7C,OAAA;oBAAIiC,SAAS,EAAC,gEAAgE;oBAAAD,QAAA,EAAC;kBAE/E;oBAAAU,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACL7C,OAAA;oBAAIiC,SAAS,EAAC,kDAAkD;oBAAAD,QAAA,EAAC;kBAEjE;oBAAAU,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACA,CAAC,eAER7C,OAAA;gBAAAgC,QAAA,EACGV,UAAU,aAAVA,UAAU,uBAAVA,UAAU,CAAE0B,GAAG,CAAC,CAACC,IAAI,EAAEC,KAAK;kBAAA,IAAAC,oBAAA,EAAAC,qBAAA,EAAAC,qBAAA,EAAAC,qBAAA;kBAAA,oBAC3BtD,OAAA;oBAAAgC,QAAA,gBACEhC,OAAA;sBAAIiC,SAAS,EAAC,4BAA4B;sBAAAD,QAAA,eACxChC,OAAA;wBAAGiC,SAAS,EAAC,6BAA6B;wBAAAD,QAAA,GAAC,GACxC,EAACiB,IAAI,CAACM,EAAE;sBAAA;wBAAAb,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACR;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACF,CAAC,eACL7C,OAAA;sBAAIiC,SAAS,EAAC,4BAA4B;sBAAAD,QAAA,eACxChC,OAAA;wBACEkC,IAAI,EAAE,8BAA8B,GAAGe,IAAI,CAACM;wBAC5C;wBAAA;wBACAtB,SAAS,EAAC;wBACV;wBACA;wBAAA;wBAAAD,QAAA,EAECiB,IAAI,CAACO,cAAc,gBAClBxD,OAAA;0BACEiC,SAAS,EAAC,iBAAiB;0BAC3BwB,GAAG,EAAE5D,WAAW,GAAGoD,IAAI,CAACO,cAAe;0BACvCE,GAAG,EAAET,IAAI,CAACU,cAAe;0BACzBC,OAAO,EAAGC,CAAC,IAAK;4BACdA,CAAC,CAACC,MAAM,CAACC,OAAO,GAAG,IAAI;4BACvBF,CAAC,CAACC,MAAM,CAACL,GAAG,GAAG,yBAAyB;0BAC1C;wBAAE;0BAAAf,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACH,CAAC,gBAEF7C,OAAA;0BACEiC,SAAS,EAAC,iBAAiB;0BAC3ByB,GAAG,EAAET,IAAI,CAACU,cAAe;0BACzBF,GAAG,EAAE,yBAA0B;0BAC/BG,OAAO,EAAGC,CAAC,IAAK;4BACdA,CAAC,CAACC,MAAM,CAACC,OAAO,GAAG,IAAI;4BACvBF,CAAC,CAACC,MAAM,CAACL,GAAG,GAAG,yBAAyB;0BAC1C;wBAAE;0BAAAf,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACH;sBACF;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACA;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACF,CAAC,eACL7C,OAAA;sBAAIiC,SAAS,EAAC,4BAA4B;sBAAAD,QAAA,eACxChC,OAAA;wBAAGiC,SAAS,EAAC,6BAA6B;wBAAAD,QAAA,GAAAmB,oBAAA,GACvCF,IAAI,CAACU,cAAc,cAAAR,oBAAA,cAAAA,oBAAA,GAAI;sBAAK;wBAAAT,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAC5B;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACF,CAAC,eACL7C,OAAA;sBAAIiC,SAAS,EAAC,4BAA4B;sBAAAD,QAAA,eACxChC,OAAA;wBAAGiC,SAAS,EAAC,6BAA6B;wBAAAD,QAAA,GAAAoB,qBAAA,GACvCH,IAAI,CAACe,iBAAiB,cAAAZ,qBAAA,cAAAA,qBAAA,GAAI;sBAAK;wBAAAV,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAC/B;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACF,CAAC,eACL7C,OAAA;sBAAIiC,SAAS,EAAC,4BAA4B;sBAAAD,QAAA,eACxChC,OAAA;wBAAGiC,SAAS,EAAC,6BAA6B;wBAAAD,QAAA,GAAAqB,qBAAA,GACvCJ,IAAI,CAACgB,eAAe,cAAAZ,qBAAA,cAAAA,qBAAA,GAAI;sBAAK;wBAAAX,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAC7B;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACF,CAAC,eACL7C,OAAA;sBAAIiC,SAAS,EAAC,4BAA4B;sBAAAD,QAAA,eACxChC,OAAA;wBAAGiC,SAAS,EAAC,6BAA6B;wBAAAD,QAAA,GAAAsB,qBAAA,GACvCL,IAAI,CAACiB,eAAe,cAAAZ,qBAAA,cAAAA,qBAAA,GAAI;sBAAK;wBAAAZ,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAC7B;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACF,CAAC,eACL7C,OAAA;sBAAIiC,SAAS,EAAC,4BAA4B;sBAAAD,QAAA,eACxChC,OAAA;wBAAGiC,SAAS,EAAC,2CAA2C;wBAAAD,QAAA,gBACtDhC,OAAA,CAACZ,IAAI;0BACH6C,SAAS,EAAC,mBAAmB;0BAC7BkC,EAAE,EAAE,2BAA2B,GAAGlB,IAAI,CAACM,EAAG;0BAAAvB,QAAA,eAE1ChC,OAAA;4BACEmC,KAAK,EAAC,4BAA4B;4BAClCC,IAAI,EAAC,MAAM;4BACXC,OAAO,EAAC,WAAW;4BACnB+B,WAAW,EAAC,KAAK;4BACjB9B,MAAM,EAAC,cAAc;4BACrBL,SAAS,EAAC,+DAA+D;4BAAAD,QAAA,eAEzEhC,OAAA;8BACEuC,aAAa,EAAC,OAAO;8BACrBC,cAAc,EAAC,OAAO;8BACtBC,CAAC,EAAC;4BAAkQ;8BAAAC,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OACrQ;0BAAC;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACC;wBAAC;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACF,CAAC,eACP7C,OAAA;0BACEqE,OAAO,EAAEA,CAAA,KAAM;4BACbvD,YAAY,CAAC,QAAQ,CAAC;4BACtBE,cAAc,CAACiC,IAAI,CAACM,EAAE,CAAC;4BACvB7C,WAAW,CAAC,IAAI,CAAC;0BACnB,CAAE;0BACFuB,SAAS,EAAC,kCAAkC;0BAAAD,QAAA,eAE5ChC,OAAA;4BACEmC,KAAK,EAAC,4BAA4B;4BAClCC,IAAI,EAAC,MAAM;4BACXC,OAAO,EAAC,WAAW;4BACnB,gBAAa,KAAK;4BAClBC,MAAM,EAAC,cAAc;4BACrBL,SAAS,EAAC,8DAA8D;4BAAAD,QAAA,eAExEhC,OAAA;8BACE,kBAAe,OAAO;8BACtB,mBAAgB,OAAO;8BACvByC,CAAC,EAAC;4BAA+T;8BAAAC,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAClU;0BAAC;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACC;wBAAC;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACH,CAAC,eACN7C,OAAA,CAACZ,IAAI;0BACH6C,SAAS,EAAC,oBAAoB;0BAC9BkC,EAAE,EAAE,8BAA8B,GAAGlB,IAAI,CAACM,EAAG;0BAAAvB,QAAA,eAE7ChC,OAAA;4BACEmC,KAAK,EAAC,4BAA4B;4BAClCC,IAAI,EAAC,MAAM;4BACXC,OAAO,EAAC,WAAW;4BACnB,gBAAa,KAAK;4BAClBC,MAAM,EAAC,cAAc;4BACrBL,SAAS,EAAC,+DAA+D;4BAAAD,QAAA,eAEzEhC,OAAA;8BACE,kBAAe,OAAO;8BACtB,mBAAgB,OAAO;8BACvByC,CAAC,EAAC;4BAAuR;8BAAAC,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAC1R;0BAAC;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACC;wBAAC;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACF,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACN;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACF,CAAC;kBAAA,GAzHEK,KAAK;oBAAAR,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OA0HV,CAAC;gBAAA,CACN;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL;QACN;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eACN7C,OAAA,CAACF,iBAAiB;QAChBwE,MAAM,EAAE7D,QAAS;QACjBsC,OAAO,EACLlC,SAAS,KAAK,QAAQ,GAClB,iDAAiD,GACjD,gBACL;QACD0D,SAAS,EAAE,MAAAA,CAAA,KAAY;UACrB,IAAI1D,SAAS,KAAK,QAAQ,EAAE;YAC1BH,WAAW,CAAC,KAAK,CAAC;YAClBI,YAAY,CAAC,EAAE,CAAC;YAChBF,YAAY,CAAC,KAAK,CAAC;UACrB,CAAC,MAAM,IAAIC,SAAS,KAAK,QAAQ,IAAIE,WAAW,KAAK,EAAE,EAAE;YACvDH,YAAY,CAAC,IAAI,CAAC;YAClBJ,QAAQ,CAAChB,eAAe,CAACuB,WAAW,CAAC,CAAC;YACtCL,WAAW,CAAC,KAAK,CAAC;YAClBI,YAAY,CAAC,EAAE,CAAC;YAChBF,YAAY,CAAC,KAAK,CAAC;UACrB,CAAC,MAAM;YACLF,WAAW,CAAC,KAAK,CAAC;YAClBI,YAAY,CAAC,EAAE,CAAC;YAChBF,YAAY,CAAC,KAAK,CAAC;UACrB;QACF,CAAE;QACF4D,QAAQ,EAAEA,CAAA,KAAM;UACd9D,WAAW,CAAC,KAAK,CAAC;UAClBI,YAAY,CAAC,EAAE,CAAC;UAChBF,YAAY,CAAC,KAAK,CAAC;QACrB,CAAE;QACFD,SAAS,EAAEA;MAAU;QAAA+B,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACtB,CAAC,eACF7C,OAAA;QAAKiC,SAAS,EAAC;MAA2C;QAAAS,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC9D;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACO,CAAC;AAEpB;AAAC3C,EAAA,CAjUQD,gBAAgB;EAAA,QACNZ,WAAW,EACXC,WAAW,EACLC,eAAe,EAErBL,WAAW,EAOVC,WAAW,EAGNA,WAAW,EAIVA,WAAW;AAAA;AAAAsF,EAAA,GAnB5BxE,gBAAgB;AAmUzB,eAAeA,gBAAgB;AAAC,IAAAwE,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}