{"ast": null, "code": "import axios from \"../../axios\";\nimport { PROVIDER_LIST_REQUEST, PROVIDER_LIST_SUCCESS, PROVIDER_LIST_FAIL,\n//\nPROVIDER_ADD_REQUEST, PROVIDER_ADD_SUCCESS, PROVIDER_ADD_FAIL,\n//\nPROVIDER_DETAIL_REQUEST, PROVIDER_DETAIL_SUCCESS, PROVIDER_DETAIL_FAIL,\n//\nPROVIDER_UPDATE_REQUEST, PROVIDER_UPDATE_SUCCESS, PROVIDER_UPDATE_FAIL,\n//\nPROVIDER_DELETE_REQUEST, PROVIDER_DELETE_SUCCESS, PROVIDER_DELETE_FAIL\n//\n} from \"../constants/providerConstants\";\nexport const updateProvider = (id, provider) => async (dispatch, getState) => {\n  try {\n    dispatch({\n      type: PROVIDER_UPDATE_REQUEST\n    });\n    var {\n      userLogin: {\n        userInfo\n      }\n    } = getState();\n    const config = {\n      headers: {\n        \"Content-Type\": \"application/json\",\n        Authorization: `Bearer ${userInfo.access}`\n      }\n    };\n    const {\n      data\n    } = await axios.put(`/providers/update/${id}/`, provider, config);\n    dispatch({\n      type: PROVIDER_UPDATE_SUCCESS,\n      payload: data\n    });\n  } catch (error) {\n    var err = error.response && error.response.data.detail ? error.response.data.detail : error.detail;\n    if (err) {\n      if (err === \"Given token not valid for any token type\") {\n        localStorage.removeItem(\"userInfoUnimedCare\");\n        document.location.href = \"/\";\n      }\n    }\n    dispatch({\n      type: PROVIDER_UPDATE_FAIL,\n      payload: error.response && error.response.data.detail ? error.response.data.detail : error.detail\n    });\n  }\n};\n\n// delete provider\nexport const deleteProvider = id => async (dispatch, getState) => {\n  try {\n    dispatch({\n      type: PROVIDER_DELETE_REQUEST\n    });\n    var {\n      userLogin: {\n        userInfo\n      }\n    } = getState();\n    const config = {\n      headers: {\n        \"Content-Type\": \"multipart/form-data\",\n        Authorization: `Bearer ${userInfo.access}`\n      }\n    };\n    const {\n      data\n    } = await axios.delete(`/providers/delete/${id}/`, config);\n    dispatch({\n      type: PROVIDER_DELETE_SUCCESS,\n      payload: data\n    });\n  } catch (error) {\n    var err = error.response && error.response.data.detail ? error.response.data.detail : error.detail;\n    if (err) {\n      if (err === \"Given token not valid for any token type\") {\n        localStorage.removeItem(\"userInfoUnimedCare\");\n        document.location.href = \"/\";\n      }\n    }\n    dispatch({\n      type: PROVIDER_DELETE_FAIL,\n      payload: error.response && error.response.data.detail ? error.response.data.detail : error.detail\n    });\n  }\n};\n\n// create provider\nexport const createNewProvider = provider => async (dispatch, getState) => {\n  try {\n    dispatch({\n      type: PROVIDER_ADD_REQUEST\n    });\n    var {\n      userLogin: {\n        userInfo\n      }\n    } = getState();\n    const config = {\n      headers: {\n        \"Content-Type\": \"application/json\",\n        Authorization: `Bearer ${userInfo.access}`\n      }\n    };\n    const {\n      data\n    } = await axios.post(`/providers/create-new-provider/`, provider, config);\n    dispatch({\n      type: PROVIDER_ADD_SUCCESS,\n      payload: data\n    });\n  } catch (error) {\n    var err = error.response && error.response.data.detail ? error.response.data.detail : error.detail;\n    if (err) {\n      if (err === \"Given token not valid for any token type\") {\n        localStorage.removeItem(\"userInfoUnimedCare\");\n        document.location.href = \"/\";\n      }\n    }\n    dispatch({\n      type: PROVIDER_ADD_FAIL,\n      payload: error.response && error.response.data.detail ? error.response.data.detail : error.detail\n    });\n  }\n};\n\n// detail case\nexport const detailProvider = id => async (dispatch, getState) => {\n  try {\n    dispatch({\n      type: PROVIDER_DETAIL_REQUEST\n    });\n    var {\n      userLogin: {\n        userInfo\n      }\n    } = getState();\n    const config = {\n      headers: {\n        \"Content-Type\": \"application/json\",\n        Authorization: `Bearer ${userInfo.access}`\n      }\n    };\n    const {\n      data\n    } = await axios.get(`/providers/detail/${id}/`, config);\n    dispatch({\n      type: PROVIDER_DETAIL_SUCCESS,\n      payload: data\n    });\n  } catch (error) {\n    var err = error.response && error.response.data.detail ? error.response.data.detail : error.detail;\n    if (err) {\n      if (err === \"Given token not valid for any token type\") {\n        localStorage.removeItem(\"userInfoUnimedCare\");\n        document.location.href = \"/\";\n      }\n    }\n    dispatch({\n      type: PROVIDER_DETAIL_FAIL,\n      payload: error.response && error.response.data.detail ? error.response.data.detail : error.detail\n    });\n  }\n};\n\n// get list cases\nexport const providersList = (page, filterName = \"\", filterType = \"\", filterCity = \"\", filterCountry = \"\", range_max = \"\", location_x = \"\", location_y = \"\") => async (dispatch, getState) => {\n  try {\n    // Dispatch request action\n    dispatch({\n      type: PROVIDER_LIST_REQUEST\n    });\n\n    // Get user info from state\n    var {\n      userLogin: {\n        userInfo\n      }\n    } = getState();\n\n    // Configure request\n    const config = {\n      headers: {\n        \"Content-Type\": \"application/json\",\n        Authorization: `Bearer ${userInfo.access}`\n      },\n      // Set a longer timeout specifically for this request\n      timeout: 60000 // 60 seconds timeout\n    };\n    console.log(\"Fetching providers data...\");\n\n    // Create a promise with timeout\n    const fetchPromise = axios.get(`/providers/?page=${page}&filtername=${filterName}&filtertype=${filterType}&filtercity=${filterCity}&filtercountry=${filterCountry}&rangemax=${range_max}&locationx=${location_x}&locationy=${location_y}`, config);\n\n    // Set a timeout promise\n    const timeoutPromise = new Promise((_, reject) => {\n      setTimeout(() => {\n        reject(new Error(\"Request timed out after 60 seconds\"));\n      }, 60000);\n    });\n\n    // Race between fetch and timeout\n    const {\n      data\n    } = await Promise.race([fetchPromise, timeoutPromise]);\n    console.log(\"Providers data fetched successfully!\");\n\n    // Dispatch success action\n    dispatch({\n      type: PROVIDER_LIST_SUCCESS,\n      payload: data\n    });\n    return data; // Return data for Promise chaining\n  } catch (error) {\n    console.error(\"Error fetching providers:\", error);\n\n    // Handle token validation error\n    var err = error.response && error.response.data.detail ? error.response.data.detail : error.detail || error.message || \"Unknown error\";\n    if (err) {\n      if (err === \"Given token not valid for any token type\") {\n        localStorage.removeItem(\"userInfoUnimedCare\");\n        document.location.href = \"/\";\n      }\n    }\n\n    // Dispatch failure action\n    dispatch({\n      type: PROVIDER_LIST_FAIL,\n      payload: err\n    });\n\n    // Return empty array as fallback data\n    return {\n      results: []\n    };\n  }\n};\n\n// Optimized get list providers for Dashboard (lightweight)\nexport const providersListDashboard = (page = \"0\") => async (dispatch, getState) => {\n  try {\n    // Dispatch request action\n    dispatch({\n      type: PROVIDER_LIST_REQUEST\n    });\n\n    // Get user info from state\n    var {\n      userLogin: {\n        userInfo\n      }\n    } = getState();\n\n    // Configure request\n    const config = {\n      headers: {\n        \"Content-Type\": \"application/json\",\n        Authorization: `Bearer ${userInfo.access}`\n      }\n    };\n\n    // Fetch only essential provider data for dashboard dropdown\n    const {\n      data\n    } = await axios.get(`/providers/?page=${page}&isdashboard=true`, config);\n\n    // Dispatch success action\n    dispatch({\n      type: PROVIDER_LIST_SUCCESS,\n      payload: data\n    });\n    return data;\n  } catch (error) {\n    var err = error.response && error.response.data.detail ? error.response.data.detail : error.message || \"An error occurred while fetching providers\";\n    if (err === \"Given token not valid for any token type\") {\n      localStorage.removeItem(\"userInfoUnimedCare\");\n      document.location.href = \"/\";\n    }\n    dispatch({\n      type: PROVIDER_LIST_FAIL,\n      payload: err\n    });\n    return {\n      results: []\n    };\n  }\n};\n\n// Optimized get list providers for EditCaseScreen (includes services)\nexport const providersListEditCase = (page = \"0\") => async (dispatch, getState) => {\n  try {\n    // Dispatch request action\n    dispatch({\n      type: PROVIDER_LIST_REQUEST\n    });\n\n    // Get user info from state\n    var {\n      userLogin: {\n        userInfo\n      }\n    } = getState();\n\n    // Configure request\n    const config = {\n      headers: {\n        \"Content-Type\": \"application/json\",\n        Authorization: `Bearer ${userInfo.access}`\n      }\n    };\n\n    // Fetch provider data with services for EditCaseScreen\n    const {\n      data\n    } = await axios.get(`/providers/?page=${page}&iseditcase=true`, config);\n\n    // Dispatch success action\n    dispatch({\n      type: PROVIDER_LIST_SUCCESS,\n      payload: data\n    });\n    return data;\n  } catch (error) {\n    var err = error.response && error.response.data.detail ? error.response.data.detail : error.message || \"An error occurred while fetching providers\";\n    if (err === \"Given token not valid for any token type\") {\n      localStorage.removeItem(\"userInfoUnimedCare\");\n      document.location.href = \"/\";\n    }\n    dispatch({\n      type: PROVIDER_LIST_FAIL,\n      payload: err\n    });\n    return {\n      providers: []\n    };\n  }\n};\n\n// Optimized get list providers for ProvidersMapScreen (map display)\nexport const providersListMapScreen = (page, filterName = \"\", filterType = \"\", filterCity = \"\", filterCountry = \"\", range_max = \"\", location_x = \"\", location_y = \"\") => async (dispatch, getState) => {\n  try {\n    // Dispatch request action\n    dispatch({\n      type: PROVIDER_LIST_REQUEST\n    });\n\n    // Get user info from state\n    var {\n      userLogin: {\n        userInfo\n      }\n    } = getState();\n\n    // Configure request\n    const config = {\n      headers: {\n        \"Content-Type\": \"application/json\",\n        Authorization: `Bearer ${userInfo.access}`\n      },\n      // Set a longer timeout specifically for this request\n      timeout: 60000 // 60 seconds timeout\n    };\n    console.log(\"Fetching optimized providers data for map screen...\");\n\n    // Create a promise with timeout\n    const fetchPromise = axios.get(`/providers/?page=${page}&filtername=${filterName}&filtertype=${filterType}&filtercity=${filterCity}&filtercountry=${filterCountry}&rangemax=${range_max}&locationx=${location_x}&locationy=${location_y}&ismapscreen=true`, config);\n\n    // Set a timeout promise\n    const timeoutPromise = new Promise((_, reject) => {\n      setTimeout(() => {\n        reject(new Error(\"Request timed out after 60 seconds\"));\n      }, 60000);\n    });\n\n    // Race between fetch and timeout\n    const {\n      data\n    } = await Promise.race([fetchPromise, timeoutPromise]);\n    console.log(\"Optimized providers data fetched successfully!\");\n\n    // Dispatch success action\n    dispatch({\n      type: PROVIDER_LIST_SUCCESS,\n      payload: data\n    });\n    return data; // Return data for Promise chaining\n  } catch (error) {\n    console.error(\"Error fetching providers for map screen:\", error);\n\n    // Handle token validation error\n    var err = error.response && error.response.data.detail ? error.response.data.detail : error.detail || error.message || \"Unknown error\";\n    if (err) {\n      if (err === \"Given token not valid for any token type\") {\n        localStorage.removeItem(\"userInfoUnimedCare\");\n        document.location.href = \"/\";\n      }\n    }\n\n    // Dispatch failure action\n    dispatch({\n      type: PROVIDER_LIST_FAIL,\n      payload: err\n    });\n\n    // Return empty array as fallback data\n    return {\n      results: []\n    };\n  }\n};", "map": {"version": 3, "names": ["axios", "PROVIDER_LIST_REQUEST", "PROVIDER_LIST_SUCCESS", "PROVIDER_LIST_FAIL", "PROVIDER_ADD_REQUEST", "PROVIDER_ADD_SUCCESS", "PROVIDER_ADD_FAIL", "PROVIDER_DETAIL_REQUEST", "PROVIDER_DETAIL_SUCCESS", "PROVIDER_DETAIL_FAIL", "PROVIDER_UPDATE_REQUEST", "PROVIDER_UPDATE_SUCCESS", "PROVIDER_UPDATE_FAIL", "PROVIDER_DELETE_REQUEST", "PROVIDER_DELETE_SUCCESS", "PROVIDER_DELETE_FAIL", "updateProvider", "id", "provider", "dispatch", "getState", "type", "userLogin", "userInfo", "config", "headers", "Authorization", "access", "data", "put", "payload", "error", "err", "response", "detail", "localStorage", "removeItem", "document", "location", "href", "deleteProvider", "delete", "createNewProvider", "post", "detail<PERSON>rovider", "get", "providersList", "page", "filterName", "filterType", "filterCity", "filterCountry", "range_max", "location_x", "location_y", "timeout", "console", "log", "fetchPromise", "timeoutPromise", "Promise", "_", "reject", "setTimeout", "Error", "race", "message", "results", "providersListDashboard", "providersListEditCase", "providers", "providersListMapScreen"], "sources": ["/Users/<USER>/Desktop/Freelances/<PERSON><PERSON><PERSON>/UNIMEDCARE/web-front/src/redux/actions/providerActions.js"], "sourcesContent": ["import axios from \"../../axios\";\nimport {\n  PROVIDER_LIST_REQUEST,\n  PROVIDER_LIST_SUCCESS,\n  PROVIDER_LIST_FAIL,\n  //\n  PROVIDER_ADD_REQUEST,\n  PROVIDER_ADD_SUCCESS,\n  PROVIDER_ADD_FAIL,\n  //\n  PROVIDER_DETAIL_REQUEST,\n  PROVIDER_DETAIL_SUCCESS,\n  PROVIDER_DETAIL_FAIL,\n  //\n  PROVIDER_UPDATE_REQUEST,\n  PROVIDER_UPDATE_SUCCESS,\n  PROVIDER_UPDATE_FAIL,\n  //\n  PROVIDER_DELETE_REQUEST,\n  PROVIDER_DELETE_SUCCESS,\n  PROVIDER_DELETE_FAIL,\n  //\n} from \"../constants/providerConstants\";\n\nexport const updateProvider = (id, provider) => async (dispatch, getState) => {\n  try {\n    dispatch({\n      type: PROVIDER_UPDATE_REQUEST,\n    });\n    var {\n      userLogin: { userInfo },\n    } = getState();\n    const config = {\n      headers: {\n        \"Content-Type\": \"application/json\",\n        Authorization: `Bearer ${userInfo.access}`,\n      },\n    };\n    const { data } = await axios.put(\n      `/providers/update/${id}/`,\n      provider,\n      config\n    );\n\n    dispatch({\n      type: PROVIDER_UPDATE_SUCCESS,\n      payload: data,\n    });\n  } catch (error) {\n    var err =\n      error.response && error.response.data.detail\n        ? error.response.data.detail\n        : error.detail;\n    if (err) {\n      if (err === \"Given token not valid for any token type\") {\n        localStorage.removeItem(\"userInfoUnimedCare\");\n        document.location.href = \"/\";\n      }\n    }\n    dispatch({\n      type: PROVIDER_UPDATE_FAIL,\n      payload:\n        error.response && error.response.data.detail\n          ? error.response.data.detail\n          : error.detail,\n    });\n  }\n};\n\n// delete provider\nexport const deleteProvider = (id) => async (dispatch, getState) => {\n  try {\n    dispatch({\n      type: PROVIDER_DELETE_REQUEST,\n    });\n    var {\n      userLogin: { userInfo },\n    } = getState();\n    const config = {\n      headers: {\n        \"Content-Type\": \"multipart/form-data\",\n        Authorization: `Bearer ${userInfo.access}`,\n      },\n    };\n    const { data } = await axios.delete(`/providers/delete/${id}/`, config);\n\n    dispatch({\n      type: PROVIDER_DELETE_SUCCESS,\n      payload: data,\n    });\n  } catch (error) {\n    var err =\n      error.response && error.response.data.detail\n        ? error.response.data.detail\n        : error.detail;\n    if (err) {\n      if (err === \"Given token not valid for any token type\") {\n        localStorage.removeItem(\"userInfoUnimedCare\");\n        document.location.href = \"/\";\n      }\n    }\n    dispatch({\n      type: PROVIDER_DELETE_FAIL,\n      payload:\n        error.response && error.response.data.detail\n          ? error.response.data.detail\n          : error.detail,\n    });\n  }\n};\n\n// create provider\nexport const createNewProvider = (provider) => async (dispatch, getState) => {\n  try {\n    dispatch({\n      type: PROVIDER_ADD_REQUEST,\n    });\n    var {\n      userLogin: { userInfo },\n    } = getState();\n    const config = {\n      headers: {\n        \"Content-Type\": \"application/json\",\n        Authorization: `Bearer ${userInfo.access}`,\n      },\n    };\n    const { data } = await axios.post(\n      `/providers/create-new-provider/`,\n      provider,\n      config\n    );\n\n    dispatch({\n      type: PROVIDER_ADD_SUCCESS,\n      payload: data,\n    });\n  } catch (error) {\n    var err =\n      error.response && error.response.data.detail\n        ? error.response.data.detail\n        : error.detail;\n    if (err) {\n      if (err === \"Given token not valid for any token type\") {\n        localStorage.removeItem(\"userInfoUnimedCare\");\n        document.location.href = \"/\";\n      }\n    }\n    dispatch({\n      type: PROVIDER_ADD_FAIL,\n      payload:\n        error.response && error.response.data.detail\n          ? error.response.data.detail\n          : error.detail,\n    });\n  }\n};\n\n// detail case\nexport const detailProvider = (id) => async (dispatch, getState) => {\n  try {\n    dispatch({\n      type: PROVIDER_DETAIL_REQUEST,\n    });\n    var {\n      userLogin: { userInfo },\n    } = getState();\n    const config = {\n      headers: {\n        \"Content-Type\": \"application/json\",\n        Authorization: `Bearer ${userInfo.access}`,\n      },\n    };\n    const { data } = await axios.get(`/providers/detail/${id}/`, config);\n\n    dispatch({\n      type: PROVIDER_DETAIL_SUCCESS,\n      payload: data,\n    });\n  } catch (error) {\n    var err =\n      error.response && error.response.data.detail\n        ? error.response.data.detail\n        : error.detail;\n    if (err) {\n      if (err === \"Given token not valid for any token type\") {\n        localStorage.removeItem(\"userInfoUnimedCare\");\n        document.location.href = \"/\";\n      }\n    }\n    dispatch({\n      type: PROVIDER_DETAIL_FAIL,\n      payload:\n        error.response && error.response.data.detail\n          ? error.response.data.detail\n          : error.detail,\n    });\n  }\n};\n\n// get list cases\nexport const providersList =\n  (\n    page,\n    filterName = \"\",\n    filterType = \"\",\n    filterCity = \"\",\n    filterCountry = \"\",\n    range_max = \"\",\n    location_x = \"\",\n    location_y = \"\"\n  ) =>\n  async (dispatch, getState) => {\n    try {\n      // Dispatch request action\n      dispatch({\n        type: PROVIDER_LIST_REQUEST,\n      });\n\n      // Get user info from state\n      var {\n        userLogin: { userInfo },\n      } = getState();\n\n      // Configure request\n      const config = {\n        headers: {\n          \"Content-Type\": \"application/json\",\n          Authorization: `Bearer ${userInfo.access}`,\n        },\n        // Set a longer timeout specifically for this request\n        timeout: 60000, // 60 seconds timeout\n      };\n\n      console.log(\"Fetching providers data...\");\n\n      // Create a promise with timeout\n      const fetchPromise = axios.get(\n        `/providers/?page=${page}&filtername=${filterName}&filtertype=${filterType}&filtercity=${filterCity}&filtercountry=${filterCountry}&rangemax=${range_max}&locationx=${location_x}&locationy=${location_y}`,\n        config\n      );\n\n      // Set a timeout promise\n      const timeoutPromise = new Promise((_, reject) => {\n        setTimeout(() => {\n          reject(new Error(\"Request timed out after 60 seconds\"));\n        }, 60000);\n      });\n\n      // Race between fetch and timeout\n      const { data } = await Promise.race([fetchPromise, timeoutPromise]);\n\n      console.log(\"Providers data fetched successfully!\");\n\n      // Dispatch success action\n      dispatch({\n        type: PROVIDER_LIST_SUCCESS,\n        payload: data,\n      });\n\n      return data; // Return data for Promise chaining\n    } catch (error) {\n      console.error(\"Error fetching providers:\", error);\n\n      // Handle token validation error\n      var err =\n        error.response && error.response.data.detail\n          ? error.response.data.detail\n          : (error.detail || error.message || \"Unknown error\");\n\n      if (err) {\n        if (err === \"Given token not valid for any token type\") {\n          localStorage.removeItem(\"userInfoUnimedCare\");\n          document.location.href = \"/\";\n        }\n      }\n\n      // Dispatch failure action\n      dispatch({\n        type: PROVIDER_LIST_FAIL,\n        payload: err,\n      });\n\n      // Return empty array as fallback data\n      return { results: [] };\n    }\n  };\n\n// Optimized get list providers for Dashboard (lightweight)\nexport const providersListDashboard = (page = \"0\") => async (dispatch, getState) => {\n  try {\n    // Dispatch request action\n    dispatch({\n      type: PROVIDER_LIST_REQUEST,\n    });\n\n    // Get user info from state\n    var {\n      userLogin: { userInfo },\n    } = getState();\n\n    // Configure request\n    const config = {\n      headers: {\n        \"Content-Type\": \"application/json\",\n        Authorization: `Bearer ${userInfo.access}`,\n      },\n    };\n\n    // Fetch only essential provider data for dashboard dropdown\n    const { data } = await axios.get(\n      `/providers/?page=${page}&isdashboard=true`,\n      config\n    );\n\n    // Dispatch success action\n    dispatch({\n      type: PROVIDER_LIST_SUCCESS,\n      payload: data,\n    });\n\n    return data;\n  } catch (error) {\n    var err =\n      error.response && error.response.data.detail\n        ? error.response.data.detail\n        : error.message || \"An error occurred while fetching providers\";\n\n    if (err === \"Given token not valid for any token type\") {\n      localStorage.removeItem(\"userInfoUnimedCare\");\n      document.location.href = \"/\";\n    }\n\n    dispatch({\n      type: PROVIDER_LIST_FAIL,\n      payload: err,\n    });\n\n    return { results: [] };\n  }\n};\n\n// Optimized get list providers for EditCaseScreen (includes services)\nexport const providersListEditCase = (page = \"0\") => async (dispatch, getState) => {\n  try {\n    // Dispatch request action\n    dispatch({\n      type: PROVIDER_LIST_REQUEST,\n    });\n\n    // Get user info from state\n    var {\n      userLogin: { userInfo },\n    } = getState();\n\n    // Configure request\n    const config = {\n      headers: {\n        \"Content-Type\": \"application/json\",\n        Authorization: `Bearer ${userInfo.access}`,\n      },\n    };\n\n    // Fetch provider data with services for EditCaseScreen\n    const { data } = await axios.get(\n      `/providers/?page=${page}&iseditcase=true`,\n      config\n    );\n\n    // Dispatch success action\n    dispatch({\n      type: PROVIDER_LIST_SUCCESS,\n      payload: data,\n    });\n\n    return data;\n  } catch (error) {\n    var err =\n      error.response && error.response.data.detail\n        ? error.response.data.detail\n        : error.message || \"An error occurred while fetching providers\";\n\n    if (err === \"Given token not valid for any token type\") {\n      localStorage.removeItem(\"userInfoUnimedCare\");\n      document.location.href = \"/\";\n    }\n\n    dispatch({\n      type: PROVIDER_LIST_FAIL,\n      payload: err,\n    });\n\n    return { providers: [] };\n  }\n};\n\n// Optimized get list providers for ProvidersMapScreen (map display)\nexport const providersListMapScreen =\n  (\n    page,\n    filterName = \"\",\n    filterType = \"\",\n    filterCity = \"\",\n    filterCountry = \"\",\n    range_max = \"\",\n    location_x = \"\",\n    location_y = \"\"\n  ) =>\n  async (dispatch, getState) => {\n    try {\n      // Dispatch request action\n      dispatch({\n        type: PROVIDER_LIST_REQUEST,\n      });\n\n      // Get user info from state\n      var {\n        userLogin: { userInfo },\n      } = getState();\n\n      // Configure request\n      const config = {\n        headers: {\n          \"Content-Type\": \"application/json\",\n          Authorization: `Bearer ${userInfo.access}`,\n        },\n        // Set a longer timeout specifically for this request\n        timeout: 60000, // 60 seconds timeout\n      };\n\n      console.log(\"Fetching optimized providers data for map screen...\");\n\n      // Create a promise with timeout\n      const fetchPromise = axios.get(\n        `/providers/?page=${page}&filtername=${filterName}&filtertype=${filterType}&filtercity=${filterCity}&filtercountry=${filterCountry}&rangemax=${range_max}&locationx=${location_x}&locationy=${location_y}&ismapscreen=true`,\n        config\n      );\n\n      // Set a timeout promise\n      const timeoutPromise = new Promise((_, reject) => {\n        setTimeout(() => {\n          reject(new Error(\"Request timed out after 60 seconds\"));\n        }, 60000);\n      });\n\n      // Race between fetch and timeout\n      const { data } = await Promise.race([fetchPromise, timeoutPromise]);\n\n      console.log(\"Optimized providers data fetched successfully!\");\n\n      // Dispatch success action\n      dispatch({\n        type: PROVIDER_LIST_SUCCESS,\n        payload: data,\n      });\n\n      return data; // Return data for Promise chaining\n    } catch (error) {\n      console.error(\"Error fetching providers for map screen:\", error);\n\n      // Handle token validation error\n      var err =\n        error.response && error.response.data.detail\n          ? error.response.data.detail\n          : (error.detail || error.message || \"Unknown error\");\n\n      if (err) {\n        if (err === \"Given token not valid for any token type\") {\n          localStorage.removeItem(\"userInfoUnimedCare\");\n          document.location.href = \"/\";\n        }\n      }\n\n      // Dispatch failure action\n      dispatch({\n        type: PROVIDER_LIST_FAIL,\n        payload: err,\n      });\n\n      // Return empty array as fallback data\n      return { results: [] };\n    }\n  };\n"], "mappings": "AAAA,OAAOA,KAAK,MAAM,aAAa;AAC/B,SACEC,qBAAqB,EACrBC,qBAAqB,EACrBC,kBAAkB;AAClB;AACAC,oBAAoB,EACpBC,oBAAoB,EACpBC,iBAAiB;AACjB;AACAC,uBAAuB,EACvBC,uBAAuB,EACvBC,oBAAoB;AACpB;AACAC,uBAAuB,EACvBC,uBAAuB,EACvBC,oBAAoB;AACpB;AACAC,uBAAuB,EACvBC,uBAAuB,EACvBC;AACA;AAAA,OACK,gCAAgC;AAEvC,OAAO,MAAMC,cAAc,GAAGA,CAACC,EAAE,EAAEC,QAAQ,KAAK,OAAOC,QAAQ,EAAEC,QAAQ,KAAK;EAC5E,IAAI;IACFD,QAAQ,CAAC;MACPE,IAAI,EAAEX;IACR,CAAC,CAAC;IACF,IAAI;MACFY,SAAS,EAAE;QAAEC;MAAS;IACxB,CAAC,GAAGH,QAAQ,CAAC,CAAC;IACd,MAAMI,MAAM,GAAG;MACbC,OAAO,EAAE;QACP,cAAc,EAAE,kBAAkB;QAClCC,aAAa,EAAG,UAASH,QAAQ,CAACI,MAAO;MAC3C;IACF,CAAC;IACD,MAAM;MAAEC;IAAK,CAAC,GAAG,MAAM5B,KAAK,CAAC6B,GAAG,CAC7B,qBAAoBZ,EAAG,GAAE,EAC1BC,QAAQ,EACRM,MACF,CAAC;IAEDL,QAAQ,CAAC;MACPE,IAAI,EAAEV,uBAAuB;MAC7BmB,OAAO,EAAEF;IACX,CAAC,CAAC;EACJ,CAAC,CAAC,OAAOG,KAAK,EAAE;IACd,IAAIC,GAAG,GACLD,KAAK,CAACE,QAAQ,IAAIF,KAAK,CAACE,QAAQ,CAACL,IAAI,CAACM,MAAM,GACxCH,KAAK,CAACE,QAAQ,CAACL,IAAI,CAACM,MAAM,GAC1BH,KAAK,CAACG,MAAM;IAClB,IAAIF,GAAG,EAAE;MACP,IAAIA,GAAG,KAAK,0CAA0C,EAAE;QACtDG,YAAY,CAACC,UAAU,CAAC,oBAAoB,CAAC;QAC7CC,QAAQ,CAACC,QAAQ,CAACC,IAAI,GAAG,GAAG;MAC9B;IACF;IACApB,QAAQ,CAAC;MACPE,IAAI,EAAET,oBAAoB;MAC1BkB,OAAO,EACLC,KAAK,CAACE,QAAQ,IAAIF,KAAK,CAACE,QAAQ,CAACL,IAAI,CAACM,MAAM,GACxCH,KAAK,CAACE,QAAQ,CAACL,IAAI,CAACM,MAAM,GAC1BH,KAAK,CAACG;IACd,CAAC,CAAC;EACJ;AACF,CAAC;;AAED;AACA,OAAO,MAAMM,cAAc,GAAIvB,EAAE,IAAK,OAAOE,QAAQ,EAAEC,QAAQ,KAAK;EAClE,IAAI;IACFD,QAAQ,CAAC;MACPE,IAAI,EAAER;IACR,CAAC,CAAC;IACF,IAAI;MACFS,SAAS,EAAE;QAAEC;MAAS;IACxB,CAAC,GAAGH,QAAQ,CAAC,CAAC;IACd,MAAMI,MAAM,GAAG;MACbC,OAAO,EAAE;QACP,cAAc,EAAE,qBAAqB;QACrCC,aAAa,EAAG,UAASH,QAAQ,CAACI,MAAO;MAC3C;IACF,CAAC;IACD,MAAM;MAAEC;IAAK,CAAC,GAAG,MAAM5B,KAAK,CAACyC,MAAM,CAAE,qBAAoBxB,EAAG,GAAE,EAAEO,MAAM,CAAC;IAEvEL,QAAQ,CAAC;MACPE,IAAI,EAAEP,uBAAuB;MAC7BgB,OAAO,EAAEF;IACX,CAAC,CAAC;EACJ,CAAC,CAAC,OAAOG,KAAK,EAAE;IACd,IAAIC,GAAG,GACLD,KAAK,CAACE,QAAQ,IAAIF,KAAK,CAACE,QAAQ,CAACL,IAAI,CAACM,MAAM,GACxCH,KAAK,CAACE,QAAQ,CAACL,IAAI,CAACM,MAAM,GAC1BH,KAAK,CAACG,MAAM;IAClB,IAAIF,GAAG,EAAE;MACP,IAAIA,GAAG,KAAK,0CAA0C,EAAE;QACtDG,YAAY,CAACC,UAAU,CAAC,oBAAoB,CAAC;QAC7CC,QAAQ,CAACC,QAAQ,CAACC,IAAI,GAAG,GAAG;MAC9B;IACF;IACApB,QAAQ,CAAC;MACPE,IAAI,EAAEN,oBAAoB;MAC1Be,OAAO,EACLC,KAAK,CAACE,QAAQ,IAAIF,KAAK,CAACE,QAAQ,CAACL,IAAI,CAACM,MAAM,GACxCH,KAAK,CAACE,QAAQ,CAACL,IAAI,CAACM,MAAM,GAC1BH,KAAK,CAACG;IACd,CAAC,CAAC;EACJ;AACF,CAAC;;AAED;AACA,OAAO,MAAMQ,iBAAiB,GAAIxB,QAAQ,IAAK,OAAOC,QAAQ,EAAEC,QAAQ,KAAK;EAC3E,IAAI;IACFD,QAAQ,CAAC;MACPE,IAAI,EAAEjB;IACR,CAAC,CAAC;IACF,IAAI;MACFkB,SAAS,EAAE;QAAEC;MAAS;IACxB,CAAC,GAAGH,QAAQ,CAAC,CAAC;IACd,MAAMI,MAAM,GAAG;MACbC,OAAO,EAAE;QACP,cAAc,EAAE,kBAAkB;QAClCC,aAAa,EAAG,UAASH,QAAQ,CAACI,MAAO;MAC3C;IACF,CAAC;IACD,MAAM;MAAEC;IAAK,CAAC,GAAG,MAAM5B,KAAK,CAAC2C,IAAI,CAC9B,iCAAgC,EACjCzB,QAAQ,EACRM,MACF,CAAC;IAEDL,QAAQ,CAAC;MACPE,IAAI,EAAEhB,oBAAoB;MAC1ByB,OAAO,EAAEF;IACX,CAAC,CAAC;EACJ,CAAC,CAAC,OAAOG,KAAK,EAAE;IACd,IAAIC,GAAG,GACLD,KAAK,CAACE,QAAQ,IAAIF,KAAK,CAACE,QAAQ,CAACL,IAAI,CAACM,MAAM,GACxCH,KAAK,CAACE,QAAQ,CAACL,IAAI,CAACM,MAAM,GAC1BH,KAAK,CAACG,MAAM;IAClB,IAAIF,GAAG,EAAE;MACP,IAAIA,GAAG,KAAK,0CAA0C,EAAE;QACtDG,YAAY,CAACC,UAAU,CAAC,oBAAoB,CAAC;QAC7CC,QAAQ,CAACC,QAAQ,CAACC,IAAI,GAAG,GAAG;MAC9B;IACF;IACApB,QAAQ,CAAC;MACPE,IAAI,EAAEf,iBAAiB;MACvBwB,OAAO,EACLC,KAAK,CAACE,QAAQ,IAAIF,KAAK,CAACE,QAAQ,CAACL,IAAI,CAACM,MAAM,GACxCH,KAAK,CAACE,QAAQ,CAACL,IAAI,CAACM,MAAM,GAC1BH,KAAK,CAACG;IACd,CAAC,CAAC;EACJ;AACF,CAAC;;AAED;AACA,OAAO,MAAMU,cAAc,GAAI3B,EAAE,IAAK,OAAOE,QAAQ,EAAEC,QAAQ,KAAK;EAClE,IAAI;IACFD,QAAQ,CAAC;MACPE,IAAI,EAAEd;IACR,CAAC,CAAC;IACF,IAAI;MACFe,SAAS,EAAE;QAAEC;MAAS;IACxB,CAAC,GAAGH,QAAQ,CAAC,CAAC;IACd,MAAMI,MAAM,GAAG;MACbC,OAAO,EAAE;QACP,cAAc,EAAE,kBAAkB;QAClCC,aAAa,EAAG,UAASH,QAAQ,CAACI,MAAO;MAC3C;IACF,CAAC;IACD,MAAM;MAAEC;IAAK,CAAC,GAAG,MAAM5B,KAAK,CAAC6C,GAAG,CAAE,qBAAoB5B,EAAG,GAAE,EAAEO,MAAM,CAAC;IAEpEL,QAAQ,CAAC;MACPE,IAAI,EAAEb,uBAAuB;MAC7BsB,OAAO,EAAEF;IACX,CAAC,CAAC;EACJ,CAAC,CAAC,OAAOG,KAAK,EAAE;IACd,IAAIC,GAAG,GACLD,KAAK,CAACE,QAAQ,IAAIF,KAAK,CAACE,QAAQ,CAACL,IAAI,CAACM,MAAM,GACxCH,KAAK,CAACE,QAAQ,CAACL,IAAI,CAACM,MAAM,GAC1BH,KAAK,CAACG,MAAM;IAClB,IAAIF,GAAG,EAAE;MACP,IAAIA,GAAG,KAAK,0CAA0C,EAAE;QACtDG,YAAY,CAACC,UAAU,CAAC,oBAAoB,CAAC;QAC7CC,QAAQ,CAACC,QAAQ,CAACC,IAAI,GAAG,GAAG;MAC9B;IACF;IACApB,QAAQ,CAAC;MACPE,IAAI,EAAEZ,oBAAoB;MAC1BqB,OAAO,EACLC,KAAK,CAACE,QAAQ,IAAIF,KAAK,CAACE,QAAQ,CAACL,IAAI,CAACM,MAAM,GACxCH,KAAK,CAACE,QAAQ,CAACL,IAAI,CAACM,MAAM,GAC1BH,KAAK,CAACG;IACd,CAAC,CAAC;EACJ;AACF,CAAC;;AAED;AACA,OAAO,MAAMY,aAAa,GACxBA,CACEC,IAAI,EACJC,UAAU,GAAG,EAAE,EACfC,UAAU,GAAG,EAAE,EACfC,UAAU,GAAG,EAAE,EACfC,aAAa,GAAG,EAAE,EAClBC,SAAS,GAAG,EAAE,EACdC,UAAU,GAAG,EAAE,EACfC,UAAU,GAAG,EAAE,KAEjB,OAAOnC,QAAQ,EAAEC,QAAQ,KAAK;EAC5B,IAAI;IACF;IACAD,QAAQ,CAAC;MACPE,IAAI,EAAEpB;IACR,CAAC,CAAC;;IAEF;IACA,IAAI;MACFqB,SAAS,EAAE;QAAEC;MAAS;IACxB,CAAC,GAAGH,QAAQ,CAAC,CAAC;;IAEd;IACA,MAAMI,MAAM,GAAG;MACbC,OAAO,EAAE;QACP,cAAc,EAAE,kBAAkB;QAClCC,aAAa,EAAG,UAASH,QAAQ,CAACI,MAAO;MAC3C,CAAC;MACD;MACA4B,OAAO,EAAE,KAAK,CAAE;IAClB,CAAC;IAEDC,OAAO,CAACC,GAAG,CAAC,4BAA4B,CAAC;;IAEzC;IACA,MAAMC,YAAY,GAAG1D,KAAK,CAAC6C,GAAG,CAC3B,oBAAmBE,IAAK,eAAcC,UAAW,eAAcC,UAAW,eAAcC,UAAW,kBAAiBC,aAAc,aAAYC,SAAU,cAAaC,UAAW,cAAaC,UAAW,EAAC,EAC1M9B,MACF,CAAC;;IAED;IACA,MAAMmC,cAAc,GAAG,IAAIC,OAAO,CAAC,CAACC,CAAC,EAAEC,MAAM,KAAK;MAChDC,UAAU,CAAC,MAAM;QACfD,MAAM,CAAC,IAAIE,KAAK,CAAC,oCAAoC,CAAC,CAAC;MACzD,CAAC,EAAE,KAAK,CAAC;IACX,CAAC,CAAC;;IAEF;IACA,MAAM;MAAEpC;IAAK,CAAC,GAAG,MAAMgC,OAAO,CAACK,IAAI,CAAC,CAACP,YAAY,EAAEC,cAAc,CAAC,CAAC;IAEnEH,OAAO,CAACC,GAAG,CAAC,sCAAsC,CAAC;;IAEnD;IACAtC,QAAQ,CAAC;MACPE,IAAI,EAAEnB,qBAAqB;MAC3B4B,OAAO,EAAEF;IACX,CAAC,CAAC;IAEF,OAAOA,IAAI,CAAC,CAAC;EACf,CAAC,CAAC,OAAOG,KAAK,EAAE;IACdyB,OAAO,CAACzB,KAAK,CAAC,2BAA2B,EAAEA,KAAK,CAAC;;IAEjD;IACA,IAAIC,GAAG,GACLD,KAAK,CAACE,QAAQ,IAAIF,KAAK,CAACE,QAAQ,CAACL,IAAI,CAACM,MAAM,GACxCH,KAAK,CAACE,QAAQ,CAACL,IAAI,CAACM,MAAM,GACzBH,KAAK,CAACG,MAAM,IAAIH,KAAK,CAACmC,OAAO,IAAI,eAAgB;IAExD,IAAIlC,GAAG,EAAE;MACP,IAAIA,GAAG,KAAK,0CAA0C,EAAE;QACtDG,YAAY,CAACC,UAAU,CAAC,oBAAoB,CAAC;QAC7CC,QAAQ,CAACC,QAAQ,CAACC,IAAI,GAAG,GAAG;MAC9B;IACF;;IAEA;IACApB,QAAQ,CAAC;MACPE,IAAI,EAAElB,kBAAkB;MACxB2B,OAAO,EAAEE;IACX,CAAC,CAAC;;IAEF;IACA,OAAO;MAAEmC,OAAO,EAAE;IAAG,CAAC;EACxB;AACF,CAAC;;AAEH;AACA,OAAO,MAAMC,sBAAsB,GAAGA,CAACrB,IAAI,GAAG,GAAG,KAAK,OAAO5B,QAAQ,EAAEC,QAAQ,KAAK;EAClF,IAAI;IACF;IACAD,QAAQ,CAAC;MACPE,IAAI,EAAEpB;IACR,CAAC,CAAC;;IAEF;IACA,IAAI;MACFqB,SAAS,EAAE;QAAEC;MAAS;IACxB,CAAC,GAAGH,QAAQ,CAAC,CAAC;;IAEd;IACA,MAAMI,MAAM,GAAG;MACbC,OAAO,EAAE;QACP,cAAc,EAAE,kBAAkB;QAClCC,aAAa,EAAG,UAASH,QAAQ,CAACI,MAAO;MAC3C;IACF,CAAC;;IAED;IACA,MAAM;MAAEC;IAAK,CAAC,GAAG,MAAM5B,KAAK,CAAC6C,GAAG,CAC7B,oBAAmBE,IAAK,mBAAkB,EAC3CvB,MACF,CAAC;;IAED;IACAL,QAAQ,CAAC;MACPE,IAAI,EAAEnB,qBAAqB;MAC3B4B,OAAO,EAAEF;IACX,CAAC,CAAC;IAEF,OAAOA,IAAI;EACb,CAAC,CAAC,OAAOG,KAAK,EAAE;IACd,IAAIC,GAAG,GACLD,KAAK,CAACE,QAAQ,IAAIF,KAAK,CAACE,QAAQ,CAACL,IAAI,CAACM,MAAM,GACxCH,KAAK,CAACE,QAAQ,CAACL,IAAI,CAACM,MAAM,GAC1BH,KAAK,CAACmC,OAAO,IAAI,4CAA4C;IAEnE,IAAIlC,GAAG,KAAK,0CAA0C,EAAE;MACtDG,YAAY,CAACC,UAAU,CAAC,oBAAoB,CAAC;MAC7CC,QAAQ,CAACC,QAAQ,CAACC,IAAI,GAAG,GAAG;IAC9B;IAEApB,QAAQ,CAAC;MACPE,IAAI,EAAElB,kBAAkB;MACxB2B,OAAO,EAAEE;IACX,CAAC,CAAC;IAEF,OAAO;MAAEmC,OAAO,EAAE;IAAG,CAAC;EACxB;AACF,CAAC;;AAED;AACA,OAAO,MAAME,qBAAqB,GAAGA,CAACtB,IAAI,GAAG,GAAG,KAAK,OAAO5B,QAAQ,EAAEC,QAAQ,KAAK;EACjF,IAAI;IACF;IACAD,QAAQ,CAAC;MACPE,IAAI,EAAEpB;IACR,CAAC,CAAC;;IAEF;IACA,IAAI;MACFqB,SAAS,EAAE;QAAEC;MAAS;IACxB,CAAC,GAAGH,QAAQ,CAAC,CAAC;;IAEd;IACA,MAAMI,MAAM,GAAG;MACbC,OAAO,EAAE;QACP,cAAc,EAAE,kBAAkB;QAClCC,aAAa,EAAG,UAASH,QAAQ,CAACI,MAAO;MAC3C;IACF,CAAC;;IAED;IACA,MAAM;MAAEC;IAAK,CAAC,GAAG,MAAM5B,KAAK,CAAC6C,GAAG,CAC7B,oBAAmBE,IAAK,kBAAiB,EAC1CvB,MACF,CAAC;;IAED;IACAL,QAAQ,CAAC;MACPE,IAAI,EAAEnB,qBAAqB;MAC3B4B,OAAO,EAAEF;IACX,CAAC,CAAC;IAEF,OAAOA,IAAI;EACb,CAAC,CAAC,OAAOG,KAAK,EAAE;IACd,IAAIC,GAAG,GACLD,KAAK,CAACE,QAAQ,IAAIF,KAAK,CAACE,QAAQ,CAACL,IAAI,CAACM,MAAM,GACxCH,KAAK,CAACE,QAAQ,CAACL,IAAI,CAACM,MAAM,GAC1BH,KAAK,CAACmC,OAAO,IAAI,4CAA4C;IAEnE,IAAIlC,GAAG,KAAK,0CAA0C,EAAE;MACtDG,YAAY,CAACC,UAAU,CAAC,oBAAoB,CAAC;MAC7CC,QAAQ,CAACC,QAAQ,CAACC,IAAI,GAAG,GAAG;IAC9B;IAEApB,QAAQ,CAAC;MACPE,IAAI,EAAElB,kBAAkB;MACxB2B,OAAO,EAAEE;IACX,CAAC,CAAC;IAEF,OAAO;MAAEsC,SAAS,EAAE;IAAG,CAAC;EAC1B;AACF,CAAC;;AAED;AACA,OAAO,MAAMC,sBAAsB,GACjCA,CACExB,IAAI,EACJC,UAAU,GAAG,EAAE,EACfC,UAAU,GAAG,EAAE,EACfC,UAAU,GAAG,EAAE,EACfC,aAAa,GAAG,EAAE,EAClBC,SAAS,GAAG,EAAE,EACdC,UAAU,GAAG,EAAE,EACfC,UAAU,GAAG,EAAE,KAEjB,OAAOnC,QAAQ,EAAEC,QAAQ,KAAK;EAC5B,IAAI;IACF;IACAD,QAAQ,CAAC;MACPE,IAAI,EAAEpB;IACR,CAAC,CAAC;;IAEF;IACA,IAAI;MACFqB,SAAS,EAAE;QAAEC;MAAS;IACxB,CAAC,GAAGH,QAAQ,CAAC,CAAC;;IAEd;IACA,MAAMI,MAAM,GAAG;MACbC,OAAO,EAAE;QACP,cAAc,EAAE,kBAAkB;QAClCC,aAAa,EAAG,UAASH,QAAQ,CAACI,MAAO;MAC3C,CAAC;MACD;MACA4B,OAAO,EAAE,KAAK,CAAE;IAClB,CAAC;IAEDC,OAAO,CAACC,GAAG,CAAC,qDAAqD,CAAC;;IAElE;IACA,MAAMC,YAAY,GAAG1D,KAAK,CAAC6C,GAAG,CAC3B,oBAAmBE,IAAK,eAAcC,UAAW,eAAcC,UAAW,eAAcC,UAAW,kBAAiBC,aAAc,aAAYC,SAAU,cAAaC,UAAW,cAAaC,UAAW,mBAAkB,EAC3N9B,MACF,CAAC;;IAED;IACA,MAAMmC,cAAc,GAAG,IAAIC,OAAO,CAAC,CAACC,CAAC,EAAEC,MAAM,KAAK;MAChDC,UAAU,CAAC,MAAM;QACfD,MAAM,CAAC,IAAIE,KAAK,CAAC,oCAAoC,CAAC,CAAC;MACzD,CAAC,EAAE,KAAK,CAAC;IACX,CAAC,CAAC;;IAEF;IACA,MAAM;MAAEpC;IAAK,CAAC,GAAG,MAAMgC,OAAO,CAACK,IAAI,CAAC,CAACP,YAAY,EAAEC,cAAc,CAAC,CAAC;IAEnEH,OAAO,CAACC,GAAG,CAAC,gDAAgD,CAAC;;IAE7D;IACAtC,QAAQ,CAAC;MACPE,IAAI,EAAEnB,qBAAqB;MAC3B4B,OAAO,EAAEF;IACX,CAAC,CAAC;IAEF,OAAOA,IAAI,CAAC,CAAC;EACf,CAAC,CAAC,OAAOG,KAAK,EAAE;IACdyB,OAAO,CAACzB,KAAK,CAAC,0CAA0C,EAAEA,KAAK,CAAC;;IAEhE;IACA,IAAIC,GAAG,GACLD,KAAK,CAACE,QAAQ,IAAIF,KAAK,CAACE,QAAQ,CAACL,IAAI,CAACM,MAAM,GACxCH,KAAK,CAACE,QAAQ,CAACL,IAAI,CAACM,MAAM,GACzBH,KAAK,CAACG,MAAM,IAAIH,KAAK,CAACmC,OAAO,IAAI,eAAgB;IAExD,IAAIlC,GAAG,EAAE;MACP,IAAIA,GAAG,KAAK,0CAA0C,EAAE;QACtDG,YAAY,CAACC,UAAU,CAAC,oBAAoB,CAAC;QAC7CC,QAAQ,CAACC,QAAQ,CAACC,IAAI,GAAG,GAAG;MAC9B;IACF;;IAEA;IACApB,QAAQ,CAAC;MACPE,IAAI,EAAElB,kBAAkB;MACxB2B,OAAO,EAAEE;IACX,CAAC,CAAC;;IAEF;IACA,OAAO;MAAEmC,OAAO,EAAE;IAAG,CAAC;EACxB;AACF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}