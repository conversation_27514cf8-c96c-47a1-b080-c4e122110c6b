import datetime
import os
from rest_framework.response import Response
from rest_framework.decorators import api_view, permission_classes
from rest_framework_simplejwt.tokens import RefreshToken
from rest_framework.permissions import IsAuthenticated
from django.core.exceptions import ObjectDoesNotExist
from django.core.paginator import Paginator, Empty<PERSON>age, PageNotAnInteger
from django.db.models import Q, Count

from base.models import (
    AssistanceModel,
    User,
    CaseModel,
    Client,
    Patient,
    ProviderModel,
    Assurance,
    FileCase,
    Comment,
    FileComment,
    ProviderService,
    ProviderCaseService,
    CaseStatusModel,
)
from django.contrib.auth import authenticate
from base.permissions import <PERSON><PERSON><PERSON>, <PERSON>Admin, IsSuperAdmin
from django.contrib.auth.hashers import make_password
from django.core.paginator import Paginator, EmptyPage, PageNotAnInteger

from base.serializers import CaseModelSerializer, CaseModelMapSerializer, CaseDashboardSerializer, CaseScreenSerializer

import datetime
from django.db.models import Q

import requests


def get_exchange_rates():
    # Replace with your actual API URL
    response = requests.get(
        "https://v6.exchangerate-api.com/v6/************************/latest/EUR"
    )
    data = response.json()

    if data["result"] == "success":
        return data["conversion_rates"]
    else:
        return None




@api_view(["GET"])
@permission_classes([IsAuthenticated])
def get_case_histories(request, pk):
    try:
        page = request.query_params.get('page', '1')
        try:
            page = int(page)
        except ValueError:
            page = 1

        case = CaseModel.objects.get(pk=pk)
        if case:
            # Get the history records for this case
            history_records = case.history.all().order_by('-history_date')

            # Get related objects history
            related_histories = []

            # Get patient history if available
            if case.patient:
                patient_history = case.patient.history.filter(
                    history_date__gte=case.created_at
                ).order_by('-history_date')
                for record in patient_history:
                    related_histories.append({
                        'record': record,
                        'type': 'patient',
                        'name': 'Patient',
                        'object_id': case.patient.id,
                        'object_name': case.patient.full_name
                    })

            # Get assistance services history
            assistance_services = AssistanceModel.objects.filter(case=case)
            for assistance in assistance_services:
                assistance_history = assistance.history.all().order_by('-history_date')
                for record in assistance_history:
                    related_histories.append({
                        'record': record,
                        'type': 'assistance',
                        'name': 'Assistance Service',
                        'object_id': assistance.id,
                        'object_name': f"Assistance on {assistance.appointment_date}"
                    })

            # Get case status history
            case_statuses = CaseStatusModel.objects.filter(case=case)
            for status in case_statuses:
                # Try to find the user who made this status change from case history
                status_user = None
                for record in history_records:
                    if record.history_date and abs((record.history_date - status.created_at).total_seconds()) < 60:  # Within 1 minute
                        if record.status_coordination == status.status_coordination:
                            status_user = record.history_user
                            break

                # If we couldn't find a user, use the case's created_by or coordinator as a fallback
                if not status_user:
                    status_user = case.created_by if case.created_by else case.coordinator

                related_histories.append({
                    'record': None,
                    'type': 'status',
                    'name': 'Case Status',
                    'object_id': status.id,
                    'object_name': status.status_coordination,
                    'date': status.created_at,
                    'user': status_user  # This will be None if we couldn't find a matching user
                })

            # Get file history
            files = FileCase.objects.filter(case=case)
            for file in files:
                # Try to find the user who uploaded this file from case history
                file_user = None
                for record in history_records:
                    if record.history_date and abs((record.history_date - file.created_at).total_seconds()) < 60:  # Within 1 minute
                        file_user = record.history_user
                        break

                # If we couldn't find a user, use the case's created_by or coordinator as a fallback
                if not file_user:
                    file_user = case.created_by if case.created_by else case.coordinator

                related_histories.append({
                    'record': None,
                    'type': 'file',
                    'name': 'File',
                    'object_id': file.id,
                    'object_name': file.get_type_display(),
                    'date': file.created_at,
                    'file_name': os.path.basename(file.file.name) if file.file else '',
                    'user': file_user  # This will be None if we couldn't find a matching user
                })

            # Combine case history with related histories
            history_data = []

            # Add case histories
            for record in history_records:
                # Determine the action type
                if record.history_type == '+':
                    action = 'Created'
                elif record.history_type == '~':
                    action = 'Modified'
                elif record.history_type == '-':
                    action = 'Deleted'
                else:
                    action = 'Unknown'

                # Get the username if available
                username = record.history_user.email if record.history_user else 'System'
                user_fullname = record.history_user.full_name if record.history_user and record.history_user.full_name else username

                # Format the date for serialization
                formatted_date = record.history_date.strftime('%Y-%m-%d %H:%M:%S') if record.history_date else None

                # Create a history entry
                history_entry = {
                    'id': record.history_id,
                    'date': formatted_date,
                    'user': {
                        'id': record.history_user.id if record.history_user else None,
                        'email': username,
                        'full_name': user_fullname
                    } if record.history_user else {'id': None, 'email': 'System', 'full_name': 'System'},
                    'action': action,
                    'data': {},
                    'object_type': 'case',
                    'object_name': f"Case #{case.id}"
                }

                # Add all the fields to the data
                for field in CaseModel._meta.fields:
                    field_name = field.name
                    if field_name not in ['id', 'history']:
                        # Handle foreign keys by getting their ID
                        if field.get_internal_type() == 'ForeignKey':
                            value = getattr(record, field_name + '_id', None)

                            # Add related object information if available
                            if value and field_name == 'patient':
                                try:
                                    patient = Patient.objects.get(id=value)
                                    history_entry['data'][field_name] = {
                                        'id': value,
                                        'full_name': patient.full_name
                                    }
                                except:
                                    history_entry['data'][field_name] = value
                            elif value and field_name == 'coordinator':
                                try:
                                    coordinator = User.objects.get(id=value)
                                    history_entry['data'][field_name] = {
                                        'id': value,
                                        'full_name': coordinator.full_name or coordinator.email
                                    }
                                except:
                                    history_entry['data'][field_name] = value
                            elif value and field_name == 'provider':
                                try:
                                    provider = ProviderModel.objects.get(id=value)
                                    history_entry['data'][field_name] = {
                                        'id': value,
                                        'full_name': provider.full_name
                                    }
                                except:
                                    history_entry['data'][field_name] = value
                            elif value and field_name == 'assurance':
                                try:
                                    assurance = Assurance.objects.get(id=value)
                                    history_entry['data'][field_name] = {
                                        'id': value,
                                        'name': assurance.assurance_name
                                    }
                                except:
                                    history_entry['data'][field_name] = value
                            elif value and field_name == 'created_by':
                                try:
                                    created_by = User.objects.get(id=value)
                                    history_entry['data'][field_name] = {
                                        'id': value,
                                        'full_name': created_by.full_name or created_by.email
                                    }
                                except:
                                    history_entry['data'][field_name] = value
                            else:
                                history_entry['data'][field_name] = value
                        else:
                            value = getattr(record, field_name, None)

                            # Format price-related fields to 2 decimal places
                            if field_name in ['eur_price', 'price_tatal', 'invoice_amount'] and value is not None:
                                try:
                                    value = round(float(value), 2)
                                except (ValueError, TypeError):
                                    pass

                            # Convert non-serializable values to strings
                            if value is not None and not isinstance(value, (str, int, float, bool, list, dict, type(None))):
                                if isinstance(value, datetime.datetime) or isinstance(value, datetime.date):
                                    # Format dates and datetimes consistently
                                    if isinstance(value, datetime.datetime):
                                        value = value.strftime('%Y-%m-%d %H:%M:%S')
                                    else:
                                        value = value.strftime('%Y-%m-%d')
                                else:
                                    # Convert other non-serializable objects to string
                                    value = str(value)

                            history_entry['data'][field_name] = value

                # If it's a modification, try to determine what changed
                if action == 'Modified':
                    try:
                        prev_record = record.prev_record
                        if prev_record:
                            changes = {}
                            for field in CaseModel._meta.fields:
                                field_name = field.name
                                if field_name not in ['updated_at', 'created_at']:
                                    # Handle foreign keys specially
                                    if field.get_internal_type() == 'ForeignKey':
                                        current_value = getattr(record, field_name + '_id')
                                        prev_value = getattr(prev_record, field_name + '_id')
                                    else:
                                        current_value = getattr(record, field_name)
                                        prev_value = getattr(prev_record, field_name)

                                    # Format price-related fields to 2 decimal places
                                    if field_name in ['eur_price', 'price_tatal', 'invoice_amount']:
                                        if current_value is not None:
                                            try:
                                                current_value = round(float(current_value), 2)
                                            except (ValueError, TypeError):
                                                pass
                                        if prev_value is not None:
                                            try:
                                                prev_value = round(float(prev_value), 2)
                                            except (ValueError, TypeError):
                                                pass

                                    # Convert values to primitive types for JSON serialization
                                    if current_value is not None and not isinstance(current_value, (str, int, float, bool, list, dict)):
                                        current_value = str(current_value)
                                    if prev_value is not None and not isinstance(prev_value, (str, int, float, bool, list, dict)):
                                        prev_value = str(prev_value)

                                    if current_value != prev_value:
                                        # Get the verbose name of the field if available
                                        verbose_name = field.verbose_name if hasattr(field, 'verbose_name') else field_name.replace('_', ' ').title()

                                        # For foreign keys, try to get the string representation
                                        if field.get_internal_type() == 'ForeignKey':
                                            # Add related object information if available
                                            if field_name == 'patient':
                                                if current_value:
                                                    try:
                                                        patient = Patient.objects.get(id=current_value)
                                                        current_value = {'id': current_value, 'name': patient.full_name}
                                                    except:
                                                        pass
                                                if prev_value:
                                                    try:
                                                        patient = Patient.objects.get(id=prev_value)
                                                        prev_value = {'id': prev_value, 'name': patient.full_name}
                                                    except:
                                                        pass
                                            elif field_name == 'coordinator' or field_name == 'created_by':
                                                if current_value:
                                                    try:
                                                        user = User.objects.get(id=current_value)
                                                        current_value = {'id': current_value, 'name': user.full_name or user.email}
                                                    except:
                                                        pass
                                                if prev_value:
                                                    try:
                                                        user = User.objects.get(id=prev_value)
                                                        prev_value = {'id': prev_value, 'name': user.full_name or user.email}
                                                    except:
                                                        pass
                                            elif field_name == 'provider':
                                                if current_value:
                                                    try:
                                                        provider = ProviderModel.objects.get(id=current_value)
                                                        current_value = {'id': current_value, 'name': provider.full_name}
                                                    except:
                                                        pass
                                                if prev_value:
                                                    try:
                                                        provider = ProviderModel.objects.get(id=prev_value)
                                                        prev_value = {'id': prev_value, 'name': provider.full_name}
                                                    except:
                                                        pass
                                            elif field_name == 'assurance':
                                                if current_value:
                                                    try:
                                                        assurance = Assurance.objects.get(id=current_value)
                                                        current_value = {'id': current_value, 'name': assurance.assurance_name}
                                                    except:
                                                        pass
                                                if prev_value:
                                                    try:
                                                        assurance = Assurance.objects.get(id=prev_value)
                                                        prev_value = {'id': prev_value, 'name': assurance.assurance_name}
                                                    except:
                                                        pass

                                        changes[field_name] = {
                                            'field_name': verbose_name,
                                            'old': prev_value,
                                            'new': current_value
                                        }

                            # Only add changes if there are actual changes
                            if changes and len(changes) > 0:
                                history_entry['changes'] = changes
                            else:
                                # Skip this entry if it's a modification with no changes
                                continue
                    except Exception as e:
                        # If we can't determine changes, just continue
                        print(f"Error determining changes: {str(e)}")
                        # Skip this entry if we can't determine changes
                        continue

                # Only add the history entry if it's not a modification or if it has changes
                if record.history_type != '~' or 'changes' in history_entry:
                    history_data.append(history_entry)

            # Process related histories
            for related in related_histories:
                if related['record']:
                    # For objects with history records
                    record = related['record']

                    # Determine the action type
                    if record.history_type == '+':
                        action = 'Created'
                    elif record.history_type == '~':
                        action = 'Modified'
                    elif record.history_type == '-':
                        action = 'Deleted'
                    else:
                        action = 'Unknown'

                    # Get the username if available
                    username = record.history_user.email if record.history_user else 'System'
                    user_fullname = record.history_user.full_name if record.history_user and record.history_user.full_name else username

                    # Format the date for serialization
                    formatted_date = record.history_date.strftime('%Y-%m-%d %H:%M:%S') if record.history_date else None

                    # Create a history entry for the related object
                    related_entry = {
                        'id': f"{related['type']}_{record.history_id}",
                        'date': formatted_date,
                        'user': {
                            'id': record.history_user.id if record.history_user else None,
                            'email': username,
                            'full_name': user_fullname
                        } if record.history_user else {'id': None, 'email': 'System', 'full_name': 'System'},
                        'action': action,
                        'object_type': related['type'],
                        'object_name': related['object_name'],
                        'data': {}
                    }

                    # Add data fields based on object type
                    if related['type'] == 'patient':
                        # Add patient fields
                        for field in Patient._meta.fields:
                            field_name = field.name
                            if field_name not in ['id', 'history', 'is_deleted', 'created_at', 'updated_at']:
                                value = getattr(record, field_name, None)

                                # Convert non-serializable values to strings
                                if value is not None and not isinstance(value, (str, int, float, bool, list, dict, type(None))):
                                    if isinstance(value, datetime.datetime) or isinstance(value, datetime.date):
                                        # Format dates and datetimes consistently
                                        if isinstance(value, datetime.datetime):
                                            value = value.strftime('%Y-%m-%d %H:%M:%S')
                                        else:
                                            value = value.strftime('%Y-%m-%d')
                                    else:
                                        # Convert other non-serializable objects to string
                                        value = str(value)

                                related_entry['data'][field_name] = value

                        # If it's a modification, try to determine what changed
                        if action == 'Modified':
                            try:
                                prev_record = record.prev_record
                                if prev_record:
                                    changes = {}
                                    for field in Patient._meta.fields:
                                        field_name = field.name
                                        if field_name not in ['updated_at', 'created_at', 'is_deleted']:
                                            current_value = getattr(record, field_name)
                                            prev_value = getattr(prev_record, field_name)

                                            # Convert values to primitive types for JSON serialization
                                            if current_value is not None and not isinstance(current_value, (str, int, float, bool, list, dict)):
                                                current_value = str(current_value)
                                            if prev_value is not None and not isinstance(prev_value, (str, int, float, bool, list, dict)):
                                                prev_value = str(prev_value)

                                            if current_value != prev_value:
                                                # Get the verbose name of the field if available
                                                verbose_name = field.verbose_name if hasattr(field, 'verbose_name') else field_name.replace('_', ' ').title()

                                                changes[field_name] = {
                                                    'field_name': verbose_name,
                                                    'old': prev_value,
                                                    'new': current_value
                                                }

                                    # Only add changes if there are actual changes
                                    if changes and len(changes) > 0:
                                        related_entry['changes'] = changes
                            except Exception as e:
                                # If we can't determine changes, just continue
                                print(f"Error determining patient changes: {str(e)}")

                    elif related['type'] == 'assistance':
                        # Add assistance fields
                        for field in AssistanceModel._meta.fields:
                            field_name = field.name
                            if field_name not in ['id', 'history', 'is_deleted', 'created_at', 'updated_at', 'case']:
                                value = getattr(record, field_name, None)

                                # Convert non-serializable values to strings
                                if value is not None and not isinstance(value, (str, int, float, bool, list, dict, type(None))):
                                    if isinstance(value, datetime.datetime) or isinstance(value, datetime.date):
                                        # Format dates and datetimes consistently
                                        if isinstance(value, datetime.datetime):
                                            value = value.strftime('%Y-%m-%d %H:%M:%S')
                                        else:
                                            value = value.strftime('%Y-%m-%d')
                                    else:
                                        # Convert other non-serializable objects to string
                                        value = str(value)

                                related_entry['data'][field_name] = value

                        # If it's a modification, try to determine what changed
                        if action == 'Modified':
                            try:
                                prev_record = record.prev_record
                                if prev_record:
                                    changes = {}
                                    for field in AssistanceModel._meta.fields:
                                        field_name = field.name
                                        if field_name not in ['updated_at', 'created_at', 'is_deleted', 'case']:
                                            current_value = getattr(record, field_name)
                                            prev_value = getattr(prev_record, field_name)

                                            # Convert values to primitive types for JSON serialization
                                            if current_value is not None and not isinstance(current_value, (str, int, float, bool, list, dict)):
                                                current_value = str(current_value)
                                            if prev_value is not None and not isinstance(prev_value, (str, int, float, bool, list, dict)):
                                                prev_value = str(prev_value)

                                            if current_value != prev_value:
                                                # Get the verbose name of the field if available
                                                verbose_name = field.verbose_name if hasattr(field, 'verbose_name') else field_name.replace('_', ' ').title()

                                                changes[field_name] = {
                                                    'field_name': verbose_name,
                                                    'old': prev_value,
                                                    'new': current_value
                                                }

                                    # Only add changes if there are actual changes
                                    if changes and len(changes) > 0:
                                        related_entry['changes'] = changes
                            except Exception as e:
                                # If we can't determine changes, just continue
                                print(f"Error determining assistance changes: {str(e)}")

                    # Only add the history entry if it's not a modification or if it has changes
                    if record.history_type != '~' or 'changes' in related_entry:
                        history_data.append(related_entry)
                else:
                    # For objects without history records (files, status)
                    formatted_date = related['date'].strftime('%Y-%m-%d %H:%M:%S') if 'date' in related and related['date'] else None

                    # Get user information if available
                    user_info = {'id': None, 'email': 'System', 'full_name': 'System'}
                    if 'user' in related and related['user']:
                        user = related['user']
                        user_info = {
                            'id': user.id,
                            'email': user.email,
                            'full_name': user.full_name if user.full_name else user.email
                        }

                    # Create a history entry for the related object
                    related_entry = {
                        'id': f"{related['type']}_{related['object_id']}",
                        'date': formatted_date,
                        'user': user_info,
                        'action': 'Created',
                        'object_type': related['type'],
                        'object_name': related['object_name'],
                        'data': {}
                    }

                    # Add file name if it's a file
                    if related['type'] == 'file' and 'file_name' in related:
                        related_entry['data']['file_name'] = related['file_name']

                    # Add to history data
                    history_data.append(related_entry)

            # Sort all history entries by date (newest first)
            history_data = sorted(history_data, key=lambda x: x['date'] if x['date'] else '', reverse=True)

            # Implement pagination
            paginator = Paginator(history_data, 10)  # Show 10 history entries per page

            try:
                paginated_history = paginator.page(page)
            except (EmptyPage, PageNotAnInteger):
                paginated_history = paginator.page(1)

            return Response({
                'history': paginated_history.object_list,
                'page': page,
                'pages': paginator.num_pages,
                'count': len(history_data)
            })
        else:
            return Response({"detail": "Sorry, this case does not exist"}, status=404)
    except ObjectDoesNotExist:
        return Response({"detail": "Sorry, this case does not exist"}, status=404)
    except Exception as error:
        print(error)
        return Response(
            {
                "detail": "Sorry, an error occurred while retrieving the history data. Please try again."
            },
            status=500,
        )


# duplicate
@api_view(["PUT"])
@permission_classes([IsAuthenticated])
def duplicate_case_detail(request, pk):
    try:

        case = CaseModel.objects.get(pk=pk)
        if case:
            new_case = CaseModel.objects.create(
                patient=case.patient,
                coordinator=case.coordinator,
                case_date=case.case_date,
                case_description=case.case_description,
                case_type=case.case_type,
                appointment_date=case.appointment_date,
                service_location=case.service_location,
                provider=case.provider,
                invoice_number=case.invoice_number,
                date_issued=case.date_issued,
                invoice_amount=case.invoice_amount,
                assurance=case.assurance,
                assurance_number=case.assurance_number,
                policy_number=case.policy_number,
                assurance_status=case.assurance_status,
                created_by=request.user,
                is_pay=case.is_pay,
                price_tatal=case.price_tatal,
                currency_price=case.currency_price,
                eur_price=case.eur_price,
            )

            if new_case:

                # initial_medical_reports
                fils_initial_medical_reports = FileCase.objects.filter(
                    case=case, type="Initial Medical Reports"
                )
                for file_initial in fils_initial_medical_reports:
                    FileCase.objects.create(
                        case=new_case,
                        file=file_initial.file,
                        type="Initial Medical Reports",
                    )

                # upload_invoice
                fils_upload_invoice = FileCase.objects.filter(
                    case=case, type="Upload Invoice"
                )
                for file_upload in fils_upload_invoice:
                    FileCase.objects.create(
                        case=new_case, file=file_upload.file, type="Upload Invoice"
                    )

                # upload_authorization_documents
                fils_upload_authorization_documents = FileCase.objects.filter(
                    case=case, type="Upload Authorization Documents"
                )
                for file_authorization in fils_upload_authorization_documents:
                    FileCase.objects.create(
                        case=new_case,
                        file=file_authorization.file,
                        type="Upload Authorization Documents",
                    )

                # update status
                list_status_case = CaseStatusModel.objects.filter(case=case)
                for item_status in list_status_case:
                    CaseStatusModel.objects.create(
                        case=new_case,
                        status_coordination=item_status.status_coordination,
                    )

                # provider services
                list_providers = ProviderCaseService.objects.filter(case=case)
                for item_provider in list_providers:

                    ProviderCaseService.objects.create(
                        case=new_case,
                        provider=item_provider.provider,
                        provider_service=item_provider.provider_service,
                        service_type=item_provider.service_type,
                        service_specialist=item_provider.service_specialist,
                    )

            return Response(
                {
                    "detail": "This Case has been duplicate successfully",
                    "new_case": new_case.id,
                }
            )

        else:
            return Response({"detail": "Sorry, this case does not exist"}, status=401)
    except ObjectDoesNotExist:
        return Response({"detail": "Sorry, this case does not exist"}, status=401)
    except Exception as error:
        print(error)
        return Response(
            {
                "detail": "Sorry, an error occurred while sending the data. Please try again."
            },
            status=401,
        )


# update case
@api_view(["GET"])
def update_case_status(request):
    cases = CaseModel.objects.all()

    count = 0

    # Iterate through each provider and create entries in ProviderService
    for case in cases:
        # Check if an entry already exists
        if not CaseStatusModel.objects.filter(
            case=case,
            status_coordination=case.status_coordination,
        ).exists():
            # Create the entry if it doesn't exist
            CaseStatusModel.objects.create(
                case=case,
                status_coordination=case.status_coordination,
            )

            count += 1

    return Response(
        {
            "detail": "case status updated successfully.",
            "new_entries": count,
            "total_cases": len(cases),
        }
    )


# get list cases by current user
@api_view(["GET"])
@permission_classes([IsAuthenticated])
def get_list_cases_byloged(request):
    try:
        coordinator = request.user
        if coordinator:
            cases = (
                CaseModel.objects.filter(is_deleted=False)
                .filter(Q(coordinator=coordinator) | Q(created_by=coordinator))
                .order_by("-created_at")
            )

            status_param = request.GET.get("status")
            if status_param:
                statuses = [
                    status.strip()
                    for status in status_param.split(",")
                    if status.strip()
                ]
                if statuses:
                    cases = cases.filter(status_coordination__in=statuses)

            # Paginate
            page = request.query_params.get("page")
            if page != "0":
                paginator = Paginator(cases, 10)
                try:
                    cases = paginator.page(page)
                except PageNotAnInteger:
                    cases = paginator.page(1)
                except EmptyPage:
                    cases = paginator.page(paginator.num_pages)
                pages = paginator.num_pages
                count = paginator.count
            else:
                pages = 1
                count = cases.count()

            serializer = CaseModelSerializer(cases, many=True)
            return Response(
                data={
                    "cases": serializer.data,
                    "page": page,
                    "pages": pages,
                    "count": count,
                }
            )
        else:
            return Response(
                {"detail": "Sorry, This Coordinator Does Not Exist"}, status=401
            )
    except ObjectDoesNotExist:
        return Response(
            {"detail": "Sorry, This Coordinator Does Not Exist"}, status=401
        )
    except Exception as e:
        return Response(
            {
                "detail": "Sorry, an error occurred while sending the data. Please try again."
            },
            status=401,
        )


# get list cases by provider
@api_view(["GET"])
@permission_classes([IsAuthenticated])
def get_list_cases_byprovider(request, pk):
    try:
        provider = ProviderModel.objects.get(pk=pk)
        if provider:

            case_ids = ProviderCaseService.objects.filter(
                provider=provider,
                is_deleted=False,  # Exclude deleted entries if applicable
            ).values_list("case_id", flat=True)
            cases = CaseModel.objects.filter(
                is_deleted=False, id__in=case_ids
            ).order_by("-created_at")

            status_param = request.GET.get("status")
            if status_param:
                statuses = [
                    status.strip()
                    for status in status_param.split(",")
                    if status.strip()
                ]
                if statuses:
                    print(statuses)
                    cases = cases.filter(status_coordination__in=statuses)

            # Paginate
            page = request.query_params.get("page")
            if page != "0":
                paginator = Paginator(cases, 10)
                try:
                    cases = paginator.page(page)
                except PageNotAnInteger:
                    cases = paginator.page(1)
                except EmptyPage:
                    cases = paginator.page(paginator.num_pages)
                pages = paginator.num_pages
                count = paginator.count
            else:
                pages = 1
                count = cases.count()

            serializer = CaseModelSerializer(cases, many=True)
            return Response(
                data={
                    "cases": serializer.data,
                    "page": page,
                    "pages": pages,
                    "count": count,
                }
            )
        else:
            return Response(
                {"detail": "Sorry, This Coordinator Does Not Exist"}, status=401
            )
    except ObjectDoesNotExist:
        return Response(
            {"detail": "Sorry, This Coordinator Does Not Exist"}, status=401
        )
    except Exception as e:
        return Response(
            {
                "detail": "Sorry, an error occurred while sending the data. Please try again."
            },
            status=401,
        )


# get list cases by insurance
@api_view(["GET"])
@permission_classes([IsAuthenticated])
def get_list_cases_byinsurance(request, pk):
    try:
        insurance = Assurance.objects.get(pk=pk)
        if insurance:
            cases = CaseModel.objects.filter(
                is_deleted=False, assurance=insurance
            ).order_by("-created_at")

            status_param = request.GET.get("status")
            if status_param:
                statuses = [
                    status.strip()
                    for status in status_param.split(",")
                    if status.strip()
                ]
                if statuses:
                    print(statuses)
                    cases = cases.filter(status_coordination__in=statuses)

            # Paginate
            page = request.query_params.get("page")
            if page != "0":
                paginator = Paginator(cases, 10)
                try:
                    cases = paginator.page(page)
                except PageNotAnInteger:
                    cases = paginator.page(1)
                except EmptyPage:
                    cases = paginator.page(paginator.num_pages)
                pages = paginator.num_pages
                count = paginator.count
            else:
                pages = 1
                count = cases.count()

            serializer = CaseModelSerializer(cases, many=True)
            return Response(
                data={
                    "cases": serializer.data,
                    "page": page,
                    "pages": pages,
                    "count": count,
                }
            )
        else:
            return Response(
                {"detail": "Sorry, This Coordinator Does Not Exist"}, status=401
            )
    except ObjectDoesNotExist:
        return Response(
            {"detail": "Sorry, This Coordinator Does Not Exist"}, status=401
        )
    except Exception as e:
        return Response(
            {
                "detail": "Sorry, an error occurred while sending the data. Please try again."
            },
            status=401,
        )


@api_view(["PUT"])
@permission_classes([IsAuthenticated])
def assigned_case_to(request, pk):
    try:
        case = CaseModel.objects.get(pk=pk)
        if case:
            if "coordinator" in request.data and request.data["coordinator"] != "":
                checkCoordinator = User.objects.get(pk=request.data["coordinator"])
                if checkCoordinator:
                    check = True
                    coordinator = checkCoordinator
                    case.coordinator = coordinator
                    case.save()
                    # add comment

                    comment = Comment.objects.create(
                        case=case,
                        user=request.user,
                        content="Coordinator "
                        + coordinator.full_name
                        + " was assigned to this case.",
                    )
                    return Response(
                        {"detail": "This Case has been updates successfully"}
                    )
                else:
                    return Response(
                        {"detail": "Sorry, this coordinator does not exist"}, status=401
                    )
            else:
                return Response(
                    {"detail": "Sorry, this coordinator does not exist"}, status=401
                )

        else:
            return Response({"detail": "Sorry, this case does not exist"}, status=401)
    except ObjectDoesNotExist:
        return Response({"detail": "Sorry, this case does not exist"}, status=401)
    except Exception as error:
        print(error)
        return Response(
            {
                "detail": "Sorry, an error occurred while sending the data. Please try again."
            },
            status=401,
        )


# get list cases by user
@api_view(["GET"])
@permission_classes([IsAuthenticated])
def get_list_cases_byuser(request, pk):
    try:
        coordinator = User.objects.get(pk=pk)
        if coordinator:
            cases = (
                CaseModel.objects.filter(is_deleted=False)
                .filter(Q(coordinator=coordinator) | Q(created_by=coordinator))
                .order_by("-created_at")
            )

            status_param = request.GET.get("status")
            if status_param:
                statuses = [
                    status.strip()
                    for status in status_param.split(",")
                    if status.strip()
                ]
                if statuses:
                    print(statuses)
                    cases = cases.filter(status_coordination__in=statuses)

            # Paginate
            page = request.query_params.get("page")
            if page != "0":
                paginator = Paginator(cases, 10)
                try:
                    cases = paginator.page(page)
                except PageNotAnInteger:
                    cases = paginator.page(1)
                except EmptyPage:
                    cases = paginator.page(paginator.num_pages)
                pages = paginator.num_pages
                count = paginator.count
            else:
                pages = 1
                count = cases.count()

            serializer = CaseModelSerializer(cases, many=True)
            return Response(
                data={
                    "cases": serializer.data,
                    "page": page,
                    "pages": pages,
                    "count": count,
                }
            )
        else:
            return Response(
                {"detail": "Sorry, This Coordinator Does Not Exist"}, status=401
            )
    except ObjectDoesNotExist:
        return Response(
            {"detail": "Sorry, This Coordinator Does Not Exist"}, status=401
        )
    except Exception as e:
        return Response(
            {
                "detail": "Sorry, an error occurred while sending the data. Please try again."
            },
            status=401,
        )


# def get_exchange_rate(from_currency, to_currency):
#     api_key = '************************'  # Replace with your actual API key
#     url = f'https://api.exchangerate-api.com/v4/{api_key}/latest/{from_currency}'  # Modify based on the chosen API

#     try:
#         response = requests.get(url)
#         data = response.json()

#         if 'rates' in data and to_currency in data['rates']:
#             return data['rates'][to_currency]
#     except Exception as e:
#         print(f"Error fetching exchange rate: {e}")

#     return None


@api_view(["PUT"])
@permission_classes([IsAuthenticated])
def update_case_detail(request, pk):
    try:
        print(request.data)
        case = CaseModel.objects.get(pk=pk)
        if case:
            birth_day = request.data.get("birth_day", None)
            if birth_day == "":
                birth_day = None
            patient_data = {
                "first_name": request.data["first_name"],
                "last_name": request.data["last_name"],
                "full_name": request.data["full_name"],
                "birth_day": birth_day,
                "patient_phone": request.data["patient_phone"],
                "patient_email": request.data["patient_email"],
                "patient_address": request.data["patient_address"],
                "patient_country": request.data["patient_country"],
                "patient_city": request.data["patient_city"],
            }
            if case.patient:
                # Update existing patient
                patient = case.patient
                for attr, value in patient_data.items():
                    setattr(patient, attr, value)
                patient.save()
            else:
                patient = Patient.objects.create(**patient_data)

            coordinator = case.coordinator
            if "coordinator" in request.data and request.data["coordinator"] != "":
                checkCoordinator = User.objects.get(pk=request.data["coordinator"])
                if checkCoordinator:
                    coordinator = checkCoordinator

            provider = case.provider
            # if "provider" in request.data and request.data["provider"] != "":
            #     checkProvider = ProviderModel.objects.get(pk=request.data["provider"])
            #     if checkProvider:
            #         provider = checkProvider

            assurance = case.assurance
            if "assurance" in request.data and request.data["assurance"] != "":
                checkAssurance = Assurance.objects.get(pk=request.data["assurance"])
                if checkAssurance:
                    assurance = checkAssurance

            case_date = request.data.get("case_date", None)
            if case_date == "":
                case_date = case.case_date

            appointment_date = request.data.get("appointment_date", None)
            if appointment_date == "":
                appointment_date = case.appointment_date

            case_type_item = request.data.get("case_type_item", None)
            if case_type_item == "":
                case_type_item = case.case_type_item

            print(case_type_item)

            # start date
            start_date = request.data.get("start_date", None)
            if start_date == "":
                start_date = case.start_date
            # end date
            end_date = request.data.get("end_date", None)
            if end_date == "":
                end_date = case.end_date

            print(start_date)
            print(end_date)

            date_issued = request.data.get("date_issued", None)
            if date_issued == "":
                date_issued = case.date_issued

            case.patient = patient
            case.coordinator = coordinator
            case.case_date = case_date
            case.case_description = request.data["case_description"]
            case.case_type = request.data["case_type"]
            case.case_type_item = case_type_item
            # case.start_date = start_date
            # case.end_date = end_date
            appointment_date=None,
            start_date=None,
            end_date=None,
            service_location=None,
            # case.status_coordination = request.data["status_coordination"]
            # case.appointment_date = appointment_date
            # case.service_location = request.data["service_location"]
            case.provider = provider
            case.invoice_number = request.data["invoice_number"]
            case.date_issued = date_issued
            case.invoice_amount = request.data["invoice_amount"]
            case.assurance = assurance
            case.assurance_number = request.data["assurance_number"]
            case.policy_number = request.data["policy_number"]
            case.assurance_status = request.data["assurance_status"]
            # case.created_by = request.user
            case.is_pay = request.data["is_pay"]
            case.price_tatal = request.data["price_tatal"]
            case.currency_price = request.data["currency_price"]
            if request.data["currency_price"] == "EUR":
                case.eur_price = request.data["price_tatal"]
            else:
                rates = get_exchange_rates()
                if rates is not None and request.data["currency_price"] in rates:
                    amount = float(request.data["price_tatal"])
                    currency_code = request.data["currency_price"]
                    print(rates)

                    euro_value = amount / float(rates[currency_code])
                    case.eur_price = euro_value
            case.save()

            # initial_medical_reports
            initial_medical_reports = request.FILES.getlist("initial_medical_reports[]")
            for file in initial_medical_reports:
                FileCase.objects.create(
                    case=case, file=file, type="Initial Medical Reports"
                )
            # upload_invoice
            upload_invoice = request.FILES.getlist("upload_invoice[]")
            for file in upload_invoice:
                FileCase.objects.create(case=case, file=file, type="Upload Invoice")
            # upload_authorization_documents
            upload_authorization_documents = request.FILES.getlist(
                "upload_authorization_documents[]"
            )
            for file in upload_authorization_documents:
                FileCase.objects.create(
                    case=case, file=file, type="Upload Authorization Documents"
                )
            # update status
            cases_status = request.data.getlist("case_status[]")
            print("cases_status")
            print(cases_status)
            CaseStatusModel.objects.filter(case=case).delete()
            for cas_stat in cases_status:
                CaseStatusModel.objects.create(case=case, status_coordination=cas_stat)
            # delete files
            files_deleted = request.data.getlist(
                "files_deleted[]"
            )  # Use request.GET for GET requests

            for file_id in files_deleted:
                try:
                    # Get the file case instance
                    file_case = FileCase.objects.get(pk=file_id)
                    # Mark as deleted
                    file_case.is_deleted = True
                    file_case.save()
                except FileCase.DoesNotExist:
                    print(f"FileCase with id {file_id} does not exist.")

            # Handle assistance deletions
            assistance_deleted = request.data.getlist("assistance_deleted[]")
            for assistance_id in assistance_deleted:
                try:
                    # Get the assistance instance
                    assistance = AssistanceModel.objects.get(pk=assistance_id)
                    # Mark as deleted
                    assistance.is_deleted = True
                    assistance.save()
                except AssistanceModel.DoesNotExist:
                    print(f"AssistanceModel with id {assistance_id} does not exist.")

            # providers deleted
            providers_deleted = request.data.getlist(
                "providers_deleted[]"
            )  # Use request.GET for GET requests

            for provider_id in providers_deleted:
                try:
                    # Get the file case instance
                    provider_case = ProviderCaseService.objects.get(pk=provider_id)
                    # Mark as deleted
                    provider_case.is_deleted = True
                    provider_case.save()
                except ProviderCaseService.DoesNotExist:
                    print(f"ProviderCaseService with id {provider_id} does not exist.")

            # Handle providers data
            providers = []

            for key, value in request.POST.items():
                if key.startswith("providers"):
                    parts = key.split("[")  # Split at '[' to handle index and field
                    if len(parts) == 3:
                        index = int(parts[1][:-1])  # Remove the trailing ']'
                        field = parts[2][:-1]  # Remove the trailing ']'

                        # Ensure the providers list has enough space for the current index
                        while len(providers) <= index:
                            providers.append({})

                        providers[index][field] = value

            # Convert provider fields to integers and create related models
            for provider in providers:
                provider["service"] = int(provider["service"])
                provider["provider"] = int(provider["provider"])
                provider["date"] = provider["date"]

                service_id = provider["service"]
                provider_id = provider["provider"]
                provider_date = provider["date"]

                if provider_id != "" and service_id != "":
                    checkProvider = ProviderModel.objects.get(pk=provider_id)
                    checkProviderService = ProviderService.objects.get(pk=service_id)
                    if checkProvider and checkProviderService:
                        ProviderCaseService.objects.create(
                            case=case,
                            provider=checkProvider,
                            provider_service=checkProviderService,
                            service_type=checkProviderService.service_type,
                            service_specialist=checkProviderService.service_specialist,
                            provider_date=provider_date if provider_date != "" else None,
                        )

            # Handle assistances data
            assistances = {}

            # Parse the assistance data from the request
            for key, value in request.POST.items():
                if key.startswith("assistances"):
                    # Parse the key to extract indices and field names
                    # Format: assistances[0][field] or assistances[0][provider_services][0][field]
                    parts = key.split("[")

                    if len(parts) == 3:  # assistances[0][field]
                        assistance_index = int(parts[1][:-1])  # Remove the trailing ']'
                        field = parts[2][:-1]  # Remove the trailing ']'

                        if assistance_index not in assistances:
                            assistances[assistance_index] = {
                                "provider_services": {}
                            }

                        assistances[assistance_index][field] = value

                    elif len(parts) == 5:  # assistances[0][provider_services][0][field]
                        assistance_index = int(parts[1][:-1])
                        provider_index = int(parts[3][:-1])
                        field = parts[4][:-1]

                        if assistance_index not in assistances:
                            assistances[assistance_index] = {
                                "provider_services": {}
                            }

                        if provider_index not in assistances[assistance_index]["provider_services"]:
                            assistances[assistance_index]["provider_services"][provider_index] = {}

                        assistances[assistance_index]["provider_services"][provider_index][field] = value

            # Create or update assistances
            for assistance_index, assistance_data in assistances.items():
                # Extract basic assistance data
                start_date = assistance_data.get("start_date", None)
                end_date = assistance_data.get("end_date", None)
                appointment_date = assistance_data.get("appointment_date", None)
                service_location = assistance_data.get("service_location", None)

                # Create the assistance
                assistance = AssistanceModel.objects.create(
                    created_by=request.user,
                    case=case,
                    start_date=start_date if start_date else None,
                    end_date=end_date if end_date else None,
                    appointment_date=appointment_date if appointment_date else None,
                    service_location=service_location
                )

                # Process provider services for this assistance
                provider_services = assistance_data.get("provider_services", {})
                for provider_index, provider_data in provider_services.items():
                    provider_id = provider_data.get("provider", None)
                    service_id = provider_data.get("service", None)
                    provider_date = provider_data.get("date", None)

                    if provider_id and service_id:
                        try:
                            provider = ProviderModel.objects.get(pk=provider_id)
                            provider_service = ProviderService.objects.get(pk=service_id)

                            # Create the provider case service linked to this assistance
                            ProviderCaseService.objects.create(
                                assistance=assistance,
                                case=case,
                                provider=provider,
                                provider_service=provider_service,
                                service_type=provider_service.service_type,
                                service_specialist=provider_service.service_specialist,
                                provider_date=provider_date if provider_date else None
                            )
                        except (ProviderModel.DoesNotExist, ProviderService.DoesNotExist) as e:
                            print(f"Error creating provider service: {e}")

            return Response({"detail": "This Case has been updates successfully"})

        else:
            return Response({"detail": "Sorry, this case does not exist"}, status=401)
    except ObjectDoesNotExist:
        return Response({"detail": "Sorry, this case does not exist"}, status=401)
    except Exception as error:
        print(error)
        import traceback
        traceback.print_exc()
        return Response(
            {
                "detail": "Sorry, an error occurred while sending the data. Please try again."
            },
            status=401,
        )

# update case
# @api_view(["PUT"])
# @permission_classes([IsAuthenticated])
# def update_case_detail(request, pk):
#     try:
#         print(request.data)
#         return Response({"detail": "Sorry, this case does not exist"}, status=401)
#         case = CaseModel.objects.get(pk=pk)
#         if case:
#             birth_day = request.data.get("birth_day", None)
#             if birth_day == "":
#                 birth_day = None
#             patient_data = {
#                 "first_name": request.data["first_name"],
#                 "last_name": request.data["last_name"],
#                 "full_name": request.data["full_name"],
#                 "birth_day": birth_day,
#                 "patient_phone": request.data["patient_phone"],
#                 "patient_email": request.data["patient_email"],
#                 "patient_address": request.data["patient_address"],
#                 "patient_country": request.data["patient_country"],
#                 "patient_city": request.data["patient_city"],
#             }
#             if case.patient:
#                 # Update existing patient
#                 patient = case.patient
#                 for attr, value in patient_data.items():
#                     setattr(patient, attr, value)
#                 patient.save()
#             else:
#                 patient = Patient.objects.create(**patient_data)

#             coordinator = case.coordinator
#             if "coordinator" in request.data and request.data["coordinator"] != "":
#                 checkCoordinator = User.objects.get(pk=request.data["coordinator"])
#                 if checkCoordinator:
#                     coordinator = checkCoordinator

#             provider = case.provider
#             # if "provider" in request.data and request.data["provider"] != "":
#             #     checkProvider = ProviderModel.objects.get(pk=request.data["provider"])
#             #     if checkProvider:
#             #         provider = checkProvider

#             assurance = case.assurance
#             if "assurance" in request.data and request.data["assurance"] != "":
#                 checkAssurance = Assurance.objects.get(pk=request.data["assurance"])
#                 if checkAssurance:
#                     assurance = checkAssurance

#             case_date = request.data.get("case_date", None)
#             if case_date == "":
#                 case_date = case.case_date

#             appointment_date = request.data.get("appointment_date", None)
#             if appointment_date == "":
#                 appointment_date = case.appointment_date

#             case_type_item = request.data.get("case_type_item", None)
#             if case_type_item == "":
#                 case_type_item = case.case_type_item

#             print(case_type_item)

#             # start date
#             start_date = request.data.get("start_date", None)
#             if start_date == "":
#                 start_date = case.start_date
#             # end date
#             end_date = request.data.get("end_date", None)
#             if end_date == "":
#                 end_date = case.end_date

#             print(start_date)
#             print(end_date)

#             date_issued = request.data.get("date_issued", None)
#             if date_issued == "":
#                 date_issued = case.date_issued

#             case.patient = patient
#             case.coordinator = coordinator
#             case.case_date = case_date
#             case.case_description = request.data["case_description"]
#             case.case_type = request.data["case_type"]
#             case.case_type_item = case_type_item
#             case.start_date = start_date
#             case.end_date = end_date
#             # case.status_coordination = request.data["status_coordination"]
#             case.appointment_date = appointment_date
#             case.service_location = request.data["service_location"]
#             case.provider = provider
#             case.invoice_number = request.data["invoice_number"]
#             case.date_issued = date_issued
#             case.invoice_amount = request.data["invoice_amount"]
#             case.assurance = assurance
#             case.assurance_number = request.data["assurance_number"]
#             case.policy_number = request.data["policy_number"]
#             case.assurance_status = request.data["assurance_status"]
#             # case.created_by = request.user
#             case.is_pay = request.data["is_pay"]
#             case.price_tatal = request.data["price_tatal"]
#             case.currency_price = request.data["currency_price"]
#             if request.data["currency_price"] == "EUR":
#                 case.eur_price = request.data["price_tatal"]
#             else:
#                 rates = get_exchange_rates()
#                 if rates is not None and request.data["currency_price"] in rates:
#                     amount = float(request.data["price_tatal"])
#                     currency_code = request.data["currency_price"]
#                     print(rates)

#                     euro_value = amount / float(rates[currency_code])
#                     case.eur_price = euro_value
#             case.save()

#             # initial_medical_reports
#             initial_medical_reports = request.FILES.getlist("initial_medical_reports[]")
#             for file in initial_medical_reports:
#                 FileCase.objects.create(
#                     case=case, file=file, type="Initial Medical Reports"
#                 )
#             # upload_invoice
#             upload_invoice = request.FILES.getlist("upload_invoice[]")
#             for file in upload_invoice:
#                 FileCase.objects.create(case=case, file=file, type="Upload Invoice")
#             # upload_authorization_documents
#             upload_authorization_documents = request.FILES.getlist(
#                 "upload_authorization_documents[]"
#             )
#             for file in upload_authorization_documents:
#                 FileCase.objects.create(
#                     case=case, file=file, type="Upload Authorization Documents"
#                 )
#             # update status
#             cases_status = request.data.getlist("case_status[]")
#             print("cases_status")
#             print(cases_status)
#             CaseStatusModel.objects.filter(case=case).delete()
#             for cas_stat in cases_status:
#                 CaseStatusModel.objects.create(case=case, status_coordination=cas_stat)
#             # delete files
#             files_deleted = request.data.getlist(
#                 "files_deleted[]"
#             )  # Use request.GET for GET requests

#             for file_id in files_deleted:
#                 try:
#                     # Get the file case instance
#                     file_case = FileCase.objects.get(pk=file_id)
#                     # Mark as deleted
#                     file_case.is_deleted = True
#                     file_case.save()
#                 except FileCase.DoesNotExist:
#                     print(f"FileCase with id {file_id} does not exist.")
#             # providers deleted
#             providers_deleted = request.data.getlist(
#                 "providers_deleted[]"
#             )  # Use request.GET for GET requests

#             for provider_id in providers_deleted:
#                 try:
#                     # Get the file case instance
#                     provider_case = ProviderCaseService.objects.get(pk=provider_id)
#                     # Mark as deleted
#                     provider_case.is_deleted = True
#                     provider_case.save()
#                 except ProviderCaseService.DoesNotExist:
#                     print(f"FileCase with id {provider_id} does not exist.")
#             # Handle providers data
#             providers = []

#             for key, value in request.POST.items():
#                 if key.startswith("providers"):
#                     parts = key.split("[")  # Split at '[' to handle index and field
#                     if len(parts) == 3:
#                         index = int(parts[1][:-1])  # Remove the trailing ']'
#                         field = parts[2][:-1]  # Remove the trailing ']'

#                         # Ensure the providers list has enough space for the current index
#                         while len(providers) <= index:
#                             providers.append({})

#                         providers[index][field] = value

#             # Convert provider fields to integers and create related models
#             for provider in providers:

#                 provider["service"] = int(provider["service"])
#                 provider["provider"] = int(provider["provider"])
#                 provider["date"] = provider["date"]

#                 service_id = provider["service"]
#                 provider_id = provider["provider"]
#                 provider_date = provider["date"]

#                 if provider_id != "" and service_id != "":
#                     checkProvider = ProviderModel.objects.get(pk=provider_id)
#                     checkProviderService = ProviderService.objects.get(pk=service_id)
#                     if checkProvider and checkProviderService:
#                         ProviderCaseService.objects.create(
#                             case=case,
#                             provider=checkProvider,
#                             provider_service=checkProviderService,
#                             service_type=checkProviderService.service_type,
#                             service_specialist=checkProviderService.service_specialist,
#                             provider_date=provider_date if provider_date != "" else None,
#                         )

#             return Response({"detail": "This Case has been updates successfully"})

#         else:
#             return Response({"detail": "Sorry, this case does not exist"}, status=401)
#     except ObjectDoesNotExist:
#         return Response({"detail": "Sorry, this case does not exist"}, status=401)
#     except Exception as error:
#         print(error)
#         import traceback
#         traceback.print_exc()
#         return Response(
#             {
#                 "detail": "Sorry, an error occurred while sending the data. Please try again."
#             },
#             status=401,
#         )


# delete case
@api_view(["DELETE"])
@permission_classes([IsAuthenticated])
def delete_case(request, pk):
    try:
        case = CaseModel.objects.get(pk=pk)
        if case:
            # change
            case.is_deleted = True
            case.save()
            return Response({"detail": "This case has been successfully deleted."})

        else:
            return Response({"detail": "Sorry, this case does not exist"}, status=401)
    except ObjectDoesNotExist:
        return Response({"detail": "Sorry, this case does not exist"}, status=401)
    except Exception as error:

        return Response(
            {
                "detail": "Sorry, an error occurred while sending the data. Please try again."
            },
            status=401,
        )


# add case
@api_view(["POST"])
@permission_classes([IsAuthenticated])
def add_case(request):
    try:
        print(request.data)
        birth_day = request.data.get("birth_day", None)
        if birth_day == "":
            birth_day = None
        patient = Patient.objects.create(
            first_name=request.data["first_name"],
            last_name=request.data["last_name"],
            full_name=request.data["full_name"],
            birth_day=birth_day,
            patient_phone=request.data["patient_phone"],
            patient_email=request.data["patient_email"],
            patient_address=request.data["patient_address"],
            patient_country=request.data["patient_country"],
            patient_city=request.data["patient_city"],
        )

        coordinator = None
        if "coordinator" in request.data and request.data["coordinator"] != "":
            checkCoordinator = User.objects.get(pk=request.data["coordinator"])
            if checkCoordinator:
                coordinator = checkCoordinator

        provider = None
        # if "provider" in request.data and request.data["provider"] != "":
        #     checkProvider = ProviderModel.objects.get(pk=request.data["provider"])
        #     if checkProvider:
        #         provider = checkProvider
        assurance = None
        if "assurance" in request.data and request.data["assurance"] != "":
            checkAssurance = Assurance.objects.get(pk=request.data["assurance"])
            if checkAssurance:
                assurance = checkAssurance

        case_date = request.data.get("case_date", None)
        if case_date == "":
            case_date = None

        appointment_date = request.data.get("appointment_date", None)
        if appointment_date == "":
            appointment_date = None

        start_date = request.data.get("start_date", None)
        if start_date == "":
            start_date = None

        end_date = request.data.get("end_date", None)
        if end_date == "":
            end_date = None

        date_issued = request.data.get("date_issued", None)
        if date_issued == "":
            date_issued = None
        eur_price = 0
        if request.data["currency_price"] == "EUR":
            eur_price = request.data["price_tatal"]
        else:
            rates = get_exchange_rates()
            if rates is not None and request.data["currency_price"] in rates:
                amount = float(request.data["price_tatal"])
                currency_code = request.data["currency_price"]
                print(rates)

                euro_value = amount / float(rates[currency_code])
                eur_price = euro_value

        case = CaseModel.objects.create(
            patient=patient,
            coordinator=coordinator,
            case_date=case_date,
            case_description=request.data["case_description"],
            case_type=request.data["case_type"],
            case_type_item=request.data["case_type_item"],
            # status_coordination=request.data["status_coordination"],
            appointment_date=None,
            start_date=None,
            end_date=None,
            service_location=None,
            provider=provider,
            invoice_number=request.data["invoice_number"],
            date_issued=date_issued,
            assurance_number=request.data["assurance_number"],
            invoice_amount=request.data["invoice_amount"],
            assurance=assurance,
            policy_number=request.data["policy_number"],
            assurance_status=request.data["assurance_status"],
            created_by=request.user,
            eur_price=eur_price,
            price_tatal=request.data["price_tatal"],
            currency_price=request.data["currency_price"],
        )

        if case:
            # create assistance
            start_date = start_date
            end_date = end_date
            appointment_date = appointment_date
            service_location = request.data.get("service_location", None)

            # Create the assistance record
            assistance = AssistanceModel.objects.create(
                created_by=request.user,
                case=case,
                start_date=start_date if start_date and start_date != "" else None,
                end_date=end_date if end_date and end_date != "" else None,
                appointment_date=appointment_date if appointment_date and appointment_date != "" else None,
                service_location=service_location if service_location and service_location != "" else None,
            )
            if assistance:
                providers = []
                # Iterate over POST data keys
                for key, value in request.POST.items():
                    if key.startswith("providers"):
                        # Extract the index and field (e.g., '0' and 'service' from 'providers[0][service]')
                        prefix, index, field = key.split("[")
                        index = index[:-1]  # Remove the trailing ']'
                        field = field[:-1]  # Remove the trailing ']'
                        # Ensure the current index is initialized in the list
                        while len(providers) <= int(index):
                            providers.append({})
                        # Assign the value to the corresponding field
                        providers[int(index)][field] = value
                # Convert data types if necessary

                for provider in providers:
                    service_id = provider["service"]
                    provider_id = provider["provider"]
                    provider_date = provider["date"]


                    # find provider
                    if provider_id != "" and service_id != "":
                        checkProvider = ProviderModel.objects.get(pk=provider_id)
                        checkProviderService = ProviderService.objects.get(pk=service_id)
                        if checkProvider and checkProviderService:
                            ProviderCaseService.objects.create(
                                case=case,
                                assistance=assistance,
                                provider=checkProvider,
                                provider_service=checkProviderService,
                                service_type=checkProviderService.service_type,
                                service_specialist=checkProviderService.service_specialist,
                                provider_date=provider_date if provider_date != "" else None,
                            )


            # initial_medical_reports
            initial_medical_reports = request.FILES.getlist("initial_medical_reports[]")
            for file in initial_medical_reports:
                FileCase.objects.create(
                    case=case, file=file, type="Initial Medical Reports"
                )
            # upload_invoice
            upload_invoice = request.FILES.getlist("upload_invoice[]")
            for file in upload_invoice:
                FileCase.objects.create(case=case, file=file, type="Upload Invoice")
            # upload_authorization_documents
            upload_authorization_documents = request.FILES.getlist(
                "upload_authorization_documents[]"
            )
            for file in upload_authorization_documents:
                FileCase.objects.create(
                    case=case, file=file, type="Upload Authorization Documents"
                )

            cases_status = request.data.getlist("case_status[]")
            for cas_stat in cases_status:
                CaseStatusModel.objects.create(case=case, status_coordination=cas_stat)

            #
            # providers = []

            # # Iterate over POST data keys
            # for key, value in request.POST.items():
            #     if key.startswith("providers"):
            #         # Extract the index and field (e.g., '0' and 'service' from 'providers[0][service]')
            #         prefix, index, field = key.split("[")
            #         index = index[:-1]  # Remove the trailing ']'
            #         field = field[:-1]  # Remove the trailing ']'
            #         # Ensure the current index is initialized in the list
            #         while len(providers) <= int(index):
            #             providers.append({})
            #         # Assign the value to the corresponding field
            #         providers[int(index)][field] = value
            # Convert data types if necessary

            # for provider in providers:
            #     service_id = provider["service"]
            #     provider_id = provider["provider"]
            #     provider_date = provider["date"]


            #     # find provider
            #     if provider_id != "" and service_id != "":
            #         checkProvider = ProviderModel.objects.get(pk=provider_id)
            #         checkProviderService = ProviderService.objects.get(pk=service_id)
            #         if checkProvider and checkProviderService:
            #             ProviderCaseService.objects.create(
            #                 case=case,
            #                 provider=checkProvider,
            #                 provider_service=checkProviderService,
            #                 service_type=checkProviderService.service_type,
            #                 service_specialist=checkProviderService.service_specialist,
            #                 provider_date=provider_date if provider_date != "" else None,
            #             )

            #
        return Response({"detail": "This Case has been added successfully"})
    except Exception as error:
        print(error)
        import traceback
        print("Error:", error)
        traceback_str = traceback.format_exc()
        print(traceback_str)  # This will show the full traceback in the console

        return Response(
            {
                "detail": "Sorry, an error occurred while sending the data. Please try again.",
                "error": str(error),
                "trace": traceback_str  # Optional: Include the traceback in the response for debugging
            },
            status=401,
        )


@api_view(["POST"])
@permission_classes([IsAuthenticated])
def add_case_new(request):
    try:
        print(request.data)
        birth_day = request.data.get("birth_day", None)
        if birth_day == "":
            birth_day = None
        patient = Patient.objects.create(
            first_name=request.data["first_name"],
            last_name=request.data["last_name"],
            full_name=request.data["full_name"],
            birth_day=birth_day,
            patient_phone=request.data["patient_phone"],
            patient_email=request.data["patient_email"],
            patient_address=request.data["patient_address"],
            patient_country=request.data["patient_country"],
            patient_city=request.data["patient_city"],
        )

        coordinator = None
        if "coordinator" in request.data and request.data["coordinator"] != "":
            checkCoordinator = User.objects.get(pk=request.data["coordinator"])
            if checkCoordinator:
                coordinator = checkCoordinator

        provider = None
        # if "provider" in request.data and request.data["provider"] != "":
        #     checkProvider = ProviderModel.objects.get(pk=request.data["provider"])
        #     if checkProvider:
        #         provider = checkProvider
        assurance = None
        if "assurance" in request.data and request.data["assurance"] != "":
            checkAssurance = Assurance.objects.get(pk=request.data["assurance"])
            if checkAssurance:
                assurance = checkAssurance

        case_date = request.data.get("case_date", None)
        if case_date == "":
            case_date = None

        appointment_date = request.data.get("appointment_date", None)
        if appointment_date == "":
            appointment_date = None

        start_date = request.data.get("start_date", None)
        if start_date == "":
            start_date = None

        end_date = request.data.get("end_date", None)
        if end_date == "":
            end_date = None

        date_issued = request.data.get("date_issued", None)
        if date_issued == "":
            date_issued = None
        eur_price = 0
        if request.data["currency_price"] == "EUR":
            eur_price = request.data["price_tatal"]
        else:
            rates = get_exchange_rates()
            if rates is not None and request.data["currency_price"] in rates:
                amount = float(request.data["price_tatal"])
                currency_code = request.data["currency_price"]
                print(rates)

                euro_value = amount / float(rates[currency_code])
                eur_price = euro_value

        case = CaseModel.objects.create(
            patient=patient,
            coordinator=coordinator,
            case_date=case_date,
            case_description=request.data["case_description"],
            case_type=request.data["case_type"],
            case_type_item=request.data["case_type_item"],
            # status_coordination=request.data["status_coordination"],
            appointment_date=appointment_date,
            start_date=start_date,
            end_date=end_date,
            service_location=request.data["service_location"],
            provider=provider,
            invoice_number=request.data["invoice_number"],
            date_issued=date_issued,
            assurance_number=request.data["assurance_number"],
            invoice_amount=request.data["invoice_amount"],
            assurance=assurance,
            policy_number=request.data["policy_number"],
            assurance_status=request.data["assurance_status"],
            created_by=request.user,
            eur_price=eur_price,
            price_tatal=request.data["price_tatal"],
            currency_price=request.data["currency_price"],
        )

        if case:
            # initial_medical_reports
            initial_medical_reports = request.FILES.getlist("initial_medical_reports[]")
            for file in initial_medical_reports:
                FileCase.objects.create(
                    case=case, file=file, type="Initial Medical Reports"
                )
            # upload_invoice
            upload_invoice = request.FILES.getlist("upload_invoice[]")
            for file in upload_invoice:
                FileCase.objects.create(case=case, file=file, type="Upload Invoice")
            # upload_authorization_documents
            upload_authorization_documents = request.FILES.getlist(
                "upload_authorization_documents[]"
            )
            for file in upload_authorization_documents:
                FileCase.objects.create(
                    case=case, file=file, type="Upload Authorization Documents"
                )

            cases_status = request.data.getlist("case_status[]")
            for cas_stat in cases_status:
                CaseStatusModel.objects.create(case=case, status_coordination=cas_stat)

            # Handle providers data (for the main case)
            providers = []

            # Iterate over POST data keys
            for key, value in request.POST.items():
                if key.startswith("providers"):
                    # Extract the index and field (e.g., '0' and 'service' from 'providers[0][service]')
                    prefix, index, field = key.split("[")
                    index = index[:-1]  # Remove the trailing ']'
                    field = field[:-1]  # Remove the trailing ']'
                    # Ensure the current index is initialized in the list
                    while len(providers) <= int(index):
                        providers.append({})
                    # Assign the value to the corresponding field
                    providers[int(index)][field] = value

            # Create provider services for the main case
            for provider in providers:
                service_id = provider["service"]
                provider_id = provider["provider"]
                provider_date = provider["date"]

                # find provider
                if provider_id != "" and service_id != "":
                    checkProvider = ProviderModel.objects.get(pk=provider_id)
                    checkProviderService = ProviderService.objects.get(pk=service_id)
                    if checkProvider and checkProviderService:
                        ProviderCaseService.objects.create(
                            case=case,
                            provider=checkProvider,
                            provider_service=checkProviderService,
                            service_type=checkProviderService.service_type,
                            service_specialist=checkProviderService.service_specialist,
                            provider_date=provider_date if provider_date != "" else None,
                        )

            # Handle assistances data
            assistances = {}

            # Parse the assistance data from the request
            for key, value in request.POST.items():
                if key.startswith("assistances"):
                    # Parse the key structure
                    parts = key.split("[")

                    if len(parts) == 3:  # assistances[0][field]
                        assistance_index = int(parts[1][:-1])  # Remove the trailing ']'
                        field = parts[2][:-1]  # Remove the trailing ']'

                        if assistance_index not in assistances:
                            assistances[assistance_index] = {
                                "provider_services": {}
                            }

                        assistances[assistance_index][field] = value

                    elif len(parts) == 5:  # assistances[0][provider_services][0][field]
                        assistance_index = int(parts[1][:-1])
                        provider_index = int(parts[3][:-1])
                        field = parts[4][:-1]

                        if assistance_index not in assistances:
                            assistances[assistance_index] = {
                                "provider_services": {}
                            }

                        if provider_index not in assistances[assistance_index]["provider_services"]:
                            assistances[assistance_index]["provider_services"][provider_index] = {}

                        assistances[assistance_index]["provider_services"][provider_index][field] = value

            # Create assistances and their provider services
            for assistance_index, assistance_data in assistances.items():
                # Extract assistance fields
                start_date = assistance_data.get("start_date", None)
                end_date = assistance_data.get("end_date", None)
                appointment_date = assistance_data.get("appointment_date", None)
                service_location = assistance_data.get("service_location", None)

                # Create the assistance record
                assistance = AssistanceModel.objects.create(
                    created_by=request.user,
                    case=case,
                    start_date=start_date if start_date and start_date != "" else None,
                    end_date=end_date if end_date and end_date != "" else None,
                    appointment_date=appointment_date if appointment_date and appointment_date != "" else None,
                    service_location=service_location
                )

                # Process provider services for this assistance
                provider_services = assistance_data.get("provider_services", {})
                for provider_index, provider_data in provider_services.items():
                    provider_id = provider_data.get("provider", None)
                    service_id = provider_data.get("service", None)
                    provider_date = provider_data.get("date", None)

                    if provider_id and service_id:
                        try:
                            provider = ProviderModel.objects.get(pk=provider_id)
                            provider_service = ProviderService.objects.get(pk=service_id)

                            # Create the provider case service linked to this assistance
                            ProviderCaseService.objects.create(
                                assistance=assistance,  # Link to the assistance
                                case=case,  # Also link to the case
                                provider=provider,
                                provider_service=provider_service,
                                service_type=provider_service.service_type,
                                service_specialist=provider_service.service_specialist,
                                provider_date=provider_date if provider_date and provider_date != "" else None
                            )
                        except (ProviderModel.DoesNotExist, ProviderService.DoesNotExist) as e:
                            print(f"Error creating provider service for assistance: {e}")

            return Response({"detail": "This Case has been added successfully"})
    except Exception as error:
        print(error)
        import traceback
        traceback.print_exc()
        return Response(
            {
                "detail": "Sorry, an error occurred while sending the data. Please try again."
            },
            status=401,
        )


# get case info
@api_view(["GET"])
@permission_classes([IsAuthenticated])
def case_detail(request, pk):
    try:
        caseModel = CaseModel.objects.get(pk=pk)
        if caseModel:
            serializer = CaseModelSerializer(caseModel, many=False)
            return Response(serializer.data, status=200)
        else:
            return Response({"detail": "Sorry, this Case does not exist"}, status=401)

    except ObjectDoesNotExist:
        return Response({"detail": "Sorry, this Case does not exist"}, status=401)
    except Exception as error:
        print(error)
        return Response(
            {
                "detail": "Sorry, an error occurred while sending the data. Please try again."
            },
            status=401,
        )


# get  list cases
@api_view(["GET"])
@permission_classes([IsAuthenticated])
def get_list_cases_map(request):
    cases = CaseModel.objects.filter(is_deleted=False).order_by("-created_at")

    status_param = request.GET.get("status")
    if status_param:
        statuses = [
            status.strip() for status in status_param.split(",") if status.strip()
        ]
        if statuses:
            case_ids = CaseStatusModel.objects.filter(
                status_coordination__in=statuses
            ).values_list("case_id", flat=True)
            cases = cases.filter(id__in=case_ids)

    case_id = request.GET.get("id")
    if case_id:
        cases = cases.filter(pk__icontains=case_id)

    cia_id = request.GET.get("ciaid")
    if cia_id:
        cases = cases.filter(assurance_number__icontains=cia_id)

    patient_name = request.GET.get("patient")
    if patient_name:
        cases = cases.filter(patient__full_name__icontains=patient_name)

    case_type = request.GET.get("type")
    if case_type:
        cases = cases.filter(case_type=case_type)

    status_case = request.GET.get("statuscase")
    if status_case:
        cases = cases.filter(status_coordination=status_case)

    insurance_case = request.GET.get("insurance")
    if insurance_case:
        cases = cases.filter(assurance=insurance_case)

    filter_paid = request.GET.get("filterpaid")
    if filter_paid and filter_paid != "":
        if filter_paid == "paid":
            cases = cases.filter(is_pay=True)
        elif filter_paid == "unpaid":
            cases = cases.filter(is_pay=False)

    provider_case = request.GET.get("provider")
    if provider_case:
        case_ids = ProviderCaseService.objects.filter(
            provider=provider_case,
            is_deleted=False,  # Exclude deleted entries if applicable
        ).values_list("case_id", flat=True)
        cases = cases.filter(id__in=case_ids)
        # cases = cases.filter(provider=providerCase)
    coordinator_case = request.GET.get("coordinator")
    if coordinator_case:
        user = User.objects.get(pk=coordinator_case)
        if user:
            cases =cases.filter(coordinator=user)
        else:
            cases = []

    # Paginate
    page = request.query_params.get("page")
    if page != "0":
        paginator = Paginator(cases, 10)
        try:
            cases = paginator.page(page)
        except PageNotAnInteger:
            cases = paginator.page(1)
        except EmptyPage:
            cases = paginator.page(paginator.num_pages)
        pages = paginator.num_pages
        count = paginator.count
    else:
        pages = 1
        count = cases.count()

    serializer = CaseModelMapSerializer(cases, many=True)
    return Response(
        data={
            "cases": serializer.data,
            "page": page,
            "pages": pages,
            "count": count,
        }
    )


# get  list cases
@api_view(["GET"])
@permission_classes([IsAuthenticated])
def get_list_cases(request):
    cases = CaseModel.objects.filter(is_deleted=False).order_by("-created_at")

    is_maps = request.GET.get("ismaps")
    is_dashboard = request.GET.get("isdashboard")
    is_casescreen = request.GET.get("iscasescreen")

    status_param = request.GET.get("status")
    if status_param:
        statuses = [status.strip() for status in status_param.split(",") if status.strip()]
        if statuses:
            # عدد statuses المطلوبة
            num_statuses = len(statuses)
            # نجمع الـ case_id اللي عندهم statuses مطابقة و نحسب عددهم
            matching_case_ids = (
                CaseStatusModel.objects
                .filter(status_coordination__in=statuses)
                .values('case_id')
                .annotate(matching_count=Count('status_coordination', filter=Q(status_coordination__in=statuses), distinct=True))
                .filter(matching_count=num_statuses)  # خاص يكون عندهم جميع statuses المطلوبة
                .values_list('case_id', flat=True)
            )

            cases = cases.filter(id__in=matching_case_ids)
            print("statuses")
            print(statuses)

    case_id = request.GET.get("id")
    if case_id:
        cases = cases.filter(pk__icontains=case_id)

    cia_id = request.GET.get("ciaid")
    if cia_id:
        cases = cases.filter(assurance_number__icontains=cia_id)

    patient_name = request.GET.get("patient")
    if patient_name:
        cases = cases.filter(patient__full_name__icontains=patient_name)

    case_type = request.GET.get("type")
    if case_type:
        cases = cases.filter(case_type=case_type)

    status_case = request.GET.get("statuscase")
    if status_case:
        cases = cases.filter(status_coordination=status_case)

    insurance_case = request.GET.get("insurance")
    if insurance_case:
        cases = cases.filter(assurance=insurance_case)

    filter_paid = request.GET.get("filterpaid")
    if filter_paid and filter_paid != "":
        if filter_paid == "paid":
            cases = cases.filter(is_pay=True)
        elif filter_paid == "unpaid":
            cases = cases.filter(is_pay=False)

    provider_case = request.GET.get("provider")
    if provider_case:
        case_ids = ProviderCaseService.objects.filter(
            provider=provider_case,
            is_deleted=False,  # Exclude deleted entries if applicable
        ).values_list("case_id", flat=True)
        cases = cases.filter(id__in=case_ids)
        # cases = cases.filter(provider=providerCase)
        
    coordinator_case = request.GET.get("coordinator")
    if coordinator_case:
        user = User.objects.get(pk=coordinator_case)
        if user:
            cases =cases.filter(coordinator=user)
        else:
            cases = []
            
            
    # Paginate
    page = request.query_params.get("page")
    if page != "0":
        paginator = Paginator(cases, 10)
        try:
            cases = paginator.page(page)
        except PageNotAnInteger:
            cases = paginator.page(1)
        except EmptyPage:
            cases = paginator.page(paginator.num_pages)
        pages = paginator.num_pages
        count = paginator.count
    else:
        pages = 1
        count = cases.count()
        
    # Choose the appropriate serializer based on the request parameters
    if is_maps:
        serializer = CaseModelMapSerializer(cases, many=True)
    elif is_dashboard:
        serializer = CaseDashboardSerializer(cases, many=True)
    elif is_casescreen:
        serializer = CaseScreenSerializer(cases, many=True)
    else:
        # Default to full serializer for backward compatibility
        serializer = CaseModelSerializer(cases, many=True)

    return Response(
        data={
            "cases": serializer.data,
            "page": page,
            "pages": pages,
            "count": count,
        }
    )
