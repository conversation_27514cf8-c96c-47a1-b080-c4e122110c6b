[{"/Users/<USER>/Desktop/Freelances/Mohssin Tetouan/Project Location/web-location/src/index.js": "1", "/Users/<USER>/Desktop/Freelances/Mohssin Tetouan/Project Location/web-location/src/App.js": "2", "/Users/<USER>/Desktop/Freelances/Mohssin Tetouan/Project Location/web-location/src/reportWebVitals.js": "3", "/Users/<USER>/Desktop/Freelances/Mohssin Tetouan/Project Location/web-location/src/redux/store.js": "4", "/Users/<USER>/Desktop/Freelances/Mohssin Tetouan/Project Location/web-location/src/axios.js": "5", "/Users/<USER>/Desktop/Freelances/Mohssin Tetouan/Project Location/web-location/src/screens/client/ContratClientScreen.js": "6", "/Users/<USER>/Desktop/Freelances/Mohssin Tetouan/Project Location/web-location/src/screens/client/ClientScreen.js": "7", "/Users/<USER>/Desktop/Freelances/Mohssin Tetouan/Project Location/web-location/src/screens/car/CarScreen.js": "8", "/Users/<USER>/Desktop/Freelances/Mohssin Tetouan/Project Location/web-location/src/screens/agences/EditAgenceScreen.js": "9", "/Users/<USER>/Desktop/Freelances/Mohssin Tetouan/Project Location/web-location/src/screens/agences/AddAgenceScreen.js": "10", "/Users/<USER>/Desktop/Freelances/Mohssin Tetouan/Project Location/web-location/src/screens/agences/AgenceScreen.js": "11", "/Users/<USER>/Desktop/Freelances/Mohssin Tetouan/Project Location/web-location/src/screens/reservation/ReservationScreen.js": "12", "/Users/<USER>/Desktop/Freelances/Mohssin Tetouan/Project Location/web-location/src/screens/reservation/EditReservationScreen.js": "13", "/Users/<USER>/Desktop/Freelances/Mohssin Tetouan/Project Location/web-location/src/screens/dashboard/DashboardScreen.js": "14", "/Users/<USER>/Desktop/Freelances/Mohssin Tetouan/Project Location/web-location/src/screens/car/EditCarScreen.js": "15", "/Users/<USER>/Desktop/Freelances/Mohssin Tetouan/Project Location/web-location/src/screens/profile/ProfileScreen.js": "16", "/Users/<USER>/Desktop/Freelances/Mohssin Tetouan/Project Location/web-location/src/screens/contrats/ContratScreen.js": "17", "/Users/<USER>/Desktop/Freelances/Mohssin Tetouan/Project Location/web-location/src/screens/car/AddCarScreen.js": "18", "/Users/<USER>/Desktop/Freelances/Mohssin Tetouan/Project Location/web-location/src/screens/reservation/AddReservationScreen.js": "19", "/Users/<USER>/Desktop/Freelances/Mohssin Tetouan/Project Location/web-location/src/screens/auth/LoginScreen.js": "20", "/Users/<USER>/Desktop/Freelances/Mohssin Tetouan/Project Location/web-location/src/screens/factures/FactureScreen.js": "21", "/Users/<USER>/Desktop/Freelances/Mohssin Tetouan/Project Location/web-location/src/screens/contrats/payment/EditPaymentContratScreen.js": "22", "/Users/<USER>/Desktop/Freelances/Mohssin Tetouan/Project Location/web-location/src/screens/contrats/payment/PaymentContratScreen.js": "23", "/Users/<USER>/Desktop/Freelances/Mohssin Tetouan/Project Location/web-location/src/screens/contrats/SearchContratScreen.js": "24", "/Users/<USER>/Desktop/Freelances/Mohssin Tetouan/Project Location/web-location/src/screens/contrats/payment/AddPaymentContratScreen.js": "25", "/Users/<USER>/Desktop/Freelances/Mohssin Tetouan/Project Location/web-location/src/screens/client/EditClientScreen.js": "26", "/Users/<USER>/Desktop/Freelances/Mohssin Tetouan/Project Location/web-location/src/screens/auth/LogoutScreen.js": "27", "/Users/<USER>/Desktop/Freelances/Mohssin Tetouan/Project Location/web-location/src/screens/client/AddClientScreen.js": "28", "/Users/<USER>/Desktop/Freelances/Mohssin Tetouan/Project Location/web-location/src/screens/contrats/return/AddReturnContratScreen.js": "29", "/Users/<USER>/Desktop/Freelances/Mohssin Tetouan/Project Location/web-location/src/screens/contrats/AddContratScreen.js": "30", "/Users/<USER>/Desktop/Freelances/Mohssin Tetouan/Project Location/web-location/src/screens/contrats/EditContratScreen.js": "31", "/Users/<USER>/Desktop/Freelances/Mohssin Tetouan/Project Location/web-location/src/screens/settings/users/AddUserScreen.js": "32", "/Users/<USER>/Desktop/Freelances/Mohssin Tetouan/Project Location/web-location/src/screens/settings/employes/EmployesScreen.js": "33", "/Users/<USER>/Desktop/Freelances/Mohssin Tetouan/Project Location/web-location/src/screens/settings/employes/AddEmployeScreen.js": "34", "/Users/<USER>/Desktop/Freelances/Mohssin Tetouan/Project Location/web-location/src/screens/settings/users/EditUserScreen.js": "35", "/Users/<USER>/Desktop/Freelances/Mohssin Tetouan/Project Location/web-location/src/screens/settings/marques-models/MarquesModelsScreen.js": "36", "/Users/<USER>/Desktop/Freelances/Mohssin Tetouan/Project Location/web-location/src/screens/settings/employes/EditEmployeScreen.js": "37", "/Users/<USER>/Desktop/Freelances/Mohssin Tetouan/Project Location/web-location/src/screens/settings/users/UserScreen.js": "38", "/Users/<USER>/Desktop/Freelances/Mohssin Tetouan/Project Location/web-location/src/screens/settings/designations/DesignationScreen.js": "39", "/Users/<USER>/Desktop/Freelances/Mohssin Tetouan/Project Location/web-location/src/screens/depenses/employes/AddDepenseEmployeScreen.js": "40", "/Users/<USER>/Desktop/Freelances/Mohssin Tetouan/Project Location/web-location/src/screens/depenses/entretiens/EditDepenseEntretienScreen.js": "41", "/Users/<USER>/Desktop/Freelances/Mohssin Tetouan/Project Location/web-location/src/screens/depenses/entretiens/AddDepenseEntretienScreen.js": "42", "/Users/<USER>/Desktop/Freelances/Mohssin Tetouan/Project Location/web-location/src/screens/depenses/entretiens/DepenseEntretienScreen.js": "43", "/Users/<USER>/Desktop/Freelances/Mohssin Tetouan/Project Location/web-location/src/screens/depenses/employes/EditDepenseEmployeScreen.js": "44", "/Users/<USER>/Desktop/Freelances/Mohssin Tetouan/Project Location/web-location/src/screens/depenses/employes/DepenseEmployeScreen.js": "45", "/Users/<USER>/Desktop/Freelances/Mohssin Tetouan/Project Location/web-location/src/screens/depenses/charges/DepenseChargeScreen.js": "46", "/Users/<USER>/Desktop/Freelances/Mohssin Tetouan/Project Location/web-location/src/screens/depenses/charges/EditDepenseChargeScreen.js": "47", "/Users/<USER>/Desktop/Freelances/Mohssin Tetouan/Project Location/web-location/src/screens/depenses/charges/AddDepenseChargeScreen.js": "48", "/Users/<USER>/Desktop/Freelances/Mohssin Tetouan/Project Location/web-location/src/redux/reducers/employeReducers.js": "49", "/Users/<USER>/Desktop/Freelances/Mohssin Tetouan/Project Location/web-location/src/redux/reducers/userReducers.js": "50", "/Users/<USER>/Desktop/Freelances/Mohssin Tetouan/Project Location/web-location/src/redux/reducers/carReducers.js": "51", "/Users/<USER>/Desktop/Freelances/Mohssin Tetouan/Project Location/web-location/src/redux/reducers/clientReducers.js": "52", "/Users/<USER>/Desktop/Freelances/Mohssin Tetouan/Project Location/web-location/src/redux/reducers/marqueReducers.js": "53", "/Users/<USER>/Desktop/Freelances/Mohssin Tetouan/Project Location/web-location/src/redux/reducers/dashReducers.js": "54", "/Users/<USER>/Desktop/Freelances/Mohssin Tetouan/Project Location/web-location/src/redux/reducers/modelReducers.js": "55", "/Users/<USER>/Desktop/Freelances/Mohssin Tetouan/Project Location/web-location/src/redux/reducers/designationReducers.js": "56", "/Users/<USER>/Desktop/Freelances/Mohssin Tetouan/Project Location/web-location/src/redux/reducers/agenceReducers.js": "57", "/Users/<USER>/Desktop/Freelances/Mohssin Tetouan/Project Location/web-location/src/redux/reducers/contratReducers.js": "58", "/Users/<USER>/Desktop/Freelances/Mohssin Tetouan/Project Location/web-location/src/redux/reducers/reservationReducers.js": "59", "/Users/<USER>/Desktop/Freelances/Mohssin Tetouan/Project Location/web-location/src/constants.js": "60", "/Users/<USER>/Desktop/Freelances/Mohssin Tetouan/Project Location/web-location/src/layouts/DefaultLayout.js": "61", "/Users/<USER>/Desktop/Freelances/Mohssin Tetouan/Project Location/web-location/src/components/Loader.js": "62", "/Users/<USER>/Desktop/Freelances/Mohssin Tetouan/Project Location/web-location/src/components/Alert.js": "63", "/Users/<USER>/Desktop/Freelances/Mohssin Tetouan/Project Location/web-location/src/components/InputModel.js": "64", "/Users/<USER>/Desktop/Freelances/Mohssin Tetouan/Project Location/web-location/src/components/ConfirmationModal.js": "65", "/Users/<USER>/Desktop/Freelances/Mohssin Tetouan/Project Location/web-location/src/components/Selector.js": "66", "/Users/<USER>/Desktop/Freelances/Mohssin Tetouan/Project Location/web-location/src/components/LayoutSection.js": "67", "/Users/<USER>/Desktop/Freelances/Mohssin Tetouan/Project Location/web-location/src/components/Paginate.js": "68", "/Users/<USER>/Desktop/Freelances/Mohssin Tetouan/Project Location/web-location/src/redux/actions/clientActions.js": "69", "/Users/<USER>/Desktop/Freelances/Mohssin Tetouan/Project Location/web-location/src/redux/actions/contratActions.js": "70", "/Users/<USER>/Desktop/Freelances/Mohssin Tetouan/Project Location/web-location/src/redux/actions/carActions.js": "71", "/Users/<USER>/Desktop/Freelances/Mohssin Tetouan/Project Location/web-location/src/redux/actions/marqueActions.js": "72", "/Users/<USER>/Desktop/Freelances/Mohssin Tetouan/Project Location/web-location/src/redux/actions/modelActions.js": "73", "/Users/<USER>/Desktop/Freelances/Mohssin Tetouan/Project Location/web-location/src/redux/actions/agenceActions.js": "74", "/Users/<USER>/Desktop/Freelances/Mohssin Tetouan/Project Location/web-location/src/redux/actions/reservationActions.js": "75", "/Users/<USER>/Desktop/Freelances/Mohssin Tetouan/Project Location/web-location/src/redux/actions/dashActions.js": "76", "/Users/<USER>/Desktop/Freelances/Mohssin Tetouan/Project Location/web-location/src/redux/actions/employeActions.js": "77", "/Users/<USER>/Desktop/Freelances/Mohssin Tetouan/Project Location/web-location/src/redux/actions/userActions.js": "78", "/Users/<USER>/Desktop/Freelances/Mohssin Tetouan/Project Location/web-location/src/redux/actions/designationActions.js": "79", "/Users/<USER>/Desktop/Freelances/Mohssin Tetouan/Project Location/web-location/src/redux/constants/marqueConstants.js": "80", "/Users/<USER>/Desktop/Freelances/Mohssin Tetouan/Project Location/web-location/src/redux/constants/modelContants.js": "81", "/Users/<USER>/Desktop/Freelances/Mohssin Tetouan/Project Location/web-location/src/redux/constants/dashConstants.js": "82", "/Users/<USER>/Desktop/Freelances/Mohssin Tetouan/Project Location/web-location/src/redux/constants/carConstants.js": "83", "/Users/<USER>/Desktop/Freelances/Mohssin Tetouan/Project Location/web-location/src/redux/constants/employeConstants.js": "84", "/Users/<USER>/Desktop/Freelances/Mohssin Tetouan/Project Location/web-location/src/redux/constants/userConstants.js": "85", "/Users/<USER>/Desktop/Freelances/Mohssin Tetouan/Project Location/web-location/src/redux/constants/contratConstant.js": "86", "/Users/<USER>/Desktop/Freelances/Mohssin Tetouan/Project Location/web-location/src/redux/constants/agenceConstants.js": "87", "/Users/<USER>/Desktop/Freelances/Mohssin Tetouan/Project Location/web-location/src/redux/constants/clientConstants.js": "88", "/Users/<USER>/Desktop/Freelances/Mohssin Tetouan/Project Location/web-location/src/redux/constants/reservationConstants.js": "89", "/Users/<USER>/Desktop/Freelances/Mohssin Tetouan/Project Location/web-location/src/redux/constants/designationConstants.js": "90", "/Users/<USER>/Desktop/Freelances/Mohssin Tetouan/Project Location/web-location/src/layouts/Header.js": "91", "/Users/<USER>/Desktop/Freelances/Mohssin Tetouan/Project Location/web-location/src/layouts/Sidebar.js": "92", "/Users/<USER>/Desktop/Freelances/Mohssin Tetouan/Project Location/web-location/src/components/DropdownProfile.js": "93", "/Users/<USER>/Desktop/Freelances/Mohssin Tetouan/Project Location/web-location/src/components/DropdownNotification.js": "94", "/Users/<USER>/Desktop/Freelances/Mohssin Tetouan/Project Location/web-location/src/screens/raport/RaportScreen.js": "95", "/Users/<USER>/Desktop/Freelances/Mohssin Tetouan/UNIMEDCARE/web-front/src/index.js": "96", "/Users/<USER>/Desktop/Freelances/Mohssin Tetouan/UNIMEDCARE/web-front/src/App.js": "97", "/Users/<USER>/Desktop/Freelances/Mohssin Tetouan/UNIMEDCARE/web-front/src/reportWebVitals.js": "98", "/Users/<USER>/Desktop/Freelances/Mohssin Tetouan/UNIMEDCARE/web-front/src/redux/store.js": "99", "/Users/<USER>/Desktop/Freelances/Mohssin Tetouan/UNIMEDCARE/web-front/src/axios.js": "100", "/Users/<USER>/Desktop/Freelances/Mohssin Tetouan/UNIMEDCARE/web-front/src/screens/auth/LoginScreen.js": "101", "/Users/<USER>/Desktop/Freelances/Mohssin Tetouan/UNIMEDCARE/web-front/src/screens/auth/LogoutScreen.js": "102", "/Users/<USER>/Desktop/Freelances/Mohssin Tetouan/UNIMEDCARE/web-front/src/screens/dashboard/DashboardScreen.js": "103", "/Users/<USER>/Desktop/Freelances/Mohssin Tetouan/UNIMEDCARE/web-front/src/redux/reducers/userReducers.js": "104", "/Users/<USER>/Desktop/Freelances/Mohssin Tetouan/UNIMEDCARE/web-front/src/redux/reducers/clientReducers.js": "105", "/Users/<USER>/Desktop/Freelances/Mohssin Tetouan/UNIMEDCARE/web-front/src/constants.js": "106", "/Users/<USER>/Desktop/Freelances/Mohssin Tetouan/UNIMEDCARE/web-front/src/components/Alert.js": "107", "/Users/<USER>/Desktop/Freelances/Mohssin Tetouan/UNIMEDCARE/web-front/src/redux/actions/userActions.js": "108", "/Users/<USER>/Desktop/Freelances/Mohssin Tetouan/UNIMEDCARE/web-front/src/redux/constants/userConstants.js": "109", "/Users/<USER>/Desktop/Freelances/Mohssin Tetouan/UNIMEDCARE/web-front/src/redux/constants/clientConstants.js": "110", "/Users/<USER>/Desktop/Freelances/Mohssin Tetouan/UNIMEDCARE/web-front/src/screens/cases/CaseScreen.js": "111", "/Users/<USER>/Desktop/Freelances/Mohssin Tetouan/UNIMEDCARE/web-front/src/redux/reducers/caseReducers.js": "112", "/Users/<USER>/Desktop/Freelances/Mohssin Tetouan/UNIMEDCARE/web-front/src/redux/constants/caseConstants.js": "113", "/Users/<USER>/Desktop/Freelances/Mohssin Tetouan/UNIMEDCARE/web-front/src/redux/actions/caseActions.js": "114", "/Users/<USER>/Desktop/Freelances/Mohssin Tetouan/UNIMEDCARE/web-front/src/components/Loader.js": "115", "/Users/<USER>/Desktop/Freelances/Mohssin Tetouan/UNIMEDCARE/web-front/src/components/Paginate.js": "116", "/Users/<USER>/Desktop/Freelances/Mohssin Tetouan/UNIMEDCARE/web-front/src/screens/cases/DetailCaseScreen.js": "117", "/Users/<USER>/Desktop/Freelances/Mohssin Tetouan/UNIMEDCARE/web-front/src/redux/reducers/providerReducers.js": "118", "/Users/<USER>/Desktop/Freelances/Mohssin Tetouan/UNIMEDCARE/web-front/src/redux/constants/providerConstants.js": "119", "/Users/<USER>/Desktop/Freelances/Mohssin Tetouan/UNIMEDCARE/web-front/src/redux/actions/providerActions.js": "120", "/Users/<USER>/Desktop/Freelances/Mohssin Tetouan/UNIMEDCARE/web-front/src/screens/cases/AddCaseScreen.js": "121", "/Users/<USER>/Desktop/Freelances/Mohssin Tetouan/UNIMEDCARE/web-front/src/layouts/DefaultLayout.js": "122", "/Users/<USER>/Desktop/Freelances/Mohssin Tetouan/UNIMEDCARE/web-front/src/layouts/Header.js": "123", "/Users/<USER>/Desktop/Freelances/Mohssin Tetouan/UNIMEDCARE/web-front/src/layouts/Sidebar.js": "124", "/Users/<USER>/Desktop/Freelances/Mohssin Tetouan/UNIMEDCARE/web-front/src/components/DropdownNotification.js": "125", "/Users/<USER>/Desktop/Freelances/Mohssin Tetouan/UNIMEDCARE/web-front/src/components/DropdownProfile.js": "126", "/Users/<USER>/Desktop/Freelances/Mohssin Tetouan/UNIMEDCARE/web-front/src/screens/clients/ClientScreen.js": "127", "/Users/<USER>/Desktop/Freelances/Mohssin Tetouan/UNIMEDCARE/web-front/src/components/ConfirmationModal.js": "128", "/Users/<USER>/Desktop/Freelances/Mohssin Tetouan/UNIMEDCARE/web-front/src/components/InputModel.js": "129", "/Users/<USER>/Desktop/Freelances/Mohssin Tetouan/UNIMEDCARE/web-front/src/redux/actions/clientActions.js": "130", "/Users/<USER>/Desktop/Freelances/Mohssin Tetouan/UNIMEDCARE/web-front/src/screens/clients/AddClientScreen.js": "131", "/Users/<USER>/Desktop/Freelances/Mohssin Tetouan/UNIMEDCARE/web-front/src/screens/clients/EditClientScreen.js": "132", "/Users/<USER>/Desktop/Freelances/Mohssin Tetouan/UNIMEDCARE/web-front/src/components/Selector.js": "133", "/Users/<USER>/Desktop/Freelances/Mohssin Tetouan/UNIMEDCARE/web-front/src/components/LayoutSection.js": "134", "/Users/<USER>/Desktop/Freelances/Mohssin Tetouan/UNIMEDCARE/web-front/src/screens/cases/EditCaseScreen.js": "135", "/Users/<USER>/Desktop/Freelances/Mohssin Tetouan/UNIMEDCARE/web-front/src/screens/proveedors/ProvidersMapScreen.js": "136", "/Users/<USER>/Desktop/Freelances/Mohssin Tetouan/UNIMEDCARE/web-front/src/screens/coordinator-space/CoordinatorSpaceScreen.js": "137", "/Users/<USER>/Desktop/Freelances/Mohssin Tetouan/UNIMEDCARE/web-front/src/screens/settings/SettingsScreen.js": "138", "/Users/<USER>/Desktop/Freelances/Mohssin Tetouan/UNIMEDCARE/web-front/src/screens/help/HelpScreen.js": "139", "/Users/<USER>/Desktop/Freelances/Mohssin Tetouan/UNIMEDCARE/web-front/src/screens/help/FaqScreen.js": "140", "/Users/<USER>/Desktop/Freelances/Mohssin Tetouan/UNIMEDCARE/web-front/src/screens/contact/ContactSupportScreen.js": "141", "/Users/<USER>/Desktop/Freelances/Mohssin Tetouan/UNIMEDCARE/web-front/src/screens/proveedors/AddProviderScreen.js": "142", "/Users/<USER>/Desktop/Freelances/Mohssin Tetouan/UNIMEDCARE/web-front/src/screens/insurances/InsurancesScreen.js": "143", "/Users/<USER>/Desktop/Freelances/Mohssin Tetouan/UNIMEDCARE/web-front/src/redux/reducers/insurancereducers.js": "144", "/Users/<USER>/Desktop/Freelances/Mohssin Tetouan/UNIMEDCARE/web-front/src/redux/constants/insuranceConstants.js": "145", "/Users/<USER>/Desktop/Freelances/Mohssin Tetouan/UNIMEDCARE/web-front/src/redux/actions/insuranceActions.js": "146", "/Users/<USER>/Desktop/Freelances/Mohssin Tetouan/UNIMEDCARE/web-front/src/screens/insurances/AddInsuranceScreen.js": "147", "/Users/<USER>/Desktop/Freelances/Mohssin Tetouan/UNIMEDCARE/web-front/src/screens/insurances/EditInsuranceScreen.js": "148", "/Users/<USER>/Desktop/Freelances/Mohssin Tetouan/UNIMEDCARE/web-front/src/screens/proveedors/EditProviderScreen.js": "149", "/Users/<USER>/Desktop/Freelances/Mohssin Tetouan/UNIMEDCARE/web-front/src/screens/coordinator-space/AddCoordinatorScreen.js": "150", "/Users/<USER>/Desktop/Freelances/Mohssin Tetouan/UNIMEDCARE/web-front/src/screens/profile/ProfileScreen.js": "151", "/Users/<USER>/Desktop/Freelances/Mohssin Tetouan/UNIMEDCARE/web-front/src/screens/coordinator-space/EditCoordinatorScreen.js": "152", "/Users/<USER>/Desktop/Freelances/Mohssin Tetouan/UNIMEDCARE/web-front/src/screens/coordinator-space/CoordinatorProfileScreen.js": "153", "/Users/<USER>/Desktop/Freelances/Mohssin Tetouan/UNIMEDCARE/web-front/src/screens/insurances/InsuranceProfileScreen.js": "154", "/Users/<USER>/Desktop/Freelances/Mohssin Tetouan/UNIMEDCARE/web-front/src/screens/proveedors/ProviderProfileScreen.js": "155", "/Users/<USER>/Desktop/Freelances/Mohssin Tetouan/UNIMEDCARE/web-front/src/screens/auth/ConfirmPasswordScreen.js": "156", "/Users/<USER>/Desktop/Freelances/Mohssin Tetouan/UNIMEDCARE/web-front/src/screens/auth/ResetPasswordScreen.js": "157", "/Users/<USER>/Desktop/Freelances/Mohssin Tetouan/UNIMEDCARE/web-front/src/screens/auth/SendResetPasswordScreen.js": "158", "/Users/<USER>/Desktop/Freelances/Mohssin Tetouan/UNIMEDCARE/web-front/src/components/LoadingSpinner.js": "159", "/Users/<USER>/Desktop/Freelances/Mohssin Tetouan/UNIMEDCARE/web-front/src/components/ErrorBoundary.js": "160", "/Users/<USER>/Desktop/Freelances/Mohssin Tetouan/UNIMEDCARE/web-front/src/screens/errors/NotFoundScreen.js": "161", "/Users/<USER>/Desktop/Freelances/Mohssin Tetouan/UNIMEDCARE/web-front/src/screens/errors/ErrorBoundaryScreen.js": "162", "/Users/<USER>/Desktop/Freelances/Mohssin Tetouan/UNIMEDCARE/web-front/src/screens/errors/ServerErrorScreen.js": "163", "/Users/<USER>/Desktop/Freelances/Mohssin Tetouan/UNIMEDCARE/web-front/src/screens/errors/UnauthorizedScreen.js": "164", "/Users/<USER>/Desktop/Freelances/Mohssin Tetouan/UNIMEDCARE/web-front/src/screens/errors/ForbiddenScreen.js": "165", "/Users/<USER>/Desktop/Freelances/Mohssin Tetouan/UNIMEDCARE/web-front/src/utils/errorHandler.js": "166", "/Users/<USER>/Desktop/Freelances/Mohssin Tetouan/UNIMEDCARE/web-front/src/components/CaseHistory.js": "167", "/Users/<USER>/Desktop/Freelances/Mohssin Tetouan/UNIMEDCARE/web-front/src/components/Pagination.js": "168", "/Users/<USER>/Desktop/Freelances/Mohssin Tetouan/UNIMEDCARE/web-front/src/components/FontTest.js": "169", "/Users/<USER>/Desktop/Freelances/Mohssin Tetouan/UNIMEDCARE/web-front/src/brockmann-font.js": "170"}, {"size": 2045, "mtime": 1717177912412, "results": "171", "hashOfConfig": "172"}, {"size": 7146, "mtime": 1716675667325, "results": "173", "hashOfConfig": "172"}, {"size": 362, "mtime": 1687533665688, "results": "174", "hashOfConfig": "172"}, {"size": 6512, "mtime": 1717284373439, "results": "175", "hashOfConfig": "172"}, {"size": 331, "mtime": 1716115383553, "results": "176", "hashOfConfig": "172"}, {"size": 13878, "mtime": 1719914313136, "results": "177", "hashOfConfig": "172"}, {"size": 22935, "mtime": 1719931320601, "results": "178", "hashOfConfig": "172"}, {"size": 15906, "mtime": 1719914313275, "results": "179", "hashOfConfig": "172"}, {"size": 12336, "mtime": 1719914313158, "results": "180", "hashOfConfig": "172"}, {"size": 11258, "mtime": 1719914313193, "results": "181", "hashOfConfig": "172"}, {"size": 12111, "mtime": 1719914313146, "results": "182", "hashOfConfig": "172"}, {"size": 14233, "mtime": 1719914313135, "results": "183", "hashOfConfig": "172"}, {"size": 40679, "mtime": 1719914313141, "results": "184", "hashOfConfig": "172"}, {"size": 32693, "mtime": 1719914313276, "results": "185", "hashOfConfig": "172"}, {"size": 40785, "mtime": 1719914313269, "results": "186", "hashOfConfig": "172"}, {"size": 12002, "mtime": 1719914313136, "results": "187", "hashOfConfig": "172"}, {"size": 35887, "mtime": 1719914313134, "results": "188", "hashOfConfig": "172"}, {"size": 38325, "mtime": 1719914313141, "results": "189", "hashOfConfig": "172"}, {"size": 38998, "mtime": 1719914313136, "results": "190", "hashOfConfig": "172"}, {"size": 5771, "mtime": 1719914316320, "results": "191", "hashOfConfig": "172"}, {"size": 13307, "mtime": 1719914313142, "results": "192", "hashOfConfig": "172"}, {"size": 14756, "mtime": 1719914316391, "results": "193", "hashOfConfig": "172"}, {"size": 12072, "mtime": 1719914316391, "results": "194", "hashOfConfig": "172"}, {"size": 14320, "mtime": 1719914313136, "results": "195", "hashOfConfig": "172"}, {"size": 13733, "mtime": 1719914316352, "results": "196", "hashOfConfig": "172"}, {"size": 22003, "mtime": 1719914313135, "results": "197", "hashOfConfig": "172"}, {"size": 283, "mtime": 1714860124249, "results": "198", "hashOfConfig": "172"}, {"size": 17164, "mtime": 1719914313141, "results": "199", "hashOfConfig": "172"}, {"size": 21616, "mtime": 1719914316377, "results": "200", "hashOfConfig": "172"}, {"size": 44857, "mtime": 1719914313127, "results": "201", "hashOfConfig": "172"}, {"size": 46700, "mtime": 1719914313135, "results": "202", "hashOfConfig": "172"}, {"size": 14292, "mtime": 1719914316554, "results": "203", "hashOfConfig": "172"}, {"size": 14063, "mtime": 1719914316402, "results": "204", "hashOfConfig": "172"}, {"size": 19570, "mtime": 1719914316422, "results": "205", "hashOfConfig": "172"}, {"size": 126, "mtime": 1710930972012, "results": "206", "hashOfConfig": "172"}, {"size": 24229, "mtime": 1719914316502, "results": "207", "hashOfConfig": "172"}, {"size": 21272, "mtime": 1719914316453, "results": "208", "hashOfConfig": "172"}, {"size": 12232, "mtime": 1719914316534, "results": "209", "hashOfConfig": "172"}, {"size": 53142, "mtime": 1719914316554, "results": "210", "hashOfConfig": "172"}, {"size": 15999, "mtime": 1719914316382, "results": "211", "hashOfConfig": "172"}, {"size": 16750, "mtime": 1719914316351, "results": "212", "hashOfConfig": "172"}, {"size": 15324, "mtime": 1719914316356, "results": "213", "hashOfConfig": "172"}, {"size": 14466, "mtime": 1719914316351, "results": "214", "hashOfConfig": "172"}, {"size": 17162, "mtime": 1719914316386, "results": "215", "hashOfConfig": "172"}, {"size": 14017, "mtime": 1719914316376, "results": "216", "hashOfConfig": "172"}, {"size": 13425, "mtime": 1719914316534, "results": "217", "hashOfConfig": "172"}, {"size": 14779, "mtime": 1719914316386, "results": "218", "hashOfConfig": "172"}, {"size": 14496, "mtime": 1719914316395, "results": "219", "hashOfConfig": "172"}, {"size": 3281, "mtime": 1717282980566, "results": "220", "hashOfConfig": "172"}, {"size": 3564, "mtime": 1717284346687, "results": "221", "hashOfConfig": "172"}, {"size": 3017, "mtime": 1716147811220, "results": "222", "hashOfConfig": "172"}, {"size": 3185, "mtime": 1712403457268, "results": "223", "hashOfConfig": "172"}, {"size": 1909, "mtime": 1710861607613, "results": "224", "hashOfConfig": "172"}, {"size": 1455, "mtime": 1715214521686, "results": "225", "hashOfConfig": "172"}, {"size": 1868, "mtime": 1710863408599, "results": "226", "hashOfConfig": "172"}, {"size": 17239, "mtime": 1717282282206, "results": "227", "hashOfConfig": "172"}, {"size": 3265, "mtime": 1716146692551, "results": "228", "hashOfConfig": "172"}, {"size": 11334, "mtime": 1717269645121, "results": "229", "hashOfConfig": "172"}, {"size": 3524, "mtime": 1716149411118, "results": "230", "hashOfConfig": "172"}, {"size": 11363, "mtime": 1718402835518, "results": "231", "hashOfConfig": "172"}, {"size": 2705, "mtime": 1714860124249, "results": "232", "hashOfConfig": "172"}, {"size": 291, "mtime": 1687700672494, "results": "233", "hashOfConfig": "172"}, {"size": 2225, "mtime": 1714230526421, "results": "234", "hashOfConfig": "172"}, {"size": 2795, "mtime": 1719918479298, "results": "235", "hashOfConfig": "172"}, {"size": 2808, "mtime": 1715293690137, "results": "236", "hashOfConfig": "172"}, {"size": 6509, "mtime": 1710513719145, "results": "237", "hashOfConfig": "172"}, {"size": 389, "mtime": 1719921746435, "results": "238", "hashOfConfig": "172"}, {"size": 746, "mtime": 1710282155725, "results": "239", "hashOfConfig": "172"}, {"size": 6026, "mtime": 1718712720480, "results": "240", "hashOfConfig": "172"}, {"size": 19408, "mtime": 1718712796980, "results": "241", "hashOfConfig": "172"}, {"size": 5518, "mtime": 1718712689535, "results": "242", "hashOfConfig": "172"}, {"size": 3440, "mtime": 1718713079642, "results": "243", "hashOfConfig": "172"}, {"size": 3557, "mtime": 1718713124177, "results": "244", "hashOfConfig": "172"}, {"size": 5704, "mtime": 1718712663093, "results": "245", "hashOfConfig": "172"}, {"size": 6141, "mtime": 1718713185895, "results": "246", "hashOfConfig": "172"}, {"size": 1166, "mtime": 1718712807573, "results": "247", "hashOfConfig": "172"}, {"size": 5790, "mtime": 1718713034557, "results": "248", "hashOfConfig": "172"}, {"size": 6924, "mtime": 1718713219876, "results": "249", "hashOfConfig": "172"}, {"size": 28200, "mtime": 1718712988317, "results": "250", "hashOfConfig": "172"}, {"size": 512, "mtime": 1710861344635, "results": "251", "hashOfConfig": "172"}, {"size": 494, "mtime": 1710863200638, "results": "252", "hashOfConfig": "172"}, {"size": 156, "mtime": 1714859733708, "results": "253", "hashOfConfig": "172"}, {"size": 784, "mtime": 1716147700514, "results": "254", "hashOfConfig": "172"}, {"size": 904, "mtime": 1717282873042, "results": "255", "hashOfConfig": "172"}, {"size": 1074, "mtime": 1717284239016, "results": "256", "hashOfConfig": "172"}, {"size": 3351, "mtime": 1717269526641, "results": "257", "hashOfConfig": "172"}, {"size": 874, "mtime": 1716146556777, "results": "258", "hashOfConfig": "172"}, {"size": 874, "mtime": 1712403317208, "results": "259", "hashOfConfig": "172"}, {"size": 1024, "mtime": 1716149287383, "results": "260", "hashOfConfig": "172"}, {"size": 4948, "mtime": 1717281657818, "results": "261", "hashOfConfig": "172"}, {"size": 3851, "mtime": 1713910560703, "results": "262", "hashOfConfig": "172"}, {"size": 49192, "mtime": 1719914312574, "results": "263", "hashOfConfig": "172"}, {"size": 1137, "mtime": 1719914312537, "results": "264", "hashOfConfig": "172"}, {"size": 3728, "mtime": 1710513168082, "results": "265", "hashOfConfig": "172"}, {"size": 38336, "mtime": 1719931682039, "results": "266", "hashOfConfig": "172"}, {"size": 1408, "mtime": 1746015028158, "results": "267", "hashOfConfig": "268"}, {"size": 5783, "mtime": 1746358248744, "results": "269", "hashOfConfig": "268"}, {"size": 362, "mtime": 1687533665688, "results": "270", "hashOfConfig": "268"}, {"size": 4071, "mtime": 1745963020066, "results": "271", "hashOfConfig": "268"}, {"size": 788, "mtime": 1749763344681, "results": "272", "hashOfConfig": "268"}, {"size": 11891, "mtime": 1745998781908, "results": "273", "hashOfConfig": "268"}, {"size": 839, "mtime": 1730802480979, "results": "274", "hashOfConfig": "268"}, {"size": 69619, "mtime": 1749929729332, "results": "275", "hashOfConfig": "268"}, {"size": 11559, "mtime": 1749842643273, "results": "276", "hashOfConfig": "268"}, {"size": 3185, "mtime": 1712403457268, "results": "277", "hashOfConfig": "268"}, {"size": 38949, "mtime": 1749930020341, "results": "278", "hashOfConfig": "268"}, {"size": 2246, "mtime": 1731682023134, "results": "279", "hashOfConfig": "268"}, {"size": 21528, "mtime": 1749842572653, "results": "280", "hashOfConfig": "268"}, {"size": 3339, "mtime": 1731708205814, "results": "281", "hashOfConfig": "268"}, {"size": 874, "mtime": 1712403317208, "results": "282", "hashOfConfig": "268"}, {"size": 26538, "mtime": 1749928910111, "results": "283", "hashOfConfig": "268"}, {"size": 10969, "mtime": 1745964381693, "results": "284", "hashOfConfig": "268"}, {"size": 3063, "mtime": 1745962594423, "results": "285", "hashOfConfig": "268"}, {"size": 24208, "mtime": 1749762621225, "results": "286", "hashOfConfig": "268"}, {"size": 274, "mtime": 1728036848028, "results": "287", "hashOfConfig": "268"}, {"size": 3016, "mtime": 1732749888809, "results": "288", "hashOfConfig": "268"}, {"size": 107832, "mtime": 1746383465280, "results": "289", "hashOfConfig": "268"}, {"size": 3427, "mtime": 1727967256141, "results": "290", "hashOfConfig": "268"}, {"size": 934, "mtime": 1723021812794, "results": "291", "hashOfConfig": "268"}, {"size": 12231, "mtime": 1749843434869, "results": "292", "hashOfConfig": "268"}, {"size": 117554, "mtime": 1749843642486, "results": "293", "hashOfConfig": "268"}, {"size": 3100, "mtime": 1729979932500, "results": "294", "hashOfConfig": "268"}, {"size": 3907, "mtime": 1745998745289, "results": "295", "hashOfConfig": "268"}, {"size": 14826, "mtime": 1746375558027, "results": "296", "hashOfConfig": "268"}, {"size": 3728, "mtime": 1722256370599, "results": "297", "hashOfConfig": "298"}, {"size": 2582, "mtime": 1742898753696, "results": "299", "hashOfConfig": "268"}, {"size": 12329, "mtime": 1724066134981, "results": "300", "hashOfConfig": "268"}, {"size": 7319, "mtime": 1745833775890, "results": "301", "hashOfConfig": "268"}, {"size": 2795, "mtime": 1722256372707, "results": "302", "hashOfConfig": "268"}, {"size": 5674, "mtime": 1724066082289, "results": "303", "hashOfConfig": "268"}, {"size": 10082, "mtime": 1724065376029, "results": "304", "hashOfConfig": "268"}, {"size": 13003, "mtime": 1724067053141, "results": "305", "hashOfConfig": "268"}, {"size": 6509, "mtime": 1722256375933, "results": "306", "hashOfConfig": "268"}, {"size": 389, "mtime": 1724063189989, "results": "307", "hashOfConfig": "268"}, {"size": 174179, "mtime": 1749930389788, "results": "308", "hashOfConfig": "268"}, {"size": 55600, "mtime": 1749842062635, "results": "309", "hashOfConfig": "268"}, {"size": 14123, "mtime": 1749928986063, "results": "310", "hashOfConfig": "268"}, {"size": 20209, "mtime": 1744910270175, "results": "311", "hashOfConfig": "268"}, {"size": 2218, "mtime": 1726822779091, "results": "312", "hashOfConfig": "268"}, {"size": 2215, "mtime": 1726822921076, "results": "313", "hashOfConfig": "268"}, {"size": 8555, "mtime": 1746380145112, "results": "314", "hashOfConfig": "268"}, {"size": 44427, "mtime": 1744294288283, "results": "315", "hashOfConfig": "268"}, {"size": 14542, "mtime": 1730586796750, "results": "316", "hashOfConfig": "268"}, {"size": 3507, "mtime": 1727966445462, "results": "317", "hashOfConfig": "268"}, {"size": 964, "mtime": 1727877840174, "results": "318", "hashOfConfig": "268"}, {"size": 7259, "mtime": 1749807693459, "results": "319", "hashOfConfig": "268"}, {"size": 18397, "mtime": 1730586826607, "results": "320", "hashOfConfig": "268"}, {"size": 19976, "mtime": 1730586807080, "results": "321", "hashOfConfig": "268"}, {"size": 50358, "mtime": 1744294292878, "results": "322", "hashOfConfig": "268"}, {"size": 16104, "mtime": 1730211981365, "results": "323", "hashOfConfig": "268"}, {"size": 24006, "mtime": 1743259424194, "results": "324", "hashOfConfig": "268"}, {"size": 17067, "mtime": 1730211961097, "results": "325", "hashOfConfig": "268"}, {"size": 24910, "mtime": 1743259273476, "results": "326", "hashOfConfig": "268"}, {"size": 16369, "mtime": 1743259419442, "results": "327", "hashOfConfig": "268"}, {"size": 17886, "mtime": 1743259431159, "results": "328", "hashOfConfig": "268"}, {"size": 5379, "mtime": 1745998825375, "results": "329", "hashOfConfig": "268"}, {"size": 3774, "mtime": 1745998795424, "results": "330", "hashOfConfig": "268"}, {"size": 1658, "mtime": 1745998803242, "results": "331", "hashOfConfig": "268"}, {"size": 476, "mtime": 1745484218634, "results": "332", "hashOfConfig": "268"}, {"size": 835, "mtime": 1745834379475, "results": "333", "hashOfConfig": "268"}, {"size": 1666, "mtime": 1745834889268, "results": "334", "hashOfConfig": "268"}, {"size": 3399, "mtime": 1745834867882, "results": "335", "hashOfConfig": "268"}, {"size": 1662, "mtime": 1745834900016, "results": "336", "hashOfConfig": "268"}, {"size": 1591, "mtime": 1745834908358, "results": "337", "hashOfConfig": "268"}, {"size": 1649, "mtime": 1745834880163, "results": "338", "hashOfConfig": "268"}, {"size": 1276, "mtime": 1745834392318, "results": "339", "hashOfConfig": "268"}, {"size": 18486, "mtime": 1746183722446, "results": "340", "hashOfConfig": "268"}, {"size": 4431, "mtime": 1745964443947, "results": "341", "hashOfConfig": "268"}, {"size": 2063, "mtime": 1745999100057, "results": "342", "hashOfConfig": "268"}, {"size": 905, "mtime": 1746015013663, "results": "343", "hashOfConfig": "268"}, {"filePath": "344", "messages": "345", "suppressedMessages": "346", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "7<PERSON><PERSON>", {"filePath": "347", "messages": "348", "suppressedMessages": "349", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "350", "messages": "351", "suppressedMessages": "352", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "353", "messages": "354", "suppressedMessages": "355", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "356", "messages": "357", "suppressedMessages": "358", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "359", "messages": "360", "suppressedMessages": "361", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "362", "messages": "363", "suppressedMessages": "364", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 14, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "365", "messages": "366", "suppressedMessages": "367", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "368", "messages": "369", "suppressedMessages": "370", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 9, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "371", "messages": "372", "suppressedMessages": "373", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "374", "messages": "375", "suppressedMessages": "376", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "377", "messages": "378", "suppressedMessages": "379", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "380", "messages": "381", "suppressedMessages": "382", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 17, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "383", "messages": "384", "suppressedMessages": "385", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "386", "messages": "387", "suppressedMessages": "388", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 17, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "389", "messages": "390", "suppressedMessages": "391", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 15, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "392", "messages": "393", "suppressedMessages": "394", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 7, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "395", "messages": "396", "suppressedMessages": "397", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 14, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "398", "messages": "399", "suppressedMessages": "400", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 14, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "401", "messages": "402", "suppressedMessages": "403", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "404", "messages": "405", "suppressedMessages": "406", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "407", "messages": "408", "suppressedMessages": "409", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 13, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "410", "messages": "411", "suppressedMessages": "412", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 10, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "413", "messages": "414", "suppressedMessages": "415", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "416", "messages": "417", "suppressedMessages": "418", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 10, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "419", "messages": "420", "suppressedMessages": "421", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 14, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "422", "messages": "423", "suppressedMessages": "424", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "425", "messages": "426", "suppressedMessages": "427", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 9, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "428", "messages": "429", "suppressedMessages": "430", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 10, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "431", "messages": "432", "suppressedMessages": "433", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 27, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "434", "messages": "435", "suppressedMessages": "436", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 31, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "437", "messages": "438", "suppressedMessages": "439", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "440", "messages": "441", "suppressedMessages": "442", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "443", "messages": "444", "suppressedMessages": "445", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 7, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "446", "messages": "447", "suppressedMessages": "448", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "449", "messages": "450", "suppressedMessages": "451", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 9, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "452", "messages": "453", "suppressedMessages": "454", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 12, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "455", "messages": "456", "suppressedMessages": "457", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 7, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "458", "messages": "459", "suppressedMessages": "460", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 22, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "461", "messages": "462", "suppressedMessages": "463", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 8, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "464", "messages": "465", "suppressedMessages": "466", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 14, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "467", "messages": "468", "suppressedMessages": "469", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 9, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "470", "messages": "471", "suppressedMessages": "472", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 6, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "473", "messages": "474", "suppressedMessages": "475", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 14, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "476", "messages": "477", "suppressedMessages": "478", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 6, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "479", "messages": "480", "suppressedMessages": "481", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 6, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "482", "messages": "483", "suppressedMessages": "484", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 13, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "485", "messages": "486", "suppressedMessages": "487", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 9, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "488", "messages": "489", "suppressedMessages": "490", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "491", "messages": "492", "suppressedMessages": "493", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "494", "messages": "495", "suppressedMessages": "496", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "497", "messages": "498", "suppressedMessages": "499", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "500", "messages": "501", "suppressedMessages": "502", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "503", "messages": "504", "suppressedMessages": "505", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "506", "messages": "507", "suppressedMessages": "508", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "509", "messages": "510", "suppressedMessages": "511", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "512", "messages": "513", "suppressedMessages": "514", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "515", "messages": "516", "suppressedMessages": "517", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "518", "messages": "519", "suppressedMessages": "520", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "521", "messages": "522", "suppressedMessages": "523", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "524", "messages": "525", "suppressedMessages": "526", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "527", "messages": "528", "suppressedMessages": "529", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "530", "messages": "531", "suppressedMessages": "532", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "533", "messages": "534", "suppressedMessages": "535", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "536", "messages": "537", "suppressedMessages": "538", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "539", "messages": "540", "suppressedMessages": "541", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "542", "messages": "543", "suppressedMessages": "544", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "545", "messages": "546", "suppressedMessages": "547", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "548", "messages": "549", "suppressedMessages": "550", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "551", "messages": "552", "suppressedMessages": "553", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "554", "messages": "555", "suppressedMessages": "556", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "557", "messages": "558", "suppressedMessages": "559", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "560", "messages": "561", "suppressedMessages": "562", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "563", "messages": "564", "suppressedMessages": "565", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "566", "messages": "567", "suppressedMessages": "568", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "569", "messages": "570", "suppressedMessages": "571", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "572", "messages": "573", "suppressedMessages": "574", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "575", "messages": "576", "suppressedMessages": "577", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "578", "messages": "579", "suppressedMessages": "580", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "581", "messages": "582", "suppressedMessages": "583", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "584", "messages": "585", "suppressedMessages": "586", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "587", "messages": "588", "suppressedMessages": "589", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "590", "messages": "591", "suppressedMessages": "592", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "593", "messages": "594", "suppressedMessages": "595", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "596", "messages": "597", "suppressedMessages": "598", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "599", "messages": "600", "suppressedMessages": "601", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "602", "messages": "603", "suppressedMessages": "604", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "605", "messages": "606", "suppressedMessages": "607", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "608", "messages": "609", "suppressedMessages": "610", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "611", "messages": "612", "suppressedMessages": "613", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "614", "messages": "615", "suppressedMessages": "616", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "617", "messages": "618", "suppressedMessages": "619", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 14, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "620", "messages": "621", "suppressedMessages": "622", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "623", "messages": "624", "suppressedMessages": "625", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "626", "messages": "627", "suppressedMessages": "628", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 14, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "629", "messages": "630", "suppressedMessages": "631", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "5csgke", {"filePath": "632", "messages": "633", "suppressedMessages": "634", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "635", "messages": "636", "suppressedMessages": "637", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "638", "messages": "639", "suppressedMessages": "640", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "641", "messages": "642", "suppressedMessages": "643", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "644", "messages": "645", "suppressedMessages": "646", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "647", "messages": "648", "suppressedMessages": "649", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 6, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "650", "messages": "651", "suppressedMessages": "652", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 14, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "653", "messages": "654", "suppressedMessages": "655", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "656", "messages": "657", "suppressedMessages": "658", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "659", "messages": "660", "suppressedMessages": "661", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "662", "messages": "663", "suppressedMessages": "664", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "665", "messages": "666", "suppressedMessages": "667", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "668", "messages": "669", "suppressedMessages": "670", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "671", "messages": "672", "suppressedMessages": "673", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "674", "messages": "675", "suppressedMessages": "676", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "677", "messages": "678", "suppressedMessages": "679", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "680", "messages": "681", "suppressedMessages": "682", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "683", "messages": "684", "suppressedMessages": "685", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "686", "messages": "687", "suppressedMessages": "688", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "689", "messages": "690", "suppressedMessages": "691", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "692", "messages": "693", "suppressedMessages": "694", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 23, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "695", "messages": "696", "suppressedMessages": "697", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "698", "messages": "699", "suppressedMessages": "700", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "701", "messages": "702", "suppressedMessages": "703", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "704", "messages": "705", "suppressedMessages": "706", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 51, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "707", "messages": "708", "suppressedMessages": "709", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "710", "messages": "711", "suppressedMessages": "712", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 6, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "713", "messages": "714", "suppressedMessages": "715", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 7, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "716", "messages": "717", "suppressedMessages": "718", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "9q2kwv", {"filePath": "719", "messages": "720", "suppressedMessages": "721", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 8, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "722", "messages": "723", "suppressedMessages": "724", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 7, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "725", "messages": "726", "suppressedMessages": "727", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "728", "messages": "729", "suppressedMessages": "730", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "731", "messages": "732", "suppressedMessages": "733", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "734", "messages": "735", "suppressedMessages": "736", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 9, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "737", "messages": "738", "suppressedMessages": "739", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 10, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "740", "messages": "741", "suppressedMessages": "742", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "743", "messages": "744", "suppressedMessages": "745", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "746", "messages": "747", "suppressedMessages": "748", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 48, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "749", "messages": "750", "suppressedMessages": "751", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 6, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "752", "messages": "753", "suppressedMessages": "754", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "755", "messages": "756", "suppressedMessages": "757", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 13, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "758", "messages": "759", "suppressedMessages": "760", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "761", "messages": "762", "suppressedMessages": "763", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "764", "messages": "765", "suppressedMessages": "766", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "767", "messages": "768", "suppressedMessages": "769", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 14, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "770", "messages": "771", "suppressedMessages": "772", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "773", "messages": "774", "suppressedMessages": "775", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "776", "messages": "777", "suppressedMessages": "778", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "779", "messages": "780", "suppressedMessages": "781", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "782", "messages": "783", "suppressedMessages": "784", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "785", "messages": "786", "suppressedMessages": "787", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 10, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "788", "messages": "789", "suppressedMessages": "790", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 16, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "791", "messages": "792", "suppressedMessages": "793", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "794", "messages": "795", "suppressedMessages": "796", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 6, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "797", "messages": "798", "suppressedMessages": "799", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 10, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "800", "messages": "801", "suppressedMessages": "802", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "803", "messages": "804", "suppressedMessages": "805", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "806", "messages": "807", "suppressedMessages": "808", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "809", "messages": "810", "suppressedMessages": "811", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "812", "messages": "813", "suppressedMessages": "814", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "815", "messages": "816", "suppressedMessages": "817", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "818", "messages": "819", "suppressedMessages": "820", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "821", "messages": "822", "suppressedMessages": "823", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "824", "messages": "825", "suppressedMessages": "826", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "827", "messages": "828", "suppressedMessages": "829", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "830", "messages": "831", "suppressedMessages": "832", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "833", "messages": "834", "suppressedMessages": "835", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "836", "messages": "837", "suppressedMessages": "838", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "839", "messages": "840", "suppressedMessages": "841", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "842", "messages": "843", "suppressedMessages": "844", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "845", "messages": "846", "suppressedMessages": "847", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "848", "messages": "849", "suppressedMessages": "850", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "851", "messages": "852", "suppressedMessages": "853", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "/Users/<USER>/Desktop/Freelances/<PERSON><PERSON><PERSON>/Project Location/web-location/src/index.js", [], [], "/Users/<USER>/Desktop/Freelances/<PERSON><PERSON><PERSON>/Project Location/web-location/src/App.js", [], [], "/Users/<USER>/Desktop/Freelances/<PERSON><PERSON><PERSON>/Project Location/web-location/src/reportWebVitals.js", [], [], "/Users/<USER>/Desktop/Freelances/<PERSON><PERSON><PERSON>/Project Location/web-location/src/redux/store.js", [], [], "/Users/<USER>/Desktop/Freelances/<PERSON><PERSON><PERSON>/Project Location/web-location/src/axios.js", [], [], "/Users/<USER>/Desktop/Freelances/<PERSON><PERSON><PERSON>/Project Location/web-location/src/screens/client/ContratClientScreen.js", ["854", "855"], [], "/Users/<USER>/Desktop/Freelances/<PERSON><PERSON><PERSON>/Project Location/web-location/src/screens/client/ClientScreen.js", ["856", "857", "858", "859", "860", "861", "862", "863", "864", "865", "866", "867", "868", "869"], [], "/Users/<USER>/Desktop/Freelances/<PERSON><PERSON><PERSON>/Project Location/web-location/src/screens/car/CarScreen.js", ["870", "871", "872", "873"], [], "/Users/<USER>/Desktop/Freelances/<PERSON><PERSON><PERSON>/Project Location/web-location/src/screens/agences/EditAgenceScreen.js", ["874", "875", "876", "877", "878", "879", "880", "881", "882"], [], "/Users/<USER>/Desktop/Freelances/<PERSON><PERSON><PERSON>/Project Location/web-location/src/screens/agences/AddAgenceScreen.js", ["883", "884", "885", "886"], [], "/Users/<USER>/Desktop/Freelances/<PERSON><PERSON><PERSON>/Project Location/web-location/src/screens/agences/AgenceScreen.js", ["887", "888", "889"], [], "/Users/<USER>/Desktop/Freelances/<PERSON><PERSON><PERSON>/Project Location/web-location/src/screens/reservation/ReservationScreen.js", ["890", "891", "892", "893"], [], "/Users/<USER>/Desktop/Freelances/<PERSON><PERSON><PERSON>/Project Location/web-location/src/screens/reservation/EditReservationScreen.js", ["894", "895", "896", "897", "898", "899", "900", "901", "902", "903", "904", "905", "906", "907", "908", "909", "910"], [], "/Users/<USER>/Desktop/Freelances/<PERSON><PERSON><PERSON>/Project Location/web-location/src/screens/dashboard/DashboardScreen.js", ["911", "912", "913", "914", "915"], [], "/Users/<USER>/Desktop/Freelances/<PERSON><PERSON><PERSON>/Project Location/web-location/src/screens/car/EditCarScreen.js", ["916", "917", "918", "919", "920", "921", "922", "923", "924", "925", "926", "927", "928", "929", "930", "931", "932"], [], "/Users/<USER>/Desktop/Freelances/<PERSON><PERSON><PERSON>/Project Location/web-location/src/screens/profile/ProfileScreen.js", ["933", "934", "935", "936", "937", "938", "939", "940", "941", "942", "943", "944", "945", "946", "947"], [], "/Users/<USER>/Desktop/Freelances/<PERSON><PERSON><PERSON>/Project Location/web-location/src/screens/contrats/ContratScreen.js", ["948", "949", "950", "951", "952", "953", "954"], [], "/Users/<USER>/Desktop/Freelances/<PERSON><PERSON><PERSON>/Project Location/web-location/src/screens/car/AddCarScreen.js", ["955", "956", "957", "958", "959", "960", "961", "962", "963", "964", "965", "966", "967", "968"], [], "/Users/<USER>/Desktop/Freelances/<PERSON><PERSON><PERSON>/Project Location/web-location/src/screens/reservation/AddReservationScreen.js", ["969", "970", "971", "972", "973", "974", "975", "976", "977", "978", "979", "980", "981", "982"], [], "/Users/<USER>/Desktop/Freelances/<PERSON><PERSON><PERSON>/Project Location/web-location/src/screens/auth/LoginScreen.js", [], [], "/Users/<USER>/Desktop/Freelances/<PERSON><PERSON><PERSON>/Project Location/web-location/src/screens/factures/FactureScreen.js", ["983", "984"], [], "/Users/<USER>/Desktop/Freelances/<PERSON><PERSON><PERSON>/Project Location/web-location/src/screens/contrats/payment/EditPaymentContratScreen.js", ["985", "986", "987", "988", "989", "990", "991", "992", "993", "994", "995", "996", "997"], [], "/Users/<USER>/Desktop/Freelances/<PERSON><PERSON><PERSON>/Project Location/web-location/src/screens/contrats/payment/PaymentContratScreen.js", ["998", "999", "1000", "1001", "1002", "1003", "1004", "1005", "1006", "1007"], [], "/Users/<USER>/Desktop/Freelances/<PERSON><PERSON><PERSON>/Project Location/web-location/src/screens/contrats/SearchContratScreen.js", [], [], "/Users/<USER>/Desktop/Freelances/<PERSON><PERSON><PERSON>/Project Location/web-location/src/screens/contrats/payment/AddPaymentContratScreen.js", ["1008", "1009", "1010", "1011", "1012", "1013", "1014", "1015", "1016", "1017"], [], "/Users/<USER>/Desktop/Freelances/<PERSON><PERSON><PERSON>/Project Location/web-location/src/screens/client/EditClientScreen.js", ["1018", "1019", "1020", "1021", "1022", "1023", "1024", "1025", "1026", "1027", "1028", "1029", "1030", "1031"], [], "/Users/<USER>/Desktop/Freelances/<PERSON><PERSON><PERSON>/Project Location/web-location/src/screens/auth/LogoutScreen.js", ["1032"], [], "/Users/<USER>/Desktop/Freelances/<PERSON><PERSON><PERSON>/Project Location/web-location/src/screens/client/AddClientScreen.js", ["1033", "1034", "1035", "1036", "1037", "1038", "1039", "1040", "1041"], [], "/Users/<USER>/Desktop/Freelances/<PERSON><PERSON><PERSON>/Project Location/web-location/src/screens/contrats/return/AddReturnContratScreen.js", ["1042", "1043", "1044", "1045", "1046", "1047", "1048", "1049", "1050", "1051"], [], "/Users/<USER>/Desktop/Freelances/<PERSON><PERSON><PERSON>/Project Location/web-location/src/screens/contrats/AddContratScreen.js", ["1052", "1053", "1054", "1055", "1056", "1057", "1058", "1059", "1060", "1061", "1062", "1063", "1064", "1065", "1066", "1067", "1068", "1069", "1070", "1071", "1072", "1073", "1074", "1075", "1076", "1077", "1078"], [], "/Users/<USER>/Desktop/Freelances/<PERSON><PERSON><PERSON>/Project Location/web-location/src/screens/contrats/EditContratScreen.js", ["1079", "1080", "1081", "1082", "1083", "1084", "1085", "1086", "1087", "1088", "1089", "1090", "1091", "1092", "1093", "1094", "1095", "1096", "1097", "1098", "1099", "1100", "1101", "1102", "1103", "1104", "1105", "1106", "1107", "1108", "1109"], [], "/Users/<USER>/Desktop/Freelances/<PERSON><PERSON><PERSON>/Project Location/web-location/src/screens/settings/users/AddUserScreen.js", ["1110", "1111", "1112"], [], "/Users/<USER>/Desktop/Freelances/<PERSON><PERSON><PERSON>/Project Location/web-location/src/screens/settings/employes/EmployesScreen.js", ["1113", "1114", "1115", "1116"], [], "/Users/<USER>/Desktop/Freelances/<PERSON><PERSON><PERSON>/Project Location/web-location/src/screens/settings/employes/AddEmployeScreen.js", ["1117", "1118", "1119", "1120", "1121", "1122", "1123"], [], "/Users/<USER>/Desktop/Freelances/<PERSON><PERSON><PERSON>/Project Location/web-location/src/screens/settings/users/EditUserScreen.js", [], [], "/Users/<USER>/Desktop/Freelances/<PERSON><PERSON><PERSON>/Project Location/web-location/src/screens/settings/marques-models/MarquesModelsScreen.js", ["1124", "1125", "1126", "1127", "1128", "1129", "1130", "1131", "1132"], [], "/Users/<USER>/Desktop/Freelances/<PERSON><PERSON><PERSON>/Project Location/web-location/src/screens/settings/employes/EditEmployeScreen.js", ["1133", "1134", "1135", "1136", "1137", "1138", "1139", "1140", "1141", "1142", "1143", "1144"], [], "/Users/<USER>/Desktop/Freelances/<PERSON><PERSON><PERSON>/Project Location/web-location/src/screens/settings/users/UserScreen.js", ["1145", "1146", "1147", "1148", "1149", "1150", "1151"], [], "/Users/<USER>/Desktop/Freelances/<PERSON><PERSON><PERSON>/Project Location/web-location/src/screens/settings/designations/DesignationScreen.js", ["1152", "1153", "1154", "1155", "1156", "1157", "1158", "1159", "1160", "1161", "1162", "1163", "1164", "1165", "1166", "1167", "1168", "1169", "1170", "1171", "1172", "1173"], [], "/Users/<USER>/Desktop/Freelances/<PERSON><PERSON><PERSON>/Project Location/web-location/src/screens/depenses/employes/AddDepenseEmployeScreen.js", ["1174", "1175", "1176", "1177", "1178", "1179", "1180", "1181"], [], "/Users/<USER>/Desktop/Freelances/<PERSON><PERSON><PERSON>/Project Location/web-location/src/screens/depenses/entretiens/EditDepenseEntretienScreen.js", ["1182", "1183", "1184", "1185", "1186", "1187", "1188", "1189", "1190", "1191", "1192", "1193", "1194", "1195"], [], "/Users/<USER>/Desktop/Freelances/<PERSON><PERSON><PERSON>/Project Location/web-location/src/screens/depenses/entretiens/AddDepenseEntretienScreen.js", ["1196", "1197", "1198", "1199", "1200", "1201", "1202", "1203", "1204"], [], "/Users/<USER>/Desktop/Freelances/<PERSON><PERSON><PERSON>/Project Location/web-location/src/screens/depenses/entretiens/DepenseEntretienScreen.js", ["1205", "1206", "1207", "1208", "1209", "1210"], [], "/Users/<USER>/Desktop/Freelances/<PERSON><PERSON><PERSON>/Project Location/web-location/src/screens/depenses/employes/EditDepenseEmployeScreen.js", ["1211", "1212", "1213", "1214", "1215", "1216", "1217", "1218", "1219", "1220", "1221", "1222", "1223", "1224"], [], "/Users/<USER>/Desktop/Freelances/<PERSON><PERSON><PERSON>/Project Location/web-location/src/screens/depenses/employes/DepenseEmployeScreen.js", ["1225", "1226", "1227", "1228", "1229", "1230"], [], "/Users/<USER>/Desktop/Freelances/<PERSON><PERSON><PERSON>/Project Location/web-location/src/screens/depenses/charges/DepenseChargeScreen.js", ["1231", "1232", "1233", "1234", "1235", "1236"], [], "/Users/<USER>/Desktop/Freelances/<PERSON><PERSON><PERSON>/Project Location/web-location/src/screens/depenses/charges/EditDepenseChargeScreen.js", ["1237", "1238", "1239", "1240", "1241", "1242", "1243", "1244", "1245", "1246", "1247", "1248", "1249"], [], "/Users/<USER>/Desktop/Freelances/<PERSON><PERSON><PERSON>/Project Location/web-location/src/screens/depenses/charges/AddDepenseChargeScreen.js", ["1250", "1251", "1252", "1253", "1254", "1255", "1256", "1257", "1258"], [], "/Users/<USER>/Desktop/Freelances/<PERSON><PERSON><PERSON>/Project Location/web-location/src/redux/reducers/employeReducers.js", [], [], "/Users/<USER>/Desktop/Freelances/<PERSON><PERSON><PERSON>/Project Location/web-location/src/redux/reducers/userReducers.js", [], [], "/Users/<USER>/Desktop/Freelances/<PERSON><PERSON><PERSON>/Project Location/web-location/src/redux/reducers/carReducers.js", [], [], "/Users/<USER>/Desktop/Freelances/<PERSON><PERSON><PERSON>/Project Location/web-location/src/redux/reducers/clientReducers.js", [], [], "/Users/<USER>/Desktop/Freelances/<PERSON><PERSON><PERSON>/Project Location/web-location/src/redux/reducers/marqueReducers.js", [], [], "/Users/<USER>/Desktop/Freelances/<PERSON><PERSON><PERSON>/Project Location/web-location/src/redux/reducers/dashReducers.js", ["1259"], [], "/Users/<USER>/Desktop/Freelances/<PERSON><PERSON><PERSON>/Project Location/web-location/src/redux/reducers/modelReducers.js", [], [], "/Users/<USER>/Desktop/Freelances/<PERSON><PERSON><PERSON>/Project Location/web-location/src/redux/reducers/designationReducers.js", [], [], "/Users/<USER>/Desktop/Freelances/<PERSON><PERSON><PERSON>/Project Location/web-location/src/redux/reducers/agenceReducers.js", [], [], "/Users/<USER>/Desktop/Freelances/<PERSON><PERSON><PERSON>/Project Location/web-location/src/redux/reducers/contratReducers.js", [], [], "/Users/<USER>/Desktop/Freelances/<PERSON><PERSON><PERSON>/Project Location/web-location/src/redux/reducers/reservationReducers.js", [], [], "/Users/<USER>/Desktop/Freelances/<PERSON><PERSON><PERSON>/Project Location/web-location/src/constants.js", [], [], "/Users/<USER>/Desktop/Freelances/<PERSON><PERSON><PERSON>/Project Location/web-location/src/layouts/DefaultLayout.js", [], [], "/Users/<USER>/Desktop/Freelances/<PERSON><PERSON><PERSON>/Project Location/web-location/src/components/Loader.js", [], [], "/Users/<USER>/Desktop/Freelances/<PERSON><PERSON><PERSON>/Project Location/web-location/src/components/Alert.js", ["1260"], [], "/Users/<USER>/Desktop/Freelances/<PERSON><PERSON><PERSON>/Project Location/web-location/src/components/InputModel.js", [], [], "/Users/<USER>/Desktop/Freelances/<PERSON><PERSON><PERSON>/Project Location/web-location/src/components/ConfirmationModal.js", [], [], "/Users/<USER>/Desktop/Freelances/<PERSON><PERSON><PERSON>/Project Location/web-location/src/components/Selector.js", ["1261", "1262"], [], "/Users/<USER>/Desktop/Freelances/<PERSON><PERSON><PERSON>/Project Location/web-location/src/components/LayoutSection.js", [], [], "/Users/<USER>/Desktop/Freelances/<PERSON><PERSON><PERSON>/Project Location/web-location/src/components/Paginate.js", ["1263"], [], "/Users/<USER>/Desktop/Freelances/<PERSON><PERSON><PERSON>/Project Location/web-location/src/redux/actions/clientActions.js", [], [], "/Users/<USER>/Desktop/Freelances/<PERSON><PERSON><PERSON>/Project Location/web-location/src/redux/actions/contratActions.js", [], [], "/Users/<USER>/Desktop/Freelances/<PERSON><PERSON><PERSON>/Project Location/web-location/src/redux/actions/carActions.js", [], [], "/Users/<USER>/Desktop/Freelances/<PERSON><PERSON><PERSON>/Project Location/web-location/src/redux/actions/marqueActions.js", [], [], "/Users/<USER>/Desktop/Freelances/<PERSON><PERSON><PERSON>/Project Location/web-location/src/redux/actions/modelActions.js", [], [], "/Users/<USER>/Desktop/Freelances/<PERSON><PERSON><PERSON>/Project Location/web-location/src/redux/actions/agenceActions.js", [], [], "/Users/<USER>/Desktop/Freelances/<PERSON><PERSON><PERSON>/Project Location/web-location/src/redux/actions/reservationActions.js", [], [], "/Users/<USER>/Desktop/Freelances/<PERSON><PERSON><PERSON>/Project Location/web-location/src/redux/actions/dashActions.js", [], [], "/Users/<USER>/Desktop/Freelances/<PERSON><PERSON><PERSON>/Project Location/web-location/src/redux/actions/employeActions.js", [], [], "/Users/<USER>/Desktop/Freelances/<PERSON><PERSON><PERSON>/Project Location/web-location/src/redux/actions/userActions.js", [], [], "/Users/<USER>/Desktop/Freelances/<PERSON><PERSON><PERSON>/Project Location/web-location/src/redux/actions/designationActions.js", [], [], "/Users/<USER>/Desktop/Freelances/<PERSON><PERSON><PERSON>/Project Location/web-location/src/redux/constants/marqueConstants.js", [], [], "/Users/<USER>/Desktop/Freelances/<PERSON><PERSON><PERSON>/Project Location/web-location/src/redux/constants/modelContants.js", [], [], "/Users/<USER>/Desktop/Freelances/<PERSON><PERSON><PERSON>/Project Location/web-location/src/redux/constants/dashConstants.js", [], [], "/Users/<USER>/Desktop/Freelances/<PERSON><PERSON><PERSON>/Project Location/web-location/src/redux/constants/carConstants.js", [], [], "/Users/<USER>/Desktop/Freelances/<PERSON><PERSON><PERSON>/Project Location/web-location/src/redux/constants/employeConstants.js", [], [], "/Users/<USER>/Desktop/Freelances/<PERSON><PERSON><PERSON>/Project Location/web-location/src/redux/constants/userConstants.js", [], [], "/Users/<USER>/Desktop/Freelances/<PERSON><PERSON><PERSON>/Project Location/web-location/src/redux/constants/contratConstant.js", [], [], "/Users/<USER>/Desktop/Freelances/<PERSON><PERSON><PERSON>/Project Location/web-location/src/redux/constants/agenceConstants.js", [], [], "/Users/<USER>/Desktop/Freelances/<PERSON><PERSON><PERSON>/Project Location/web-location/src/redux/constants/clientConstants.js", [], [], "/Users/<USER>/Desktop/Freelances/<PERSON><PERSON><PERSON>/Project Location/web-location/src/redux/constants/reservationConstants.js", [], [], "/Users/<USER>/Desktop/Freelances/<PERSON><PERSON><PERSON>/Project Location/web-location/src/redux/constants/designationConstants.js", [], [], "/Users/<USER>/Desktop/Freelances/<PERSON><PERSON><PERSON>/Project Location/web-location/src/layouts/Header.js", ["1264", "1265", "1266"], [], "/Users/<USER>/Desktop/Freelances/<PERSON><PERSON><PERSON>/Project Location/web-location/src/layouts/Sidebar.js", ["1267", "1268", "1269", "1270", "1271", "1272", "1273", "1274", "1275", "1276", "1277", "1278", "1279", "1280"], [], "/Users/<USER>/Desktop/Freelances/<PERSON><PERSON><PERSON>/Project Location/web-location/src/components/DropdownProfile.js", ["1281", "1282"], [], "/Users/<USER>/Desktop/Freelances/<PERSON><PERSON><PERSON>/Project Location/web-location/src/components/DropdownNotification.js", ["1283", "1284", "1285", "1286", "1287"], [], "/Users/<USER>/Desktop/Freelances/<PERSON><PERSON><PERSON>/Project Location/web-location/src/screens/raport/RaportScreen.js", ["1288", "1289", "1290", "1291", "1292", "1293", "1294", "1295", "1296", "1297", "1298", "1299", "1300", "1301"], [], "/Users/<USER>/Desktop/Freelances/<PERSON><PERSON><PERSON>/UNIMEDCARE/web-front/src/index.js", [], [], "/Users/<USER>/Desktop/Freelances/<PERSON><PERSON><PERSON>/UNIMEDCARE/web-front/src/App.js", ["1302"], [], "/Users/<USER>/Desktop/Freelances/<PERSON><PERSON><PERSON>/UNIMEDCARE/web-front/src/reportWebVitals.js", [], [], "/Users/<USER>/Desktop/Freelances/<PERSON><PERSON><PERSON>/UNIMEDCARE/web-front/src/redux/store.js", ["1303"], [], "/Users/<USER>/Desktop/Freelances/<PERSON><PERSON><PERSON>/UNIMEDCARE/web-front/src/axios.js", [], [], "/Users/<USER>/Desktop/Freelances/<PERSON><PERSON><PERSON>/UNIMEDCARE/web-front/src/screens/auth/LoginScreen.js", ["1304", "1305", "1306"], [], "/Users/<USER>/Desktop/Freelances/<PERSON><PERSON><PERSON>/UNIMEDCARE/web-front/src/screens/auth/LogoutScreen.js", ["1307", "1308", "1309", "1310", "1311", "1312"], [], "/Users/<USER>/Desktop/Freelances/<PERSON><PERSON><PERSON>/UNIMEDCARE/web-front/src/screens/dashboard/DashboardScreen.js", ["1313", "1314", "1315", "1316", "1317", "1318", "1319", "1320", "1321", "1322", "1323", "1324", "1325", "1326"], [], "/Users/<USER>/Desktop/Freelances/<PERSON><PERSON><PERSON>/UNIMEDCARE/web-front/src/redux/reducers/userReducers.js", [], [], "/Users/<USER>/Desktop/Freelances/<PERSON><PERSON><PERSON>/UNIMEDCARE/web-front/src/redux/reducers/clientReducers.js", [], [], "/Users/<USER>/Desktop/Freelances/<PERSON><PERSON><PERSON>/UNIMEDCARE/web-front/src/constants.js", [], [], "/Users/<USER>/Desktop/Freelances/<PERSON><PERSON><PERSON>/UNIMEDCARE/web-front/src/components/Alert.js", ["1327"], [], "/Users/<USER>/Desktop/Freelances/<PERSON><PERSON><PERSON>/UNIMEDCARE/web-front/src/redux/actions/userActions.js", [], [], "/Users/<USER>/Desktop/Freelances/<PERSON><PERSON><PERSON>/UNIMEDCARE/web-front/src/redux/constants/userConstants.js", [], [], "/Users/<USER>/Desktop/Freelances/<PERSON><PERSON><PERSON>/UNIMEDCARE/web-front/src/redux/constants/clientConstants.js", [], [], "/Users/<USER>/Desktop/Freelances/<PERSON><PERSON><PERSON>/UNIMEDCARE/web-front/src/screens/cases/CaseScreen.js", ["1328", "1329", "1330", "1331"], [], "/Users/<USER>/Desktop/Freelances/<PERSON><PERSON><PERSON>/UNIMEDCARE/web-front/src/redux/reducers/caseReducers.js", [], [], "/Users/<USER>/Desktop/Freelances/<PERSON><PERSON><PERSON>/UNIMEDCARE/web-front/src/redux/constants/caseConstants.js", [], [], "/Users/<USER>/Desktop/Freelances/<PERSON><PERSON><PERSON>/UNIMEDCARE/web-front/src/redux/actions/caseActions.js", [], [], "/Users/<USER>/Desktop/Freelances/<PERSON><PERSON><PERSON>/UNIMEDCARE/web-front/src/components/Loader.js", [], [], "/Users/<USER>/Desktop/Freelances/<PERSON><PERSON><PERSON>/UNIMEDCARE/web-front/src/components/Paginate.js", [], [], "/Users/<USER>/Desktop/Freelances/<PERSON><PERSON><PERSON>/UNIMEDCARE/web-front/src/screens/cases/DetailCaseScreen.js", ["1332", "1333", "1334", "1335", "1336", "1337", "1338", "1339", "1340", "1341", "1342", "1343", "1344", "1345", "1346", "1347", "1348", "1349", "1350", "1351", "1352", "1353", "1354"], [], "/Users/<USER>/Desktop/Freelances/<PERSON><PERSON><PERSON>/UNIMEDCARE/web-front/src/redux/reducers/providerReducers.js", [], [], "/Users/<USER>/Desktop/Freelances/<PERSON><PERSON><PERSON>/UNIMEDCARE/web-front/src/redux/constants/providerConstants.js", [], [], "/Users/<USER>/Desktop/Freelances/<PERSON><PERSON><PERSON>/UNIMEDCARE/web-front/src/redux/actions/providerActions.js", [], [], "/Users/<USER>/Desktop/Freelances/<PERSON><PERSON><PERSON>/UNIMEDCARE/web-front/src/screens/cases/AddCaseScreen.js", ["1355", "1356", "1357", "1358", "1359", "1360", "1361", "1362", "1363", "1364", "1365", "1366", "1367", "1368", "1369", "1370", "1371", "1372", "1373", "1374", "1375", "1376", "1377", "1378", "1379", "1380", "1381", "1382", "1383", "1384", "1385", "1386", "1387", "1388", "1389", "1390", "1391", "1392", "1393", "1394", "1395", "1396", "1397", "1398", "1399", "1400", "1401", "1402", "1403", "1404", "1405"], [], "/Users/<USER>/Desktop/Freelances/<PERSON><PERSON><PERSON>/UNIMEDCARE/web-front/src/layouts/DefaultLayout.js", ["1406", "1407", "1408", "1409"], [], "/Users/<USER>/Desktop/Freelances/<PERSON><PERSON><PERSON>/UNIMEDCARE/web-front/src/layouts/Header.js", ["1410", "1411", "1412", "1413", "1414", "1415"], [], "/Users/<USER>/Desktop/Freelances/<PERSON><PERSON><PERSON>/UNIMEDCARE/web-front/src/layouts/Sidebar.js", ["1416", "1417", "1418", "1419", "1420", "1421", "1422"], [], "/Users/<USER>/Desktop/Freelances/<PERSON><PERSON><PERSON>/UNIMEDCARE/web-front/src/components/DropdownNotification.js", ["1423", "1424", "1425", "1426", "1427"], [], "/Users/<USER>/Desktop/Freelances/<PERSON><PERSON><PERSON>/UNIMEDCARE/web-front/src/components/DropdownProfile.js", ["1428", "1429", "1430", "1431", "1432", "1433", "1434", "1435"], [], "/Users/<USER>/Desktop/Freelances/<PERSON><PERSON><PERSON>/UNIMEDCARE/web-front/src/screens/clients/ClientScreen.js", ["1436", "1437", "1438", "1439", "1440", "1441", "1442"], [], "/Users/<USER>/Desktop/Freelances/<PERSON><PERSON><PERSON>/UNIMEDCARE/web-front/src/components/ConfirmationModal.js", [], [], "/Users/<USER>/Desktop/Freelances/<PERSON><PERSON><PERSON>/UNIMEDCARE/web-front/src/components/InputModel.js", [], [], "/Users/<USER>/Desktop/Freelances/<PERSON><PERSON><PERSON>/UNIMEDCARE/web-front/src/redux/actions/clientActions.js", [], [], "/Users/<USER>/Desktop/Freelances/<PERSON><PERSON><PERSON>/UNIMEDCARE/web-front/src/screens/clients/AddClientScreen.js", ["1443", "1444", "1445", "1446", "1447", "1448", "1449", "1450", "1451"], [], "/Users/<USER>/Desktop/Freelances/<PERSON><PERSON><PERSON>/UNIMEDCARE/web-front/src/screens/clients/EditClientScreen.js", ["1452", "1453", "1454", "1455", "1456", "1457", "1458", "1459", "1460", "1461"], [], "/Users/<USER>/Desktop/Freelances/<PERSON><PERSON><PERSON>/UNIMEDCARE/web-front/src/components/Selector.js", ["1462", "1463"], [], "/Users/<USER>/Desktop/Freelances/<PERSON><PERSON><PERSON>/UNIMEDCARE/web-front/src/components/LayoutSection.js", [], [], "/Users/<USER>/Desktop/Freelances/<PERSON><PERSON><PERSON>/UNIMEDCARE/web-front/src/screens/cases/EditCaseScreen.js", ["1464", "1465", "1466", "1467", "1468", "1469", "1470", "1471", "1472", "1473", "1474", "1475", "1476", "1477", "1478", "1479", "1480", "1481", "1482", "1483", "1484", "1485", "1486", "1487", "1488", "1489", "1490", "1491", "1492", "1493", "1494", "1495", "1496", "1497", "1498", "1499", "1500", "1501", "1502", "1503", "1504", "1505", "1506", "1507", "1508", "1509", "1510", "1511"], [], "/Users/<USER>/Desktop/Freelances/<PERSON><PERSON><PERSON>/UNIMEDCARE/web-front/src/screens/proveedors/ProvidersMapScreen.js", ["1512", "1513", "1514", "1515", "1516", "1517"], [], "/Users/<USER>/Desktop/Freelances/<PERSON><PERSON><PERSON>/UNIMEDCARE/web-front/src/screens/coordinator-space/CoordinatorSpaceScreen.js", ["1518", "1519", "1520", "1521"], [], "/Users/<USER>/Desktop/Freelances/<PERSON><PERSON><PERSON>/UNIMEDCARE/web-front/src/screens/settings/SettingsScreen.js", ["1522", "1523", "1524", "1525", "1526", "1527", "1528", "1529", "1530", "1531", "1532", "1533", "1534"], [], "/Users/<USER>/Desktop/Freelances/<PERSON><PERSON><PERSON>/UNIMEDCARE/web-front/src/screens/help/HelpScreen.js", ["1535", "1536"], [], "/Users/<USER>/Desktop/Freelances/<PERSON><PERSON><PERSON>/UNIMEDCARE/web-front/src/screens/help/FaqScreen.js", ["1537", "1538"], [], "/Users/<USER>/Desktop/Freelances/<PERSON><PERSON><PERSON>/UNIMEDCARE/web-front/src/screens/contact/ContactSupportScreen.js", [], [], "/Users/<USER>/Desktop/Freelances/<PERSON><PERSON><PERSON>/UNIMEDCARE/web-front/src/screens/proveedors/AddProviderScreen.js", ["1539", "1540", "1541", "1542", "1543", "1544", "1545", "1546", "1547", "1548", "1549", "1550", "1551", "1552"], [], "/Users/<USER>/Desktop/Freelances/<PERSON><PERSON><PERSON>/UNIMEDCARE/web-front/src/screens/insurances/InsurancesScreen.js", ["1553", "1554", "1555", "1556", "1557"], [], "/Users/<USER>/Desktop/Freelances/<PERSON><PERSON><PERSON>/UNIMEDCARE/web-front/src/redux/reducers/insurancereducers.js", [], [], "/Users/<USER>/Desktop/Freelances/<PERSON><PERSON><PERSON>/UNIMEDCARE/web-front/src/redux/constants/insuranceConstants.js", [], [], "/Users/<USER>/Desktop/Freelances/<PERSON><PERSON><PERSON>/UNIMEDCARE/web-front/src/redux/actions/insuranceActions.js", [], [], "/Users/<USER>/Desktop/Freelances/<PERSON><PERSON><PERSON>/UNIMEDCARE/web-front/src/screens/insurances/AddInsuranceScreen.js", ["1558", "1559", "1560", "1561", "1562"], [], "/Users/<USER>/Desktop/Freelances/<PERSON><PERSON><PERSON>/UNIMEDCARE/web-front/src/screens/insurances/EditInsuranceScreen.js", ["1563", "1564", "1565", "1566", "1567", "1568", "1569", "1570", "1571", "1572"], [], "/Users/<USER>/Desktop/Freelances/<PERSON><PERSON><PERSON>/UNIMEDCARE/web-front/src/screens/proveedors/EditProviderScreen.js", ["1573", "1574", "1575", "1576", "1577", "1578", "1579", "1580", "1581", "1582", "1583", "1584", "1585", "1586", "1587", "1588"], [], "/Users/<USER>/Desktop/Freelances/<PERSON><PERSON><PERSON>/UNIMEDCARE/web-front/src/screens/coordinator-space/AddCoordinatorScreen.js", ["1589", "1590", "1591", "1592", "1593"], [], "/Users/<USER>/Desktop/Freelances/<PERSON><PERSON><PERSON>/UNIMEDCARE/web-front/src/screens/profile/ProfileScreen.js", ["1594", "1595", "1596", "1597", "1598", "1599"], [], "/Users/<USER>/Desktop/Freelances/<PERSON><PERSON><PERSON>/UNIMEDCARE/web-front/src/screens/coordinator-space/EditCoordinatorScreen.js", ["1600", "1601", "1602", "1603", "1604", "1605", "1606", "1607", "1608", "1609"], [], "/Users/<USER>/Desktop/Freelances/<PERSON><PERSON><PERSON>/UNIMEDCARE/web-front/src/screens/coordinator-space/CoordinatorProfileScreen.js", ["1610", "1611", "1612"], [], "/Users/<USER>/Desktop/Freelances/<PERSON><PERSON><PERSON>/UNIMEDCARE/web-front/src/screens/insurances/InsuranceProfileScreen.js", ["1613", "1614"], [], "/Users/<USER>/Desktop/Freelances/<PERSON><PERSON><PERSON>/UNIMEDCARE/web-front/src/screens/proveedors/ProviderProfileScreen.js", ["1615", "1616"], [], "/Users/<USER>/Desktop/Freelances/<PERSON><PERSON><PERSON>/UNIMEDCARE/web-front/src/screens/auth/ConfirmPasswordScreen.js", ["1617", "1618"], [], "/Users/<USER>/Desktop/Freelances/<PERSON><PERSON><PERSON>/UNIMEDCARE/web-front/src/screens/auth/ResetPasswordScreen.js", ["1619", "1620"], [], "/Users/<USER>/Desktop/Freelances/<PERSON><PERSON><PERSON>/UNIMEDCARE/web-front/src/screens/auth/SendResetPasswordScreen.js", ["1621", "1622", "1623", "1624"], [], "/Users/<USER>/Desktop/Freelances/<PERSON><PERSON><PERSON>/UNIMEDCARE/web-front/src/components/LoadingSpinner.js", [], [], "/Users/<USER>/Desktop/Freelances/<PERSON><PERSON><PERSON>/UNIMEDCARE/web-front/src/components/ErrorBoundary.js", [], [], "/Users/<USER>/Desktop/Freelances/<PERSON><PERSON><PERSON>/UNIMEDCARE/web-front/src/screens/errors/NotFoundScreen.js", [], [], "/Users/<USER>/Desktop/Freelances/<PERSON><PERSON><PERSON>/UNIMEDCARE/web-front/src/screens/errors/ErrorBoundaryScreen.js", [], [], "/Users/<USER>/Desktop/Freelances/<PERSON><PERSON><PERSON>/UNIMEDCARE/web-front/src/screens/errors/ServerErrorScreen.js", [], [], "/Users/<USER>/Desktop/Freelances/<PERSON><PERSON><PERSON>/UNIMEDCARE/web-front/src/screens/errors/UnauthorizedScreen.js", [], [], "/Users/<USER>/Desktop/Freelances/<PERSON><PERSON><PERSON>/UNIMEDCARE/web-front/src/screens/errors/ForbiddenScreen.js", [], [], "/Users/<USER>/Desktop/Freelances/<PERSON><PERSON><PERSON>/UNIMEDCARE/web-front/src/utils/errorHandler.js", [], [], "/Users/<USER>/Desktop/Freelances/<PERSON><PERSON><PERSON>/UNIMEDCARE/web-front/src/components/CaseHistory.js", [], [], "/Users/<USER>/Desktop/Freelances/<PERSON><PERSON><PERSON>/UNIMEDCARE/web-front/src/components/Pagination.js", [], [], "/Users/<USER>/Desktop/Freelances/<PERSON><PERSON><PERSON>/UNIMEDCARE/web-front/src/components/FontTest.js", [], [], "/Users/<USER>/Desktop/Freelances/<PERSON><PERSON><PERSON>/UNIMEDCARE/web-front/src/brockmann-font.js", [], [], {"ruleId": "1625", "severity": 1, "message": "1626", "line": 1, "column": 28, "nodeType": "1627", "messageId": "1628", "endLine": 1, "endColumn": 34}, {"ruleId": "1625", "severity": 1, "message": "1629", "line": 10, "column": 3, "nodeType": "1627", "messageId": "1628", "endLine": 10, "endColumn": 18}, {"ruleId": "1625", "severity": 1, "message": "1630", "line": 5, "column": 3, "nodeType": "1627", "messageId": "1628", "endLine": 5, "endColumn": 14}, {"ruleId": "1625", "severity": 1, "message": "1631", "line": 16, "column": 10, "nodeType": "1627", "messageId": "1628", "endLine": 16, "endColumn": 17}, {"ruleId": "1625", "severity": 1, "message": "1632", "line": 49, "column": 11, "nodeType": "1627", "messageId": "1628", "endLine": 49, "endColumn": 30}, {"ruleId": "1625", "severity": 1, "message": "1633", "line": 49, "column": 32, "nodeType": "1627", "messageId": "1628", "endLine": 49, "endColumn": 49}, {"ruleId": "1634", "severity": 1, "message": "1635", "line": 100, "column": 6, "nodeType": "1636", "endLine": 100, "endColumn": 27, "suggestions": "1637"}, {"ruleId": "1638", "severity": 1, "message": "1639", "line": 271, "column": 41, "nodeType": "1640", "messageId": "1641", "endLine": 271, "endColumn": 43}, {"ruleId": "1642", "severity": 1, "message": "1643", "line": 282, "column": 29, "nodeType": "1644", "endLine": 282, "endColumn": 76}, {"ruleId": "1642", "severity": 1, "message": "1643", "line": 284, "column": 29, "nodeType": "1644", "endLine": 284, "endColumn": 76}, {"ruleId": "1638", "severity": 1, "message": "1639", "line": 296, "column": 41, "nodeType": "1640", "messageId": "1641", "endLine": 296, "endColumn": 43}, {"ruleId": "1642", "severity": 1, "message": "1643", "line": 307, "column": 29, "nodeType": "1644", "endLine": 307, "endColumn": 76}, {"ruleId": "1642", "severity": 1, "message": "1643", "line": 309, "column": 29, "nodeType": "1644", "endLine": 309, "endColumn": 76}, {"ruleId": "1638", "severity": 1, "message": "1639", "line": 321, "column": 41, "nodeType": "1640", "messageId": "1641", "endLine": 321, "endColumn": 43}, {"ruleId": "1642", "severity": 1, "message": "1643", "line": 332, "column": 29, "nodeType": "1644", "endLine": 332, "endColumn": 76}, {"ruleId": "1642", "severity": 1, "message": "1643", "line": 334, "column": 29, "nodeType": "1644", "endLine": 334, "endColumn": 76}, {"ruleId": "1625", "severity": 1, "message": "1645", "line": 19, "column": 9, "nodeType": "1627", "messageId": "1628", "endLine": 19, "endColumn": 17}, {"ruleId": "1625", "severity": 1, "message": "1646", "line": 36, "column": 11, "nodeType": "1627", "messageId": "1628", "endLine": 36, "endColumn": 27}, {"ruleId": "1625", "severity": 1, "message": "1647", "line": 36, "column": 29, "nodeType": "1627", "messageId": "1628", "endLine": 36, "endColumn": 43}, {"ruleId": "1634", "severity": 1, "message": "1648", "line": 56, "column": 6, "nodeType": "1636", "endLine": 56, "endColumn": 24, "suggestions": "1649"}, {"ruleId": "1625", "severity": 1, "message": "1650", "line": 10, "column": 3, "nodeType": "1627", "messageId": "1628", "endLine": 10, "endColumn": 15}, {"ruleId": "1625", "severity": 1, "message": "1645", "line": 18, "column": 9, "nodeType": "1627", "messageId": "1628", "endLine": 18, "endColumn": 17}, {"ruleId": "1625", "severity": 1, "message": "1651", "line": 48, "column": 21, "nodeType": "1627", "messageId": "1628", "endLine": 48, "endColumn": 28}, {"ruleId": "1625", "severity": 1, "message": "1652", "line": 48, "column": 30, "nodeType": "1627", "messageId": "1628", "endLine": 48, "endColumn": 35}, {"ruleId": "1625", "severity": 1, "message": "1653", "line": 51, "column": 11, "nodeType": "1627", "messageId": "1628", "endLine": 51, "endColumn": 30}, {"ruleId": "1625", "severity": 1, "message": "1654", "line": 56, "column": 5, "nodeType": "1627", "messageId": "1628", "endLine": 56, "endColumn": 24}, {"ruleId": "1625", "severity": 1, "message": "1655", "line": 58, "column": 5, "nodeType": "1627", "messageId": "1628", "endLine": 58, "endColumn": 24}, {"ruleId": "1634", "severity": 1, "message": "1656", "line": 70, "column": 6, "nodeType": "1636", "endLine": 70, "endColumn": 36, "suggestions": "1657"}, {"ruleId": "1634", "severity": 1, "message": "1658", "line": 114, "column": 6, "nodeType": "1636", "endLine": 114, "endColumn": 27, "suggestions": "1659"}, {"ruleId": "1625", "severity": 1, "message": "1645", "line": 14, "column": 9, "nodeType": "1627", "messageId": "1628", "endLine": 14, "endColumn": 17}, {"ruleId": "1625", "severity": 1, "message": "1651", "line": 42, "column": 21, "nodeType": "1627", "messageId": "1628", "endLine": 42, "endColumn": 28}, {"ruleId": "1625", "severity": 1, "message": "1652", "line": 42, "column": 30, "nodeType": "1627", "messageId": "1628", "endLine": 42, "endColumn": 35}, {"ruleId": "1625", "severity": 1, "message": "1660", "line": 45, "column": 11, "nodeType": "1627", "messageId": "1628", "endLine": 45, "endColumn": 27}, {"ruleId": "1625", "severity": 1, "message": "1661", "line": 34, "column": 11, "nodeType": "1627", "messageId": "1628", "endLine": 34, "endColumn": 30}, {"ruleId": "1625", "severity": 1, "message": "1662", "line": 34, "column": 32, "nodeType": "1627", "messageId": "1628", "endLine": 34, "endColumn": 49}, {"ruleId": "1634", "severity": 1, "message": "1648", "line": 55, "column": 6, "nodeType": "1636", "endLine": 55, "endColumn": 27, "suggestions": "1663"}, {"ruleId": "1625", "severity": 1, "message": "1630", "line": 5, "column": 3, "nodeType": "1627", "messageId": "1628", "endLine": 5, "endColumn": 14}, {"ruleId": "1625", "severity": 1, "message": "1664", "line": 38, "column": 5, "nodeType": "1627", "messageId": "1628", "endLine": 38, "endColumn": 29}, {"ruleId": "1625", "severity": 1, "message": "1665", "line": 39, "column": 5, "nodeType": "1627", "messageId": "1628", "endLine": 39, "endColumn": 27}, {"ruleId": "1634", "severity": 1, "message": "1648", "line": 61, "column": 6, "nodeType": "1636", "endLine": 61, "endColumn": 32, "suggestions": "1666"}, {"ruleId": "1625", "severity": 1, "message": "1667", "line": 3, "column": 8, "nodeType": "1627", "messageId": "1628", "endLine": 3, "endColumn": 23}, {"ruleId": "1625", "severity": 1, "message": "1668", "line": 4, "column": 10, "nodeType": "1627", "messageId": "1628", "endLine": 4, "endColumn": 19}, {"ruleId": "1625", "severity": 1, "message": "1669", "line": 8, "column": 10, "nodeType": "1627", "messageId": "1628", "endLine": 8, "endColumn": 22}, {"ruleId": "1625", "severity": 1, "message": "1670", "line": 10, "column": 10, "nodeType": "1627", "messageId": "1628", "endLine": 10, "endColumn": 24}, {"ruleId": "1625", "severity": 1, "message": "1671", "line": 11, "column": 10, "nodeType": "1627", "messageId": "1628", "endLine": 11, "endColumn": 23}, {"ruleId": "1625", "severity": 1, "message": "1672", "line": 12, "column": 10, "nodeType": "1627", "messageId": "1628", "endLine": 12, "endColumn": 22}, {"ruleId": "1625", "severity": 1, "message": "1673", "line": 14, "column": 10, "nodeType": "1627", "messageId": "1628", "endLine": 14, "endColumn": 19}, {"ruleId": "1625", "severity": 1, "message": "1674", "line": 17, "column": 3, "nodeType": "1627", "messageId": "1628", "endLine": 17, "endColumn": 20}, {"ruleId": "1625", "severity": 1, "message": "1645", "line": 24, "column": 9, "nodeType": "1627", "messageId": "1628", "endLine": 24, "endColumn": 17}, {"ruleId": "1625", "severity": 1, "message": "1675", "line": 54, "column": 10, "nodeType": "1627", "messageId": "1628", "endLine": 54, "endColumn": 22}, {"ruleId": "1625", "severity": 1, "message": "1651", "line": 75, "column": 21, "nodeType": "1627", "messageId": "1628", "endLine": 75, "endColumn": 28}, {"ruleId": "1625", "severity": 1, "message": "1652", "line": 75, "column": 30, "nodeType": "1627", "messageId": "1628", "endLine": 75, "endColumn": 35}, {"ruleId": "1625", "severity": 1, "message": "1676", "line": 84, "column": 11, "nodeType": "1627", "messageId": "1628", "endLine": 84, "endColumn": 18}, {"ruleId": "1625", "severity": 1, "message": "1677", "line": 87, "column": 11, "nodeType": "1627", "messageId": "1628", "endLine": 87, "endColumn": 18}, {"ruleId": "1625", "severity": 1, "message": "1678", "line": 91, "column": 5, "nodeType": "1627", "messageId": "1628", "endLine": 91, "endColumn": 29}, {"ruleId": "1625", "severity": 1, "message": "1679", "line": 92, "column": 5, "nodeType": "1627", "messageId": "1628", "endLine": 92, "endColumn": 27}, {"ruleId": "1634", "severity": 1, "message": "1658", "line": 187, "column": 6, "nodeType": "1636", "endLine": 187, "endColumn": 32, "suggestions": "1680"}, {"ruleId": "1625", "severity": 1, "message": "1645", "line": 10, "column": 9, "nodeType": "1627", "messageId": "1628", "endLine": 10, "endColumn": 17}, {"ruleId": "1625", "severity": 1, "message": "1681", "line": 21, "column": 5, "nodeType": "1627", "messageId": "1628", "endLine": 21, "endColumn": 20}, {"ruleId": "1625", "severity": 1, "message": "1682", "line": 22, "column": 5, "nodeType": "1627", "messageId": "1628", "endLine": 22, "endColumn": 18}, {"ruleId": "1642", "severity": 1, "message": "1643", "line": 301, "column": 19, "nodeType": "1644", "endLine": 304, "endColumn": 21}, {"ruleId": "1642", "severity": 1, "message": "1643", "line": 339, "column": 19, "nodeType": "1644", "endLine": 342, "endColumn": 21}, {"ruleId": "1625", "severity": 1, "message": "1667", "line": 3, "column": 8, "nodeType": "1627", "messageId": "1628", "endLine": 3, "endColumn": 23}, {"ruleId": "1625", "severity": 1, "message": "1668", "line": 4, "column": 10, "nodeType": "1627", "messageId": "1628", "endLine": 4, "endColumn": 19}, {"ruleId": "1625", "severity": 1, "message": "1669", "line": 8, "column": 10, "nodeType": "1627", "messageId": "1628", "endLine": 8, "endColumn": 22}, {"ruleId": "1625", "severity": 1, "message": "1673", "line": 15, "column": 3, "nodeType": "1627", "messageId": "1628", "endLine": 15, "endColumn": 12}, {"ruleId": "1625", "severity": 1, "message": "1645", "line": 23, "column": 9, "nodeType": "1627", "messageId": "1628", "endLine": 23, "endColumn": 17}, {"ruleId": "1625", "severity": 1, "message": "1683", "line": 28, "column": 10, "nodeType": "1627", "messageId": "1628", "endLine": 28, "endColumn": 27}, {"ruleId": "1625", "severity": 1, "message": "1676", "line": 120, "column": 11, "nodeType": "1627", "messageId": "1628", "endLine": 120, "endColumn": 18}, {"ruleId": "1625", "severity": 1, "message": "1684", "line": 123, "column": 11, "nodeType": "1627", "messageId": "1628", "endLine": 123, "endColumn": 27}, {"ruleId": "1625", "severity": 1, "message": "1685", "line": 123, "column": 29, "nodeType": "1627", "messageId": "1628", "endLine": 123, "endColumn": 43}, {"ruleId": "1625", "severity": 1, "message": "1651", "line": 126, "column": 11, "nodeType": "1627", "messageId": "1628", "endLine": 126, "endColumn": 18}, {"ruleId": "1625", "severity": 1, "message": "1652", "line": 126, "column": 20, "nodeType": "1627", "messageId": "1628", "endLine": 126, "endColumn": 25}, {"ruleId": "1625", "severity": 1, "message": "1677", "line": 126, "column": 27, "nodeType": "1627", "messageId": "1628", "endLine": 126, "endColumn": 34}, {"ruleId": "1625", "severity": 1, "message": "1686", "line": 129, "column": 20, "nodeType": "1627", "messageId": "1628", "endLine": 129, "endColumn": 33}, {"ruleId": "1625", "severity": 1, "message": "1687", "line": 129, "column": 35, "nodeType": "1627", "messageId": "1628", "endLine": 129, "endColumn": 46}, {"ruleId": "1625", "severity": 1, "message": "1688", "line": 132, "column": 19, "nodeType": "1627", "messageId": "1628", "endLine": 132, "endColumn": 31}, {"ruleId": "1625", "severity": 1, "message": "1689", "line": 132, "column": 33, "nodeType": "1627", "messageId": "1628", "endLine": 132, "endColumn": 43}, {"ruleId": "1634", "severity": 1, "message": "1658", "line": 309, "column": 6, "nodeType": "1636", "endLine": 309, "endColumn": 24, "suggestions": "1690"}, {"ruleId": "1625", "severity": 1, "message": "1691", "line": 6, "column": 3, "nodeType": "1627", "messageId": "1628", "endLine": 6, "endColumn": 13}, {"ruleId": "1625", "severity": 1, "message": "1692", "line": 7, "column": 3, "nodeType": "1627", "messageId": "1628", "endLine": 7, "endColumn": 19}, {"ruleId": "1625", "severity": 1, "message": "1645", "line": 19, "column": 9, "nodeType": "1627", "messageId": "1628", "endLine": 19, "endColumn": 17}, {"ruleId": "1625", "severity": 1, "message": "1693", "line": 22, "column": 10, "nodeType": "1627", "messageId": "1628", "endLine": 22, "endColumn": 14}, {"ruleId": "1625", "severity": 1, "message": "1694", "line": 23, "column": 10, "nodeType": "1627", "messageId": "1628", "endLine": 23, "endColumn": 19}, {"ruleId": "1625", "severity": 1, "message": "1695", "line": 45, "column": 11, "nodeType": "1627", "messageId": "1628", "endLine": 45, "endColumn": 25}, {"ruleId": "1625", "severity": 1, "message": "1696", "line": 45, "column": 27, "nodeType": "1627", "messageId": "1628", "endLine": 45, "endColumn": 39}, {"ruleId": "1625", "severity": 1, "message": "1697", "line": 45, "column": 41, "nodeType": "1627", "messageId": "1628", "endLine": 45, "endColumn": 55}, {"ruleId": "1625", "severity": 1, "message": "1698", "line": 49, "column": 5, "nodeType": "1627", "messageId": "1628", "endLine": 49, "endColumn": 23}, {"ruleId": "1625", "severity": 1, "message": "1699", "line": 50, "column": 5, "nodeType": "1627", "messageId": "1628", "endLine": 50, "endColumn": 21}, {"ruleId": "1625", "severity": 1, "message": "1700", "line": 51, "column": 5, "nodeType": "1627", "messageId": "1628", "endLine": 51, "endColumn": 23}, {"ruleId": "1625", "severity": 1, "message": "1701", "line": 57, "column": 5, "nodeType": "1627", "messageId": "1628", "endLine": 57, "endColumn": 29}, {"ruleId": "1625", "severity": 1, "message": "1702", "line": 58, "column": 5, "nodeType": "1627", "messageId": "1628", "endLine": 58, "endColumn": 27}, {"ruleId": "1634", "severity": 1, "message": "1648", "line": 70, "column": 6, "nodeType": "1636", "endLine": 70, "endColumn": 26, "suggestions": "1703"}, {"ruleId": "1634", "severity": 1, "message": "1704", "line": 98, "column": 6, "nodeType": "1636", "endLine": 98, "endColumn": 32, "suggestions": "1705"}, {"ruleId": "1625", "severity": 1, "message": "1630", "line": 5, "column": 3, "nodeType": "1627", "messageId": "1628", "endLine": 5, "endColumn": 14}, {"ruleId": "1625", "severity": 1, "message": "1706", "line": 40, "column": 5, "nodeType": "1627", "messageId": "1628", "endLine": 40, "endColumn": 30}, {"ruleId": "1625", "severity": 1, "message": "1707", "line": 42, "column": 5, "nodeType": "1627", "messageId": "1628", "endLine": 42, "endColumn": 28}, {"ruleId": "1625", "severity": 1, "message": "1708", "line": 51, "column": 11, "nodeType": "1627", "messageId": "1628", "endLine": 51, "endColumn": 31}, {"ruleId": "1625", "severity": 1, "message": "1709", "line": 51, "column": 33, "nodeType": "1627", "messageId": "1628", "endLine": 51, "endColumn": 51}, {"ruleId": "1634", "severity": 1, "message": "1648", "line": 74, "column": 6, "nodeType": "1636", "endLine": 74, "endColumn": 33, "suggestions": "1710"}, {"ruleId": "1634", "severity": 1, "message": "1648", "line": 85, "column": 6, "nodeType": "1636", "endLine": 85, "endColumn": 28, "suggestions": "1711"}, {"ruleId": "1625", "severity": 1, "message": "1667", "line": 3, "column": 8, "nodeType": "1627", "messageId": "1628", "endLine": 3, "endColumn": 23}, {"ruleId": "1625", "severity": 1, "message": "1668", "line": 4, "column": 10, "nodeType": "1627", "messageId": "1628", "endLine": 4, "endColumn": 19}, {"ruleId": "1625", "severity": 1, "message": "1669", "line": 8, "column": 10, "nodeType": "1627", "messageId": "1628", "endLine": 8, "endColumn": 22}, {"ruleId": "1625", "severity": 1, "message": "1645", "line": 19, "column": 9, "nodeType": "1627", "messageId": "1628", "endLine": 19, "endColumn": 17}, {"ruleId": "1625", "severity": 1, "message": "1683", "line": 23, "column": 10, "nodeType": "1627", "messageId": "1628", "endLine": 23, "endColumn": 27}, {"ruleId": "1625", "severity": 1, "message": "1651", "line": 112, "column": 21, "nodeType": "1627", "messageId": "1628", "endLine": 112, "endColumn": 28}, {"ruleId": "1625", "severity": 1, "message": "1652", "line": 112, "column": 30, "nodeType": "1627", "messageId": "1628", "endLine": 112, "endColumn": 35}, {"ruleId": "1625", "severity": 1, "message": "1676", "line": 115, "column": 11, "nodeType": "1627", "messageId": "1628", "endLine": 115, "endColumn": 18}, {"ruleId": "1625", "severity": 1, "message": "1712", "line": 118, "column": 11, "nodeType": "1627", "messageId": "1628", "endLine": 118, "endColumn": 24}, {"ruleId": "1625", "severity": 1, "message": "1713", "line": 118, "column": 26, "nodeType": "1627", "messageId": "1628", "endLine": 118, "endColumn": 37}, {"ruleId": "1625", "severity": 1, "message": "1686", "line": 121, "column": 20, "nodeType": "1627", "messageId": "1628", "endLine": 121, "endColumn": 33}, {"ruleId": "1625", "severity": 1, "message": "1687", "line": 121, "column": 35, "nodeType": "1627", "messageId": "1628", "endLine": 121, "endColumn": 46}, {"ruleId": "1625", "severity": 1, "message": "1688", "line": 124, "column": 19, "nodeType": "1627", "messageId": "1628", "endLine": 124, "endColumn": 31}, {"ruleId": "1625", "severity": 1, "message": "1689", "line": 124, "column": 33, "nodeType": "1627", "messageId": "1628", "endLine": 124, "endColumn": 43}, {"ruleId": "1625", "severity": 1, "message": "1667", "line": 3, "column": 8, "nodeType": "1627", "messageId": "1628", "endLine": 3, "endColumn": 23}, {"ruleId": "1625", "severity": 1, "message": "1668", "line": 4, "column": 10, "nodeType": "1627", "messageId": "1628", "endLine": 4, "endColumn": 19}, {"ruleId": "1625", "severity": 1, "message": "1669", "line": 8, "column": 10, "nodeType": "1627", "messageId": "1628", "endLine": 8, "endColumn": 22}, {"ruleId": "1625", "severity": 1, "message": "1670", "line": 10, "column": 10, "nodeType": "1627", "messageId": "1628", "endLine": 10, "endColumn": 24}, {"ruleId": "1625", "severity": 1, "message": "1671", "line": 11, "column": 10, "nodeType": "1627", "messageId": "1628", "endLine": 11, "endColumn": 23}, {"ruleId": "1625", "severity": 1, "message": "1672", "line": 12, "column": 10, "nodeType": "1627", "messageId": "1628", "endLine": 12, "endColumn": 22}, {"ruleId": "1625", "severity": 1, "message": "1673", "line": 14, "column": 10, "nodeType": "1627", "messageId": "1628", "endLine": 14, "endColumn": 19}, {"ruleId": "1625", "severity": 1, "message": "1645", "line": 20, "column": 9, "nodeType": "1627", "messageId": "1628", "endLine": 20, "endColumn": 17}, {"ruleId": "1625", "severity": 1, "message": "1675", "line": 49, "column": 10, "nodeType": "1627", "messageId": "1628", "endLine": 49, "endColumn": 22}, {"ruleId": "1625", "severity": 1, "message": "1651", "line": 70, "column": 21, "nodeType": "1627", "messageId": "1628", "endLine": 70, "endColumn": 28}, {"ruleId": "1625", "severity": 1, "message": "1652", "line": 70, "column": 30, "nodeType": "1627", "messageId": "1628", "endLine": 70, "endColumn": 35}, {"ruleId": "1625", "severity": 1, "message": "1676", "line": 79, "column": 11, "nodeType": "1627", "messageId": "1628", "endLine": 79, "endColumn": 18}, {"ruleId": "1625", "severity": 1, "message": "1714", "line": 82, "column": 11, "nodeType": "1627", "messageId": "1628", "endLine": 82, "endColumn": 32}, {"ruleId": "1625", "severity": 1, "message": "1715", "line": 82, "column": 34, "nodeType": "1627", "messageId": "1628", "endLine": 82, "endColumn": 53}, {"ruleId": "1625", "severity": 1, "message": "1630", "line": 5, "column": 3, "nodeType": "1627", "messageId": "1628", "endLine": 5, "endColumn": 14}, {"ruleId": "1625", "severity": 1, "message": "1629", "line": 15, "column": 3, "nodeType": "1627", "messageId": "1628", "endLine": 15, "endColumn": 18}, {"ruleId": "1625", "severity": 1, "message": "1626", "line": 1, "column": 28, "nodeType": "1627", "messageId": "1628", "endLine": 1, "endColumn": 34}, {"ruleId": "1625", "severity": 1, "message": "1716", "line": 3, "column": 10, "nodeType": "1627", "messageId": "1628", "endLine": 3, "endColumn": 14}, {"ruleId": "1625", "severity": 1, "message": "1717", "line": 7, "column": 8, "nodeType": "1627", "messageId": "1628", "endLine": 7, "endColumn": 16}, {"ruleId": "1625", "severity": 1, "message": "1718", "line": 9, "column": 3, "nodeType": "1627", "messageId": "1628", "endLine": 9, "endColumn": 21}, {"ruleId": "1625", "severity": 1, "message": "1719", "line": 10, "column": 3, "nodeType": "1627", "messageId": "1628", "endLine": 10, "endColumn": 16}, {"ruleId": "1625", "severity": 1, "message": "1720", "line": 12, "column": 3, "nodeType": "1627", "messageId": "1628", "endLine": 12, "endColumn": 25}, {"ruleId": "1625", "severity": 1, "message": "1721", "line": 15, "column": 10, "nodeType": "1627", "messageId": "1628", "endLine": 15, "endColumn": 21}, {"ruleId": "1625", "severity": 1, "message": "1645", "line": 23, "column": 9, "nodeType": "1627", "messageId": "1628", "endLine": 23, "endColumn": 17}, {"ruleId": "1625", "severity": 1, "message": "1722", "line": 54, "column": 5, "nodeType": "1627", "messageId": "1628", "endLine": 54, "endColumn": 32}, {"ruleId": "1625", "severity": 1, "message": "1723", "line": 63, "column": 5, "nodeType": "1627", "messageId": "1628", "endLine": 63, "endColumn": 32}, {"ruleId": "1625", "severity": 1, "message": "1724", "line": 65, "column": 5, "nodeType": "1627", "messageId": "1628", "endLine": 65, "endColumn": 30}, {"ruleId": "1634", "severity": 1, "message": "1656", "line": 76, "column": 6, "nodeType": "1636", "endLine": 76, "endColumn": 36, "suggestions": "1725"}, {"ruleId": "1634", "severity": 1, "message": "1658", "line": 105, "column": 6, "nodeType": "1636", "endLine": 105, "endColumn": 35, "suggestions": "1726"}, {"ruleId": "1625", "severity": 1, "message": "1626", "line": 1, "column": 28, "nodeType": "1627", "messageId": "1628", "endLine": 1, "endColumn": 34}, {"ruleId": "1625", "severity": 1, "message": "1717", "line": 7, "column": 8, "nodeType": "1627", "messageId": "1628", "endLine": 7, "endColumn": 16}, {"ruleId": "1625", "severity": 1, "message": "1721", "line": 13, "column": 10, "nodeType": "1627", "messageId": "1628", "endLine": 13, "endColumn": 21}, {"ruleId": "1625", "severity": 1, "message": "1645", "line": 18, "column": 9, "nodeType": "1627", "messageId": "1628", "endLine": 18, "endColumn": 17}, {"ruleId": "1625", "severity": 1, "message": "1651", "line": 32, "column": 11, "nodeType": "1627", "messageId": "1628", "endLine": 32, "endColumn": 18}, {"ruleId": "1625", "severity": 1, "message": "1677", "line": 32, "column": 20, "nodeType": "1627", "messageId": "1628", "endLine": 32, "endColumn": 27}, {"ruleId": "1625", "severity": 1, "message": "1652", "line": 32, "column": 38, "nodeType": "1627", "messageId": "1628", "endLine": 32, "endColumn": 43}, {"ruleId": "1625", "severity": 1, "message": "1727", "line": 37, "column": 5, "nodeType": "1627", "messageId": "1628", "endLine": 37, "endColumn": 26}, {"ruleId": "1625", "severity": 1, "message": "1728", "line": 46, "column": 5, "nodeType": "1627", "messageId": "1628", "endLine": 46, "endColumn": 32}, {"ruleId": "1625", "severity": 1, "message": "1729", "line": 48, "column": 5, "nodeType": "1627", "messageId": "1628", "endLine": 48, "endColumn": 30}, {"ruleId": "1625", "severity": 1, "message": "1626", "line": 1, "column": 28, "nodeType": "1627", "messageId": "1628", "endLine": 1, "endColumn": 34}, {"ruleId": "1625", "severity": 1, "message": "1716", "line": 3, "column": 10, "nodeType": "1627", "messageId": "1628", "endLine": 3, "endColumn": 14}, {"ruleId": "1625", "severity": 1, "message": "1717", "line": 7, "column": 8, "nodeType": "1627", "messageId": "1628", "endLine": 7, "endColumn": 16}, {"ruleId": "1625", "severity": 1, "message": "1720", "line": 11, "column": 3, "nodeType": "1627", "messageId": "1628", "endLine": 11, "endColumn": 25}, {"ruleId": "1625", "severity": 1, "message": "1721", "line": 13, "column": 10, "nodeType": "1627", "messageId": "1628", "endLine": 13, "endColumn": 21}, {"ruleId": "1625", "severity": 1, "message": "1645", "line": 21, "column": 9, "nodeType": "1627", "messageId": "1628", "endLine": 21, "endColumn": 17}, {"ruleId": "1625", "severity": 1, "message": "1677", "line": 48, "column": 20, "nodeType": "1627", "messageId": "1628", "endLine": 48, "endColumn": 27}, {"ruleId": "1625", "severity": 1, "message": "1730", "line": 52, "column": 5, "nodeType": "1627", "messageId": "1628", "endLine": 52, "endColumn": 29}, {"ruleId": "1625", "severity": 1, "message": "1731", "line": 54, "column": 5, "nodeType": "1627", "messageId": "1628", "endLine": 54, "endColumn": 27}, {"ruleId": "1634", "severity": 1, "message": "1656", "line": 65, "column": 6, "nodeType": "1636", "endLine": 65, "endColumn": 36, "suggestions": "1732"}, {"ruleId": "1625", "severity": 1, "message": "1667", "line": 6, "column": 8, "nodeType": "1627", "messageId": "1628", "endLine": 6, "endColumn": 23}, {"ruleId": "1625", "severity": 1, "message": "1721", "line": 7, "column": 21, "nodeType": "1627", "messageId": "1628", "endLine": 7, "endColumn": 32}, {"ruleId": "1625", "severity": 1, "message": "1645", "line": 25, "column": 9, "nodeType": "1627", "messageId": "1628", "endLine": 25, "endColumn": 17}, {"ruleId": "1625", "severity": 1, "message": "1733", "line": 62, "column": 10, "nodeType": "1627", "messageId": "1628", "endLine": 62, "endColumn": 25}, {"ruleId": "1625", "severity": 1, "message": "1734", "line": 63, "column": 10, "nodeType": "1627", "messageId": "1628", "endLine": 63, "endColumn": 25}, {"ruleId": "1625", "severity": 1, "message": "1735", "line": 64, "column": 10, "nodeType": "1627", "messageId": "1628", "endLine": 64, "endColumn": 23}, {"ruleId": "1625", "severity": 1, "message": "1736", "line": 65, "column": 10, "nodeType": "1627", "messageId": "1628", "endLine": 65, "endColumn": 23}, {"ruleId": "1625", "severity": 1, "message": "1737", "line": 71, "column": 10, "nodeType": "1627", "messageId": "1628", "endLine": 71, "endColumn": 16}, {"ruleId": "1625", "severity": 1, "message": "1738", "line": 71, "column": 18, "nodeType": "1627", "messageId": "1628", "endLine": 71, "endColumn": 27}, {"ruleId": "1625", "severity": 1, "message": "1677", "line": 77, "column": 27, "nodeType": "1627", "messageId": "1628", "endLine": 77, "endColumn": 34}, {"ruleId": "1625", "severity": 1, "message": "1739", "line": 80, "column": 11, "nodeType": "1627", "messageId": "1628", "endLine": 80, "endColumn": 30}, {"ruleId": "1625", "severity": 1, "message": "1740", "line": 80, "column": 32, "nodeType": "1627", "messageId": "1628", "endLine": 80, "endColumn": 49}, {"ruleId": "1634", "severity": 1, "message": "1741", "line": 124, "column": 6, "nodeType": "1636", "endLine": 124, "endColumn": 14, "suggestions": "1742"}, {"ruleId": "1634", "severity": 1, "message": "1658", "line": 130, "column": 6, "nodeType": "1636", "endLine": 130, "endColumn": 27, "suggestions": "1743"}, {"ruleId": "1625", "severity": 1, "message": "1744", "line": 2, "column": 10, "nodeType": "1627", "messageId": "1628", "endLine": 2, "endColumn": 21}, {"ruleId": "1625", "severity": 1, "message": "1667", "line": 3, "column": 8, "nodeType": "1627", "messageId": "1628", "endLine": 3, "endColumn": 23}, {"ruleId": "1625", "severity": 1, "message": "1645", "line": 22, "column": 9, "nodeType": "1627", "messageId": "1628", "endLine": 22, "endColumn": 17}, {"ruleId": "1625", "severity": 1, "message": "1737", "line": 60, "column": 10, "nodeType": "1627", "messageId": "1628", "endLine": 60, "endColumn": 16}, {"ruleId": "1625", "severity": 1, "message": "1738", "line": 60, "column": 18, "nodeType": "1627", "messageId": "1628", "endLine": 60, "endColumn": 27}, {"ruleId": "1625", "severity": 1, "message": "1745", "line": 61, "column": 10, "nodeType": "1627", "messageId": "1628", "endLine": 61, "endColumn": 19}, {"ruleId": "1625", "severity": 1, "message": "1651", "line": 64, "column": 21, "nodeType": "1627", "messageId": "1628", "endLine": 64, "endColumn": 28}, {"ruleId": "1625", "severity": 1, "message": "1652", "line": 64, "column": 30, "nodeType": "1627", "messageId": "1628", "endLine": 64, "endColumn": 35}, {"ruleId": "1625", "severity": 1, "message": "1746", "line": 67, "column": 11, "nodeType": "1627", "messageId": "1628", "endLine": 67, "endColumn": 27}, {"ruleId": "1625", "severity": 1, "message": "1747", "line": 67, "column": 29, "nodeType": "1627", "messageId": "1628", "endLine": 67, "endColumn": 43}, {"ruleId": "1625", "severity": 1, "message": "1626", "line": 1, "column": 28, "nodeType": "1627", "messageId": "1628", "endLine": 1, "endColumn": 34}, {"ruleId": "1625", "severity": 1, "message": "1716", "line": 3, "column": 10, "nodeType": "1627", "messageId": "1628", "endLine": 3, "endColumn": 14}, {"ruleId": "1625", "severity": 1, "message": "1717", "line": 7, "column": 8, "nodeType": "1627", "messageId": "1628", "endLine": 7, "endColumn": 16}, {"ruleId": "1625", "severity": 1, "message": "1718", "line": 9, "column": 3, "nodeType": "1627", "messageId": "1628", "endLine": 9, "endColumn": 21}, {"ruleId": "1625", "severity": 1, "message": "1720", "line": 12, "column": 3, "nodeType": "1627", "messageId": "1628", "endLine": 12, "endColumn": 25}, {"ruleId": "1625", "severity": 1, "message": "1721", "line": 14, "column": 10, "nodeType": "1627", "messageId": "1628", "endLine": 14, "endColumn": 21}, {"ruleId": "1625", "severity": 1, "message": "1645", "line": 22, "column": 9, "nodeType": "1627", "messageId": "1628", "endLine": 22, "endColumn": 17}, {"ruleId": "1625", "severity": 1, "message": "1677", "line": 60, "column": 20, "nodeType": "1627", "messageId": "1628", "endLine": 60, "endColumn": 27}, {"ruleId": "1625", "severity": 1, "message": "1748", "line": 64, "column": 5, "nodeType": "1627", "messageId": "1628", "endLine": 64, "endColumn": 28}, {"ruleId": "1625", "severity": 1, "message": "1749", "line": 66, "column": 5, "nodeType": "1627", "messageId": "1628", "endLine": 66, "endColumn": 26}, {"ruleId": "1625", "severity": 1, "message": "1667", "line": 3, "column": 8, "nodeType": "1627", "messageId": "1628", "endLine": 3, "endColumn": 23}, {"ruleId": "1625", "severity": 1, "message": "1668", "line": 4, "column": 10, "nodeType": "1627", "messageId": "1628", "endLine": 4, "endColumn": 19}, {"ruleId": "1625", "severity": 1, "message": "1669", "line": 8, "column": 10, "nodeType": "1627", "messageId": "1628", "endLine": 8, "endColumn": 22}, {"ruleId": "1625", "severity": 1, "message": "1670", "line": 10, "column": 10, "nodeType": "1627", "messageId": "1628", "endLine": 10, "endColumn": 24}, {"ruleId": "1625", "severity": 1, "message": "1671", "line": 11, "column": 10, "nodeType": "1627", "messageId": "1628", "endLine": 11, "endColumn": 23}, {"ruleId": "1625", "severity": 1, "message": "1672", "line": 12, "column": 10, "nodeType": "1627", "messageId": "1628", "endLine": 12, "endColumn": 22}, {"ruleId": "1625", "severity": 1, "message": "1673", "line": 14, "column": 10, "nodeType": "1627", "messageId": "1628", "endLine": 14, "endColumn": 19}, {"ruleId": "1625", "severity": 1, "message": "1674", "line": 16, "column": 10, "nodeType": "1627", "messageId": "1628", "endLine": 16, "endColumn": 27}, {"ruleId": "1625", "severity": 1, "message": "1645", "line": 21, "column": 9, "nodeType": "1627", "messageId": "1628", "endLine": 21, "endColumn": 17}, {"ruleId": "1625", "severity": 1, "message": "1675", "line": 63, "column": 10, "nodeType": "1627", "messageId": "1628", "endLine": 63, "endColumn": 22}, {"ruleId": "1625", "severity": 1, "message": "1651", "line": 84, "column": 21, "nodeType": "1627", "messageId": "1628", "endLine": 84, "endColumn": 28}, {"ruleId": "1625", "severity": 1, "message": "1652", "line": 84, "column": 30, "nodeType": "1627", "messageId": "1628", "endLine": 84, "endColumn": 35}, {"ruleId": "1625", "severity": 1, "message": "1676", "line": 93, "column": 11, "nodeType": "1627", "messageId": "1628", "endLine": 93, "endColumn": 18}, {"ruleId": "1625", "severity": 1, "message": "1750", "line": 96, "column": 11, "nodeType": "1627", "messageId": "1628", "endLine": 96, "endColumn": 28}, {"ruleId": "1625", "severity": 1, "message": "1751", "line": 96, "column": 30, "nodeType": "1627", "messageId": "1628", "endLine": 96, "endColumn": 45}, {"ruleId": "1752", "severity": 1, "message": "1753", "line": 1112, "column": 19, "nodeType": "1754", "messageId": "1641", "endLine": 1112, "endColumn": 25}, {"ruleId": "1752", "severity": 1, "message": "1755", "line": 1114, "column": 19, "nodeType": "1754", "messageId": "1641", "endLine": 1114, "endColumn": 22}, {"ruleId": "1752", "severity": 1, "message": "1756", "line": 1116, "column": 19, "nodeType": "1754", "messageId": "1641", "endLine": 1116, "endColumn": 28}, {"ruleId": "1752", "severity": 1, "message": "1757", "line": 1119, "column": 19, "nodeType": "1754", "messageId": "1641", "endLine": 1119, "endColumn": 29}, {"ruleId": "1752", "severity": 1, "message": "1758", "line": 1120, "column": 19, "nodeType": "1754", "messageId": "1641", "endLine": 1120, "endColumn": 27}, {"ruleId": "1752", "severity": 1, "message": "1759", "line": 1121, "column": 19, "nodeType": "1754", "messageId": "1641", "endLine": 1121, "endColumn": 26}, {"ruleId": "1752", "severity": 1, "message": "1760", "line": 1122, "column": 19, "nodeType": "1754", "messageId": "1641", "endLine": 1122, "endColumn": 33}, {"ruleId": "1752", "severity": 1, "message": "1761", "line": 1123, "column": 19, "nodeType": "1754", "messageId": "1641", "endLine": 1123, "endColumn": 31}, {"ruleId": "1752", "severity": 1, "message": "1762", "line": 1125, "column": 19, "nodeType": "1754", "messageId": "1641", "endLine": 1125, "endColumn": 30}, {"ruleId": "1752", "severity": 1, "message": "1763", "line": 1126, "column": 19, "nodeType": "1754", "messageId": "1641", "endLine": 1126, "endColumn": 29}, {"ruleId": "1752", "severity": 1, "message": "1764", "line": 1127, "column": 19, "nodeType": "1754", "messageId": "1641", "endLine": 1127, "endColumn": 31}, {"ruleId": "1752", "severity": 1, "message": "1765", "line": 1128, "column": 19, "nodeType": "1754", "messageId": "1641", "endLine": 1128, "endColumn": 30}, {"ruleId": "1625", "severity": 1, "message": "1667", "line": 3, "column": 8, "nodeType": "1627", "messageId": "1628", "endLine": 3, "endColumn": 23}, {"ruleId": "1625", "severity": 1, "message": "1668", "line": 4, "column": 10, "nodeType": "1627", "messageId": "1628", "endLine": 4, "endColumn": 19}, {"ruleId": "1625", "severity": 1, "message": "1669", "line": 8, "column": 10, "nodeType": "1627", "messageId": "1628", "endLine": 8, "endColumn": 22}, {"ruleId": "1625", "severity": 1, "message": "1670", "line": 10, "column": 10, "nodeType": "1627", "messageId": "1628", "endLine": 10, "endColumn": 24}, {"ruleId": "1625", "severity": 1, "message": "1671", "line": 11, "column": 10, "nodeType": "1627", "messageId": "1628", "endLine": 11, "endColumn": 23}, {"ruleId": "1625", "severity": 1, "message": "1672", "line": 12, "column": 10, "nodeType": "1627", "messageId": "1628", "endLine": 12, "endColumn": 22}, {"ruleId": "1625", "severity": 1, "message": "1673", "line": 14, "column": 10, "nodeType": "1627", "messageId": "1628", "endLine": 14, "endColumn": 19}, {"ruleId": "1625", "severity": 1, "message": "1674", "line": 16, "column": 10, "nodeType": "1627", "messageId": "1628", "endLine": 16, "endColumn": 27}, {"ruleId": "1625", "severity": 1, "message": "1766", "line": 18, "column": 3, "nodeType": "1627", "messageId": "1628", "endLine": 18, "endColumn": 17}, {"ruleId": "1625", "severity": 1, "message": "1645", "line": 25, "column": 9, "nodeType": "1627", "messageId": "1628", "endLine": 25, "endColumn": 17}, {"ruleId": "1625", "severity": 1, "message": "1675", "line": 69, "column": 10, "nodeType": "1627", "messageId": "1628", "endLine": 69, "endColumn": 22}, {"ruleId": "1625", "severity": 1, "message": "1651", "line": 90, "column": 21, "nodeType": "1627", "messageId": "1628", "endLine": 90, "endColumn": 28}, {"ruleId": "1625", "severity": 1, "message": "1652", "line": 90, "column": 30, "nodeType": "1627", "messageId": "1628", "endLine": 90, "endColumn": 35}, {"ruleId": "1625", "severity": 1, "message": "1676", "line": 99, "column": 11, "nodeType": "1627", "messageId": "1628", "endLine": 99, "endColumn": 18}, {"ruleId": "1625", "severity": 1, "message": "1677", "line": 102, "column": 11, "nodeType": "1627", "messageId": "1628", "endLine": 102, "endColumn": 18}, {"ruleId": "1625", "severity": 1, "message": "1678", "line": 106, "column": 5, "nodeType": "1627", "messageId": "1628", "endLine": 106, "endColumn": 29}, {"ruleId": "1625", "severity": 1, "message": "1679", "line": 107, "column": 5, "nodeType": "1627", "messageId": "1628", "endLine": 107, "endColumn": 27}, {"ruleId": "1634", "severity": 1, "message": "1656", "line": 120, "column": 6, "nodeType": "1636", "endLine": 120, "endColumn": 36, "suggestions": "1767"}, {"ruleId": "1634", "severity": 1, "message": "1658", "line": 211, "column": 6, "nodeType": "1636", "endLine": 211, "endColumn": 32, "suggestions": "1768"}, {"ruleId": "1752", "severity": 1, "message": "1753", "line": 1163, "column": 19, "nodeType": "1754", "messageId": "1641", "endLine": 1163, "endColumn": 25}, {"ruleId": "1752", "severity": 1, "message": "1755", "line": 1165, "column": 19, "nodeType": "1754", "messageId": "1641", "endLine": 1165, "endColumn": 22}, {"ruleId": "1752", "severity": 1, "message": "1756", "line": 1167, "column": 19, "nodeType": "1754", "messageId": "1641", "endLine": 1167, "endColumn": 28}, {"ruleId": "1752", "severity": 1, "message": "1757", "line": 1170, "column": 19, "nodeType": "1754", "messageId": "1641", "endLine": 1170, "endColumn": 29}, {"ruleId": "1752", "severity": 1, "message": "1758", "line": 1171, "column": 19, "nodeType": "1754", "messageId": "1641", "endLine": 1171, "endColumn": 27}, {"ruleId": "1752", "severity": 1, "message": "1759", "line": 1172, "column": 19, "nodeType": "1754", "messageId": "1641", "endLine": 1172, "endColumn": 26}, {"ruleId": "1752", "severity": 1, "message": "1760", "line": 1173, "column": 19, "nodeType": "1754", "messageId": "1641", "endLine": 1173, "endColumn": 33}, {"ruleId": "1752", "severity": 1, "message": "1761", "line": 1174, "column": 19, "nodeType": "1754", "messageId": "1641", "endLine": 1174, "endColumn": 31}, {"ruleId": "1752", "severity": 1, "message": "1762", "line": 1176, "column": 19, "nodeType": "1754", "messageId": "1641", "endLine": 1176, "endColumn": 30}, {"ruleId": "1752", "severity": 1, "message": "1763", "line": 1177, "column": 19, "nodeType": "1754", "messageId": "1641", "endLine": 1177, "endColumn": 29}, {"ruleId": "1752", "severity": 1, "message": "1764", "line": 1178, "column": 19, "nodeType": "1754", "messageId": "1641", "endLine": 1178, "endColumn": 31}, {"ruleId": "1752", "severity": 1, "message": "1765", "line": 1179, "column": 19, "nodeType": "1754", "messageId": "1641", "endLine": 1179, "endColumn": 30}, {"ruleId": "1625", "severity": 1, "message": "1645", "line": 12, "column": 9, "nodeType": "1627", "messageId": "1628", "endLine": 12, "endColumn": 17}, {"ruleId": "1625", "severity": 1, "message": "1695", "line": 38, "column": 11, "nodeType": "1627", "messageId": "1628", "endLine": 38, "endColumn": 25}, {"ruleId": "1625", "severity": 1, "message": "1696", "line": 38, "column": 27, "nodeType": "1627", "messageId": "1628", "endLine": 38, "endColumn": 39}, {"ruleId": "1625", "severity": 1, "message": "1630", "line": 5, "column": 3, "nodeType": "1627", "messageId": "1628", "endLine": 5, "endColumn": 14}, {"ruleId": "1625", "severity": 1, "message": "1769", "line": 34, "column": 11, "nodeType": "1627", "messageId": "1628", "endLine": 34, "endColumn": 31}, {"ruleId": "1625", "severity": 1, "message": "1770", "line": 34, "column": 33, "nodeType": "1627", "messageId": "1628", "endLine": 34, "endColumn": 51}, {"ruleId": "1634", "severity": 1, "message": "1771", "line": 60, "column": 6, "nodeType": "1636", "endLine": 60, "endColumn": 28, "suggestions": "1772"}, {"ruleId": "1625", "severity": 1, "message": "1645", "line": 13, "column": 9, "nodeType": "1627", "messageId": "1628", "endLine": 13, "endColumn": 17}, {"ruleId": "1625", "severity": 1, "message": "1773", "line": 46, "column": 25, "nodeType": "1627", "messageId": "1628", "endLine": 46, "endColumn": 41}, {"ruleId": "1625", "severity": 1, "message": "1774", "line": 48, "column": 25, "nodeType": "1627", "messageId": "1628", "endLine": 48, "endColumn": 41}, {"ruleId": "1625", "severity": 1, "message": "1775", "line": 55, "column": 11, "nodeType": "1627", "messageId": "1628", "endLine": 55, "endColumn": 28}, {"ruleId": "1625", "severity": 1, "message": "1776", "line": 55, "column": 30, "nodeType": "1627", "messageId": "1628", "endLine": 55, "endColumn": 45}, {"ruleId": "1777", "severity": 1, "message": "1778", "line": 306, "column": 21, "nodeType": "1644", "endLine": 319, "endColumn": 23}, {"ruleId": "1777", "severity": 1, "message": "1778", "line": 340, "column": 21, "nodeType": "1644", "endLine": 353, "endColumn": 23}, {"ruleId": "1625", "severity": 1, "message": "1645", "line": 24, "column": 9, "nodeType": "1627", "messageId": "1628", "endLine": 24, "endColumn": 17}, {"ruleId": "1625", "severity": 1, "message": "1779", "line": 51, "column": 11, "nodeType": "1627", "messageId": "1628", "endLine": 51, "endColumn": 27}, {"ruleId": "1625", "severity": 1, "message": "1780", "line": 51, "column": 29, "nodeType": "1627", "messageId": "1628", "endLine": 51, "endColumn": 43}, {"ruleId": "1625", "severity": 1, "message": "1781", "line": 54, "column": 11, "nodeType": "1627", "messageId": "1628", "endLine": 54, "endColumn": 30}, {"ruleId": "1625", "severity": 1, "message": "1782", "line": 54, "column": 32, "nodeType": "1627", "messageId": "1628", "endLine": 54, "endColumn": 49}, {"ruleId": "1625", "severity": 1, "message": "1783", "line": 61, "column": 11, "nodeType": "1627", "messageId": "1628", "endLine": 61, "endColumn": 29}, {"ruleId": "1625", "severity": 1, "message": "1784", "line": 61, "column": 31, "nodeType": "1627", "messageId": "1628", "endLine": 61, "endColumn": 47}, {"ruleId": "1625", "severity": 1, "message": "1785", "line": 65, "column": 11, "nodeType": "1627", "messageId": "1628", "endLine": 65, "endColumn": 26}, {"ruleId": "1625", "severity": 1, "message": "1786", "line": 65, "column": 28, "nodeType": "1627", "messageId": "1628", "endLine": 65, "endColumn": 41}, {"ruleId": "1625", "severity": 1, "message": "1787", "line": 10, "column": 3, "nodeType": "1627", "messageId": "1628", "endLine": 10, "endColumn": 16}, {"ruleId": "1625", "severity": 1, "message": "1645", "line": 18, "column": 9, "nodeType": "1627", "messageId": "1628", "endLine": 18, "endColumn": 17}, {"ruleId": "1625", "severity": 1, "message": "1773", "line": 52, "column": 25, "nodeType": "1627", "messageId": "1628", "endLine": 52, "endColumn": 41}, {"ruleId": "1625", "severity": 1, "message": "1774", "line": 54, "column": 25, "nodeType": "1627", "messageId": "1628", "endLine": 54, "endColumn": 41}, {"ruleId": "1625", "severity": 1, "message": "1651", "line": 69, "column": 11, "nodeType": "1627", "messageId": "1628", "endLine": 69, "endColumn": 18}, {"ruleId": "1625", "severity": 1, "message": "1652", "line": 69, "column": 20, "nodeType": "1627", "messageId": "1628", "endLine": 69, "endColumn": 25}, {"ruleId": "1625", "severity": 1, "message": "1677", "line": 69, "column": 27, "nodeType": "1627", "messageId": "1628", "endLine": 69, "endColumn": 34}, {"ruleId": "1625", "severity": 1, "message": "1788", "line": 72, "column": 11, "nodeType": "1627", "messageId": "1628", "endLine": 72, "endColumn": 31}, {"ruleId": "1625", "severity": 1, "message": "1789", "line": 72, "column": 33, "nodeType": "1627", "messageId": "1628", "endLine": 72, "endColumn": 51}, {"ruleId": "1634", "severity": 1, "message": "1658", "line": 146, "column": 6, "nodeType": "1636", "endLine": 146, "endColumn": 28, "suggestions": "1790"}, {"ruleId": "1777", "severity": 1, "message": "1778", "line": 351, "column": 21, "nodeType": "1644", "endLine": 366, "endColumn": 23}, {"ruleId": "1777", "severity": 1, "message": "1778", "line": 387, "column": 21, "nodeType": "1644", "endLine": 402, "endColumn": 23}, {"ruleId": "1625", "severity": 1, "message": "1630", "line": 5, "column": 3, "nodeType": "1627", "messageId": "1628", "endLine": 5, "endColumn": 14}, {"ruleId": "1625", "severity": 1, "message": "1791", "line": 9, "column": 10, "nodeType": "1627", "messageId": "1628", "endLine": 9, "endColumn": 25}, {"ruleId": "1625", "severity": 1, "message": "1792", "line": 22, "column": 10, "nodeType": "1627", "messageId": "1628", "endLine": 22, "endColumn": 16}, {"ruleId": "1625", "severity": 1, "message": "1793", "line": 22, "column": 18, "nodeType": "1627", "messageId": "1628", "endLine": 22, "endColumn": 27}, {"ruleId": "1625", "severity": 1, "message": "1794", "line": 36, "column": 11, "nodeType": "1627", "messageId": "1628", "endLine": 36, "endColumn": 28}, {"ruleId": "1625", "severity": 1, "message": "1795", "line": 36, "column": 30, "nodeType": "1627", "messageId": "1628", "endLine": 36, "endColumn": 45}, {"ruleId": "1634", "severity": 1, "message": "1648", "line": 56, "column": 6, "nodeType": "1636", "endLine": 56, "endColumn": 25, "suggestions": "1796"}, {"ruleId": "1625", "severity": 1, "message": "1716", "line": 3, "column": 10, "nodeType": "1627", "messageId": "1628", "endLine": 3, "endColumn": 14}, {"ruleId": "1625", "severity": 1, "message": "1797", "line": 19, "column": 25, "nodeType": "1627", "messageId": "1628", "endLine": 19, "endColumn": 34}, {"ruleId": "1625", "severity": 1, "message": "1798", "line": 27, "column": 7, "nodeType": "1627", "messageId": "1628", "endLine": 27, "endColumn": 17}, {"ruleId": "1625", "severity": 1, "message": "1645", "line": 31, "column": 9, "nodeType": "1627", "messageId": "1628", "endLine": 31, "endColumn": 17}, {"ruleId": "1625", "severity": 1, "message": "1799", "line": 68, "column": 48, "nodeType": "1627", "messageId": "1628", "endLine": 68, "endColumn": 61}, {"ruleId": "1625", "severity": 1, "message": "1800", "line": 71, "column": 11, "nodeType": "1627", "messageId": "1628", "endLine": 71, "endColumn": 27}, {"ruleId": "1625", "severity": 1, "message": "1801", "line": 71, "column": 29, "nodeType": "1627", "messageId": "1628", "endLine": 71, "endColumn": 43}, {"ruleId": "1625", "severity": 1, "message": "1802", "line": 74, "column": 11, "nodeType": "1627", "messageId": "1628", "endLine": 74, "endColumn": 30}, {"ruleId": "1625", "severity": 1, "message": "1803", "line": 74, "column": 32, "nodeType": "1627", "messageId": "1628", "endLine": 74, "endColumn": 49}, {"ruleId": "1625", "severity": 1, "message": "1804", "line": 78, "column": 11, "nodeType": "1627", "messageId": "1628", "endLine": 78, "endColumn": 30}, {"ruleId": "1625", "severity": 1, "message": "1805", "line": 78, "column": 32, "nodeType": "1627", "messageId": "1628", "endLine": 78, "endColumn": 49}, {"ruleId": "1625", "severity": 1, "message": "1806", "line": 83, "column": 57, "nodeType": "1627", "messageId": "1628", "endLine": 83, "endColumn": 73}, {"ruleId": "1625", "severity": 1, "message": "1807", "line": 88, "column": 5, "nodeType": "1627", "messageId": "1628", "endLine": 88, "endColumn": 27}, {"ruleId": "1625", "severity": 1, "message": "1808", "line": 89, "column": 5, "nodeType": "1627", "messageId": "1628", "endLine": 89, "endColumn": 25}, {"ruleId": "1625", "severity": 1, "message": "1809", "line": 94, "column": 11, "nodeType": "1627", "messageId": "1628", "endLine": 94, "endColumn": 30}, {"ruleId": "1625", "severity": 1, "message": "1810", "line": 94, "column": 32, "nodeType": "1627", "messageId": "1628", "endLine": 94, "endColumn": 49}, {"ruleId": "1625", "severity": 1, "message": "1811", "line": 99, "column": 5, "nodeType": "1627", "messageId": "1628", "endLine": 99, "endColumn": 27}, {"ruleId": "1625", "severity": 1, "message": "1812", "line": 100, "column": 5, "nodeType": "1627", "messageId": "1628", "endLine": 100, "endColumn": 25}, {"ruleId": "1634", "severity": 1, "message": "1648", "line": 197, "column": 6, "nodeType": "1636", "endLine": 204, "endColumn": 4, "suggestions": "1813"}, {"ruleId": "1625", "severity": 1, "message": "1814", "line": 206, "column": 9, "nodeType": "1627", "messageId": "1628", "endLine": 206, "endColumn": 20}, {"ruleId": "1638", "severity": 1, "message": "1639", "line": 304, "column": 40, "nodeType": "1640", "messageId": "1641", "endLine": 304, "endColumn": 42}, {"ruleId": "1638", "severity": 1, "message": "1639", "line": 676, "column": 40, "nodeType": "1640", "messageId": "1641", "endLine": 676, "endColumn": 42}, {"ruleId": "1625", "severity": 1, "message": "1716", "line": 3, "column": 10, "nodeType": "1627", "messageId": "1628", "endLine": 3, "endColumn": 14}, {"ruleId": "1625", "severity": 1, "message": "1645", "line": 18, "column": 9, "nodeType": "1627", "messageId": "1628", "endLine": 18, "endColumn": 17}, {"ruleId": "1625", "severity": 1, "message": "1815", "line": 58, "column": 21, "nodeType": "1627", "messageId": "1628", "endLine": 58, "endColumn": 35}, {"ruleId": "1625", "severity": 1, "message": "1816", "line": 58, "column": 37, "nodeType": "1627", "messageId": "1628", "endLine": 58, "endColumn": 49}, {"ruleId": "1625", "severity": 1, "message": "1817", "line": 58, "column": 51, "nodeType": "1627", "messageId": "1628", "endLine": 58, "endColumn": 56}, {"ruleId": "1625", "severity": 1, "message": "1818", "line": 64, "column": 5, "nodeType": "1627", "messageId": "1628", "endLine": 64, "endColumn": 29}, {"ruleId": "1625", "severity": 1, "message": "1819", "line": 65, "column": 5, "nodeType": "1627", "messageId": "1628", "endLine": 65, "endColumn": 27}, {"ruleId": "1634", "severity": 1, "message": "1648", "line": 77, "column": 6, "nodeType": "1636", "endLine": 77, "endColumn": 26, "suggestions": "1820"}, {"ruleId": "1625", "severity": 1, "message": "1716", "line": 3, "column": 10, "nodeType": "1627", "messageId": "1628", "endLine": 3, "endColumn": 14}, {"ruleId": "1625", "severity": 1, "message": "1630", "line": 3, "column": 16, "nodeType": "1627", "messageId": "1628", "endLine": 3, "endColumn": 27}, {"ruleId": "1625", "severity": 1, "message": "1821", "line": 26, "column": 10, "nodeType": "1627", "messageId": "1628", "endLine": 26, "endColumn": 21}, {"ruleId": "1625", "severity": 1, "message": "1822", "line": 56, "column": 23, "nodeType": "1627", "messageId": "1628", "endLine": 56, "endColumn": 39}, {"ruleId": "1625", "severity": 1, "message": "1823", "line": 56, "column": 41, "nodeType": "1627", "messageId": "1628", "endLine": 56, "endColumn": 55}, {"ruleId": "1625", "severity": 1, "message": "1806", "line": 56, "column": 57, "nodeType": "1627", "messageId": "1628", "endLine": 56, "endColumn": 73}, {"ruleId": "1625", "severity": 1, "message": "1824", "line": 59, "column": 9, "nodeType": "1627", "messageId": "1628", "endLine": 59, "endColumn": 28}, {"ruleId": "1625", "severity": 1, "message": "1825", "line": 70, "column": 5, "nodeType": "1627", "messageId": "1628", "endLine": 70, "endColumn": 34}, {"ruleId": "1625", "severity": 1, "message": "1826", "line": 71, "column": 5, "nodeType": "1627", "messageId": "1628", "endLine": 71, "endColumn": 32}, {"ruleId": "1625", "severity": 1, "message": "1827", "line": 72, "column": 5, "nodeType": "1627", "messageId": "1628", "endLine": 72, "endColumn": 34}, {"ruleId": "1625", "severity": 1, "message": "1828", "line": 80, "column": 5, "nodeType": "1627", "messageId": "1628", "endLine": 80, "endColumn": 34}, {"ruleId": "1625", "severity": 1, "message": "1829", "line": 81, "column": 5, "nodeType": "1627", "messageId": "1628", "endLine": 81, "endColumn": 32}, {"ruleId": "1634", "severity": 1, "message": "1658", "line": 94, "column": 6, "nodeType": "1636", "endLine": 94, "endColumn": 26, "suggestions": "1830"}, {"ruleId": "1634", "severity": 1, "message": "1658", "line": 147, "column": 6, "nodeType": "1636", "endLine": 147, "endColumn": 37, "suggestions": "1831"}, {"ruleId": "1625", "severity": 1, "message": "1716", "line": 3, "column": 10, "nodeType": "1627", "messageId": "1628", "endLine": 3, "endColumn": 14}, {"ruleId": "1625", "severity": 1, "message": "1630", "line": 3, "column": 16, "nodeType": "1627", "messageId": "1628", "endLine": 3, "endColumn": 27}, {"ruleId": "1625", "severity": 1, "message": "1821", "line": 23, "column": 10, "nodeType": "1627", "messageId": "1628", "endLine": 23, "endColumn": 21}, {"ruleId": "1625", "severity": 1, "message": "1822", "line": 53, "column": 23, "nodeType": "1627", "messageId": "1628", "endLine": 53, "endColumn": 39}, {"ruleId": "1625", "severity": 1, "message": "1823", "line": 53, "column": 41, "nodeType": "1627", "messageId": "1628", "endLine": 53, "endColumn": 55}, {"ruleId": "1625", "severity": 1, "message": "1806", "line": 53, "column": 57, "nodeType": "1627", "messageId": "1628", "endLine": 53, "endColumn": 73}, {"ruleId": "1625", "severity": 1, "message": "1832", "line": 60, "column": 5, "nodeType": "1627", "messageId": "1628", "endLine": 60, "endColumn": 31}, {"ruleId": "1625", "severity": 1, "message": "1833", "line": 61, "column": 5, "nodeType": "1627", "messageId": "1628", "endLine": 61, "endColumn": 29}, {"ruleId": "1634", "severity": 1, "message": "1648", "line": 75, "column": 6, "nodeType": "1636", "endLine": 75, "endColumn": 26, "suggestions": "1834"}, {"ruleId": "1625", "severity": 1, "message": "1630", "line": 5, "column": 3, "nodeType": "1627", "messageId": "1628", "endLine": 5, "endColumn": 14}, {"ruleId": "1625", "severity": 1, "message": "1835", "line": 36, "column": 5, "nodeType": "1627", "messageId": "1628", "endLine": 36, "endColumn": 34}, {"ruleId": "1625", "severity": 1, "message": "1836", "line": 37, "column": 5, "nodeType": "1627", "messageId": "1628", "endLine": 37, "endColumn": 32}, {"ruleId": "1625", "severity": 1, "message": "1837", "line": 51, "column": 5, "nodeType": "1627", "messageId": "1628", "endLine": 51, "endColumn": 28}, {"ruleId": "1634", "severity": 1, "message": "1648", "line": 62, "column": 6, "nodeType": "1636", "endLine": 62, "endColumn": 32, "suggestions": "1838"}, {"ruleId": "1634", "severity": 1, "message": "1648", "line": 72, "column": 6, "nodeType": "1636", "endLine": 72, "endColumn": 37, "suggestions": "1839"}, {"ruleId": "1625", "severity": 1, "message": "1716", "line": 3, "column": 10, "nodeType": "1627", "messageId": "1628", "endLine": 3, "endColumn": 14}, {"ruleId": "1625", "severity": 1, "message": "1840", "line": 6, "column": 3, "nodeType": "1627", "messageId": "1628", "endLine": 6, "endColumn": 23}, {"ruleId": "1625", "severity": 1, "message": "1841", "line": 8, "column": 3, "nodeType": "1627", "messageId": "1628", "endLine": 8, "endColumn": 17}, {"ruleId": "1625", "severity": 1, "message": "1645", "line": 20, "column": 9, "nodeType": "1627", "messageId": "1628", "endLine": 20, "endColumn": 17}, {"ruleId": "1625", "severity": 1, "message": "1815", "line": 60, "column": 21, "nodeType": "1627", "messageId": "1628", "endLine": 60, "endColumn": 35}, {"ruleId": "1625", "severity": 1, "message": "1816", "line": 60, "column": 37, "nodeType": "1627", "messageId": "1628", "endLine": 60, "endColumn": 49}, {"ruleId": "1625", "severity": 1, "message": "1817", "line": 60, "column": 51, "nodeType": "1627", "messageId": "1628", "endLine": 60, "endColumn": 56}, {"ruleId": "1625", "severity": 1, "message": "1842", "line": 66, "column": 5, "nodeType": "1627", "messageId": "1628", "endLine": 66, "endColumn": 32}, {"ruleId": "1625", "severity": 1, "message": "1843", "line": 67, "column": 5, "nodeType": "1627", "messageId": "1628", "endLine": 67, "endColumn": 30}, {"ruleId": "1625", "severity": 1, "message": "1844", "line": 68, "column": 5, "nodeType": "1627", "messageId": "1628", "endLine": 68, "endColumn": 32}, {"ruleId": "1625", "severity": 1, "message": "1845", "line": 76, "column": 5, "nodeType": "1627", "messageId": "1628", "endLine": 76, "endColumn": 32}, {"ruleId": "1625", "severity": 1, "message": "1846", "line": 77, "column": 5, "nodeType": "1627", "messageId": "1628", "endLine": 77, "endColumn": 30}, {"ruleId": "1634", "severity": 1, "message": "1658", "line": 89, "column": 6, "nodeType": "1636", "endLine": 89, "endColumn": 26, "suggestions": "1847"}, {"ruleId": "1634", "severity": 1, "message": "1658", "line": 148, "column": 6, "nodeType": "1636", "endLine": 148, "endColumn": 35, "suggestions": "1848"}, {"ruleId": "1625", "severity": 1, "message": "1630", "line": 5, "column": 3, "nodeType": "1627", "messageId": "1628", "endLine": 5, "endColumn": 14}, {"ruleId": "1625", "severity": 1, "message": "1849", "line": 39, "column": 5, "nodeType": "1627", "messageId": "1628", "endLine": 39, "endColumn": 26}, {"ruleId": "1625", "severity": 1, "message": "1850", "line": 47, "column": 5, "nodeType": "1627", "messageId": "1628", "endLine": 47, "endColumn": 32}, {"ruleId": "1625", "severity": 1, "message": "1851", "line": 48, "column": 5, "nodeType": "1627", "messageId": "1628", "endLine": 48, "endColumn": 30}, {"ruleId": "1634", "severity": 1, "message": "1648", "line": 59, "column": 6, "nodeType": "1636", "endLine": 59, "endColumn": 32, "suggestions": "1852"}, {"ruleId": "1634", "severity": 1, "message": "1648", "line": 69, "column": 6, "nodeType": "1636", "endLine": 69, "endColumn": 35, "suggestions": "1853"}, {"ruleId": "1625", "severity": 1, "message": "1630", "line": 5, "column": 3, "nodeType": "1627", "messageId": "1628", "endLine": 5, "endColumn": 14}, {"ruleId": "1625", "severity": 1, "message": "1854", "line": 32, "column": 5, "nodeType": "1627", "messageId": "1628", "endLine": 32, "endColumn": 25}, {"ruleId": "1625", "severity": 1, "message": "1855", "line": 38, "column": 5, "nodeType": "1627", "messageId": "1628", "endLine": 38, "endColumn": 31}, {"ruleId": "1625", "severity": 1, "message": "1856", "line": 39, "column": 5, "nodeType": "1627", "messageId": "1628", "endLine": 39, "endColumn": 29}, {"ruleId": "1634", "severity": 1, "message": "1648", "line": 55, "column": 6, "nodeType": "1636", "endLine": 55, "endColumn": 32, "suggestions": "1857"}, {"ruleId": "1634", "severity": 1, "message": "1648", "line": 65, "column": 6, "nodeType": "1636", "endLine": 65, "endColumn": 34, "suggestions": "1858"}, {"ruleId": "1625", "severity": 1, "message": "1716", "line": 3, "column": 10, "nodeType": "1627", "messageId": "1628", "endLine": 3, "endColumn": 14}, {"ruleId": "1625", "severity": 1, "message": "1630", "line": 3, "column": 16, "nodeType": "1627", "messageId": "1628", "endLine": 3, "endColumn": 27}, {"ruleId": "1625", "severity": 1, "message": "1821", "line": 25, "column": 10, "nodeType": "1627", "messageId": "1628", "endLine": 25, "endColumn": 21}, {"ruleId": "1625", "severity": 1, "message": "1859", "line": 52, "column": 20, "nodeType": "1627", "messageId": "1628", "endLine": 52, "endColumn": 33}, {"ruleId": "1625", "severity": 1, "message": "1860", "line": 52, "column": 35, "nodeType": "1627", "messageId": "1628", "endLine": 52, "endColumn": 46}, {"ruleId": "1625", "severity": 1, "message": "1799", "line": 52, "column": 48, "nodeType": "1627", "messageId": "1628", "endLine": 52, "endColumn": 61}, {"ruleId": "1625", "severity": 1, "message": "1861", "line": 58, "column": 5, "nodeType": "1627", "messageId": "1628", "endLine": 58, "endColumn": 31}, {"ruleId": "1625", "severity": 1, "message": "1862", "line": 59, "column": 5, "nodeType": "1627", "messageId": "1628", "endLine": 59, "endColumn": 29}, {"ruleId": "1625", "severity": 1, "message": "1863", "line": 60, "column": 5, "nodeType": "1627", "messageId": "1628", "endLine": 60, "endColumn": 31}, {"ruleId": "1625", "severity": 1, "message": "1864", "line": 66, "column": 5, "nodeType": "1627", "messageId": "1628", "endLine": 66, "endColumn": 31}, {"ruleId": "1625", "severity": 1, "message": "1865", "line": 67, "column": 5, "nodeType": "1627", "messageId": "1628", "endLine": 67, "endColumn": 29}, {"ruleId": "1634", "severity": 1, "message": "1658", "line": 79, "column": 6, "nodeType": "1636", "endLine": 79, "endColumn": 26, "suggestions": "1866"}, {"ruleId": "1634", "severity": 1, "message": "1658", "line": 127, "column": 6, "nodeType": "1636", "endLine": 127, "endColumn": 34, "suggestions": "1867"}, {"ruleId": "1625", "severity": 1, "message": "1716", "line": 3, "column": 10, "nodeType": "1627", "messageId": "1628", "endLine": 3, "endColumn": 14}, {"ruleId": "1625", "severity": 1, "message": "1630", "line": 3, "column": 16, "nodeType": "1627", "messageId": "1628", "endLine": 3, "endColumn": 27}, {"ruleId": "1625", "severity": 1, "message": "1859", "line": 48, "column": 20, "nodeType": "1627", "messageId": "1628", "endLine": 48, "endColumn": 33}, {"ruleId": "1625", "severity": 1, "message": "1860", "line": 48, "column": 35, "nodeType": "1627", "messageId": "1628", "endLine": 48, "endColumn": 46}, {"ruleId": "1625", "severity": 1, "message": "1799", "line": 48, "column": 48, "nodeType": "1627", "messageId": "1628", "endLine": 48, "endColumn": 61}, {"ruleId": "1625", "severity": 1, "message": "1868", "line": 52, "column": 5, "nodeType": "1627", "messageId": "1628", "endLine": 52, "endColumn": 28}, {"ruleId": "1625", "severity": 1, "message": "1869", "line": 53, "column": 5, "nodeType": "1627", "messageId": "1628", "endLine": 53, "endColumn": 26}, {"ruleId": "1634", "severity": 1, "message": "1648", "line": 64, "column": 6, "nodeType": "1636", "endLine": 64, "endColumn": 26, "suggestions": "1870"}, {"ruleId": "1625", "severity": 1, "message": "1871", "line": 186, "column": 29, "nodeType": "1627", "messageId": "1628", "endLine": 186, "endColumn": 34}, {"ruleId": "1625", "severity": 1, "message": "1872", "line": 1, "column": 10, "nodeType": "1627", "messageId": "1628", "endLine": 1, "endColumn": 15}, {"ruleId": "1625", "severity": 1, "message": "1873", "line": 17, "column": 7, "nodeType": "1627", "messageId": "1628", "endLine": 17, "endColumn": 17}, {"ruleId": "1634", "severity": 1, "message": "1874", "line": 29, "column": 6, "nodeType": "1636", "endLine": 29, "endColumn": 11, "suggestions": "1875"}, {"ruleId": "1876", "severity": 1, "message": "1877", "line": 124, "column": 25, "nodeType": "1878", "endLine": 124, "endColumn": 38}, {"ruleId": "1638", "severity": 1, "message": "1639", "line": 15, "column": 23, "nodeType": "1640", "messageId": "1641", "endLine": 15, "endColumn": 25}, {"ruleId": "1625", "severity": 1, "message": "1652", "line": 9, "column": 21, "nodeType": "1627", "messageId": "1628", "endLine": 9, "endColumn": 26}, {"ruleId": "1625", "severity": 1, "message": "1651", "line": 9, "column": 28, "nodeType": "1627", "messageId": "1628", "endLine": 9, "endColumn": 35}, {"ruleId": "1625", "severity": 1, "message": "1879", "line": 11, "column": 9, "nodeType": "1627", "messageId": "1628", "endLine": 11, "endColumn": 17}, {"ruleId": "1625", "severity": 1, "message": "1872", "line": 6, "column": 10, "nodeType": "1627", "messageId": "1628", "endLine": 6, "endColumn": 15}, {"ruleId": "1625", "severity": 1, "message": "1652", "line": 19, "column": 21, "nodeType": "1627", "messageId": "1628", "endLine": 19, "endColumn": 26}, {"ruleId": "1625", "severity": 1, "message": "1651", "line": 19, "column": 28, "nodeType": "1627", "messageId": "1628", "endLine": 19, "endColumn": 35}, {"ruleId": "1625", "severity": 1, "message": "1880", "line": 21, "column": 10, "nodeType": "1627", "messageId": "1628", "endLine": 21, "endColumn": 20}, {"ruleId": "1625", "severity": 1, "message": "1881", "line": 21, "column": 22, "nodeType": "1627", "messageId": "1628", "endLine": 21, "endColumn": 35}, {"ruleId": "1642", "severity": 1, "message": "1643", "line": 148, "column": 19, "nodeType": "1644", "endLine": 151, "endColumn": 21}, {"ruleId": "1642", "severity": 1, "message": "1643", "line": 164, "column": 19, "nodeType": "1644", "endLine": 167, "endColumn": 21}, {"ruleId": "1642", "severity": 1, "message": "1643", "line": 180, "column": 19, "nodeType": "1644", "endLine": 183, "endColumn": 21}, {"ruleId": "1642", "severity": 1, "message": "1643", "line": 212, "column": 19, "nodeType": "1644", "endLine": 215, "endColumn": 21}, {"ruleId": "1642", "severity": 1, "message": "1643", "line": 242, "column": 19, "nodeType": "1644", "endLine": 245, "endColumn": 21}, {"ruleId": "1642", "severity": 1, "message": "1643", "line": 272, "column": 19, "nodeType": "1644", "endLine": 275, "endColumn": 21}, {"ruleId": "1642", "severity": 1, "message": "1643", "line": 288, "column": 19, "nodeType": "1644", "endLine": 291, "endColumn": 21}, {"ruleId": "1642", "severity": 1, "message": "1643", "line": 305, "column": 21, "nodeType": "1644", "endLine": 308, "endColumn": 23}, {"ruleId": "1642", "severity": 1, "message": "1643", "line": 383, "column": 21, "nodeType": "1644", "endLine": 386, "endColumn": 23}, {"ruleId": "1625", "severity": 1, "message": "1882", "line": 5, "column": 9, "nodeType": "1627", "messageId": "1628", "endLine": 5, "endColumn": 17}, {"ruleId": "1625", "severity": 1, "message": "1883", "line": 6, "column": 9, "nodeType": "1627", "messageId": "1628", "endLine": 6, "endColumn": 17}, {"ruleId": "1625", "severity": 1, "message": "1884", "line": 1, "column": 10, "nodeType": "1627", "messageId": "1628", "endLine": 1, "endColumn": 19}, {"ruleId": "1625", "severity": 1, "message": "1885", "line": 2, "column": 23, "nodeType": "1627", "messageId": "1628", "endLine": 2, "endColumn": 34}, {"ruleId": "1625", "severity": 1, "message": "1882", "line": 6, "column": 9, "nodeType": "1627", "messageId": "1628", "endLine": 6, "endColumn": 17}, {"ruleId": "1625", "severity": 1, "message": "1883", "line": 7, "column": 9, "nodeType": "1627", "messageId": "1628", "endLine": 7, "endColumn": 17}, {"ruleId": "1625", "severity": 1, "message": "1886", "line": 26, "column": 9, "nodeType": "1627", "messageId": "1628", "endLine": 26, "endColumn": 19}, {"ruleId": "1625", "severity": 1, "message": "1872", "line": 1, "column": 10, "nodeType": "1627", "messageId": "1628", "endLine": 1, "endColumn": 15}, {"ruleId": "1625", "severity": 1, "message": "1669", "line": 4, "column": 10, "nodeType": "1627", "messageId": "1628", "endLine": 4, "endColumn": 22}, {"ruleId": "1625", "severity": 1, "message": "1887", "line": 4, "column": 24, "nodeType": "1627", "messageId": "1628", "endLine": 4, "endColumn": 34}, {"ruleId": "1625", "severity": 1, "message": "1670", "line": 6, "column": 10, "nodeType": "1627", "messageId": "1628", "endLine": 6, "endColumn": 24}, {"ruleId": "1625", "severity": 1, "message": "1671", "line": 7, "column": 10, "nodeType": "1627", "messageId": "1628", "endLine": 7, "endColumn": 23}, {"ruleId": "1625", "severity": 1, "message": "1672", "line": 8, "column": 10, "nodeType": "1627", "messageId": "1628", "endLine": 8, "endColumn": 22}, {"ruleId": "1625", "severity": 1, "message": "1673", "line": 10, "column": 10, "nodeType": "1627", "messageId": "1628", "endLine": 10, "endColumn": 19}, {"ruleId": "1625", "severity": 1, "message": "1888", "line": 11, "column": 8, "nodeType": "1627", "messageId": "1628", "endLine": 11, "endColumn": 25}, {"ruleId": "1625", "severity": 1, "message": "1674", "line": 12, "column": 10, "nodeType": "1627", "messageId": "1628", "endLine": 12, "endColumn": 27}, {"ruleId": "1625", "severity": 1, "message": "1721", "line": 15, "column": 19, "nodeType": "1627", "messageId": "1628", "endLine": 15, "endColumn": 30}, {"ruleId": "1625", "severity": 1, "message": "1645", "line": 19, "column": 9, "nodeType": "1627", "messageId": "1628", "endLine": 19, "endColumn": 17}, {"ruleId": "1625", "severity": 1, "message": "1651", "line": 65, "column": 21, "nodeType": "1627", "messageId": "1628", "endLine": 65, "endColumn": 28}, {"ruleId": "1625", "severity": 1, "message": "1652", "line": 65, "column": 30, "nodeType": "1627", "messageId": "1628", "endLine": 65, "endColumn": 35}, {"ruleId": "1634", "severity": 1, "message": "1648", "line": 77, "column": 6, "nodeType": "1636", "endLine": 77, "endColumn": 26, "suggestions": "1889"}, {"ruleId": "1625", "severity": 1, "message": "1890", "line": 40, "column": 8, "nodeType": "1627", "messageId": "1628", "endLine": 40, "endColumn": 27}, {"ruleId": "1625", "severity": 1, "message": "1891", "line": 3, "column": 10, "nodeType": "1627", "messageId": "1628", "endLine": 3, "endColumn": 29}, {"ruleId": "1642", "severity": 1, "message": "1643", "line": 77, "column": 11, "nodeType": "1644", "endLine": 77, "endColumn": 53}, {"ruleId": "1642", "severity": 1, "message": "1643", "line": 81, "column": 11, "nodeType": "1644", "endLine": 81, "endColumn": 79}, {"ruleId": "1892", "severity": 1, "message": "1893", "line": 187, "column": 13, "nodeType": "1644", "endLine": 187, "endColumn": 46}, {"ruleId": "1625", "severity": 1, "message": "1744", "line": 3, "column": 10, "nodeType": "1627", "messageId": "1628", "endLine": 3, "endColumn": 21}, {"ruleId": "1625", "severity": 1, "message": "1894", "line": 10, "column": 11, "nodeType": "1627", "messageId": "1628", "endLine": 10, "endColumn": 19}, {"ruleId": "1625", "severity": 1, "message": "1652", "line": 10, "column": 21, "nodeType": "1627", "messageId": "1628", "endLine": 10, "endColumn": 26}, {"ruleId": "1625", "severity": 1, "message": "1651", "line": 10, "column": 28, "nodeType": "1627", "messageId": "1628", "endLine": 10, "endColumn": 35}, {"ruleId": "1625", "severity": 1, "message": "1895", "line": 13, "column": 11, "nodeType": "1627", "messageId": "1628", "endLine": 13, "endColumn": 28}, {"ruleId": "1625", "severity": 1, "message": "1896", "line": 13, "column": 30, "nodeType": "1627", "messageId": "1628", "endLine": 13, "endColumn": 45}, {"ruleId": "1625", "severity": 1, "message": "1897", "line": 118, "column": 21, "nodeType": "1627", "messageId": "1628", "endLine": 118, "endColumn": 36}, {"ruleId": "1625", "severity": 1, "message": "1898", "line": 118, "column": 38, "nodeType": "1627", "messageId": "1628", "endLine": 118, "endColumn": 51}, {"ruleId": "1625", "severity": 1, "message": "1899", "line": 121, "column": 11, "nodeType": "1627", "messageId": "1628", "endLine": 121, "endColumn": 28}, {"ruleId": "1625", "severity": 1, "message": "1900", "line": 121, "column": 30, "nodeType": "1627", "messageId": "1628", "endLine": 121, "endColumn": 45}, {"ruleId": "1625", "severity": 1, "message": "1901", "line": 124, "column": 22, "nodeType": "1627", "messageId": "1628", "endLine": 124, "endColumn": 38}, {"ruleId": "1625", "severity": 1, "message": "1902", "line": 124, "column": 40, "nodeType": "1627", "messageId": "1628", "endLine": 124, "endColumn": 54}, {"ruleId": "1625", "severity": 1, "message": "1903", "line": 127, "column": 23, "nodeType": "1627", "messageId": "1628", "endLine": 127, "endColumn": 40}, {"ruleId": "1625", "severity": 1, "message": "1904", "line": 127, "column": 42, "nodeType": "1627", "messageId": "1628", "endLine": 127, "endColumn": 57}, {"ruleId": "1625", "severity": 1, "message": "1905", "line": 130, "column": 25, "nodeType": "1627", "messageId": "1628", "endLine": 130, "endColumn": 44}, {"ruleId": "1625", "severity": 1, "message": "1906", "line": 130, "column": 46, "nodeType": "1627", "messageId": "1628", "endLine": 130, "endColumn": 63}, {"ruleId": "1625", "severity": 1, "message": "1907", "line": 142, "column": 13, "nodeType": "1627", "messageId": "1628", "endLine": 142, "endColumn": 20}, {"ruleId": "1625", "severity": 1, "message": "1908", "line": 143, "column": 13, "nodeType": "1627", "messageId": "1628", "endLine": 143, "endColumn": 19}, {"ruleId": "1634", "severity": 1, "message": "1909", "line": 184, "column": 6, "nodeType": "1636", "endLine": 190, "endColumn": 4, "suggestions": "1910"}, {"ruleId": "1634", "severity": 1, "message": "1911", "line": 223, "column": 6, "nodeType": "1636", "endLine": 223, "endColumn": 25, "suggestions": "1912"}, {"ruleId": "1625", "severity": 1, "message": "1873", "line": 17, "column": 7, "nodeType": "1627", "messageId": "1628", "endLine": 17, "endColumn": 17}, {"ruleId": "1625", "severity": 1, "message": "1899", "line": 41, "column": 11, "nodeType": "1627", "messageId": "1628", "endLine": 41, "endColumn": 28}, {"ruleId": "1625", "severity": 1, "message": "1900", "line": 41, "column": 30, "nodeType": "1627", "messageId": "1628", "endLine": 41, "endColumn": 45}, {"ruleId": "1634", "severity": 1, "message": "1913", "line": 63, "column": 6, "nodeType": "1636", "endLine": 63, "endColumn": 42, "suggestions": "1914"}, {"ruleId": "1634", "severity": 1, "message": "1915", "line": 72, "column": 6, "nodeType": "1636", "endLine": 72, "endColumn": 25, "suggestions": "1916"}, {"ruleId": "1625", "severity": 1, "message": "1917", "line": 13, "column": 3, "nodeType": "1627", "messageId": "1628", "endLine": 13, "endColumn": 16}, {"ruleId": "1625", "severity": 1, "message": "1918", "line": 27, "column": 10, "nodeType": "1627", "messageId": "1628", "endLine": 27, "endColumn": 32}, {"ruleId": "1625", "severity": 1, "message": "1919", "line": 30, "column": 7, "nodeType": "1627", "messageId": "1628", "endLine": 30, "endColumn": 22}, {"ruleId": "1625", "severity": 1, "message": "1645", "line": 39, "column": 9, "nodeType": "1627", "messageId": "1628", "endLine": 39, "endColumn": 17}, {"ruleId": "1625", "severity": 1, "message": "1920", "line": 45, "column": 9, "nodeType": "1627", "messageId": "1628", "endLine": 45, "endColumn": 25}, {"ruleId": "1625", "severity": 1, "message": "1921", "line": 56, "column": 10, "nodeType": "1627", "messageId": "1628", "endLine": 56, "endColumn": 21}, {"ruleId": "1634", "severity": 1, "message": "1922", "line": 85, "column": 6, "nodeType": "1636", "endLine": 85, "endColumn": 8, "suggestions": "1923"}, {"ruleId": "1625", "severity": 1, "message": "1651", "line": 89, "column": 21, "nodeType": "1627", "messageId": "1628", "endLine": 89, "endColumn": 28}, {"ruleId": "1625", "severity": 1, "message": "1652", "line": 89, "column": 30, "nodeType": "1627", "messageId": "1628", "endLine": 89, "endColumn": 35}, {"ruleId": "1625", "severity": 1, "message": "1924", "line": 92, "column": 43, "nodeType": "1627", "messageId": "1628", "endLine": 92, "endColumn": 58}, {"ruleId": "1625", "severity": 1, "message": "1817", "line": 96, "column": 59, "nodeType": "1627", "messageId": "1628", "endLine": 96, "endColumn": 64}, {"ruleId": "1625", "severity": 1, "message": "1925", "line": 103, "column": 5, "nodeType": "1627", "messageId": "1628", "endLine": 103, "endColumn": 27}, {"ruleId": "1625", "severity": 1, "message": "1926", "line": 107, "column": 57, "nodeType": "1627", "messageId": "1628", "endLine": 107, "endColumn": 76}, {"ruleId": "1625", "severity": 1, "message": "1905", "line": 111, "column": 25, "nodeType": "1627", "messageId": "1628", "endLine": 111, "endColumn": 44}, {"ruleId": "1625", "severity": 1, "message": "1906", "line": 111, "column": 46, "nodeType": "1627", "messageId": "1628", "endLine": 111, "endColumn": 63}, {"ruleId": "1625", "severity": 1, "message": "1927", "line": 116, "column": 5, "nodeType": "1627", "messageId": "1628", "endLine": 116, "endColumn": 30}, {"ruleId": "1625", "severity": 1, "message": "1928", "line": 117, "column": 5, "nodeType": "1627", "messageId": "1628", "endLine": 117, "endColumn": 28}, {"ruleId": "1625", "severity": 1, "message": "1929", "line": 123, "column": 5, "nodeType": "1627", "messageId": "1628", "endLine": 123, "endColumn": 25}, {"ruleId": "1625", "severity": 1, "message": "1930", "line": 124, "column": 5, "nodeType": "1627", "messageId": "1628", "endLine": 124, "endColumn": 23}, {"ruleId": "1634", "severity": 1, "message": "1658", "line": 154, "column": 6, "nodeType": "1636", "endLine": 154, "endColumn": 29, "suggestions": "1931"}, {"ruleId": "1634", "severity": 1, "message": "1658", "line": 163, "column": 6, "nodeType": "1636", "endLine": 163, "endColumn": 32, "suggestions": "1932"}, {"ruleId": "1634", "severity": 1, "message": "1933", "line": 170, "column": 6, "nodeType": "1636", "endLine": 170, "endColumn": 43, "suggestions": "1934"}, {"ruleId": "1634", "severity": 1, "message": "1658", "line": 186, "column": 6, "nodeType": "1636", "endLine": 186, "endColumn": 33, "suggestions": "1935"}, {"ruleId": "1625", "severity": 1, "message": "1645", "line": 64, "column": 9, "nodeType": "1627", "messageId": "1628", "endLine": 64, "endColumn": 17}, {"ruleId": "1625", "severity": 1, "message": "1936", "line": 72, "column": 10, "nodeType": "1627", "messageId": "1628", "endLine": 72, "endColumn": 23}, {"ruleId": "1625", "severity": 1, "message": "1937", "line": 77, "column": 21, "nodeType": "1627", "messageId": "1628", "endLine": 77, "endColumn": 33}, {"ruleId": "1625", "severity": 1, "message": "1938", "line": 78, "column": 10, "nodeType": "1627", "messageId": "1628", "endLine": 78, "endColumn": 24}, {"ruleId": "1625", "severity": 1, "message": "1939", "line": 83, "column": 19, "nodeType": "1627", "messageId": "1628", "endLine": 83, "endColumn": 29}, {"ruleId": "1625", "severity": 1, "message": "1940", "line": 84, "column": 10, "nodeType": "1627", "messageId": "1628", "endLine": 84, "endColumn": 22}, {"ruleId": "1625", "severity": 1, "message": "1941", "line": 124, "column": 10, "nodeType": "1627", "messageId": "1628", "endLine": 124, "endColumn": 30}, {"ruleId": "1625", "severity": 1, "message": "1942", "line": 124, "column": 32, "nodeType": "1627", "messageId": "1628", "endLine": 124, "endColumn": 55}, {"ruleId": "1625", "severity": 1, "message": "1943", "line": 126, "column": 27, "nodeType": "1627", "messageId": "1628", "endLine": 126, "endColumn": 45}, {"ruleId": "1625", "severity": 1, "message": "1944", "line": 127, "column": 10, "nodeType": "1627", "messageId": "1628", "endLine": 127, "endColumn": 30}, {"ruleId": "1625", "severity": 1, "message": "1945", "line": 127, "column": 32, "nodeType": "1627", "messageId": "1628", "endLine": 127, "endColumn": 55}, {"ruleId": "1625", "severity": 1, "message": "1946", "line": 133, "column": 10, "nodeType": "1627", "messageId": "1628", "endLine": 133, "endColumn": 30}, {"ruleId": "1625", "severity": 1, "message": "1947", "line": 133, "column": 32, "nodeType": "1627", "messageId": "1628", "endLine": 133, "endColumn": 55}, {"ruleId": "1625", "severity": 1, "message": "1948", "line": 136, "column": 10, "nodeType": "1627", "messageId": "1628", "endLine": 136, "endColumn": 24}, {"ruleId": "1625", "severity": 1, "message": "1949", "line": 136, "column": 26, "nodeType": "1627", "messageId": "1628", "endLine": 136, "endColumn": 43}, {"ruleId": "1625", "severity": 1, "message": "1950", "line": 139, "column": 10, "nodeType": "1627", "messageId": "1628", "endLine": 139, "endColumn": 22}, {"ruleId": "1625", "severity": 1, "message": "1951", "line": 139, "column": 24, "nodeType": "1627", "messageId": "1628", "endLine": 139, "endColumn": 39}, {"ruleId": "1625", "severity": 1, "message": "1952", "line": 142, "column": 10, "nodeType": "1627", "messageId": "1628", "endLine": 142, "endColumn": 30}, {"ruleId": "1625", "severity": 1, "message": "1953", "line": 142, "column": 32, "nodeType": "1627", "messageId": "1628", "endLine": 142, "endColumn": 55}, {"ruleId": "1625", "severity": 1, "message": "1954", "line": 147, "column": 10, "nodeType": "1627", "messageId": "1628", "endLine": 147, "endColumn": 23}, {"ruleId": "1625", "severity": 1, "message": "1955", "line": 147, "column": 25, "nodeType": "1627", "messageId": "1628", "endLine": 147, "endColumn": 41}, {"ruleId": "1625", "severity": 1, "message": "1956", "line": 148, "column": 10, "nodeType": "1627", "messageId": "1628", "endLine": 148, "endColumn": 28}, {"ruleId": "1625", "severity": 1, "message": "1957", "line": 148, "column": 30, "nodeType": "1627", "messageId": "1628", "endLine": 148, "endColumn": 51}, {"ruleId": "1625", "severity": 1, "message": "1958", "line": 150, "column": 10, "nodeType": "1627", "messageId": "1628", "endLine": 150, "endColumn": 23}, {"ruleId": "1625", "severity": 1, "message": "1959", "line": 150, "column": 25, "nodeType": "1627", "messageId": "1628", "endLine": 150, "endColumn": 41}, {"ruleId": "1625", "severity": 1, "message": "1960", "line": 151, "column": 10, "nodeType": "1627", "messageId": "1628", "endLine": 151, "endColumn": 28}, {"ruleId": "1625", "severity": 1, "message": "1961", "line": 151, "column": 30, "nodeType": "1627", "messageId": "1628", "endLine": 151, "endColumn": 51}, {"ruleId": "1625", "severity": 1, "message": "1962", "line": 153, "column": 10, "nodeType": "1627", "messageId": "1628", "endLine": 153, "endColumn": 25}, {"ruleId": "1625", "severity": 1, "message": "1963", "line": 153, "column": 27, "nodeType": "1627", "messageId": "1628", "endLine": 153, "endColumn": 45}, {"ruleId": "1625", "severity": 1, "message": "1964", "line": 154, "column": 10, "nodeType": "1627", "messageId": "1628", "endLine": 154, "endColumn": 30}, {"ruleId": "1625", "severity": 1, "message": "1965", "line": 154, "column": 32, "nodeType": "1627", "messageId": "1628", "endLine": 154, "endColumn": 55}, {"ruleId": "1625", "severity": 1, "message": "1966", "line": 157, "column": 10, "nodeType": "1627", "messageId": "1628", "endLine": 157, "endColumn": 28}, {"ruleId": "1625", "severity": 1, "message": "1967", "line": 157, "column": 30, "nodeType": "1627", "messageId": "1628", "endLine": 157, "endColumn": 51}, {"ruleId": "1625", "severity": 1, "message": "1968", "line": 160, "column": 10, "nodeType": "1627", "messageId": "1628", "endLine": 160, "endColumn": 25}, {"ruleId": "1625", "severity": 1, "message": "1969", "line": 160, "column": 27, "nodeType": "1627", "messageId": "1628", "endLine": 160, "endColumn": 45}, {"ruleId": "1625", "severity": 1, "message": "1970", "line": 163, "column": 10, "nodeType": "1627", "messageId": "1628", "endLine": 163, "endColumn": 21}, {"ruleId": "1625", "severity": 1, "message": "1971", "line": 163, "column": 23, "nodeType": "1627", "messageId": "1628", "endLine": 163, "endColumn": 37}, {"ruleId": "1625", "severity": 1, "message": "1972", "line": 166, "column": 33, "nodeType": "1627", "messageId": "1628", "endLine": 166, "endColumn": 57}, {"ruleId": "1625", "severity": 1, "message": "1973", "line": 169, "column": 32, "nodeType": "1627", "messageId": "1628", "endLine": 169, "endColumn": 55}, {"ruleId": "1625", "severity": 1, "message": "1974", "line": 172, "column": 10, "nodeType": "1627", "messageId": "1628", "endLine": 172, "endColumn": 27}, {"ruleId": "1625", "severity": 1, "message": "1975", "line": 172, "column": 29, "nodeType": "1627", "messageId": "1628", "endLine": 172, "endColumn": 49}, {"ruleId": "1625", "severity": 1, "message": "1976", "line": 175, "column": 10, "nodeType": "1627", "messageId": "1628", "endLine": 175, "endColumn": 28}, {"ruleId": "1625", "severity": 1, "message": "1977", "line": 175, "column": 30, "nodeType": "1627", "messageId": "1628", "endLine": 175, "endColumn": 51}, {"ruleId": "1634", "severity": 1, "message": "1978", "line": 206, "column": 6, "nodeType": "1636", "endLine": 206, "endColumn": 8, "suggestions": "1979"}, {"ruleId": "1634", "severity": 1, "message": "1980", "line": 232, "column": 6, "nodeType": "1636", "endLine": 232, "endColumn": 8, "suggestions": "1981"}, {"ruleId": "1634", "severity": 1, "message": "1982", "line": 262, "column": 6, "nodeType": "1636", "endLine": 262, "endColumn": 8, "suggestions": "1983"}, {"ruleId": "1625", "severity": 1, "message": "1902", "line": 275, "column": 40, "nodeType": "1627", "messageId": "1628", "endLine": 275, "endColumn": 54}, {"ruleId": "1625", "severity": 1, "message": "1984", "line": 278, "column": 43, "nodeType": "1627", "messageId": "1628", "endLine": 278, "endColumn": 55}, {"ruleId": "1625", "severity": 1, "message": "1904", "line": 281, "column": 42, "nodeType": "1627", "messageId": "1628", "endLine": 281, "endColumn": 57}, {"ruleId": "1625", "severity": 1, "message": "1906", "line": 284, "column": 46, "nodeType": "1627", "messageId": "1628", "endLine": 284, "endColumn": 63}, {"ruleId": "1642", "severity": 1, "message": "1643", "line": 427, "column": 23, "nodeType": "1644", "endLine": 434, "endColumn": 25}, {"ruleId": "1625", "severity": 1, "message": "1985", "line": 42, "column": 5, "nodeType": "1627", "messageId": "1628", "endLine": 42, "endColumn": 27}, {"ruleId": "1625", "severity": 1, "message": "1986", "line": 43, "column": 5, "nodeType": "1627", "messageId": "1628", "endLine": 43, "endColumn": 25}, {"ruleId": "1625", "severity": 1, "message": "1987", "line": 44, "column": 5, "nodeType": "1627", "messageId": "1628", "endLine": 44, "endColumn": 27}, {"ruleId": "1634", "severity": 1, "message": "1648", "line": 49, "column": 6, "nodeType": "1636", "endLine": 49, "endColumn": 8, "suggestions": "1988"}, {"ruleId": "1625", "severity": 1, "message": "1989", "line": 3, "column": 10, "nodeType": "1627", "messageId": "1628", "endLine": 3, "endColumn": 21}, {"ruleId": "1625", "severity": 1, "message": "1990", "line": 6, "column": 10, "nodeType": "1627", "messageId": "1628", "endLine": 6, "endColumn": 24}, {"ruleId": "1625", "severity": 1, "message": "1721", "line": 7, "column": 10, "nodeType": "1627", "messageId": "1628", "endLine": 7, "endColumn": 21}, {"ruleId": "1625", "severity": 1, "message": "1652", "line": 10, "column": 21, "nodeType": "1627", "messageId": "1628", "endLine": 10, "endColumn": 26}, {"ruleId": "1625", "severity": 1, "message": "1651", "line": 10, "column": 28, "nodeType": "1627", "messageId": "1628", "endLine": 10, "endColumn": 35}, {"ruleId": "1625", "severity": 1, "message": "1879", "line": 12, "column": 9, "nodeType": "1627", "messageId": "1628", "endLine": 12, "endColumn": 17}, {"ruleId": "1625", "severity": 1, "message": "1872", "line": 5, "column": 10, "nodeType": "1627", "messageId": "1628", "endLine": 5, "endColumn": 15}, {"ruleId": "1625", "severity": 1, "message": "1991", "line": 12, "column": 10, "nodeType": "1627", "messageId": "1628", "endLine": 12, "endColumn": 23}, {"ruleId": "1625", "severity": 1, "message": "1992", "line": 13, "column": 10, "nodeType": "1627", "messageId": "1628", "endLine": 13, "endColumn": 22}, {"ruleId": "1625", "severity": 1, "message": "1652", "line": 18, "column": 21, "nodeType": "1627", "messageId": "1628", "endLine": 18, "endColumn": 26}, {"ruleId": "1625", "severity": 1, "message": "1651", "line": 18, "column": 28, "nodeType": "1627", "messageId": "1628", "endLine": 18, "endColumn": 35}, {"ruleId": "1625", "severity": 1, "message": "1880", "line": 20, "column": 10, "nodeType": "1627", "messageId": "1628", "endLine": 20, "endColumn": 20}, {"ruleId": "1625", "severity": 1, "message": "1881", "line": 20, "column": 22, "nodeType": "1627", "messageId": "1628", "endLine": 20, "endColumn": 35}, {"ruleId": "1625", "severity": 1, "message": "1884", "line": 1, "column": 10, "nodeType": "1627", "messageId": "1628", "endLine": 1, "endColumn": 19}, {"ruleId": "1625", "severity": 1, "message": "1885", "line": 2, "column": 23, "nodeType": "1627", "messageId": "1628", "endLine": 2, "endColumn": 34}, {"ruleId": "1625", "severity": 1, "message": "1882", "line": 6, "column": 9, "nodeType": "1627", "messageId": "1628", "endLine": 6, "endColumn": 17}, {"ruleId": "1625", "severity": 1, "message": "1883", "line": 7, "column": 9, "nodeType": "1627", "messageId": "1628", "endLine": 7, "endColumn": 17}, {"ruleId": "1625", "severity": 1, "message": "1886", "line": 26, "column": 9, "nodeType": "1627", "messageId": "1628", "endLine": 26, "endColumn": 19}, {"ruleId": "1625", "severity": 1, "message": "1883", "line": 9, "column": 9, "nodeType": "1627", "messageId": "1628", "endLine": 9, "endColumn": 17}, {"ruleId": "1625", "severity": 1, "message": "1652", "line": 12, "column": 21, "nodeType": "1627", "messageId": "1628", "endLine": 12, "endColumn": 26}, {"ruleId": "1625", "severity": 1, "message": "1651", "line": 12, "column": 28, "nodeType": "1627", "messageId": "1628", "endLine": 12, "endColumn": 35}, {"ruleId": "1625", "severity": 1, "message": "1698", "line": 16, "column": 5, "nodeType": "1627", "messageId": "1628", "endLine": 16, "endColumn": 23}, {"ruleId": "1625", "severity": 1, "message": "1700", "line": 18, "column": 5, "nodeType": "1627", "messageId": "1628", "endLine": 18, "endColumn": 23}, {"ruleId": "1625", "severity": 1, "message": "1699", "line": 19, "column": 5, "nodeType": "1627", "messageId": "1628", "endLine": 19, "endColumn": 21}, {"ruleId": "1625", "severity": 1, "message": "1879", "line": 22, "column": 9, "nodeType": "1627", "messageId": "1628", "endLine": 22, "endColumn": 17}, {"ruleId": "1634", "severity": 1, "message": "1648", "line": 27, "column": 6, "nodeType": "1636", "endLine": 27, "endColumn": 16, "suggestions": "1993"}, {"ruleId": "1625", "severity": 1, "message": "1630", "line": 5, "column": 3, "nodeType": "1627", "messageId": "1628", "endLine": 5, "endColumn": 14}, {"ruleId": "1625", "severity": 1, "message": "1994", "line": 14, "column": 8, "nodeType": "1627", "messageId": "1628", "endLine": 14, "endColumn": 18}, {"ruleId": "1625", "severity": 1, "message": "1631", "line": 16, "column": 10, "nodeType": "1627", "messageId": "1628", "endLine": 16, "endColumn": 17}, {"ruleId": "1625", "severity": 1, "message": "1721", "line": 19, "column": 10, "nodeType": "1627", "messageId": "1628", "endLine": 19, "endColumn": 21}, {"ruleId": "1625", "severity": 1, "message": "1632", "line": 39, "column": 11, "nodeType": "1627", "messageId": "1628", "endLine": 39, "endColumn": 30}, {"ruleId": "1625", "severity": 1, "message": "1633", "line": 39, "column": 32, "nodeType": "1627", "messageId": "1628", "endLine": 39, "endColumn": 49}, {"ruleId": "1634", "severity": 1, "message": "1648", "line": 56, "column": 6, "nodeType": "1636", "endLine": 56, "endColumn": 27, "suggestions": "1995"}, {"ruleId": "1625", "severity": 1, "message": "1667", "line": 3, "column": 8, "nodeType": "1627", "messageId": "1628", "endLine": 3, "endColumn": 23}, {"ruleId": "1625", "severity": 1, "message": "1645", "line": 14, "column": 9, "nodeType": "1627", "messageId": "1628", "endLine": 14, "endColumn": 17}, {"ruleId": "1625", "severity": 1, "message": "1737", "line": 33, "column": 10, "nodeType": "1627", "messageId": "1628", "endLine": 33, "endColumn": 16}, {"ruleId": "1625", "severity": 1, "message": "1738", "line": 33, "column": 18, "nodeType": "1627", "messageId": "1628", "endLine": 33, "endColumn": 27}, {"ruleId": "1625", "severity": 1, "message": "1745", "line": 34, "column": 10, "nodeType": "1627", "messageId": "1628", "endLine": 34, "endColumn": 19}, {"ruleId": "1625", "severity": 1, "message": "1651", "line": 37, "column": 21, "nodeType": "1627", "messageId": "1628", "endLine": 37, "endColumn": 28}, {"ruleId": "1625", "severity": 1, "message": "1652", "line": 37, "column": 30, "nodeType": "1627", "messageId": "1628", "endLine": 37, "endColumn": 35}, {"ruleId": "1625", "severity": 1, "message": "1746", "line": 40, "column": 11, "nodeType": "1627", "messageId": "1628", "endLine": 40, "endColumn": 27}, {"ruleId": "1625", "severity": 1, "message": "1747", "line": 40, "column": 29, "nodeType": "1627", "messageId": "1628", "endLine": 40, "endColumn": 43}, {"ruleId": "1625", "severity": 1, "message": "1667", "line": 6, "column": 8, "nodeType": "1627", "messageId": "1628", "endLine": 6, "endColumn": 23}, {"ruleId": "1625", "severity": 1, "message": "1721", "line": 7, "column": 21, "nodeType": "1627", "messageId": "1628", "endLine": 7, "endColumn": 32}, {"ruleId": "1625", "severity": 1, "message": "1645", "line": 17, "column": 9, "nodeType": "1627", "messageId": "1628", "endLine": 17, "endColumn": 17}, {"ruleId": "1625", "severity": 1, "message": "1737", "line": 39, "column": 10, "nodeType": "1627", "messageId": "1628", "endLine": 39, "endColumn": 16}, {"ruleId": "1625", "severity": 1, "message": "1738", "line": 39, "column": 18, "nodeType": "1627", "messageId": "1628", "endLine": 39, "endColumn": 27}, {"ruleId": "1625", "severity": 1, "message": "1677", "line": 45, "column": 27, "nodeType": "1627", "messageId": "1628", "endLine": 45, "endColumn": 34}, {"ruleId": "1625", "severity": 1, "message": "1739", "line": 48, "column": 11, "nodeType": "1627", "messageId": "1628", "endLine": 48, "endColumn": 30}, {"ruleId": "1625", "severity": 1, "message": "1740", "line": 48, "column": 32, "nodeType": "1627", "messageId": "1628", "endLine": 48, "endColumn": 49}, {"ruleId": "1634", "severity": 1, "message": "1741", "line": 71, "column": 6, "nodeType": "1636", "endLine": 71, "endColumn": 14, "suggestions": "1996"}, {"ruleId": "1634", "severity": 1, "message": "1658", "line": 77, "column": 6, "nodeType": "1636", "endLine": 77, "endColumn": 27, "suggestions": "1997"}, {"ruleId": "1634", "severity": 1, "message": "1874", "line": 29, "column": 6, "nodeType": "1636", "endLine": 29, "endColumn": 11, "suggestions": "1998"}, {"ruleId": "1876", "severity": 1, "message": "1877", "line": 124, "column": 25, "nodeType": "1878", "endLine": 124, "endColumn": 38}, {"ruleId": "1625", "severity": 1, "message": "1999", "line": 14, "column": 3, "nodeType": "1627", "messageId": "1628", "endLine": 14, "endColumn": 13}, {"ruleId": "1625", "severity": 1, "message": "2000", "line": 18, "column": 8, "nodeType": "1627", "messageId": "1628", "endLine": 18, "endColumn": 22}, {"ruleId": "1625", "severity": 1, "message": "1645", "line": 74, "column": 9, "nodeType": "1627", "messageId": "1628", "endLine": 74, "endColumn": 17}, {"ruleId": "1625", "severity": 1, "message": "1936", "line": 85, "column": 10, "nodeType": "1627", "messageId": "1628", "endLine": 85, "endColumn": 23}, {"ruleId": "1625", "severity": 1, "message": "1938", "line": 91, "column": 10, "nodeType": "1627", "messageId": "1628", "endLine": 91, "endColumn": 24}, {"ruleId": "1625", "severity": 1, "message": "1940", "line": 97, "column": 10, "nodeType": "1627", "messageId": "1628", "endLine": 97, "endColumn": 22}, {"ruleId": "1625", "severity": 1, "message": "2001", "line": 111, "column": 37, "nodeType": "1627", "messageId": "1628", "endLine": 111, "endColumn": 65}, {"ruleId": "1625", "severity": 1, "message": "2002", "line": 117, "column": 10, "nodeType": "1627", "messageId": "1628", "endLine": 117, "endColumn": 33}, {"ruleId": "1625", "severity": 1, "message": "1941", "line": 137, "column": 10, "nodeType": "1627", "messageId": "1628", "endLine": 137, "endColumn": 30}, {"ruleId": "1625", "severity": 1, "message": "1942", "line": 137, "column": 32, "nodeType": "1627", "messageId": "1628", "endLine": 137, "endColumn": 55}, {"ruleId": "1625", "severity": 1, "message": "1944", "line": 148, "column": 10, "nodeType": "1627", "messageId": "1628", "endLine": 148, "endColumn": 30}, {"ruleId": "1625", "severity": 1, "message": "1954", "line": 171, "column": 10, "nodeType": "1627", "messageId": "1628", "endLine": 171, "endColumn": 23}, {"ruleId": "1625", "severity": 1, "message": "1955", "line": 171, "column": 25, "nodeType": "1627", "messageId": "1628", "endLine": 171, "endColumn": 41}, {"ruleId": "1625", "severity": 1, "message": "1956", "line": 172, "column": 10, "nodeType": "1627", "messageId": "1628", "endLine": 172, "endColumn": 28}, {"ruleId": "1625", "severity": 1, "message": "1957", "line": 172, "column": 30, "nodeType": "1627", "messageId": "1628", "endLine": 172, "endColumn": 51}, {"ruleId": "1625", "severity": 1, "message": "1958", "line": 174, "column": 10, "nodeType": "1627", "messageId": "1628", "endLine": 174, "endColumn": 23}, {"ruleId": "1625", "severity": 1, "message": "1959", "line": 174, "column": 25, "nodeType": "1627", "messageId": "1628", "endLine": 174, "endColumn": 41}, {"ruleId": "1625", "severity": 1, "message": "1960", "line": 175, "column": 10, "nodeType": "1627", "messageId": "1628", "endLine": 175, "endColumn": 28}, {"ruleId": "1625", "severity": 1, "message": "1961", "line": 175, "column": 30, "nodeType": "1627", "messageId": "1628", "endLine": 175, "endColumn": 51}, {"ruleId": "1625", "severity": 1, "message": "1962", "line": 177, "column": 10, "nodeType": "1627", "messageId": "1628", "endLine": 177, "endColumn": 25}, {"ruleId": "1625", "severity": 1, "message": "1963", "line": 177, "column": 27, "nodeType": "1627", "messageId": "1628", "endLine": 177, "endColumn": 45}, {"ruleId": "1625", "severity": 1, "message": "1964", "line": 178, "column": 10, "nodeType": "1627", "messageId": "1628", "endLine": 178, "endColumn": 30}, {"ruleId": "1625", "severity": 1, "message": "1965", "line": 178, "column": 32, "nodeType": "1627", "messageId": "1628", "endLine": 178, "endColumn": 55}, {"ruleId": "1625", "severity": 1, "message": "1966", "line": 181, "column": 10, "nodeType": "1627", "messageId": "1628", "endLine": 181, "endColumn": 28}, {"ruleId": "1625", "severity": 1, "message": "1967", "line": 181, "column": 30, "nodeType": "1627", "messageId": "1628", "endLine": 181, "endColumn": 51}, {"ruleId": "1625", "severity": 1, "message": "1968", "line": 184, "column": 10, "nodeType": "1627", "messageId": "1628", "endLine": 184, "endColumn": 25}, {"ruleId": "1625", "severity": 1, "message": "1969", "line": 184, "column": 27, "nodeType": "1627", "messageId": "1628", "endLine": 184, "endColumn": 45}, {"ruleId": "1625", "severity": 1, "message": "1970", "line": 187, "column": 10, "nodeType": "1627", "messageId": "1628", "endLine": 187, "endColumn": 21}, {"ruleId": "1625", "severity": 1, "message": "1971", "line": 187, "column": 23, "nodeType": "1627", "messageId": "1628", "endLine": 187, "endColumn": 37}, {"ruleId": "1625", "severity": 1, "message": "1972", "line": 190, "column": 33, "nodeType": "1627", "messageId": "1628", "endLine": 190, "endColumn": 57}, {"ruleId": "1625", "severity": 1, "message": "1973", "line": 193, "column": 32, "nodeType": "1627", "messageId": "1628", "endLine": 193, "endColumn": 55}, {"ruleId": "1625", "severity": 1, "message": "1974", "line": 196, "column": 10, "nodeType": "1627", "messageId": "1628", "endLine": 196, "endColumn": 27}, {"ruleId": "1625", "severity": 1, "message": "1975", "line": 196, "column": 29, "nodeType": "1627", "messageId": "1628", "endLine": 196, "endColumn": 49}, {"ruleId": "1625", "severity": 1, "message": "1976", "line": 199, "column": 10, "nodeType": "1627", "messageId": "1628", "endLine": 199, "endColumn": 28}, {"ruleId": "1625", "severity": 1, "message": "1977", "line": 199, "column": 30, "nodeType": "1627", "messageId": "1628", "endLine": 199, "endColumn": 51}, {"ruleId": "1634", "severity": 1, "message": "1978", "line": 241, "column": 6, "nodeType": "1636", "endLine": 241, "endColumn": 8, "suggestions": "2003"}, {"ruleId": "1634", "severity": 1, "message": "1980", "line": 267, "column": 6, "nodeType": "1636", "endLine": 267, "endColumn": 8, "suggestions": "2004"}, {"ruleId": "1634", "severity": 1, "message": "1982", "line": 297, "column": 6, "nodeType": "1636", "endLine": 297, "endColumn": 8, "suggestions": "2005"}, {"ruleId": "1625", "severity": 1, "message": "1902", "line": 310, "column": 40, "nodeType": "1627", "messageId": "1628", "endLine": 310, "endColumn": 54}, {"ruleId": "1625", "severity": 1, "message": "1903", "line": 320, "column": 23, "nodeType": "1627", "messageId": "1628", "endLine": 320, "endColumn": 40}, {"ruleId": "1625", "severity": 1, "message": "1904", "line": 320, "column": 42, "nodeType": "1627", "messageId": "1628", "endLine": 320, "endColumn": 57}, {"ruleId": "1625", "severity": 1, "message": "2006", "line": 323, "column": 28, "nodeType": "1627", "messageId": "1628", "endLine": 323, "endColumn": 41}, {"ruleId": "1625", "severity": 1, "message": "1924", "line": 323, "column": 43, "nodeType": "1627", "messageId": "1628", "endLine": 323, "endColumn": 58}, {"ruleId": "1625", "severity": 1, "message": "1905", "line": 327, "column": 25, "nodeType": "1627", "messageId": "1628", "endLine": 327, "endColumn": 44}, {"ruleId": "1625", "severity": 1, "message": "1906", "line": 327, "column": 46, "nodeType": "1627", "messageId": "1628", "endLine": 327, "endColumn": 63}, {"ruleId": "1625", "severity": 1, "message": "2007", "line": 374, "column": 30, "nodeType": "1627", "messageId": "1628", "endLine": 374, "endColumn": 45}, {"ruleId": "1634", "severity": 1, "message": "2008", "line": 593, "column": 6, "nodeType": "1636", "endLine": 593, "endColumn": 16, "suggestions": "2009"}, {"ruleId": "1642", "severity": 1, "message": "1643", "line": 673, "column": 23, "nodeType": "1644", "endLine": 680, "endColumn": 25}, {"ruleId": "1625", "severity": 1, "message": "1645", "line": 35, "column": 9, "nodeType": "1627", "messageId": "1628", "endLine": 35, "endColumn": 17}, {"ruleId": "1625", "severity": 1, "message": "2010", "line": 75, "column": 11, "nodeType": "1627", "messageId": "1628", "endLine": 75, "endColumn": 32}, {"ruleId": "1625", "severity": 1, "message": "2011", "line": 75, "column": 34, "nodeType": "1627", "messageId": "1628", "endLine": 75, "endColumn": 53}, {"ruleId": "1634", "severity": 1, "message": "2012", "line": 95, "column": 6, "nodeType": "1636", "endLine": 104, "endColumn": 4, "suggestions": "2013"}, {"ruleId": "1634", "severity": 1, "message": "2014", "line": 123, "column": 6, "nodeType": "1636", "endLine": 126, "endColumn": 4, "suggestions": "2015"}, {"ruleId": "1625", "severity": 1, "message": "2016", "line": 143, "column": 9, "nodeType": "1627", "messageId": "1628", "endLine": 143, "endColumn": 24}, {"ruleId": "1625", "severity": 1, "message": "1645", "line": 22, "column": 9, "nodeType": "1627", "messageId": "1628", "endLine": 22, "endColumn": 17}, {"ruleId": "1625", "severity": 1, "message": "1794", "line": 40, "column": 11, "nodeType": "1627", "messageId": "1628", "endLine": 40, "endColumn": 28}, {"ruleId": "1625", "severity": 1, "message": "2017", "line": 40, "column": 49, "nodeType": "1627", "messageId": "1628", "endLine": 40, "endColumn": 65}, {"ruleId": "1634", "severity": 1, "message": "1648", "line": 63, "column": 6, "nodeType": "1636", "endLine": 63, "endColumn": 25, "suggestions": "2018"}, {"ruleId": "1625", "severity": 1, "message": "1645", "line": 16, "column": 9, "nodeType": "1627", "messageId": "1628", "endLine": 16, "endColumn": 17}, {"ruleId": "1625", "severity": 1, "message": "2019", "line": 17, "column": 10, "nodeType": "1627", "messageId": "1628", "endLine": 17, "endColumn": 22}, {"ruleId": "1625", "severity": 1, "message": "2020", "line": 27, "column": 32, "nodeType": "1627", "messageId": "1628", "endLine": 27, "endColumn": 55}, {"ruleId": "1625", "severity": 1, "message": "1745", "line": 46, "column": 10, "nodeType": "1627", "messageId": "1628", "endLine": 46, "endColumn": 19}, {"ruleId": "1625", "severity": 1, "message": "1698", "line": 53, "column": 5, "nodeType": "1627", "messageId": "1628", "endLine": 53, "endColumn": 23}, {"ruleId": "1625", "severity": 1, "message": "1701", "line": 61, "column": 5, "nodeType": "1627", "messageId": "1628", "endLine": 61, "endColumn": 29}, {"ruleId": "1625", "severity": 1, "message": "2021", "line": 68, "column": 5, "nodeType": "1627", "messageId": "1628", "endLine": 68, "endColumn": 30}, {"ruleId": "1634", "severity": 1, "message": "2022", "line": 93, "column": 6, "nodeType": "1636", "endLine": 93, "endColumn": 26, "suggestions": "2023"}, {"ruleId": "1634", "severity": 1, "message": "1648", "line": 99, "column": 6, "nodeType": "1636", "endLine": 99, "endColumn": 32, "suggestions": "2024"}, {"ruleId": "1634", "severity": 1, "message": "1648", "line": 111, "column": 6, "nodeType": "1636", "endLine": 111, "endColumn": 33, "suggestions": "2025"}, {"ruleId": "1638", "severity": 1, "message": "1639", "line": 172, "column": 51, "nodeType": "1640", "messageId": "1641", "endLine": 172, "endColumn": 53}, {"ruleId": "1642", "severity": 1, "message": "1643", "line": 177, "column": 21, "nodeType": "1644", "endLine": 184, "endColumn": 23}, {"ruleId": "1642", "severity": 1, "message": "1643", "line": 208, "column": 19, "nodeType": "1644", "endLine": 215, "endColumn": 21}, {"ruleId": "1625", "severity": 1, "message": "1645", "line": 8, "column": 9, "nodeType": "1627", "messageId": "1628", "endLine": 8, "endColumn": 17}, {"ruleId": "1625", "severity": 1, "message": "2019", "line": 9, "column": 10, "nodeType": "1627", "messageId": "1628", "endLine": 9, "endColumn": 22}, {"ruleId": "1625", "severity": 1, "message": "1645", "line": 8, "column": 9, "nodeType": "1627", "messageId": "1628", "endLine": 8, "endColumn": 17}, {"ruleId": "1625", "severity": 1, "message": "2019", "line": 9, "column": 10, "nodeType": "1627", "messageId": "1628", "endLine": 9, "endColumn": 22}, {"ruleId": "1625", "severity": 1, "message": "2026", "line": 7, "column": 8, "nodeType": "1627", "messageId": "1628", "endLine": 7, "endColumn": 13}, {"ruleId": "1625", "severity": 1, "message": "1645", "line": 22, "column": 9, "nodeType": "1627", "messageId": "1628", "endLine": 22, "endColumn": 17}, {"ruleId": "1625", "severity": 1, "message": "1737", "line": 25, "column": 10, "nodeType": "1627", "messageId": "1628", "endLine": 25, "endColumn": 16}, {"ruleId": "1625", "severity": 1, "message": "1738", "line": 25, "column": 18, "nodeType": "1627", "messageId": "1628", "endLine": 25, "endColumn": 27}, {"ruleId": "1625", "severity": 1, "message": "1745", "line": 26, "column": 10, "nodeType": "1627", "messageId": "1628", "endLine": 26, "endColumn": 19}, {"ruleId": "1625", "severity": 1, "message": "1936", "line": 32, "column": 10, "nodeType": "1627", "messageId": "1628", "endLine": 32, "endColumn": 23}, {"ruleId": "1625", "severity": 1, "message": "2027", "line": 35, "column": 10, "nodeType": "1627", "messageId": "1628", "endLine": 35, "endColumn": 20}, {"ruleId": "1625", "severity": 1, "message": "2028", "line": 38, "column": 10, "nodeType": "1627", "messageId": "1628", "endLine": 38, "endColumn": 26}, {"ruleId": "1625", "severity": 1, "message": "2029", "line": 47, "column": 10, "nodeType": "1627", "messageId": "1628", "endLine": 47, "endColumn": 20}, {"ruleId": "1625", "severity": 1, "message": "2030", "line": 50, "column": 10, "nodeType": "1627", "messageId": "1628", "endLine": 50, "endColumn": 26}, {"ruleId": "1625", "severity": 1, "message": "2031", "line": 58, "column": 10, "nodeType": "1627", "messageId": "1628", "endLine": 58, "endColumn": 16}, {"ruleId": "1625", "severity": 1, "message": "1651", "line": 85, "column": 21, "nodeType": "1627", "messageId": "1628", "endLine": 85, "endColumn": 28}, {"ruleId": "1625", "severity": 1, "message": "1652", "line": 85, "column": 30, "nodeType": "1627", "messageId": "1628", "endLine": 85, "endColumn": 35}, {"ruleId": "1625", "severity": 1, "message": "2032", "line": 88, "column": 31, "nodeType": "1627", "messageId": "1628", "endLine": 88, "endColumn": 47}, {"ruleId": "1625", "severity": 1, "message": "1645", "line": 20, "column": 9, "nodeType": "1627", "messageId": "1628", "endLine": 20, "endColumn": 17}, {"ruleId": "1625", "severity": 1, "message": "1817", "line": 35, "column": 59, "nodeType": "1627", "messageId": "1628", "endLine": 35, "endColumn": 64}, {"ruleId": "1625", "severity": 1, "message": "2033", "line": 40, "column": 5, "nodeType": "1627", "messageId": "1628", "endLine": 40, "endColumn": 27}, {"ruleId": "1625", "severity": 1, "message": "2034", "line": 41, "column": 5, "nodeType": "1627", "messageId": "1628", "endLine": 41, "endColumn": 25}, {"ruleId": "1634", "severity": 1, "message": "1648", "line": 65, "column": 6, "nodeType": "1636", "endLine": 65, "endColumn": 30, "suggestions": "2035"}, {"ruleId": "1625", "severity": 1, "message": "1645", "line": 12, "column": 9, "nodeType": "1627", "messageId": "1628", "endLine": 12, "endColumn": 17}, {"ruleId": "1625", "severity": 1, "message": "1745", "line": 15, "column": 10, "nodeType": "1627", "messageId": "1628", "endLine": 15, "endColumn": 19}, {"ruleId": "1625", "severity": 1, "message": "1651", "line": 46, "column": 21, "nodeType": "1627", "messageId": "1628", "endLine": 46, "endColumn": 28}, {"ruleId": "1625", "severity": 1, "message": "1652", "line": 46, "column": 30, "nodeType": "1627", "messageId": "1628", "endLine": 46, "endColumn": 35}, {"ruleId": "1625", "severity": 1, "message": "2036", "line": 49, "column": 32, "nodeType": "1627", "messageId": "1628", "endLine": 49, "endColumn": 49}, {"ruleId": "1625", "severity": 1, "message": "2037", "line": 5, "column": 3, "nodeType": "1627", "messageId": "1628", "endLine": 5, "endColumn": 21}, {"ruleId": "1625", "severity": 1, "message": "1645", "line": 16, "column": 9, "nodeType": "1627", "messageId": "1628", "endLine": 16, "endColumn": 17}, {"ruleId": "1625", "severity": 1, "message": "1737", "line": 20, "column": 10, "nodeType": "1627", "messageId": "1628", "endLine": 20, "endColumn": 16}, {"ruleId": "1625", "severity": 1, "message": "1738", "line": 20, "column": 18, "nodeType": "1627", "messageId": "1628", "endLine": 20, "endColumn": 27}, {"ruleId": "1625", "severity": 1, "message": "2038", "line": 56, "column": 5, "nodeType": "1627", "messageId": "1628", "endLine": 56, "endColumn": 25}, {"ruleId": "1625", "severity": 1, "message": "2039", "line": 57, "column": 5, "nodeType": "1627", "messageId": "1628", "endLine": 57, "endColumn": 23}, {"ruleId": "1625", "severity": 1, "message": "2040", "line": 58, "column": 5, "nodeType": "1627", "messageId": "1628", "endLine": 58, "endColumn": 25}, {"ruleId": "1625", "severity": 1, "message": "2041", "line": 64, "column": 5, "nodeType": "1627", "messageId": "1628", "endLine": 64, "endColumn": 27}, {"ruleId": "1625", "severity": 1, "message": "2042", "line": 65, "column": 5, "nodeType": "1627", "messageId": "1628", "endLine": 65, "endColumn": 25}, {"ruleId": "1634", "severity": 1, "message": "1658", "line": 139, "column": 6, "nodeType": "1636", "endLine": 139, "endColumn": 30, "suggestions": "2043"}, {"ruleId": "1625", "severity": 1, "message": "2044", "line": 5, "column": 3, "nodeType": "1627", "messageId": "1628", "endLine": 5, "endColumn": 20}, {"ruleId": "1625", "severity": 1, "message": "1645", "line": 25, "column": 9, "nodeType": "1627", "messageId": "1628", "endLine": 25, "endColumn": 17}, {"ruleId": "1625", "severity": 1, "message": "1737", "line": 29, "column": 10, "nodeType": "1627", "messageId": "1628", "endLine": 29, "endColumn": 16}, {"ruleId": "1625", "severity": 1, "message": "1738", "line": 29, "column": 18, "nodeType": "1627", "messageId": "1628", "endLine": 29, "endColumn": 27}, {"ruleId": "1625", "severity": 1, "message": "1936", "line": 36, "column": 10, "nodeType": "1627", "messageId": "1628", "endLine": 36, "endColumn": 23}, {"ruleId": "1625", "severity": 1, "message": "2027", "line": 45, "column": 10, "nodeType": "1627", "messageId": "1628", "endLine": 45, "endColumn": 20}, {"ruleId": "1625", "severity": 1, "message": "2028", "line": 48, "column": 10, "nodeType": "1627", "messageId": "1628", "endLine": 48, "endColumn": 26}, {"ruleId": "1625", "severity": 1, "message": "2029", "line": 51, "column": 10, "nodeType": "1627", "messageId": "1628", "endLine": 51, "endColumn": 20}, {"ruleId": "1625", "severity": 1, "message": "2030", "line": 54, "column": 10, "nodeType": "1627", "messageId": "1628", "endLine": 54, "endColumn": 26}, {"ruleId": "1625", "severity": 1, "message": "2031", "line": 62, "column": 10, "nodeType": "1627", "messageId": "1628", "endLine": 62, "endColumn": 16}, {"ruleId": "1625", "severity": 1, "message": "2045", "line": 95, "column": 5, "nodeType": "1627", "messageId": "1628", "endLine": 95, "endColumn": 24}, {"ruleId": "1625", "severity": 1, "message": "2046", "line": 96, "column": 5, "nodeType": "1627", "messageId": "1628", "endLine": 96, "endColumn": 22}, {"ruleId": "1625", "severity": 1, "message": "2047", "line": 97, "column": 5, "nodeType": "1627", "messageId": "1628", "endLine": 97, "endColumn": 24}, {"ruleId": "1625", "severity": 1, "message": "2048", "line": 102, "column": 11, "nodeType": "1627", "messageId": "1628", "endLine": 102, "endColumn": 32}, {"ruleId": "1625", "severity": 1, "message": "2049", "line": 102, "column": 34, "nodeType": "1627", "messageId": "1628", "endLine": 102, "endColumn": 53}, {"ruleId": "1634", "severity": 1, "message": "1658", "line": 219, "column": 6, "nodeType": "1636", "endLine": 219, "endColumn": 29, "suggestions": "2050"}, {"ruleId": "1625", "severity": 1, "message": "1645", "line": 10, "column": 9, "nodeType": "1627", "messageId": "1628", "endLine": 10, "endColumn": 17}, {"ruleId": "1625", "severity": 1, "message": "1745", "line": 13, "column": 10, "nodeType": "1627", "messageId": "1628", "endLine": 13, "endColumn": 19}, {"ruleId": "1625", "severity": 1, "message": "1651", "line": 41, "column": 21, "nodeType": "1627", "messageId": "1628", "endLine": 41, "endColumn": 28}, {"ruleId": "1625", "severity": 1, "message": "1652", "line": 41, "column": 30, "nodeType": "1627", "messageId": "1628", "endLine": 41, "endColumn": 35}, {"ruleId": "1625", "severity": 1, "message": "2051", "line": 44, "column": 34, "nodeType": "1627", "messageId": "1628", "endLine": 44, "endColumn": 53}, {"ruleId": "1625", "severity": 1, "message": "2052", "line": 1, "column": 28, "nodeType": "1627", "messageId": "1628", "endLine": 1, "endColumn": 36}, {"ruleId": "1625", "severity": 1, "message": "1872", "line": 10, "column": 10, "nodeType": "1627", "messageId": "1628", "endLine": 10, "endColumn": 15}, {"ruleId": "1625", "severity": 1, "message": "2053", "line": 14, "column": 3, "nodeType": "1627", "messageId": "1628", "endLine": 14, "endColumn": 21}, {"ruleId": "1625", "severity": 1, "message": "2054", "line": 15, "column": 3, "nodeType": "1627", "messageId": "1628", "endLine": 15, "endColumn": 20}, {"ruleId": "1625", "severity": 1, "message": "1645", "line": 26, "column": 9, "nodeType": "1627", "messageId": "1628", "endLine": 26, "endColumn": 17}, {"ruleId": "1625", "severity": 1, "message": "1700", "line": 39, "column": 5, "nodeType": "1627", "messageId": "1628", "endLine": 39, "endColumn": 23}, {"ruleId": "1625", "severity": 1, "message": "2055", "line": 3, "column": 3, "nodeType": "1627", "messageId": "1628", "endLine": 3, "endColumn": 23}, {"ruleId": "1625", "severity": 1, "message": "1645", "line": 14, "column": 9, "nodeType": "1627", "messageId": "1628", "endLine": 14, "endColumn": 17}, {"ruleId": "1625", "severity": 1, "message": "1737", "line": 18, "column": 10, "nodeType": "1627", "messageId": "1628", "endLine": 18, "endColumn": 16}, {"ruleId": "1625", "severity": 1, "message": "1738", "line": 18, "column": 18, "nodeType": "1627", "messageId": "1628", "endLine": 18, "endColumn": 27}, {"ruleId": "1625", "severity": 1, "message": "2056", "line": 51, "column": 5, "nodeType": "1627", "messageId": "1628", "endLine": 51, "endColumn": 27}, {"ruleId": "1625", "severity": 1, "message": "2057", "line": 52, "column": 5, "nodeType": "1627", "messageId": "1628", "endLine": 52, "endColumn": 25}, {"ruleId": "1625", "severity": 1, "message": "2058", "line": 53, "column": 5, "nodeType": "1627", "messageId": "1628", "endLine": 53, "endColumn": 27}, {"ruleId": "1625", "severity": 1, "message": "2059", "line": 59, "column": 5, "nodeType": "1627", "messageId": "1628", "endLine": 59, "endColumn": 29}, {"ruleId": "1625", "severity": 1, "message": "2060", "line": 60, "column": 5, "nodeType": "1627", "messageId": "1628", "endLine": 60, "endColumn": 27}, {"ruleId": "1634", "severity": 1, "message": "1658", "line": 112, "column": 6, "nodeType": "1636", "endLine": 112, "endColumn": 32, "suggestions": "2061"}, {"ruleId": "1625", "severity": 1, "message": "1645", "line": 24, "column": 9, "nodeType": "1627", "messageId": "1628", "endLine": 24, "endColumn": 17}, {"ruleId": "1625", "severity": 1, "message": "2062", "line": 29, "column": 9, "nodeType": "1627", "messageId": "1628", "endLine": 29, "endColumn": 20}, {"ruleId": "1625", "severity": 1, "message": "2058", "line": 38, "column": 5, "nodeType": "1627", "messageId": "1628", "endLine": 38, "endColumn": 27}, {"ruleId": "1625", "severity": 1, "message": "1645", "line": 20, "column": 9, "nodeType": "1627", "messageId": "1628", "endLine": 20, "endColumn": 17}, {"ruleId": "1625", "severity": 1, "message": "2040", "line": 33, "column": 5, "nodeType": "1627", "messageId": "1628", "endLine": 33, "endColumn": 25}, {"ruleId": "1625", "severity": 1, "message": "1645", "line": 20, "column": 9, "nodeType": "1627", "messageId": "1628", "endLine": 20, "endColumn": 17}, {"ruleId": "1625", "severity": 1, "message": "2047", "line": 33, "column": 5, "nodeType": "1627", "messageId": "1628", "endLine": 33, "endColumn": 24}, {"ruleId": "1634", "severity": 1, "message": "2063", "line": 40, "column": 6, "nodeType": "1636", "endLine": 40, "endColumn": 35, "suggestions": "2064"}, {"ruleId": "1642", "severity": 1, "message": "1643", "line": 57, "column": 9, "nodeType": "1644", "endLine": 57, "endColumn": 55}, {"ruleId": "1634", "severity": 1, "message": "2063", "line": 29, "column": 6, "nodeType": "1636", "endLine": 29, "endColumn": 28, "suggestions": "2065"}, {"ruleId": "1642", "severity": 1, "message": "1643", "line": 47, "column": 9, "nodeType": "1644", "endLine": 47, "endColumn": 55}, {"ruleId": "1625", "severity": 1, "message": "1884", "line": 1, "column": 17, "nodeType": "1627", "messageId": "1628", "endLine": 1, "endColumn": 26}, {"ruleId": "1625", "severity": 1, "message": "2052", "line": 1, "column": 28, "nodeType": "1627", "messageId": "1628", "endLine": 1, "endColumn": 36}, {"ruleId": "1642", "severity": 1, "message": "1643", "line": 23, "column": 9, "nodeType": "1644", "endLine": 23, "endColumn": 55}, {"ruleId": "1642", "severity": 1, "message": "1643", "line": 29, "column": 9, "nodeType": "1644", "endLine": 29, "endColumn": 72}, "no-unused-vars", "'useRef' is defined but never used.", "Identifier", "unusedVar", "'getListContrats' is defined but never used.", "'useLocation' is defined but never used.", "'Tooltip' is defined but never used.", "'loadingClientDelete' is assigned a value but never used.", "'errorClientDelete' is assigned a value but never used.", "react-hooks/exhaustive-deps", "React Hook useEffect has missing dependencies: 'cinNumber', 'code', 'dispatch', 'firstName', 'gsmPhone', 'lastName', 'orderBy', 'page', and 'permiNumber'. Either include them or remove the dependency array.", "ArrayExpression", ["2066"], "eqeqeq", "Expected '===' and instead saw '=='.", "BinaryExpression", "unexpected", "jsx-a11y/alt-text", "img elements must have an alt prop, either with meaningful text, or an empty string for decorative images.", "JSXOpeningElement", "'location' is assigned a value but never used.", "'loadingCarDelete' is assigned a value but never used.", "'errorCarDelete' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'dispatch'. Either include it or remove the dependency array.", ["2067"], "'addNewAgence' is defined but never used.", "'loading' is assigned a value but never used.", "'error' is assigned a value but never used.", "'loadingAgenceUpdate' is assigned a value but never used.", "'loadingAgenceDetail' is assigned a value but never used.", "'successAgenceDetail' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'id'. Either include it or remove the dependency array.", ["2068"], "React Hook useEffect has missing dependencies: 'dispatch' and 'id'. Either include them or remove the dependency array.", ["2069"], "'loadingAgenceAdd' is assigned a value but never used.", "'loadingAgenceDelete' is assigned a value but never used.", "'errorAgenceDelete' is assigned a value but never used.", ["2070"], "'loadingReservationDelete' is assigned a value but never used.", "'errorReservationDelete' is assigned a value but never used.", ["2071"], "'CountrySelector' is defined but never used.", "'COUNTRIES' is defined but never used.", "'addNewClient' is defined but never used.", "'getListAgences' is defined but never used.", "'getMarqueList' is defined but never used.", "'getModelList' is defined but never used.", "'addNewCar' is defined but never used.", "'addNewReservation' is defined but never used.", "'montantTotal' is assigned a value but never used.", "'agences' is assigned a value but never used.", "'success' is assigned a value but never used.", "'loadingReservationUpdate' is assigned a value but never used.", "'errorReservationUpdate' is assigned a value but never used.", ["2072"], "'loadingDashData' is assigned a value but never used.", "'errorDashData' is assigned a value but never used.", "'agenceSelectError' is assigned a value but never used.", "'loadingCarUpdate' is assigned a value but never used.", "'errorCarUpdate' is assigned a value but never used.", "'loadingMarque' is assigned a value but never used.", "'errorMarque' is assigned a value but never used.", "'loadingModel' is assigned a value but never used.", "'errorModel' is assigned a value but never used.", ["2073"], "'addNewUser' is defined but never used.", "'getMyProfileUser' is defined but never used.", "'role' is assigned a value but never used.", "'errorRole' is assigned a value but never used.", "'loadingUserAdd' is assigned a value but never used.", "'errorUserAdd' is assigned a value but never used.", "'successUserAdd' is assigned a value but never used.", "'loadingUserProfile' is assigned a value but never used.", "'errorUserProfile' is assigned a value but never used.", "'successUserProfile' is assigned a value but never used.", "'loadingUserProfileUpdate' is assigned a value but never used.", "'errorUserProfileUpdate' is assigned a value but never used.", ["2074"], "React Hook useEffect has missing dependencies: 'dispatch', 'email', and 'password'. Either include them or remove the dependency array.", ["2075"], "'loadingContratValidReturn' is assigned a value but never used.", "'errorContratValidReturn' is assigned a value but never used.", "'loadingContratDelete' is assigned a value but never used.", "'errorContratDelete' is assigned a value but never used.", ["2076"], ["2077"], "'loadingCarAdd' is assigned a value but never used.", "'errorCarAdd' is assigned a value but never used.", "'loadingReservationAdd' is assigned a value but never used.", "'errorReservationAdd' is assigned a value but never used.", "'Link' is defined but never used.", "'Paginate' is defined but never used.", "'addContratPayments' is defined but never used.", "'detailContrat' is defined but never used.", "'getListContratPayments' is defined but never used.", "'baseURLFile' is defined but never used.", "'successContratPaymentDetail' is assigned a value but never used.", "'loadingContratPaymentUpdate' is assigned a value but never used.", "'errorContratPaymentUpdate' is assigned a value but never used.", ["2078"], ["2079"], "'successContratPayment' is assigned a value but never used.", "'loadingContratPaymentDelete' is assigned a value but never used.", "'errorContratPaymentDelete' is assigned a value but never used.", "'loadingContratPaymentAdd' is assigned a value but never used.", "'errorContratPaymentAdd' is assigned a value but never used.", ["2080"], "'permiRectoImage' is assigned a value but never used.", "'permiVersoImage' is assigned a value but never used.", "'cinRectoImage' is assigned a value but never used.", "'cinVersoImage' is assigned a value but never used.", "'isOpen' is assigned a value but never used.", "'setIsOpen' is assigned a value but never used.", "'loadingClientUpdate' is assigned a value but never used.", "'errorClientUpdate' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'country'. Either include it or remove the dependency array.", ["2081"], ["2082"], "'useNavigate' is defined but never used.", "'loadEvent' is assigned a value but never used.", "'loadingClientAdd' is assigned a value but never used.", "'errorClientAdd' is assigned a value but never used.", "'loadingContratAddReturn' is assigned a value but never used.", "'errorContratAddReturn' is assigned a value but never used.", "'loadingContratAdd' is assigned a value but never used.", "'errorContratAdd' is assigned a value but never used.", "no-dupe-keys", "Duplicate key 'client'.", "ObjectExpression", "Duplicate key 'car'.", "Duplicate key 'price_day'.", "Duplicate key 'start_date'.", "Duplicate key 'end_date'.", "Duplicate key 'nbr_day'.", "Duplicate key 'delivery_place'.", "Duplicate key 'return_place'.", "Duplicate key 'price_total'.", "Duplicate key 'price_rest'.", "Duplicate key 'price_avance'.", "Duplicate key 'type_avance'.", "'addNewContrats' is defined but never used.", ["2083"], ["2084"], "'loadingEmployeDelete' is assigned a value but never used.", "'errorEmployeDelete' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'dispatch' and 'status'. Either include them or remove the dependency array.", ["2085"], "'setErrorCinRecto' is assigned a value but never used.", "'setErrorCinVerso' is assigned a value but never used.", "'loadingEmployeAdd' is assigned a value but never used.", "'errorEmployeAdd' is assigned a value but never used.", "jsx-a11y/img-redundant-alt", "Redundant alt attribute. Screen-readers already announce `img` tags as an image. You don’t need to use the words `image`, `photo,` or `picture` (or any specified custom words) in the alt prop.", "'loadingMarqueAdd' is assigned a value but never used.", "'errorMarqueAdd' is assigned a value but never used.", "'loadingMarqueDelete' is assigned a value but never used.", "'errorMarqueDelete' is assigned a value but never used.", "'loadingModelDelete' is assigned a value but never used.", "'errorModelDelete' is assigned a value but never used.", "'loadingModelAdd' is assigned a value but never used.", "'errorModelAdd' is assigned a value but never used.", "'addNewEmploye' is defined but never used.", "'loadingEmployeUpdate' is assigned a value but never used.", "'errorEmployeUpdate' is assigned a value but never used.", ["2086"], "'getEmployesList' is defined but never used.", "'status' is assigned a value but never used.", "'setStatus' is assigned a value but never used.", "'loadingUserDelete' is assigned a value but never used.", "'errorUserDelete' is assigned a value but never used.", ["2087"], "'ReactTags' is defined but never used.", "'delimiters' is assigned a value but never used.", "'successCharge' is assigned a value but never used.", "'loadingChargeAdd' is assigned a value but never used.", "'errorChargeAdd' is assigned a value but never used.", "'loadingChargeDelete' is assigned a value but never used.", "'errorChargeDelete' is assigned a value but never used.", "'loadingChargeUpdate' is assigned a value but never used.", "'errorChargeUpdate' is assigned a value but never used.", "'successEntretien' is assigned a value but never used.", "'loadingEntretienDelete' is assigned a value but never used.", "'errorEntretienDelete' is assigned a value but never used.", "'loadingEntretienAdd' is assigned a value but never used.", "'errorEntretienAdd' is assigned a value but never used.", "'loadingEntretienUpdate' is assigned a value but never used.", "'errorEntretienUpdate' is assigned a value but never used.", ["2088"], "'suggestions' is assigned a value but never used.", "'loadingEmploye' is assigned a value but never used.", "'errorEmploye' is assigned a value but never used.", "'pages' is assigned a value but never used.", "'loadingDepenseEmployeAdd' is assigned a value but never used.", "'errorDepenseEmployeAdd' is assigned a value but never used.", ["2089"], "'ItemsCharge' is assigned a value but never used.", "'loadingEntretien' is assigned a value but never used.", "'errorEntretien' is assigned a value but never used.", "'addDepenseEntretien' is assigned a value but never used.", "'loadingDepenseEntretienDetail' is assigned a value but never used.", "'errorDepenseEntretienDetail' is assigned a value but never used.", "'successDepenseEntretienDetail' is assigned a value but never used.", "'loadingDepenseEntretienUpdate' is assigned a value but never used.", "'errorDepenseEntretienUpdate' is assigned a value but never used.", ["2090"], ["2091"], "'loadingDepenseEntretienAdd' is assigned a value but never used.", "'errorDepenseEntretienAdd' is assigned a value but never used.", ["2092"], "'loadingDepenseEntretienDelete' is assigned a value but never used.", "'errorDepenseEntretienDelete' is assigned a value but never used.", "'successDepenseEntretien' is assigned a value but never used.", ["2093"], ["2094"], "'addNewDepenseEmploye' is defined but never used.", "'getListCharges' is defined but never used.", "'loadingDepenseEmployeDetail' is assigned a value but never used.", "'errorDepenseEmployeDetail' is assigned a value but never used.", "'successDepenseEmployeDetail' is assigned a value but never used.", "'loadingDepenseEmployeUpdate' is assigned a value but never used.", "'errorDepenseEmployeUpdate' is assigned a value but never used.", ["2095"], ["2096"], "'successDepenseEmploye' is assigned a value but never used.", "'loadingDepenseEmployeDelete' is assigned a value but never used.", "'errorDepenseEmployeDelete' is assigned a value but never used.", ["2097"], ["2098"], "'successDepenseCharge' is assigned a value but never used.", "'loadingDepenseChargeDelete' is assigned a value but never used.", "'errorDepenseChargeDelete' is assigned a value but never used.", ["2099"], ["2100"], "'loadingCharge' is assigned a value but never used.", "'errorCharge' is assigned a value but never used.", "'loadingDepenseChargeDetail' is assigned a value but never used.", "'errorDepenseChargeDetail' is assigned a value but never used.", "'successDepenseChargeDetail' is assigned a value but never used.", "'loadingDepenseChargeUpdate' is assigned a value but never used.", "'errorDepenseChargeUpdate' is assigned a value but never used.", ["2101"], ["2102"], "'loadingDepenseChargeAdd' is assigned a value but never used.", "'errorDepenseChargeAdd' is assigned a value but never used.", ["2103"], "'items' is assigned a value but never used.", "'toast' is defined but never used.", "'alertClass' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'onToggle' and 'open'. Either include them or remove the dependency array. If 'onToggle' changes too often, find the parent component that defines it and wrap that definition in useCallback.", ["2104"], "jsx-a11y/role-has-required-aria-props", "Elements with the ARIA role \"option\" must have the following attributes defined: aria-selected", "JSXAttribute", "'redirect' is assigned a value but never used.", "'codeSearch' is assigned a value but never used.", "'setCodeSearch' is assigned a value but never used.", "'dispatch' is assigned a value but never used.", "'navigate' is assigned a value but never used.", "'useEffect' is defined but never used.", "'useSelector' is defined but never used.", "'dateFormat' is assigned a value but never used.", "'clientList' is defined but never used.", "'ConfirmationModal' is defined but never used.", ["2105"], "'ErrorBoundaryScreen' is defined but never used.", "'composeWithDevTools' is defined but never used.", "jsx-a11y/anchor-is-valid", "The href attribute requires a valid value to be accessible. Provide a valid, navigable address as the href value. If you cannot provide a valid href, but still need the element to resemble a link, use a button and change it with appropriate styles. Learn more: https://github.com/jsx-eslint/eslint-plugin-jsx-a11y/blob/HEAD/docs/rules/anchor-is-valid.md", "'userInfo' is assigned a value but never used.", "'loadingUserLogout' is assigned a value but never used.", "'errorUserLogout' is assigned a value but never used.", "'loadingCasesMap' is assigned a value but never used.", "'errorCasesMap' is assigned a value but never used.", "'loadingCaseDelete' is assigned a value but never used.", "'errorCaseDelete' is assigned a value but never used.", "'loadingProviders' is assigned a value but never used.", "'errorProviders' is assigned a value but never used.", "'loadingInsurances' is assigned a value but never used.", "'errorInsurances' is assigned a value but never used.", "'loadingCoordinators' is assigned a value but never used.", "'errorCoordinators' is assigned a value but never used.", "'browser' is assigned a value but never used.", "'device' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'ciaIdFilter', 'coordinationFilter', 'idFilter', 'insuranceFilter', 'patientFilter', 'providerFilter', 'statusFilter', and 'typeFilter'. Either include them or remove the dependency array.", ["2106"], "React Hook useEffect has missing dependencies: 'ciaIdFilter', 'coordinationFilter', 'dispatch', 'idFilter', 'insuranceFilter', 'patientFilter', 'providerFilter', 'statusFilter', and 'typeFilter'. Either include them or remove the dependency array.", ["2107"], "React Hook useEffect has a missing dependency: 'handleError'. Either include it or remove the dependency array.", ["2108"], "React Hook useEffect has missing dependencies: 'dispatch' and 'filterSelect'. Either include them or remove the dependency array.", ["2109"], "'duplicateCase' is defined but never used.", "'CASE_DUPLICATE_REQUEST' is defined but never used.", "'thumbsContainer' is assigned a value but never used.", "'historyPageParam' is assigned a value but never used.", "'isDuplicate' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'filesComments'. Either include it or remove the dependency array.", ["2110"], "'successCaseInfo' is assigned a value but never used.", "'errorCommentCaseDelete' is assigned a value but never used.", "'errorCommentCaseAdd' is assigned a value but never used.", "'loadingCaseAssignedUpdate' is assigned a value but never used.", "'errorCaseAssignedUpdate' is assigned a value but never used.", "'loadingCaseDuplicate' is assigned a value but never used.", "'errorCaseDuplicate' is assigned a value but never used.", ["2111"], ["2112"], "React Hook useEffect has missing dependencies: 'dispatch' and 'navigate'. Either include them or remove the dependency array.", ["2113"], ["2114"], "'lastNameError' is assigned a value but never used.", "'setBirthDate' is assigned a value but never used.", "'birthDateError' is assigned a value but never used.", "'setAddress' is assigned a value but never used.", "'addressError' is assigned a value but never used.", "'caseDescriptionError' is assigned a value but never used.", "'setCaseDescriptionError' is assigned a value but never used.", "'setCoordinatStatus' is assigned a value but never used.", "'coordinatStatusError' is assigned a value but never used.", "'setCoordinatStatusError' is assigned a value but never used.", "'appointmentDateError' is assigned a value but never used.", "'setAppointmentDateError' is assigned a value but never used.", "'startDateError' is assigned a value but never used.", "'setStartDateError' is assigned a value but never used.", "'endDateError' is assigned a value but never used.", "'setEndDateError' is assigned a value but never used.", "'serviceLocationError' is assigned a value but never used.", "'setServiceLocationError' is assigned a value but never used.", "'providerPhone' is assigned a value but never used.", "'setProviderPhone' is assigned a value but never used.", "'providerPhoneError' is assigned a value but never used.", "'setProviderPhoneError' is assigned a value but never used.", "'providerEmail' is assigned a value but never used.", "'setProviderEmail' is assigned a value but never used.", "'providerEmailError' is assigned a value but never used.", "'setProviderEmailError' is assigned a value but never used.", "'providerAddress' is assigned a value but never used.", "'setProviderAddress' is assigned a value but never used.", "'providerAddressError' is assigned a value but never used.", "'setProviderAddressError' is assigned a value but never used.", "'invoiceNumberError' is assigned a value but never used.", "'setInvoiceNumberError' is assigned a value but never used.", "'dateIssuedError' is assigned a value but never used.", "'setDateIssuedError' is assigned a value but never used.", "'amountError' is assigned a value but never used.", "'setAmountError' is assigned a value but never used.", "'setInsuranceCompanyError' is assigned a value but never used.", "'setInsuranceNumberError' is assigned a value but never used.", "'policyNumberError' is assigned a value but never used.", "'setPolicyNumberError' is assigned a value but never used.", "'initialStatusError' is assigned a value but never used.", "'setInitialStatusError' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'filesInitialMedicalReports'. Either include it or remove the dependency array.", ["2115"], "React Hook useEffect has a missing dependency: 'filesUploadInvoice'. Either include it or remove the dependency array.", ["2116"], "React Hook useEffect has a missing dependency: 'filesUploadAuthorizationDocuments'. Either include it or remove the dependency array.", ["2117"], "'errorCaseAdd' is assigned a value but never used.", "'loadingUpdateLastLogin' is assigned a value but never used.", "'errorUpdateLastLogin' is assigned a value but never used.", "'successUpdateLastLogin' is assigned a value but never used.", ["2118"], "'useDispatch' is defined but never used.", "'getUserProfile' is defined but never used.", "'openParametrs' is assigned a value but never used.", "'openDepenses' is assigned a value but never used.", ["2119"], "'InputModel' is defined but never used.", ["2120"], ["2121"], ["2122"], ["2123"], "'addNewCase' is defined but never used.", "'LoadingSpinner' is defined but never used.", "'setProviderMultiSelectDelete' is assigned a value but never used.", "'providerMultiSelectLast' is assigned a value but never used.", ["2124"], ["2125"], ["2126"], "'errorCaseInfo' is assigned a value but never used.", "'errorCaseUpdate' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'insurances' and 'providers'. Either include them or remove the dependency array.", ["2127"], "'loadingProviderDelete' is assigned a value but never used.", "'errorProviderDelete' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'isMaps', 'range.max', 'searchCity', 'searchCountry', 'searchName', and 'searchType'. Either include them or remove the dependency array.", ["2128"], "React Hook useEffect has missing dependencies: 'dispatch', 'isMaps', 'range.max', 'searchCity', 'searchCountry', 'searchName', and 'searchType'. Either include them or remove the dependency array.", ["2129"], "'handleMinChange' is assigned a value but never used.", "'errorUsersDelete' is assigned a value but never used.", ["2130"], "'searchParams' is assigned a value but never used.", "'setCoordinatorLogoError' is assigned a value but never used.", "'loadingUserPasswordUpdate' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'userProfile'. Either include it or remove the dependency array.", ["2131"], ["2132"], ["2133"], "'axios' is defined but never used.", "'emailError' is assigned a value but never used.", "'emailSecondError' is assigned a value but never used.", "'phoneError' is assigned a value but never used.", "'phoneSecondError' is assigned a value but never used.", "'cityVl' is assigned a value but never used.", "'errorProviderAdd' is assigned a value but never used.", "'loadingInsuranceDelete' is assigned a value but never used.", "'errorInsuranceDelete' is assigned a value but never used.", ["2134"], "'errorInsuranceAdd' is assigned a value but never used.", "'createNewInsurance' is defined but never used.", "'loadingInsuranceInfo' is assigned a value but never used.", "'errorInsuranceInfo' is assigned a value but never used.", "'successInsuranceInfo' is assigned a value but never used.", "'loadingInsuranceUpdate' is assigned a value but never used.", "'errorInsuranceUpdate' is assigned a value but never used.", ["2135"], "'createNewProvider' is defined but never used.", "'loadingProviderInfo' is assigned a value but never used.", "'errorProviderInfo' is assigned a value but never used.", "'successProviderInfo' is assigned a value but never used.", "'loadingProviderUpdate' is assigned a value but never used.", "'errorProviderUpdate' is assigned a value but never used.", ["2136"], "'errorCoordinatorAdd' is assigned a value but never used.", "'useState' is defined but never used.", "'updateUserPassword' is defined but never used.", "'updateUserProfile' is defined but never used.", "'createNewCoordinator' is defined but never used.", "'loadingCoordinatorInfo' is assigned a value but never used.", "'errorCoordinatorInfo' is assigned a value but never used.", "'successCoordinatorInfo' is assigned a value but never used.", "'loadingCoordinatorUpdate' is assigned a value but never used.", "'errorCoordinatorUpdate' is assigned a value but never used.", ["2137"], "'pageHistory' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'navigate'. Either include it or remove the dependency array.", ["2138"], ["2139"], {"desc": "2140", "fix": "2141"}, {"desc": "2142", "fix": "2143"}, {"desc": "2144", "fix": "2145"}, {"desc": "2146", "fix": "2147"}, {"desc": "2148", "fix": "2149"}, {"desc": "2150", "fix": "2151"}, {"desc": "2152", "fix": "2153"}, {"desc": "2154", "fix": "2155"}, {"desc": "2156", "fix": "2157"}, {"desc": "2158", "fix": "2159"}, {"desc": "2160", "fix": "2161"}, {"desc": "2162", "fix": "2163"}, {"desc": "2144", "fix": "2164"}, {"desc": "2165", "fix": "2166"}, {"desc": "2144", "fix": "2167"}, {"desc": "2168", "fix": "2169"}, {"desc": "2170", "fix": "2171"}, {"desc": "2144", "fix": "2172"}, {"desc": "2152", "fix": "2173"}, {"desc": "2174", "fix": "2175"}, {"desc": "2176", "fix": "2177"}, {"desc": "2178", "fix": "2179"}, {"desc": "2180", "fix": "2181"}, {"desc": "2156", "fix": "2182"}, {"desc": "2183", "fix": "2184"}, {"desc": "2185", "fix": "2186"}, {"desc": "2156", "fix": "2187"}, {"desc": "2188", "fix": "2189"}, {"desc": "2190", "fix": "2191"}, {"desc": "2183", "fix": "2192"}, {"desc": "2193", "fix": "2194"}, {"desc": "2188", "fix": "2195"}, {"desc": "2196", "fix": "2197"}, {"desc": "2188", "fix": "2198"}, {"desc": "2199", "fix": "2200"}, {"desc": "2183", "fix": "2201"}, {"desc": "2202", "fix": "2203"}, {"desc": "2156", "fix": "2204"}, {"desc": "2205", "fix": "2206"}, {"desc": "2156", "fix": "2207"}, {"desc": "2208", "fix": "2209"}, {"desc": "2210", "fix": "2211"}, {"desc": "2212", "fix": "2213"}, {"desc": "2214", "fix": "2215"}, {"desc": "2216", "fix": "2217"}, {"desc": "2218", "fix": "2219"}, {"desc": "2220", "fix": "2221"}, {"desc": "2222", "fix": "2223"}, {"desc": "2224", "fix": "2225"}, {"desc": "2226", "fix": "2227"}, {"desc": "2228", "fix": "2229"}, {"desc": "2230", "fix": "2231"}, {"desc": "2232", "fix": "2233"}, {"desc": "2234", "fix": "2235"}, {"desc": "2236", "fix": "2237"}, {"desc": "2168", "fix": "2238"}, {"desc": "2170", "fix": "2239"}, {"desc": "2205", "fix": "2240"}, {"desc": "2226", "fix": "2241"}, {"desc": "2228", "fix": "2242"}, {"desc": "2230", "fix": "2243"}, {"desc": "2244", "fix": "2245"}, {"desc": "2246", "fix": "2247"}, {"desc": "2248", "fix": "2249"}, {"desc": "2178", "fix": "2250"}, {"desc": "2251", "fix": "2252"}, {"desc": "2253", "fix": "2254"}, {"desc": "2255", "fix": "2256"}, {"desc": "2257", "fix": "2258"}, {"desc": "2259", "fix": "2260"}, {"desc": "2261", "fix": "2262"}, {"desc": "2263", "fix": "2264"}, {"desc": "2265", "fix": "2266"}, {"desc": "2267", "fix": "2268"}, "Update the dependencies array to be: [cinNumber, code, dispatch, firstName, gsmPhone, lastName, orderBy, page, permiNumber, successClientDelete]", {"range": "2269", "text": "2270"}, "Update the dependencies array to be: [dispatch, successCarDelete]", {"range": "2271", "text": "2272"}, "Update the dependencies array to be: [navigate, userInfo, dispatch, id]", {"range": "2273", "text": "2274"}, "Update the dependencies array to be: [dispatch, id, successAgenceUpdate]", {"range": "2275", "text": "2276"}, "Update the dependencies array to be: [dispatch, successAgenceDelete]", {"range": "2277", "text": "2278"}, "Update the dependencies array to be: [dispatch, successReservationDelete]", {"range": "2279", "text": "2280"}, "Update the dependencies array to be: [dispatch, id, successReservationUpdate]", {"range": "2281", "text": "2282"}, "Update the dependencies array to be: [dispatch, id, successCarUpdate]", {"range": "2283", "text": "2284"}, "Update the dependencies array to be: [dispatch, navigate, userInfo]", {"range": "2285", "text": "2286"}, "Update the dependencies array to be: [dispatch, email, password, successUserProfileUpdate]", {"range": "2287", "text": "2288"}, "Update the dependencies array to be: [dispatch, successContratValidReturn]", {"range": "2289", "text": "2290"}, "Update the dependencies array to be: [dispatch, successContratDelete]", {"range": "2291", "text": "2292"}, {"range": "2293", "text": "2274"}, "Update the dependencies array to be: [dispatch, id, successContratPaymentUpdate]", {"range": "2294", "text": "2295"}, {"range": "2296", "text": "2274"}, "Update the dependencies array to be: [client, country]", {"range": "2297", "text": "2298"}, "Update the dependencies array to be: [dispatch, id, successClientUpdate]", {"range": "2299", "text": "2300"}, {"range": "2301", "text": "2274"}, {"range": "2302", "text": "2282"}, "Update the dependencies array to be: [dispatch, status, successEmployeDelete]", {"range": "2303", "text": "2304"}, "Update the dependencies array to be: [dispatch, id, successEmployeUpdate]", {"range": "2305", "text": "2306"}, "Update the dependencies array to be: [dispatch, successUserDelete]", {"range": "2307", "text": "2308"}, "Update the dependencies array to be: [successChargeDelete, successChargeUpdate, successChargeAdd, successEntretienDelete, successEntretienAdd, successEntretienUpdate, dispatch]", {"range": "2309", "text": "2310"}, {"range": "2311", "text": "2286"}, "Update the dependencies array to be: [dispatch, id, navigate, userInfo]", {"range": "2312", "text": "2313"}, "Update the dependencies array to be: [dispatch, id, successDepenseEntretienUpdate]", {"range": "2314", "text": "2315"}, {"range": "2316", "text": "2286"}, "Update the dependencies array to be: [navigate, userInfo, page, dispatch]", {"range": "2317", "text": "2318"}, "Update the dependencies array to be: [dispatch, successDepenseEntretienDelete]", {"range": "2319", "text": "2320"}, {"range": "2321", "text": "2313"}, "Update the dependencies array to be: [dispatch, id, successDepenseEmployeUpdate]", {"range": "2322", "text": "2323"}, {"range": "2324", "text": "2318"}, "Update the dependencies array to be: [dispatch, successDepenseEmployeDelete]", {"range": "2325", "text": "2326"}, {"range": "2327", "text": "2318"}, "Update the dependencies array to be: [dispatch, successDepenseChargeDelete]", {"range": "2328", "text": "2329"}, {"range": "2330", "text": "2313"}, "Update the dependencies array to be: [dispatch, id, successDepenseChargeUpdate]", {"range": "2331", "text": "2332"}, {"range": "2333", "text": "2286"}, "Update the dependencies array to be: [onToggle, open, ref]", {"range": "2334", "text": "2335"}, {"range": "2336", "text": "2286"}, "Update the dependencies array to be: [navigate, userInfo, dispatch, page, idFilter, patientFilter, statusFilter, insuranceFilter, providerFilter, coordinationFilter, typeFilter, ciaIdFilter]", {"range": "2337", "text": "2338"}, "Update the dependencies array to be: [ciaIdFilter, coordinationFilter, dispatch, idFilter, insuranceFilter, patientFilter, providerFilter, statusFilter, successCaseDelete, typeFilter]", {"range": "2339", "text": "2340"}, "Update the dependencies array to be: [navigate, userInfo, dispatch, page, handleError]", {"range": "2341", "text": "2342"}, "Update the dependencies array to be: [dispatch, filterSelect, successCaseDelete]", {"range": "2343", "text": "2344"}, "Update the dependencies array to be: [filesComments]", {"range": "2345", "text": "2346"}, "Update the dependencies array to be: [dispatch, id, successCommentCaseAdd]", {"range": "2347", "text": "2348"}, "Update the dependencies array to be: [dispatch, id, successCommentCaseDelete]", {"range": "2349", "text": "2350"}, "Update the dependencies array to be: [successCaseDuplicate, caseDuplicate, navigate, dispatch]", {"range": "2351", "text": "2352"}, "Update the dependencies array to be: [dispatch, id, successCaseAssignedUpdate]", {"range": "2353", "text": "2354"}, "Update the dependencies array to be: [filesInitialMedicalReports]", {"range": "2355", "text": "2356"}, "Update the dependencies array to be: [filesUploadInvoice]", {"range": "2357", "text": "2358"}, "Update the dependencies array to be: [filesUploadAuthorizationDocuments]", {"range": "2359", "text": "2360"}, "Update the dependencies array to be: [dispatch]", {"range": "2361", "text": "2362"}, "Update the dependencies array to be: [dispatch, userInfo]", {"range": "2363", "text": "2364"}, "Update the dependencies array to be: [dispatch, successClientDelete]", {"range": "2365", "text": "2366"}, {"range": "2367", "text": "2298"}, {"range": "2368", "text": "2300"}, {"range": "2369", "text": "2335"}, {"range": "2370", "text": "2356"}, {"range": "2371", "text": "2358"}, {"range": "2372", "text": "2360"}, "Update the dependencies array to be: [caseInfo, insurances, providers]", {"range": "2373", "text": "2374"}, "Update the dependencies array to be: [navigate, userInfo, dispatch, page, isMaps, searchName, searchType, searchCity, searchCountry, range.max]", {"range": "2375", "text": "2376"}, "Update the dependencies array to be: [dispatch, isMaps, range.max, searchCity, searchCountry, searchName, searchType, successProviderDelete]", {"range": "2377", "text": "2378"}, {"range": "2379", "text": "2308"}, "Update the dependencies array to be: [successUserProfile, userProfile]", {"range": "2380", "text": "2381"}, "Update the dependencies array to be: [dispatch, successUserProfileUpdate]", {"range": "2382", "text": "2383"}, "Update the dependencies array to be: [dispatch, successUserPasswordUpdate]", {"range": "2384", "text": "2385"}, "Update the dependencies array to be: [dispatch, successInsuranceDelete]", {"range": "2386", "text": "2387"}, "Update the dependencies array to be: [dispatch, id, successInsuranceUpdate]", {"range": "2388", "text": "2389"}, "Update the dependencies array to be: [dispatch, id, successProviderUpdate]", {"range": "2390", "text": "2391"}, "Update the dependencies array to be: [dispatch, id, successCoordinatorUpdate]", {"range": "2392", "text": "2393"}, "Update the dependencies array to be: [navigate, successConfirmResetPassword]", {"range": "2394", "text": "2395"}, "Update the dependencies array to be: [navigate, successResetPassword]", {"range": "2396", "text": "2397"}, [2757, 2778], "[cinNumber, code, dispatch, firstName, gsmPhone, lastName, orderBy, page, permiNumber, successClientDelete]", [1726, 1744], "[dispatch, successCarDelete]", [2167, 2197], "[navigate, userInfo, dispatch, id]", [2972, 2993], "[dispatch, id, successAgenceUpdate]", [1734, 1755], "[dispatch, successAgenceDelete]", [1825, 1851], "[dispatch, successReservationDelete]", [6220, 6246], "[dispatch, id, successReservationUpdate]", [10307, 10325], "[dispatch, id, successCarUpdate]", [2360, 2380], "[dispatch, navigate, userInfo]", [3066, 3092], "[dispatch, email, password, successUserProfileUpdate]", [2271, 2298], "[dispatch, successContratValidReturn]", [2541, 2563], "[dispatch, successContratDelete]", [2364, 2394], [3096, 3125], "[dispatch, id, successContratPaymentUpdate]", [2152, 2182], [5215, 5223], "[client, country]", [5323, 5344], "[dispatch, id, successClientUpdate]", [4872, 4902], [7442, 7468], [1896, 1918], "[dispatch, status, successEmployeDelete]", [4368, 4390], "[dispatch, id, successEmployeUpdate]", [1823, 1842], "[dispatch, successUserDelete]", [6226, 6384], "[successChargeDelete, successChargeUpdate, successChargeAdd, successEntretienDelete, successEntretienAdd, successEntretienUpdate, dispatch]", [2645, 2665], [3187, 3207], "[dispatch, id, navigate, userInfo]", [4463, 4494], "[dispatch, id, successDepenseEntretienUpdate]", [2695, 2715], [1748, 1774], "[navigate, userInfo, page, dispatch]", [1996, 2027], "[dispatch, successDepenseEntretienDelete]", [3002, 3022], [4338, 4367], "[dispatch, id, successDepenseEmployeUpdate]", [1705, 1731], [1949, 1978], "[dispatch, successDepenseEmployeDelete]", [1678, 1704], [1920, 1948], "[dispatch, successDepenseChargeDelete]", [2694, 2714], [3755, 3783], "[dispatch, id, successDepenseChargeUpdate]", [2326, 2346], [704, 709], "[onToggle, open, ref]", [3294, 3314], [6185, 6247], "[navigate, userInfo, dispatch, page, idFilter, patientFilter, statusFilter, insuranceFilter, providerFilter, coordinationFilter, typeFilter, ciaIdFilter]", [7137, 7156], "[ciaIdFilter, coordinationFilter, dispatch, idFilter, insuranceFilter, patientFilter, providerFilter, statusFilter, successCaseDelete, typeFilter]", [2063, 2099], "[navigate, userInfo, dispatch, page, handleError]", [2330, 2349], "[dispatch, filterSelect, successCaseDelete]", [2796, 2798], "[filesComments]", [4971, 4994], "[dispatch, id, successCommentCaseAdd]", [5197, 5223], "[dispatch, id, successCommentCaseDelete]", [5410, 5447], "[successCaseDuplicate, caseDuplicate, navigate, dispatch]", [5839, 5866], "[dispatch, id, successCaseAssignedUpdate]", [7035, 7037], "[filesInitialMedicalReports]", [7675, 7677], "[filesUploadInvoice]", [8448, 8450], "[filesUploadAuthorizationDocuments]", [1530, 1532], "[dispatch]", [760, 770], "[dispatch, userInfo]", [1787, 1808], "[dispatch, successClientDelete]", [2767, 2775], [2875, 2896], [704, 709], [8116, 8118], [8756, 8758], [9529, 9531], [19043, 19053], "[caseInfo, insurances, providers]", [3104, 3240], "[navigate, userInfo, dispatch, page, isMaps, searchName, searchType, searchCity, searchCountry, range.max]", [3603, 3640], "[dispatch, isMaps, range.max, searchCity, searchCountry, searchName, searchType, successProviderDelete]", [1927, 1946], [2994, 3014], "[successUserProfile, userProfile]", [3119, 3145], "[dispatch, successUserProfileUpdate]", [3430, 3457], "[dispatch, successUserPasswordUpdate]", [1902, 1926], "[dispatch, successInsuranceDelete]", [4796, 4820], "[dispatch, id, successInsuranceUpdate]", [6965, 6988], "[dispatch, id, successProviderUpdate]", [3704, 3730], "[dispatch, id, successCoordinatorUpdate]", [1307, 1336], "[navigate, successConfirmResetPassword]", [984, 1006], "[navigate, successResetPassword]"]