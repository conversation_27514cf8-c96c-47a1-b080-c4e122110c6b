{"ast": null, "code": "import React,{useEffect,useRef,useState}from\"react\";import{useDispatch,useSelector}from\"react-redux\";import{Link,useLocation,useNavigate,useSearchParams}from\"react-router-dom\";import DefaultLayout from\"../../layouts/DefaultLayout\";import{deleteProvider,providersList}from\"../../redux/actions/providerActions\";import Loader from\"../../components/Loader\";import Alert from\"../../components/Alert\";import{<PERSON><PERSON><PERSON><PERSON>,TileLayer,Marker,Popup,useMap}from\"react-leaflet\";import\"leaflet/dist/leaflet.css\";import L from\"leaflet\";import ConfirmationModal from\"../../components/ConfirmationModal\";import Select from\"react-select\";import{COUNTRIES,SERVICETYPE}from\"../../constants\";import GoogleComponent from\"react-google-autocomplete\";import Paginate from\"../../components/Paginate\";import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";delete L.Icon.Default.prototype._getIconUrl;L.Icon.Default.mergeOptions({iconRetinaUrl:\"https://unpkg.com/leaflet@1.7.1/dist/images/marker-icon-2x.png\",iconUrl:\"https://unpkg.com/leaflet@1.7.1/dist/images/marker-icon.png\",shadowUrl:\"https://unpkg.com/leaflet@1.7.1/dist/images/marker-shadow.png\"});function ProvidersMapScreen(){var _providerMapSelect$fu,_providerMapSelect$ad,_providerMapSelect$pa;const navigate=useNavigate();const location=useLocation();const[searchParams]=useSearchParams();const dispatch=useDispatch();const page=searchParams.get(\"page\")||\"1\";const[isMaps,setIsMaps]=useState(false);const[providerMapSelect,setProviderMapSelect]=useState(null);const[isOpenMap,setIsOpenMap]=useState(false);const[isDelete,setIsDelete]=useState(false);const[loadEvent,setLoadEvent]=useState(false);const[eventType,setEventType]=useState(\"\");const[providerId,setProviderId]=useState(\"\");const[isOpenFilter,setIsOpenFilter]=useState(false);const[searchName,setSearchName]=useState(searchParams.get(\"searchname\")||\"\");const[searchCity,setSearchCity]=useState(searchParams.get(\"searchcity\")||\"\");const[searchType,setSearchType]=useState(searchParams.get(\"searchtype\")||\"\");const[searchCountrySelect,setSearchCountrySelect]=useState(\"\");const[searchCountry,setSearchCountry]=useState(searchParams.get(\"searchcountry\")||\"\");const[range,setRange]=useState({min:0,max:0});const userLogin=useSelector(state=>state.userLogin);const{userInfo}=userLogin;const listProviders=useSelector(state=>state.providerList);const{providers,loadingProviders,errorProviders,pages}=listProviders;const providerDelete=useSelector(state=>state.deleteProvider);const{loadingProviderDelete,errorProviderDelete,successProviderDelete}=providerDelete;const redirect=\"/\";useEffect(()=>{if(!userInfo){navigate(redirect);}else{dispatch(providersList(isMaps?\"0\":page,searchName,searchType,searchCity,searchCountry,range.max));}},[navigate,userInfo,dispatch,// searchName,\n// searchType,\n// searchCity,\n// searchCountry,\npage]);useEffect(()=>{if(successProviderDelete){dispatch(providersList(isMaps?\"0\":1,searchName,searchType,searchCity,searchCountry,range.max,\"\",\"\"));setIsOpenMap(false);setProviderMapSelect(null);}},[successProviderDelete// searchName,\n// searchType,\n// searchCity,\n// searchCountry,\n]);// useEffect(() => {\n//   if (!isOpenFilter) {\n//     const params = new URLSearchParams();\n//     if (page) {\n//       params.set(\"page\", page);\n//     } else {\n//       params.delete(\"page\");\n//     }\n//     if (searchName) {\n//       params.set(\"searchname\", searchName);\n//     } else {\n//       params.delete(\"searchname\");\n//     }\n//     if (searchType) {\n//       params.set(\"searchtype\", searchType);\n//     } else {\n//       params.delete(\"searchtype\");\n//     }\n//     if (searchCity) {\n//       params.set(\"searchcity\", searchCity);\n//     } else {\n//       params.delete(\"searchcity\");\n//     }\n//     if (searchCountry) {\n//       params.set(\"searchcountry\", searchCountry);\n//     } else {\n//       params.delete(\"searchcountry\");\n//     }\n//     // Update the URL with new query params\n//     navigate({\n//       pathname: \"/providers-list\",\n//       search: params.toString(),\n//     });\n//   }\n// }, [\n//   searchName,\n//   searchType,\n//   searchCity,\n//   searchCountry,\n//   navigate,\n//   isOpenFilter,\n// ]);\n//\nconst[mapSearchX,setMapSearchX]=useState(0);const[mapSearchY,setMapSearchY]=useState(0);const[mapCenter,setMapCenter]=useState([0,0]);const[mapZoom,setMapZoom]=useState(2);// Initial zoom level\nconst mapRef=useRef(null);const UpdateMapView=_ref=>{let{center,zoom}=_ref;const map=useMap();map.setView(center,zoom);return null;};const handleMinChange=e=>{const value=parseInt(e.target.value,10);setRange(prev=>({...prev,min:value}));};const handleMaxChange=e=>{const value=parseInt(e.target.value,10);setRange(prev=>({...prev,max:value}));};return/*#__PURE__*/_jsx(DefaultLayout,{children:/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsxs(\"div\",{className:\"flex flex-row text-sm items-center my-1\",children:[/*#__PURE__*/_jsx(\"a\",{href:\"/dashboard\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"flex flex-row  items-center hover:text-black \",children:[/*#__PURE__*/_jsx(\"svg\",{xmlns:\"http://www.w3.org/2000/svg\",fill:\"none\",viewBox:\"0 0 24 24\",\"stroke-width\":\"1.5\",stroke:\"currentColor\",className:\"w-4 h-4\",children:/*#__PURE__*/_jsx(\"path\",{strokeLinecap:\"round\",strokeLinejoin:\"round\",d:\"m2.25 12 8.954-8.955c.44-.439 1.152-.439 1.591 0L21.75 12M4.5 9.75v10.125c0 .621.504 1.125 1.125 1.125H9.75v-4.875c0-.621.504-1.125 1.125-1.125h2.25c.621 0 1.125.504 1.125 1.125V21h4.125c.621 0 1.125-.504 1.125-1.125V9.75M8.25 21h8.25\"})}),/*#__PURE__*/_jsx(\"span\",{className:\"mx-1\",children:\"Dashboard\"})]})}),/*#__PURE__*/_jsx(\"span\",{children:/*#__PURE__*/_jsx(\"svg\",{xmlns:\"http://www.w3.org/2000/svg\",fill:\"none\",viewBox:\"0 0 24 24\",\"stroke-width\":\"1.5\",stroke:\"currentColor\",className:\"w-4 h-4\",children:/*#__PURE__*/_jsx(\"path\",{strokeLinecap:\"round\",strokeLinejoin:\"round\",d:\"m8.25 4.5 7.5 7.5-7.5 7.5\"})})}),/*#__PURE__*/_jsx(\"div\",{className:\"\",children:\"Providers List\"})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex flex-row  justify-between  items-center my-3\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"mx-1 font-bold text-black \",children:\"Providers List\"}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex flex-row items-center justify-end\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"mx-2 \",children:/*#__PURE__*/_jsx(\"button\",{onClick:()=>{setIsOpenFilter(!isOpenFilter);},className:\" rounded px-3 py-1 bg-white shadow-1 border-[#0388A6] text-[#0388A6] hover:bg-[#0388A6] hover:text-white\",children:/*#__PURE__*/_jsx(\"svg\",{xmlns:\"http://www.w3.org/2000/svg\",fill:\"none\",viewBox:\"0 0 24 24\",\"stroke-width\":\"1.5\",stroke:\"currentColor\",className:\"size-5\",children:/*#__PURE__*/_jsx(\"path\",{\"stroke-linecap\":\"round\",\"stroke-linejoin\":\"round\",d:\"M10.5 6h9.75M10.5 6a1.5 1.5 0 1 1-3 0m3 0a1.5 1.5 0 1 0-3 0M3.75 6H7.5m3 12h9.75m-9.75 0a1.5 1.5 0 0 1-3 0m3 0a1.5 1.5 0 0 0-3 0m-3.75 0H7.5m9-6h3.75m-3.75 0a1.5 1.5 0 0 1-3 0m3 0a1.5 1.5 0 0 0-3 0m-9.75 0h9.75\"})})})}),/*#__PURE__*/_jsx(\"div\",{className:\"mx-2 \",children:/*#__PURE__*/_jsx(\"button\",{onClick:()=>{setIsMaps(!isMaps);dispatch(providersList(!isMaps?\"0\":\"1\",searchName,searchType,searchCity,searchCountry,range.max,mapSearchX===0?\"\":mapSearchX,mapSearchY===0?\"\":mapSearchY));},className:\" rounded px-3 py-1 bg-white shadow-1 border-[#0388A6] text-[#0388A6] hover:bg-[#0388A6] hover:text-white\",children:isMaps?/*#__PURE__*/_jsx(\"svg\",{xmlns:\"http://www.w3.org/2000/svg\",fill:\"none\",viewBox:\"0 0 24 24\",\"stroke-width\":\"1.5\",stroke:\"currentColor\",className:\"size-5\",children:/*#__PURE__*/_jsx(\"path\",{\"stroke-linecap\":\"round\",\"stroke-linejoin\":\"round\",d:\"M3.75 12h16.5m-16.5 3.75h16.5M3.75 19.5h16.5M5.625 4.5h12.75a1.875 1.875 0 0 1 0 3.75H5.625a1.875 1.875 0 0 1 0-3.75Z\"})}):/*#__PURE__*/_jsx(\"svg\",{xmlns:\"http://www.w3.org/2000/svg\",fill:\"none\",viewBox:\"0 0 24 24\",\"stroke-width\":\"1.5\",stroke:\"currentColor\",className:\"size-5\",children:/*#__PURE__*/_jsx(\"path\",{\"stroke-linecap\":\"round\",\"stroke-linejoin\":\"round\",d:\"M9 6.75V15m6-6v8.25m.503 3.498 4.875-2.437c.381-.19.622-.58.622-1.006V4.82c0-.836-.88-1.38-1.628-1.006l-3.869 1.934c-.317.159-.69.159-1.006 0L9.503 3.252a1.125 1.125 0 0 0-1.006 0L3.622 5.689C3.24 5.88 3 6.27 3 6.695V19.18c0 .836.88 1.38 1.628 1.006l3.869-1.934c.317-.159.69-.159 1.006 0l4.994 2.497c.317.158.69.158 1.006 0Z\"})})})}),/*#__PURE__*/_jsxs(\"a\",{href:\"/providers-list/new-provider\",className:\"mx-2 flex flex-row bg-[#0388A6] text-white text-xs rounded-full px-5 py-3 items-center\",children:[/*#__PURE__*/_jsx(\"svg\",{xmlns:\"http://www.w3.org/2000/svg\",fill:\"none\",viewBox:\"0 0 24 24\",\"stroke-width\":\"1.5\",stroke:\"currentColor\",className:\"size-4 mx-1\",children:/*#__PURE__*/_jsx(\"path\",{\"stroke-linecap\":\"round\",\"stroke-linejoin\":\"round\",d:\"M12 4.5v15m7.5-7.5h-15\"})}),/*#__PURE__*/_jsx(\"div\",{children:\"New Provider\"})]})]})]}),isOpenFilter?/*#__PURE__*/_jsx(\"div\",{className:\"fixed  top-0 left-0 w-full h-full flex items-center justify-center z-999999 bg-black bg-opacity-20\",children:/*#__PURE__*/_jsx(\"div\",{className:\"bg-white p-6 rounded shadow-md mx-3 relative\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"flex  flex-col my-2\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"flex flex-col  \",children:[/*#__PURE__*/_jsx(\"div\",{className:\"flex items-end justify-end\",children:/*#__PURE__*/_jsx(\"button\",{className:\" mb-5  font-bold bg-danger text-white px-2 py-2 rounded text-xs\",onClick:()=>setIsOpenFilter(false),children:/*#__PURE__*/_jsx(\"svg\",{xmlns:\"http://www.w3.org/2000/svg\",fill:\"none\",viewBox:\"0 0 24 24\",\"stroke-width\":\"1.5\",stroke:\"currentColor\",class:\"size-4\",children:/*#__PURE__*/_jsx(\"path\",{\"stroke-linecap\":\"round\",\"stroke-linejoin\":\"round\",d:\"M6 18 18 6M6 6l12 12\"})})})}),/*#__PURE__*/_jsxs(\"div\",{className:\" w-full  md:pr-1 my-1\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"text-[#000000bf] font-bold text-xs  mb-1\",children:\"Provider Name\"}),/*#__PURE__*/_jsx(\"div\",{children:/*#__PURE__*/_jsx(\"input\",{className:\" outline-none border border-[#F1F3FF] px-3 py-2 w-full rounded text-sm\",type:\"text\",placeholder:\"Search by Name ..\",value:searchName,onChange:v=>{setSearchName(v.target.value);// dispatch(\n//   providersList(\n//     isMaps ? \"0\" : \"1\",\n//     v.target.value,\n//     searchType,\n//     searchCity,\n//     searchCountry\n//   )\n// );\n}})})]})]}),/*#__PURE__*/_jsx(\"div\",{className:\"flex flex-col  \",children:/*#__PURE__*/_jsxs(\"div\",{className:\" w-full  md:pr-1 my-1\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"text-[#000000bf] font-bold text-xs  mb-1\",children:\"Service Type\"}),/*#__PURE__*/_jsx(\"div\",{children:/*#__PURE__*/_jsxs(\"select\",{onChange:v=>{setSearchType(v.target.value);// dispatch(\n//   providersList(\n//     isMaps ? \"0\" : \"1\",\n//     searchName,\n//     v.target.value,\n//     searchCity,\n//     searchCountry\n//   )\n// );\n},value:searchType,className:\" outline-none border border-[#F1F3FF] px-3 py-2 w-full rounded text-sm\",children:[/*#__PURE__*/_jsx(\"option\",{value:\"\",children:\"Select a Type\"}),SERVICETYPE===null||SERVICETYPE===void 0?void 0:SERVICETYPE.map((item,index)=>/*#__PURE__*/_jsx(\"option\",{value:item,children:item}))]})})]})}),/*#__PURE__*/_jsx(\"div\",{className:\"flex flex-col  \",children:/*#__PURE__*/_jsxs(\"div\",{className:\" w-full  md:pr-1 my-1\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"text-[#000000bf] font-bold text-xs  mb-1\",children:\"Country\"}),/*#__PURE__*/_jsx(\"div\",{children:/*#__PURE__*/_jsx(Select,{value:searchCountrySelect,onChange:option=>{setSearchCountry(option.value);setSearchCountrySelect(option);},className:\"outline-none border border-[#F1F3FF] min-w-3  w-full rounded text-sm \",options:COUNTRIES===null||COUNTRIES===void 0?void 0:COUNTRIES.map(country=>({value:country.title,label:/*#__PURE__*/_jsxs(\"div\",{className:`${country.title===\"\"?\"\":\"\"} flex flex-row items-center`,children:[/*#__PURE__*/_jsx(\"span\",{className:\"mr-2\",children:country.icon}),/*#__PURE__*/_jsx(\"span\",{children:country.title})]})})),placeholder:\"Select a country...\",isSearchable:true,styles:{control:(base,state)=>({...base,background:\"#fff\",border:\"1px solid #F1F3FF\",boxShadow:state.isFocused?\"none\":\"none\",\"&:hover\":{border:\"1px solid #F1F3FF\"},minWidth:\"10rem\"}),option:base=>({...base,display:\"flex\",alignItems:\"center\"}),singleValue:base=>({...base,display:\"flex\",alignItems:\"center\"})}})})]})}),/*#__PURE__*/_jsx(\"div\",{className:\"flex flex-col  \",children:/*#__PURE__*/_jsxs(\"div\",{className:\" w-full  md:pr-1 my-1\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"text-[#000000bf] font-bold text-xs  mb-1\",children:\"City\"}),/*#__PURE__*/_jsx(\"div\",{children:/*#__PURE__*/_jsx(GoogleComponent,{apiKey:\"AIzaSyD5rhJGfXPbttVQzftoMPVjJOWbBvfcTFE\",className:`] outline-none border ${1==2?\"border-danger\":\"border-[#F1F3FF]\"} px-3 py-2 w-full rounded text-sm `,onChange:v=>{setSearchCity(v.target.value);},onPlaceSelected:place=>{if(place&&place.geometry){var _place$formatted_addr;setSearchCity((_place$formatted_addr=place.formatted_address)!==null&&_place$formatted_addr!==void 0?_place$formatted_addr:\"\");const latitude=place.geometry.location.lat();const longitude=place.geometry.location.lng();setMapSearchX(latitude);setMapSearchY(longitude);// setMapCenter([latitude, longitude]); // Update map center\n// setMapZoom(10);\n}},defaultValue:searchCity,types:[\"city\"],language:\"en\"})})]})}),/*#__PURE__*/_jsx(\"div\",{className:\"flex flex-col  \",children:/*#__PURE__*/_jsxs(\"div\",{className:\" w-full  md:pr-1 my-1\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"text-[#000000bf] font-bold text-xs  mb-1\",children:[\"Range (\",range.max,\" km)\"]}),/*#__PURE__*/_jsx(\"div\",{children:/*#__PURE__*/_jsx(\"input\",{type:\"range\",className:\" outline-none border border-[#F1F3FF] px-3 py-2 w-full rounded text-sm\",value:range.max,onChange:handleMaxChange,min:\"0\",max:\"500\"})})]})}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex flex-row  \",children:[/*#__PURE__*/_jsxs(\"button\",{onClick:()=>{// setSearchCity(\"\");\n// setSearchName(\"\");\n// setSearchCountry(\"\");\n// setSearchCountrySelect(\"\");\n// setSearchType(\"\");\n// setMapCenter([0, 0]); // Update map center\n// setMapZoom(2);\ndispatch(providersList(isMaps?\"0\":\"1\",searchName,searchType,searchCity,searchCountry,range.max,mapSearchX,mapSearchY));if(searchCity!==\"\"){setMapCenter([mapSearchX,mapSearchY]);// Update map center\nsetMapZoom(10);}setIsOpenFilter(false);},className:\"flex flex-row items-center bg-primary mx-2 text-white px-3 py-1 text-sm rounded\",children:[/*#__PURE__*/_jsx(\"svg\",{xmlns:\"http://www.w3.org/2000/svg\",fill:\"none\",viewBox:\"0 0 24 24\",\"stroke-width\":\"1.5\",stroke:\"currentColor\",class:\"size-4 mx-1\",children:/*#__PURE__*/_jsx(\"path\",{\"stroke-linecap\":\"round\",\"stroke-linejoin\":\"round\",d:\"m4.5 12.75 6 6 9-13.5\"})}),/*#__PURE__*/_jsx(\"div\",{children:\" Filter\"})]}),/*#__PURE__*/_jsxs(\"button\",{onClick:()=>{setSearchCity(\"\");setSearchName(\"\");setSearchCountry(\"\");setSearchCountrySelect(\"\");setSearchType(\"\");setMapCenter([0,0]);// Update map center\nsetMapZoom(2);dispatch(providersList(isMaps?\"0\":\"1\",\"\",\"\",\"\",\"\",400,\"\",\"\"));setIsOpenFilter(false);},className:\"flex flex-row items-center bg-danger mx-2 text-white px-3 py-1 text-sm rounded\",children:[/*#__PURE__*/_jsx(\"svg\",{xmlns:\"http://www.w3.org/2000/svg\",fill:\"none\",viewBox:\"0 0 24 24\",\"stroke-width\":\"1.5\",stroke:\"currentColor\",className:\"size-4 mx-1\",children:/*#__PURE__*/_jsx(\"path\",{\"stroke-linecap\":\"round\",\"stroke-linejoin\":\"round\",d:\"M16.023 9.348h4.992v-.001M2.985 19.644v-4.992m0 0h4.992m-4.993 0 3.181 3.183a8.25 8.25 0 0 0 13.803-3.7M4.031 9.865a8.25 8.25 0 0 1 13.803-3.7l3.181 3.182m0-4.991v4.99\"})}),/*#__PURE__*/_jsx(\"div\",{children:\" Reset\"})]})]})]})})}):null,/*#__PURE__*/_jsxs(\"div\",{className:\"rounded-sm border border-stroke bg-white px-5 pt-6 pb-2.5 shadow-default \",children:[/*#__PURE__*/_jsx(\"div\",{className:\" mx-auto flex flex-col\",children:isMaps?/*#__PURE__*/_jsxs(\"div\",{className:\" relative\",children:[/*#__PURE__*/_jsxs(MapContainer,{center:mapCenter,zoom:mapZoom,style:{height:\"500px\",width:\"100%\"},whenCreated:mapInstance=>mapRef.current=mapInstance// Store map instance\n,children:[/*#__PURE__*/_jsx(UpdateMapView,{center:mapCenter,zoom:mapZoom}),/*#__PURE__*/_jsx(TileLayer,{url:\"https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png\",attribution:\"\\xA9 <a href=\\\"https://www.openstreetmap.org/copyright\\\">OpenStreetMap</a> contributors\"}),providers===null||providers===void 0?void 0:providers.filter(provider=>provider.location_x&&provider.location_y).map((provider,index)=>/*#__PURE__*/_jsx(Marker,{eventHandlers:{click:()=>{setIsOpenMap(true);setProviderMapSelect(provider);}},position:[provider.location_x,provider.location_y],children:/*#__PURE__*/_jsxs(Popup,{children:[provider.full_name,/*#__PURE__*/_jsx(\"br\",{})]})},index))]}),isOpenMap?/*#__PURE__*/_jsx(\"div\",{className:\" absolute top-0 left-0 z-99999  p-2 md:w-1/3 w-2/3 h-full \",children:/*#__PURE__*/_jsxs(\"div\",{className:\"bg-white shadow-1 w-full h-full\",children:[/*#__PURE__*/_jsx(\"div\",{className:\" p-3 float-right \",children:/*#__PURE__*/_jsx(\"button\",{onClick:()=>{setIsOpenMap(false);setProviderMapSelect(null);},className:\"rounded-full p-1 bg-danger shadow-1 text-white flex items-center w-max \",children:/*#__PURE__*/_jsx(\"svg\",{xmlns:\"http://www.w3.org/2000/svg\",fill:\"none\",viewBox:\"0 0 24 24\",\"stroke-width\":\"1.5\",stroke:\"currentColor\",className:\"size-4\",children:/*#__PURE__*/_jsx(\"path\",{\"stroke-linecap\":\"round\",\"stroke-linejoin\":\"round\",d:\"M6 18 18 6M6 6l12 12\"})})})}),/*#__PURE__*/_jsx(\"div\",{className:\"pt-10 py-4 px-3\",children:providerMapSelect&&/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsxs(\"div\",{className:\"flex flex-row items-center text-xs my-3\",children:[/*#__PURE__*/_jsx(\"div\",{children:/*#__PURE__*/_jsx(\"svg\",{xmlns:\"http://www.w3.org/2000/svg\",fill:\"none\",viewBox:\"0 0 24 24\",\"stroke-width\":\"1.5\",stroke:\"currentColor\",className:\"size-4\",children:/*#__PURE__*/_jsx(\"path\",{\"stroke-linecap\":\"round\",\"stroke-linejoin\":\"round\",d:\"M11.42 15.17 17.25 21A2.652 2.652 0 0 0 21 17.25l-5.877-5.877M11.42 15.17l2.496-3.03c.317-.384.74-.626 1.208-.766M11.42 15.17l-4.655 5.653a2.548 2.548 0 1 1-3.586-3.586l6.837-5.63m5.108-.233c.55-.164 1.163-.188 1.743-.14a4.5 4.5 0 0 0 4.486-6.336l-3.276 3.277a3.004 3.004 0 0 1-2.25-2.25l3.276-3.276a4.5 4.5 0 0 0-6.336 4.486c.091 1.076-.071 2.264-.904 2.95l-.102.085m-1.745 1.437L5.909 7.5H4.5L2.25 3.75l1.5-1.5L7.5 4.5v1.409l4.26 4.26m-1.745 1.437 1.745-1.437m6.615 8.206L15.75 15.75M4.867 19.125h.008v.008h-.008v-.008Z\"})})}),/*#__PURE__*/_jsx(\"div\",{className:\"flex-1 px-2\",children:providerMapSelect.services&&providerMapSelect.services.length>0?providerMapSelect.services.map((service,index)=>/*#__PURE__*/_jsxs(\"div\",{className:\"my-1\",children:[\"-\",\" \",service.service_type+(service.service_specialist!==\"\"&&service.service_specialist!==null?\": \"+service.service_specialist:\"\")]},index)):/*#__PURE__*/_jsx(\"div\",{className:\"my-1\",children:\"No services available\"})})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex flex-row items-center text-xs my-3\",children:[/*#__PURE__*/_jsx(\"div\",{children:/*#__PURE__*/_jsx(\"svg\",{xmlns:\"http://www.w3.org/2000/svg\",fill:\"none\",viewBox:\"0 0 24 24\",\"stroke-width\":\"1.5\",stroke:\"currentColor\",className:\"size-4\",children:/*#__PURE__*/_jsx(\"path\",{\"stroke-linecap\":\"round\",\"stroke-linejoin\":\"round\",d:\"M15.75 6a3.75 3.75 0 1 1-7.5 0 3.75 3.75 0 0 1 7.5 0ZM4.501 20.118a7.5 7.5 0 0 1 14.998 0A17.933 17.933 0 0 1 12 21.75c-2.676 0-5.216-.584-7.499-1.632Z\"})})}),/*#__PURE__*/_jsx(\"div\",{className:\"flex-1 px-2\",children:(_providerMapSelect$fu=providerMapSelect.full_name)!==null&&_providerMapSelect$fu!==void 0?_providerMapSelect$fu:\"---\"})]}),providerMapSelect.provider_infos&&providerMapSelect.provider_infos.length>0?providerMapSelect.provider_infos.map((item,index)=>/*#__PURE__*/_jsx(\"div\",{children:/*#__PURE__*/_jsxs(\"div\",{className:\"flex flex-row items-center text-xs my-3\",children:[/*#__PURE__*/_jsx(\"div\",{children:[\"Main Phone\",\"Whatsapp\",\"Billing Phone\"].includes(item.info_type)?/*#__PURE__*/_jsx(\"svg\",{xmlns:\"http://www.w3.org/2000/svg\",fill:\"none\",viewBox:\"0 0 24 24\",\"stroke-width\":\"1.5\",stroke:\"currentColor\",className:\"size-4\",children:/*#__PURE__*/_jsx(\"path\",{\"stroke-linecap\":\"round\",\"stroke-linejoin\":\"round\",d:\"M2.25 6.75c0 8.284 6.716 15 15 15h2.25a2.25 2.25 0 0 0 2.25-2.25v-1.372c0-.516-.351-.966-.852-1.091l-4.423-1.106c-.44-.11-.902.055-1.173.417l-.97 1.293c-.282.376-.769.542-1.21.38a12.035 12.035 0 0 1-7.143-7.143c-.162-.441.004-.928.38-1.21l1.293-.97c.363-.271.527-.734.417-1.173L6.963 3.102a1.125 1.125 0 0 0-1.091-.852H4.5A2.25 2.25 0 0 0 2.25 4.5v2.25Z\"})}):/*#__PURE__*/_jsx(\"svg\",{xmlns:\"http://www.w3.org/2000/svg\",fill:\"none\",viewBox:\"0 0 24 24\",\"stroke-width\":\"1.5\",stroke:\"currentColor\",className:\"size-4\",children:/*#__PURE__*/_jsx(\"path\",{\"stroke-linecap\":\"round\",\"stroke-linejoin\":\"round\",d:\"M21.75 6.75v10.5a2.25 2.25 0 0 1-2.25 2.25h-15a2.25 2.25 0 0 1-2.25-2.25V6.75m19.5 0A2.25 2.25 0 0 0 19.5 4.5h-15a2.25 2.25 0 0 0-2.25 2.25m19.5 0v.243a2.25 2.25 0 0 1-1.07 1.916l-7.5 4.615a2.25 2.25 0 0 1-2.36 0L3.32 8.91a2.25 2.25 0 0 1-1.07-1.916V6.75\"})})}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex-1 px-2\",children:[item.info_type,\" : \",item.info_value]})]})},index)):null,/*#__PURE__*/_jsxs(\"div\",{className:\"flex flex-row items-center text-xs my-3\",children:[/*#__PURE__*/_jsx(\"div\",{children:/*#__PURE__*/_jsxs(\"svg\",{xmlns:\"http://www.w3.org/2000/svg\",fill:\"none\",viewBox:\"0 0 24 24\",\"stroke-width\":\"1.5\",stroke:\"currentColor\",className:\"size-4\",children:[/*#__PURE__*/_jsx(\"path\",{\"stroke-linecap\":\"round\",\"stroke-linejoin\":\"round\",d:\"M15 10.5a3 3 0 1 1-6 0 3 3 0 0 1 6 0Z\"}),/*#__PURE__*/_jsx(\"path\",{\"stroke-linecap\":\"round\",\"stroke-linejoin\":\"round\",d:\"M19.5 10.5c0 7.142-7.5 11.25-7.5 11.25S4.5 17.642 4.5 10.5a7.5 7.5 0 1 1 15 0Z\"})]})}),/*#__PURE__*/_jsx(\"div\",{className:\"flex-1 px-2\",children:(_providerMapSelect$ad=providerMapSelect.address)!==null&&_providerMapSelect$ad!==void 0?_providerMapSelect$ad:\"---\"})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex flex-row items-center text-xs my-3\",children:[/*#__PURE__*/_jsx(\"div\",{children:/*#__PURE__*/_jsx(\"svg\",{xmlns:\"http://www.w3.org/2000/svg\",fill:\"none\",viewBox:\"0 0 24 24\",\"stroke-width\":\"1.5\",stroke:\"currentColor\",class:\"size-4\",children:/*#__PURE__*/_jsx(\"path\",{\"stroke-linecap\":\"round\",\"stroke-linejoin\":\"round\",d:\"M2.25 8.25h19.5M2.25 9h19.5m-16.5 5.25h6m-6 2.25h3m-3.75 3h15a2.25 2.25 0 0 0 2.25-2.25V6.75A2.25 2.25 0 0 0 19.5 4.5h-15a2.25 2.25 0 0 0-2.25 2.25v10.5A2.25 2.25 0 0 0 4.5 19.5Z\"})})}),/*#__PURE__*/_jsx(\"div\",{className:\"flex-1 px-2\",children:(_providerMapSelect$pa=providerMapSelect.payment_method)!==null&&_providerMapSelect$pa!==void 0?_providerMapSelect$pa:\"---\"})]}),/*#__PURE__*/_jsxs(\"p\",{className:\"text-black  text-xs w-max flex flex-row my-4 \",children:[/*#__PURE__*/_jsx(Link,{className:\"mx-1 update-class \",to:\"/providers-list/edit/\"+providerMapSelect.id,children:/*#__PURE__*/_jsx(\"svg\",{xmlns:\"http://www.w3.org/2000/svg\",fill:\"none\",viewBox:\"0 0 24 24\",strokeWidth:\"1.5\",stroke:\"currentColor\",className:\"w-8 h-5 bg-primary rounded p-1 text-white text-center text-xs\",children:/*#__PURE__*/_jsx(\"path\",{strokeLinecap:\"round\",strokeLinejoin:\"round\",d:\"m16.862 4.487 1.687-1.688a1.875 1.875 0 1 1 2.652 2.652L10.582 16.07a4.5 4.5 0 0 1-1.897 1.13L6 18l.8-2.685a4.5 4.5 0 0 1 1.13-1.897l8.932-8.931Zm0 0L19.5 7.125M18 14v4.75A2.25 2.25 0 0 1 15.75 21H5.25A2.25 2.25 0 0 1 3 18.75V8.25A2.25 2.25 0 0 1 5.25 6H10\"})})}),/*#__PURE__*/_jsx(\"div\",{onClick:()=>{setEventType(\"delete\");setProviderId(providerMapSelect.id);setIsDelete(true);},className:\"mx-1 delete-class cursor-pointer\",children:/*#__PURE__*/_jsx(\"svg\",{xmlns:\"http://www.w3.org/2000/svg\",fill:\"none\",viewBox:\"0 0 24 24\",\"stroke-width\":\"1.5\",stroke:\"currentColor\",className:\"w-8 h-5 bg-danger rounded p-1 text-white text-center text-xs\",children:/*#__PURE__*/_jsx(\"path\",{\"stroke-linecap\":\"round\",\"stroke-linejoin\":\"round\",d:\"m20.25 7.5-.625 10.632a2.25 2.25 0 0 1-2.247 2.118H6.622a2.25 2.25 0 0 1-2.247-2.118L3.75 7.5m6 4.125 2.25 2.25m0 0 2.25 2.25M12 13.875l2.25-2.25M12 13.875l-2.25 2.25M3.375 7.5h17.25c.621 0 1.125-.504 1.125-1.125v-1.5c0-.621-.504-1.125-1.125-1.125H3.375c-.621 0-1.125.504-1.125 1.125v1.5c0 .621.504 1.125 1.125 1.125Z\"})})}),/*#__PURE__*/_jsx(Link,{className:\"mx-1 profile-class\",to:\"/providers-list/profile/\"+providerMapSelect.id,children:/*#__PURE__*/_jsx(\"svg\",{xmlns:\"http://www.w3.org/2000/svg\",fill:\"none\",viewBox:\"0 0 24 24\",\"stroke-width\":\"1.5\",stroke:\"currentColor\",className:\"w-5 h-5 bg-success rounded p-1 text-white text-center text-xs\",children:/*#__PURE__*/_jsx(\"path\",{\"stroke-linecap\":\"round\",\"stroke-linejoin\":\"round\",d:\"M8.25 6.75h12M8.25 12h12m-12 5.25h12M3.75 6.75h.007v.008H3.75V6.75Zm.375 0a.375.375 0 1 1-.75 0 .375.375 0 0 1 .75 0ZM3.75 12h.007v.008H3.75V12Zm.375 0a.375.375 0 1 1-.75 0 .375.375 0 0 1 .75 0Zm-.375 5.25h.007v.008H3.75v-.008Zm.375 0a.375.375 0 1 1-.75 0 .375.375 0 0 1 .75 0Z\"})})})]})]})})]})}):null]}):/*#__PURE__*/ // <iframe\n//   title=\"Providers List\"\n//   src=\"https://www.google.com/maps/d/u/0/embed?mid=1KH5CWcxgH2OO_t1rr6OqMCS-pCVTaik&ehbc=2E312F\"\n//   className=\"min-h-[500px] w-full\"\n// ></iframe>\n_jsxs(\"div\",{children:[loadingProviders?/*#__PURE__*/_jsx(Loader,{}):errorProviders?/*#__PURE__*/_jsx(Alert,{type:\"error\",message:errorProviders}):/*#__PURE__*/_jsx(\"div\",{className:\"max-w-full overflow-x-auto \",children:/*#__PURE__*/_jsxs(\"table\",{className:\"w-full table-auto\",children:[/*#__PURE__*/_jsx(\"thead\",{children:/*#__PURE__*/_jsxs(\"tr\",{className:\" bg-[#F3F5FB] text-left \",children:[/*#__PURE__*/_jsx(\"th\",{className:\"min-w-[30px] py-4 px-4 font-bold text-[#8E8E8E] text-xs w-max\",children:\"#\"}),/*#__PURE__*/_jsx(\"th\",{className:\"min-w-[60px] py-4 px-4 font-bold text-[#8E8E8E] text-xs w-max \",children:\"Provider\"}),/*#__PURE__*/_jsx(\"th\",{className:\"min-w-[60px] py-4 px-4 font-bold text-[#8E8E8E] text-xs w-max \",children:\"Services Type\"}),/*#__PURE__*/_jsx(\"th\",{className:\"min-w-[120px] py-4 px-4 font-bold text-[#8E8E8E] text-xs w-max\",children:\"Country\"}),/*#__PURE__*/_jsx(\"th\",{className:\"min-w-[120px] py-4 px-4 font-bold text-[#8E8E8E] text-xs w-max\",children:\"City\"}),/*#__PURE__*/_jsx(\"th\",{className:\"min-w-[120px] py-4 px-4 font-bold text-[#8E8E8E] text-xs w-max\",children:\"Address\"}),/*#__PURE__*/_jsx(\"th\",{className:\"py-4 px-4 font-bold text-[#8E8E8E] text-xs w-max\",children:\"Operation\"})]})}),/*#__PURE__*/_jsx(\"tbody\",{children:providers===null||providers===void 0?void 0:providers.map((item,index)=>{var _item$services,_item$services2,_item$country,_item$city;return/*#__PURE__*/_jsxs(\"tr\",{children:[/*#__PURE__*/_jsx(\"td\",{className:\" py-3 px-4 max-w-[200px]  \",children:/*#__PURE__*/_jsx(\"p\",{className:\"text-black  text-xs truncate  \",children:item.id})}),/*#__PURE__*/_jsx(\"td\",{className:\" py-3 px-4 max-w-[200px]  \",children:/*#__PURE__*/_jsx(\"p\",{className:\"text-black  text-xs truncate  \",children:/*#__PURE__*/_jsx(\"a\",{href:\"/providers-list/profile/\"+item.id,children:/*#__PURE__*/_jsx(\"p\",{className:\"text-black  text-xs w-max  \",children:item.full_name})})})}),/*#__PURE__*/_jsx(\"td\",{className:\" py-3 px-4 max-w-[200px]  \",children:/*#__PURE__*/_jsx(\"p\",{className:\"text-black  text-xs truncate  \",children:/*#__PURE__*/_jsxs(\"a\",{href:\"/providers-list/profile/\"+item.id,children:[((_item$services=item.services)===null||_item$services===void 0?void 0:_item$services.length)||0,\" \",(((_item$services2=item.services)===null||_item$services2===void 0?void 0:_item$services2.length)||0)>1?\"Services\":\"Service\"]})})}),/*#__PURE__*/_jsx(\"td\",{className:\" py-3 px-4 max-w-[200px]  \",children:/*#__PURE__*/_jsx(\"p\",{className:\"text-black  text-xs truncate  \",children:(_item$country=item.country)!==null&&_item$country!==void 0?_item$country:\"----\"})}),/*#__PURE__*/_jsx(\"td\",{className:\" py-3 px-4 max-w-[200px]  \",children:/*#__PURE__*/_jsx(\"p\",{className:\"text-black  text-xs truncate  \",children:(_item$city=item.city)!==null&&_item$city!==void 0?_item$city:\"----\"})}),/*#__PURE__*/_jsx(\"td\",{className:\" py-3 px-4 max-w-[200px]  \",children:/*#__PURE__*/_jsx(\"p\",{className:\"text-black  text-xs truncate  \",children:item.address})}),/*#__PURE__*/_jsx(\"td\",{className:\" py-3 px-4 min-w-[120px]  \",children:/*#__PURE__*/_jsxs(\"p\",{className:\"text-black  text-xs w-max flex flex-row  \",children:[/*#__PURE__*/_jsx(Link,{className:\"mx-1 update-class\",to:\"/providers-list/edit/\"+item.id,children:/*#__PURE__*/_jsx(\"svg\",{xmlns:\"http://www.w3.org/2000/svg\",fill:\"none\",viewBox:\"0 0 24 24\",strokeWidth:\"1.5\",stroke:\"currentColor\",className:\"w-5 h-5 bg-primary rounded p-1 text-white text-center text-xs\",children:/*#__PURE__*/_jsx(\"path\",{strokeLinecap:\"round\",strokeLinejoin:\"round\",d:\"m16.862 4.487 1.687-1.688a1.875 1.875 0 1 1 2.652 2.652L10.582 16.07a4.5 4.5 0 0 1-1.897 1.13L6 18l.8-2.685a4.5 4.5 0 0 1 1.13-1.897l8.932-8.931Zm0 0L19.5 7.125M18 14v4.75A2.25 2.25 0 0 1 15.75 21H5.25A2.25 2.25 0 0 1 3 18.75V8.25A2.25 2.25 0 0 1 5.25 6H10\"})})}),/*#__PURE__*/_jsx(\"div\",{onClick:()=>{setEventType(\"delete\");setProviderId(item.id);setIsDelete(true);},className:\"mx-1 delete-class cursor-pointer\",children:/*#__PURE__*/_jsx(\"svg\",{xmlns:\"http://www.w3.org/2000/svg\",fill:\"none\",viewBox:\"0 0 24 24\",\"stroke-width\":\"1.5\",stroke:\"currentColor\",className:\"w-5 h-5 bg-danger rounded p-1 text-white text-center text-xs\",children:/*#__PURE__*/_jsx(\"path\",{\"stroke-linecap\":\"round\",\"stroke-linejoin\":\"round\",d:\"m20.25 7.5-.625 10.632a2.25 2.25 0 0 1-2.247 2.118H6.622a2.25 2.25 0 0 1-2.247-2.118L3.75 7.5m6 4.125 2.25 2.25m0 0 2.25 2.25M12 13.875l2.25-2.25M12 13.875l-2.25 2.25M3.375 7.5h17.25c.621 0 1.125-.504 1.125-1.125v-1.5c0-.621-.504-1.125-1.125-1.125H3.375c-.621 0-1.125.504-1.125 1.125v1.5c0 .621.504 1.125 1.125 1.125Z\"})})}),/*#__PURE__*/_jsx(Link,{className:\"mx-1 profile-class\",to:\"/providers-list/profile/\"+item.id,children:/*#__PURE__*/_jsx(\"svg\",{xmlns:\"http://www.w3.org/2000/svg\",fill:\"none\",viewBox:\"0 0 24 24\",\"stroke-width\":\"1.5\",stroke:\"currentColor\",className:\"w-5 h-5 bg-success rounded p-1 text-white text-center text-xs\",children:/*#__PURE__*/_jsx(\"path\",{\"stroke-linecap\":\"round\",\"stroke-linejoin\":\"round\",d:\"M8.25 6.75h12M8.25 12h12m-12 5.25h12M3.75 6.75h.007v.008H3.75V6.75Zm.375 0a.375.375 0 1 1-.75 0 .375.375 0 0 1 .75 0ZM3.75 12h.007v.008H3.75V12Zm.375 0a.375.375 0 1 1-.75 0 .375.375 0 0 1 .75 0Zm-.375 5.25h.007v.008H3.75v-.008Zm.375 0a.375.375 0 1 1-.75 0 .375.375 0 0 1 .75 0Z\"})})})]})})]},index);})})]})}),/*#__PURE__*/_jsx(\"div\",{className:\"\",children:/*#__PURE__*/_jsx(Paginate,{route:`/providers-list?searchname=${searchName}&searchtype=${searchType}&searchcity=${searchCity}&searchcountry=${searchCountry}&`,search:\"\",page:page,pages:pages})})]})}),/*#__PURE__*/_jsx(\"div\",{className:\"my-5\"})]}),/*#__PURE__*/_jsx(ConfirmationModal,{isOpen:isDelete,message:eventType===\"delete\"?\"Are you sure you want to delete this Provider?\":\"Are you sure ?\",onConfirm:async()=>{if(eventType===\"cancel\"){setIsDelete(false);setEventType(\"\");setLoadEvent(false);}else if(eventType===\"delete\"&&providerId!==\"\"){setLoadEvent(true);dispatch(deleteProvider(providerId));setIsDelete(false);setEventType(\"\");setLoadEvent(false);}else{setIsDelete(false);setEventType(\"\");setLoadEvent(false);}},onCancel:()=>{setIsDelete(false);setEventType(\"\");setLoadEvent(false);},loadEvent:loadEvent}),/*#__PURE__*/_jsx(\"div\",{className:\"grid md:grid-cols-2 w-full container mt-5\"})]})});}export default ProvidersMapScreen;", "map": {"version": 3, "names": ["React", "useEffect", "useRef", "useState", "useDispatch", "useSelector", "Link", "useLocation", "useNavigate", "useSearchParams", "DefaultLayout", "deleteProvider", "providersList", "Loader", "<PERSON><PERSON>", "MapContainer", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Popup", "useMap", "L", "ConfirmationModal", "Select", "COUNTRIES", "SERVICETYPE", "GoogleComponent", "Paginate", "jsx", "_jsx", "jsxs", "_jsxs", "Icon", "<PERSON><PERSON><PERSON>", "prototype", "_getIconUrl", "mergeOptions", "iconRetinaUrl", "iconUrl", "shadowUrl", "ProvidersMapScreen", "_providerMapSelect$fu", "_providerMapSelect$ad", "_providerMapSelect$pa", "navigate", "location", "searchParams", "dispatch", "page", "get", "isMaps", "setIsMaps", "providerMapSelect", "setProviderMapSelect", "isOpenMap", "setIsOpenMap", "isDelete", "setIsDelete", "loadEvent", "setLoadEvent", "eventType", "setEventType", "providerId", "setProviderId", "isOpenFilter", "setIs<PERSON>pen<PERSON><PERSON>er", "searchName", "setSearchName", "searchCity", "setSearchCity", "searchType", "setSearchType", "searchCountrySelect", "setSearchCountrySelect", "searchCountry", "setSearchCountry", "range", "setRang<PERSON>", "min", "max", "userLogin", "state", "userInfo", "listProviders", "providerList", "providers", "loadingProviders", "errorProviders", "pages", "providerDelete", "loadingProviderDelete", "errorProviderDelete", "successProviderDelete", "redirect", "mapSearchX", "setMapSearchX", "mapSearchY", "setMapSearchY", "mapCenter", "setMapCenter", "mapZoom", "setMapZoom", "mapRef", "UpdateMapView", "_ref", "center", "zoom", "map", "<PERSON><PERSON><PERSON><PERSON>", "handleMinChange", "e", "value", "parseInt", "target", "prev", "handleMaxChange", "children", "className", "href", "xmlns", "fill", "viewBox", "stroke", "strokeLinecap", "strokeLinejoin", "d", "onClick", "class", "type", "placeholder", "onChange", "v", "item", "index", "option", "options", "country", "title", "label", "icon", "isSearchable", "styles", "control", "base", "background", "border", "boxShadow", "isFocused", "min<PERSON><PERSON><PERSON>", "display", "alignItems", "singleValue", "<PERSON><PERSON><PERSON><PERSON>", "onPlaceSelected", "place", "geometry", "_place$formatted_addr", "formatted_address", "latitude", "lat", "longitude", "lng", "defaultValue", "types", "language", "style", "height", "width", "whenCreated", "mapInstance", "current", "url", "attribution", "filter", "provider", "location_x", "location_y", "eventHandlers", "click", "position", "full_name", "services", "length", "service", "service_type", "service_specialist", "provider_infos", "includes", "info_type", "info_value", "address", "payment_method", "to", "id", "strokeWidth", "message", "_item$services", "_item$services2", "_item$country", "_item$city", "city", "route", "search", "isOpen", "onConfirm", "onCancel"], "sources": ["/Users/<USER>/Desktop/Freelances/<PERSON><PERSON><PERSON>/UNIMEDCARE/web-front/src/screens/proveedors/ProvidersMapScreen.js"], "sourcesContent": ["import React, { useEffect, useRef, useState } from \"react\";\nimport { useDispatch, useSelector } from \"react-redux\";\nimport {\n  Link,\n  useLocation,\n  useNavigate,\n  useSearchParams,\n} from \"react-router-dom\";\nimport DefaultLayout from \"../../layouts/DefaultLayout\";\nimport {\n  deleteProvider,\n  providersList,\n} from \"../../redux/actions/providerActions\";\nimport Loader from \"../../components/Loader\";\nimport Alert from \"../../components/Alert\";\nimport { <PERSON><PERSON>ontaine<PERSON>, TileLayer, Marker, Popup, useMap } from \"react-leaflet\";\nimport \"leaflet/dist/leaflet.css\";\nimport L from \"leaflet\";\nimport ConfirmationModal from \"../../components/ConfirmationModal\";\nimport Select from \"react-select\";\nimport { COUNTRIES, SERVICETYPE } from \"../../constants\";\nimport GoogleComponent from \"react-google-autocomplete\";\nimport Paginate from \"../../components/Paginate\";\n\ndelete L.Icon.Default.prototype._getIconUrl;\nL.Icon.Default.mergeOptions({\n  iconRetinaUrl:\n    \"https://unpkg.com/leaflet@1.7.1/dist/images/marker-icon-2x.png\",\n  iconUrl: \"https://unpkg.com/leaflet@1.7.1/dist/images/marker-icon.png\",\n  shadowUrl: \"https://unpkg.com/leaflet@1.7.1/dist/images/marker-shadow.png\",\n});\n\nfunction ProvidersMapScreen() {\n  const navigate = useNavigate();\n  const location = useLocation();\n  const [searchParams] = useSearchParams();\n  const dispatch = useDispatch();\n  const page = searchParams.get(\"page\") || \"1\";\n\n  const [isMaps, setIsMaps] = useState(false);\n\n  const [providerMapSelect, setProviderMapSelect] = useState(null);\n  const [isOpenMap, setIsOpenMap] = useState(false);\n\n  const [isDelete, setIsDelete] = useState(false);\n  const [loadEvent, setLoadEvent] = useState(false);\n  const [eventType, setEventType] = useState(\"\");\n  const [providerId, setProviderId] = useState(\"\");\n\n  const [isOpenFilter, setIsOpenFilter] = useState(false);\n\n  const [searchName, setSearchName] = useState(\n    searchParams.get(\"searchname\") || \"\"\n  );\n  const [searchCity, setSearchCity] = useState(\n    searchParams.get(\"searchcity\") || \"\"\n  );\n  const [searchType, setSearchType] = useState(\n    searchParams.get(\"searchtype\") || \"\"\n  );\n  const [searchCountrySelect, setSearchCountrySelect] = useState(\"\");\n  const [searchCountry, setSearchCountry] = useState(\n    searchParams.get(\"searchcountry\") || \"\"\n  );\n\n  const [range, setRange] = useState({ min: 0, max: 0 });\n\n  const userLogin = useSelector((state) => state.userLogin);\n  const { userInfo } = userLogin;\n\n  const listProviders = useSelector((state) => state.providerList);\n  const { providers, loadingProviders, errorProviders, pages } = listProviders;\n\n  const providerDelete = useSelector((state) => state.deleteProvider);\n  const { loadingProviderDelete, errorProviderDelete, successProviderDelete } =\n    providerDelete;\n\n  const redirect = \"/\";\n\n  useEffect(() => {\n    if (!userInfo) {\n      navigate(redirect);\n    } else {\n      dispatch(\n        providersList(\n          isMaps ? \"0\" : page,\n          searchName,\n          searchType,\n          searchCity,\n          searchCountry,\n          range.max\n        )\n      );\n    }\n  }, [\n    navigate,\n    userInfo,\n    dispatch,\n    // searchName,\n    // searchType,\n    // searchCity,\n    // searchCountry,\n    page,\n  ]);\n\n  useEffect(() => {\n    if (successProviderDelete) {\n      dispatch(\n        providersList(\n          isMaps ? \"0\" : 1,\n          searchName,\n          searchType,\n          searchCity,\n          searchCountry,\n          range.max,\n          \"\",\n          \"\"\n        )\n      );\n      setIsOpenMap(false);\n      setProviderMapSelect(null);\n    }\n  }, [\n    successProviderDelete,\n    // searchName,\n    // searchType,\n    // searchCity,\n    // searchCountry,\n  ]);\n\n  // useEffect(() => {\n  //   if (!isOpenFilter) {\n  //     const params = new URLSearchParams();\n  //     if (page) {\n  //       params.set(\"page\", page);\n  //     } else {\n  //       params.delete(\"page\");\n  //     }\n\n  //     if (searchName) {\n  //       params.set(\"searchname\", searchName);\n  //     } else {\n  //       params.delete(\"searchname\");\n  //     }\n\n  //     if (searchType) {\n  //       params.set(\"searchtype\", searchType);\n  //     } else {\n  //       params.delete(\"searchtype\");\n  //     }\n\n  //     if (searchCity) {\n  //       params.set(\"searchcity\", searchCity);\n  //     } else {\n  //       params.delete(\"searchcity\");\n  //     }\n\n  //     if (searchCountry) {\n  //       params.set(\"searchcountry\", searchCountry);\n  //     } else {\n  //       params.delete(\"searchcountry\");\n  //     }\n\n  //     // Update the URL with new query params\n  //     navigate({\n  //       pathname: \"/providers-list\",\n  //       search: params.toString(),\n  //     });\n  //   }\n  // }, [\n  //   searchName,\n  //   searchType,\n  //   searchCity,\n  //   searchCountry,\n  //   navigate,\n  //   isOpenFilter,\n  // ]);\n\n  //\n\n  const [mapSearchX, setMapSearchX] = useState(0);\n  const [mapSearchY, setMapSearchY] = useState(0);\n  const [mapCenter, setMapCenter] = useState([0, 0]);\n  const [mapZoom, setMapZoom] = useState(2); // Initial zoom level\n  const mapRef = useRef(null);\n\n  const UpdateMapView = ({ center, zoom }) => {\n    const map = useMap();\n    map.setView(center, zoom);\n    return null;\n  };\n\n  const handleMinChange = (e) => {\n    const value = parseInt(e.target.value, 10);\n    setRange((prev) => ({ ...prev, min: value }));\n  };\n\n  const handleMaxChange = (e) => {\n    const value = parseInt(e.target.value, 10);\n    setRange((prev) => ({ ...prev, max: value }));\n  };\n\n  return (\n    <DefaultLayout>\n      <div>\n        <div className=\"flex flex-row text-sm items-center my-1\">\n          {/* home */}\n          <a href=\"/dashboard\">\n            <div className=\"flex flex-row  items-center hover:text-black \">\n              <svg\n                xmlns=\"http://www.w3.org/2000/svg\"\n                fill=\"none\"\n                viewBox=\"0 0 24 24\"\n                stroke-width=\"1.5\"\n                stroke=\"currentColor\"\n                className=\"w-4 h-4\"\n              >\n                <path\n                  strokeLinecap=\"round\"\n                  strokeLinejoin=\"round\"\n                  d=\"m2.25 12 8.954-8.955c.44-.439 1.152-.439 1.591 0L21.75 12M4.5 9.75v10.125c0 .621.504 1.125 1.125 1.125H9.75v-4.875c0-.621.504-1.125 1.125-1.125h2.25c.621 0 1.125.504 1.125 1.125V21h4.125c.621 0 1.125-.504 1.125-1.125V9.75M8.25 21h8.25\"\n                />\n              </svg>\n              <span className=\"mx-1\">Dashboard</span>\n            </div>\n          </a>\n          <span>\n            <svg\n              xmlns=\"http://www.w3.org/2000/svg\"\n              fill=\"none\"\n              viewBox=\"0 0 24 24\"\n              stroke-width=\"1.5\"\n              stroke=\"currentColor\"\n              className=\"w-4 h-4\"\n            >\n              <path\n                strokeLinecap=\"round\"\n                strokeLinejoin=\"round\"\n                d=\"m8.25 4.5 7.5 7.5-7.5 7.5\"\n              />\n            </svg>\n          </span>\n          <div className=\"\">Providers List</div>\n        </div>\n\n        {/*  */}\n        <div className=\"flex flex-row  justify-between  items-center my-3\">\n          <div className=\"mx-1 font-bold text-black \">Providers List</div>\n\n          <div className=\"flex flex-row items-center justify-end\">\n            <div className=\"mx-2 \">\n              <button\n                onClick={() => {\n                  setIsOpenFilter(!isOpenFilter);\n                }}\n                className=\" rounded px-3 py-1 bg-white shadow-1 border-[#0388A6] text-[#0388A6] hover:bg-[#0388A6] hover:text-white\"\n              >\n                <svg\n                  xmlns=\"http://www.w3.org/2000/svg\"\n                  fill=\"none\"\n                  viewBox=\"0 0 24 24\"\n                  stroke-width=\"1.5\"\n                  stroke=\"currentColor\"\n                  className=\"size-5\"\n                >\n                  <path\n                    stroke-linecap=\"round\"\n                    stroke-linejoin=\"round\"\n                    d=\"M10.5 6h9.75M10.5 6a1.5 1.5 0 1 1-3 0m3 0a1.5 1.5 0 1 0-3 0M3.75 6H7.5m3 12h9.75m-9.75 0a1.5 1.5 0 0 1-3 0m3 0a1.5 1.5 0 0 0-3 0m-3.75 0H7.5m9-6h3.75m-3.75 0a1.5 1.5 0 0 1-3 0m3 0a1.5 1.5 0 0 0-3 0m-9.75 0h9.75\"\n                  />\n                </svg>\n              </button>\n            </div>\n            <div className=\"mx-2 \">\n              <button\n                onClick={() => {\n                  setIsMaps(!isMaps);\n                  dispatch(\n                    providersList(\n                      !isMaps ? \"0\" : \"1\",\n                      searchName,\n                      searchType,\n                      searchCity,\n                      searchCountry,\n                      range.max,\n                      mapSearchX === 0 ? \"\" : mapSearchX,\n                      mapSearchY === 0 ? \"\" : mapSearchY\n                    )\n                  );\n                }}\n                className=\" rounded px-3 py-1 bg-white shadow-1 border-[#0388A6] text-[#0388A6] hover:bg-[#0388A6] hover:text-white\"\n              >\n                {isMaps ? (\n                  <svg\n                    xmlns=\"http://www.w3.org/2000/svg\"\n                    fill=\"none\"\n                    viewBox=\"0 0 24 24\"\n                    stroke-width=\"1.5\"\n                    stroke=\"currentColor\"\n                    className=\"size-5\"\n                  >\n                    <path\n                      stroke-linecap=\"round\"\n                      stroke-linejoin=\"round\"\n                      d=\"M3.75 12h16.5m-16.5 3.75h16.5M3.75 19.5h16.5M5.625 4.5h12.75a1.875 1.875 0 0 1 0 3.75H5.625a1.875 1.875 0 0 1 0-3.75Z\"\n                    />\n                  </svg>\n                ) : (\n                  <svg\n                    xmlns=\"http://www.w3.org/2000/svg\"\n                    fill=\"none\"\n                    viewBox=\"0 0 24 24\"\n                    stroke-width=\"1.5\"\n                    stroke=\"currentColor\"\n                    className=\"size-5\"\n                  >\n                    <path\n                      stroke-linecap=\"round\"\n                      stroke-linejoin=\"round\"\n                      d=\"M9 6.75V15m6-6v8.25m.503 3.498 4.875-2.437c.381-.19.622-.58.622-1.006V4.82c0-.836-.88-1.38-1.628-1.006l-3.869 1.934c-.317.159-.69.159-1.006 0L9.503 3.252a1.125 1.125 0 0 0-1.006 0L3.622 5.689C3.24 5.88 3 6.27 3 6.695V19.18c0 .836.88 1.38 1.628 1.006l3.869-1.934c.317-.159.69-.159 1.006 0l4.994 2.497c.317.158.69.158 1.006 0Z\"\n                    />\n                  </svg>\n                )}\n              </button>\n            </div>\n            <a\n              href=\"/providers-list/new-provider\"\n              className=\"mx-2 flex flex-row bg-[#0388A6] text-white text-xs rounded-full px-5 py-3 items-center\"\n            >\n              <svg\n                xmlns=\"http://www.w3.org/2000/svg\"\n                fill=\"none\"\n                viewBox=\"0 0 24 24\"\n                stroke-width=\"1.5\"\n                stroke=\"currentColor\"\n                className=\"size-4 mx-1\"\n              >\n                <path\n                  stroke-linecap=\"round\"\n                  stroke-linejoin=\"round\"\n                  d=\"M12 4.5v15m7.5-7.5h-15\"\n                />\n              </svg>\n\n              <div>New Provider</div>\n            </a>\n          </div>\n        </div>\n        {isOpenFilter ? (\n          <div className=\"fixed  top-0 left-0 w-full h-full flex items-center justify-center z-999999 bg-black bg-opacity-20\">\n            <div className=\"bg-white p-6 rounded shadow-md mx-3 relative\">\n              <div className=\"flex  flex-col my-2\">\n                {/*  */}\n                <div className=\"flex flex-col  \">\n                  <div className=\"flex items-end justify-end\">\n                    <button\n                      className=\" mb-5  font-bold bg-danger text-white px-2 py-2 rounded text-xs\"\n                      onClick={() => setIsOpenFilter(false)}\n                    >\n                      <svg\n                        xmlns=\"http://www.w3.org/2000/svg\"\n                        fill=\"none\"\n                        viewBox=\"0 0 24 24\"\n                        stroke-width=\"1.5\"\n                        stroke=\"currentColor\"\n                        class=\"size-4\"\n                      >\n                        <path\n                          stroke-linecap=\"round\"\n                          stroke-linejoin=\"round\"\n                          d=\"M6 18 18 6M6 6l12 12\"\n                        />\n                      </svg>\n                    </button>\n                  </div>\n                  <div className=\" w-full  md:pr-1 my-1\">\n                    <div className=\"text-[#000000bf] font-bold text-xs  mb-1\">\n                      Provider Name\n                    </div>\n                    <div>\n                      <input\n                        className=\" outline-none border border-[#F1F3FF] px-3 py-2 w-full rounded text-sm\"\n                        type=\"text\"\n                        placeholder=\"Search by Name ..\"\n                        value={searchName}\n                        onChange={(v) => {\n                          setSearchName(v.target.value);\n                          // dispatch(\n                          //   providersList(\n                          //     isMaps ? \"0\" : \"1\",\n                          //     v.target.value,\n                          //     searchType,\n                          //     searchCity,\n                          //     searchCountry\n                          //   )\n                          // );\n                        }}\n                      />\n                    </div>\n                  </div>\n                </div>\n                {/*  */}\n                <div className=\"flex flex-col  \">\n                  <div className=\" w-full  md:pr-1 my-1\">\n                    <div className=\"text-[#000000bf] font-bold text-xs  mb-1\">\n                      Service Type\n                    </div>\n                    <div>\n                      <select\n                        onChange={(v) => {\n                          setSearchType(v.target.value);\n                          // dispatch(\n                          //   providersList(\n                          //     isMaps ? \"0\" : \"1\",\n                          //     searchName,\n                          //     v.target.value,\n                          //     searchCity,\n                          //     searchCountry\n                          //   )\n                          // );\n                        }}\n                        value={searchType}\n                        className=\" outline-none border border-[#F1F3FF] px-3 py-2 w-full rounded text-sm\"\n                      >\n                        <option value={\"\"}>Select a Type</option>\n                        {SERVICETYPE?.map((item, index) => (\n                          <option value={item}>{item}</option>\n                        ))}\n                      </select>\n                    </div>\n                  </div>\n                </div>\n                <div className=\"flex flex-col  \">\n                  <div className=\" w-full  md:pr-1 my-1\">\n                    <div className=\"text-[#000000bf] font-bold text-xs  mb-1\">\n                      Country\n                    </div>\n                    <div>\n                      <Select\n                        value={searchCountrySelect}\n                        onChange={(option) => {\n                          setSearchCountry(option.value);\n                          setSearchCountrySelect(option);\n                        }}\n                        className=\"outline-none border border-[#F1F3FF] min-w-3  w-full rounded text-sm \"\n                        options={COUNTRIES?.map((country) => ({\n                          value: country.title,\n                          label: (\n                            <div\n                              className={`${\n                                country.title === \"\" ? \"\" : \"\"\n                              } flex flex-row items-center`}\n                            >\n                              <span className=\"mr-2\">{country.icon}</span>\n                              <span>{country.title}</span>\n                            </div>\n                          ),\n                        }))}\n                        placeholder=\"Select a country...\"\n                        isSearchable\n                        styles={{\n                          control: (base, state) => ({\n                            ...base,\n                            background: \"#fff\",\n                            border: \"1px solid #F1F3FF\",\n                            boxShadow: state.isFocused ? \"none\" : \"none\",\n                            \"&:hover\": {\n                              border: \"1px solid #F1F3FF\",\n                            },\n                            minWidth: \"10rem\",\n                          }),\n                          option: (base) => ({\n                            ...base,\n                            display: \"flex\",\n                            alignItems: \"center\",\n                          }),\n                          singleValue: (base) => ({\n                            ...base,\n                            display: \"flex\",\n                            alignItems: \"center\",\n                          }),\n                        }}\n                      />\n                    </div>\n                  </div>\n                </div>\n                <div className=\"flex flex-col  \">\n                  <div className=\" w-full  md:pr-1 my-1\">\n                    <div className=\"text-[#000000bf] font-bold text-xs  mb-1\">\n                      City\n                    </div>\n                    <div>\n                      <GoogleComponent\n                        apiKey=\"AIzaSyD5rhJGfXPbttVQzftoMPVjJOWbBvfcTFE\"\n                        className={`] outline-none border ${\n                          1 == 2 ? \"border-danger\" : \"border-[#F1F3FF]\"\n                        } px-3 py-2 w-full rounded text-sm `}\n                        onChange={(v) => {\n                          setSearchCity(v.target.value);\n                        }}\n                        onPlaceSelected={(place) => {\n                          if (place && place.geometry) {\n                            setSearchCity(place.formatted_address ?? \"\");\n                            const latitude = place.geometry.location.lat();\n                            const longitude = place.geometry.location.lng();\n                            setMapSearchX(latitude);\n                            setMapSearchY(longitude);\n                            // setMapCenter([latitude, longitude]); // Update map center\n                            // setMapZoom(10);\n                          }\n                        }}\n                        defaultValue={searchCity}\n                        types={[\"city\"]}\n                        language=\"en\"\n                      />\n                    </div>\n                  </div>\n                </div>\n                <div className=\"flex flex-col  \">\n                  <div className=\" w-full  md:pr-1 my-1\">\n                    <div className=\"text-[#000000bf] font-bold text-xs  mb-1\">\n                      Range ({range.max} km)\n                    </div>\n                    <div>\n                      <input\n                        type=\"range\"\n                        className=\" outline-none border border-[#F1F3FF] px-3 py-2 w-full rounded text-sm\"\n                        value={range.max}\n                        onChange={handleMaxChange}\n                        min=\"0\"\n                        max=\"500\"\n                      />\n                    </div>\n                  </div>\n                </div>\n\n                <div className=\"flex flex-row  \">\n                  <button\n                    onClick={() => {\n                      // setSearchCity(\"\");\n                      // setSearchName(\"\");\n                      // setSearchCountry(\"\");\n                      // setSearchCountrySelect(\"\");\n                      // setSearchType(\"\");\n                      // setMapCenter([0, 0]); // Update map center\n                      // setMapZoom(2);\n\n                      dispatch(\n                        providersList(\n                          isMaps ? \"0\" : \"1\",\n                          searchName,\n                          searchType,\n                          searchCity,\n                          searchCountry,\n                          range.max,\n                          mapSearchX,\n                          mapSearchY\n                        )\n                      );\n                      if (searchCity !== \"\") {\n                        setMapCenter([mapSearchX, mapSearchY]); // Update map center\n                        setMapZoom(10);\n                      }\n\n                      setIsOpenFilter(false);\n                    }}\n                    className=\"flex flex-row items-center bg-primary mx-2 text-white px-3 py-1 text-sm rounded\"\n                  >\n                    <svg\n                      xmlns=\"http://www.w3.org/2000/svg\"\n                      fill=\"none\"\n                      viewBox=\"0 0 24 24\"\n                      stroke-width=\"1.5\"\n                      stroke=\"currentColor\"\n                      class=\"size-4 mx-1\"\n                    >\n                      <path\n                        stroke-linecap=\"round\"\n                        stroke-linejoin=\"round\"\n                        d=\"m4.5 12.75 6 6 9-13.5\"\n                      />\n                    </svg>\n\n                    <div> Filter</div>\n                  </button>\n                  <button\n                    onClick={() => {\n                      setSearchCity(\"\");\n                      setSearchName(\"\");\n                      setSearchCountry(\"\");\n                      setSearchCountrySelect(\"\");\n                      setSearchType(\"\");\n                      setMapCenter([0, 0]); // Update map center\n                      setMapZoom(2);\n                      dispatch(\n                        providersList(\n                          isMaps ? \"0\" : \"1\",\n                          \"\",\n                          \"\",\n                          \"\",\n                          \"\",\n                          400,\n                          \"\",\n                          \"\"\n                        )\n                      );\n                      setIsOpenFilter(false);\n                    }}\n                    className=\"flex flex-row items-center bg-danger mx-2 text-white px-3 py-1 text-sm rounded\"\n                  >\n                    <svg\n                      xmlns=\"http://www.w3.org/2000/svg\"\n                      fill=\"none\"\n                      viewBox=\"0 0 24 24\"\n                      stroke-width=\"1.5\"\n                      stroke=\"currentColor\"\n                      className=\"size-4 mx-1\"\n                    >\n                      <path\n                        stroke-linecap=\"round\"\n                        stroke-linejoin=\"round\"\n                        d=\"M16.023 9.348h4.992v-.001M2.985 19.644v-4.992m0 0h4.992m-4.993 0 3.181 3.183a8.25 8.25 0 0 0 13.803-3.7M4.031 9.865a8.25 8.25 0 0 1 13.803-3.7l3.181 3.182m0-4.991v4.99\"\n                      />\n                    </svg>\n                    <div> Reset</div>\n                  </button>\n                </div>\n              </div>\n            </div>\n          </div>\n        ) : null}\n\n        <div className=\"rounded-sm border border-stroke bg-white px-5 pt-6 pb-2.5 shadow-default \">\n          <div className=\" mx-auto flex flex-col\">\n            {isMaps ? (\n              <div className=\" relative\">\n                <MapContainer\n                  center={mapCenter}\n                  zoom={mapZoom}\n                  style={{ height: \"500px\", width: \"100%\" }}\n                  whenCreated={(mapInstance) => (mapRef.current = mapInstance)} // Store map instance\n                >\n                  <UpdateMapView center={mapCenter} zoom={mapZoom} />\n                  <TileLayer\n                    url=\"https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png\"\n                    attribution='&copy; <a href=\"https://www.openstreetmap.org/copyright\">OpenStreetMap</a> contributors'\n                  />\n                  {providers\n                    ?.filter(\n                      (provider) => provider.location_x && provider.location_y\n                    )\n                    .map((provider, index) => (\n                      <Marker\n                        eventHandlers={{\n                          click: () => {\n                            setIsOpenMap(true);\n                            setProviderMapSelect(provider);\n                          },\n                        }}\n                        key={index}\n                        position={[provider.location_x, provider.location_y]}\n                      >\n                        <Popup>\n                          {provider.full_name}\n                          <br />\n                        </Popup>\n                      </Marker>\n                    ))}\n                </MapContainer>\n                {/* <MapContainer\n                  center={[0, 0]}\n                  zoom={2}\n                  style={{ height: \"500px\", width: \"100%\" }}\n                  className=\"\"\n                >\n                  <TileLayer\n                    url=\"https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png\"\n                    attribution='&copy; <a href=\"https://www.openstreetmap.org/copyright\">OpenStreetMap</a> contributors'\n                  />\n                  {providers\n                    ?.filter(\n                      (provider) => provider.location_x && provider.location_y\n                    )\n                    .map((provider, index) => (\n                      <Marker\n                        eventHandlers={{\n                          click: () => {\n                            setIsOpenMap(true);\n                            setProviderMapSelect(provider);\n                          }, // Trigger onClick event\n                        }}\n                        key={index}\n                        position={[provider.location_x, provider.location_y]}\n                      >\n                        <Popup>\n                          {provider.full_name}\n                          <br />\n                        </Popup>\n                      </Marker>\n                    ))}\n                </MapContainer> */}\n                {isOpenMap ? (\n                  <div className=\" absolute top-0 left-0 z-99999  p-2 md:w-1/3 w-2/3 h-full \">\n                    <div className=\"bg-white shadow-1 w-full h-full\">\n                      <div className=\" p-3 float-right \">\n                        <button\n                          onClick={() => {\n                            setIsOpenMap(false);\n                            setProviderMapSelect(null);\n                          }}\n                          className=\"rounded-full p-1 bg-danger shadow-1 text-white flex items-center w-max \"\n                        >\n                          <svg\n                            xmlns=\"http://www.w3.org/2000/svg\"\n                            fill=\"none\"\n                            viewBox=\"0 0 24 24\"\n                            stroke-width=\"1.5\"\n                            stroke=\"currentColor\"\n                            className=\"size-4\"\n                          >\n                            <path\n                              stroke-linecap=\"round\"\n                              stroke-linejoin=\"round\"\n                              d=\"M6 18 18 6M6 6l12 12\"\n                            />\n                          </svg>\n                        </button>\n                      </div>\n                      <div className=\"pt-10 py-4 px-3\">\n                        {providerMapSelect && (\n                          <div>\n                            <div className=\"flex flex-row items-center text-xs my-3\">\n                              <div>\n                                <svg\n                                  xmlns=\"http://www.w3.org/2000/svg\"\n                                  fill=\"none\"\n                                  viewBox=\"0 0 24 24\"\n                                  stroke-width=\"1.5\"\n                                  stroke=\"currentColor\"\n                                  className=\"size-4\"\n                                >\n                                  <path\n                                    stroke-linecap=\"round\"\n                                    stroke-linejoin=\"round\"\n                                    d=\"M11.42 15.17 17.25 21A2.652 2.652 0 0 0 21 17.25l-5.877-5.877M11.42 15.17l2.496-3.03c.317-.384.74-.626 1.208-.766M11.42 15.17l-4.655 5.653a2.548 2.548 0 1 1-3.586-3.586l6.837-5.63m5.108-.233c.55-.164 1.163-.188 1.743-.14a4.5 4.5 0 0 0 4.486-6.336l-3.276 3.277a3.004 3.004 0 0 1-2.25-2.25l3.276-3.276a4.5 4.5 0 0 0-6.336 4.486c.091 1.076-.071 2.264-.904 2.95l-.102.085m-1.745 1.437L5.909 7.5H4.5L2.25 3.75l1.5-1.5L7.5 4.5v1.409l4.26 4.26m-1.745 1.437 1.745-1.437m6.615 8.206L15.75 15.75M4.867 19.125h.008v.008h-.008v-.008Z\"\n                                  />\n                                </svg>\n                              </div>\n                              <div className=\"flex-1 px-2\">\n                                {providerMapSelect.services && providerMapSelect.services.length > 0 ? (\n                                  providerMapSelect.services.map(\n                                    (service, index) => (\n                                      <div key={index} className=\"my-1\">\n                                        -{\" \"}\n                                        {service.service_type +\n                                          (service.service_specialist !== \"\" &&\n                                          service.service_specialist !== null\n                                            ? \": \" + service.service_specialist\n                                            : \"\")}\n                                      </div>\n                                    )\n                                  )\n                                ) : (\n                                  <div className=\"my-1\">No services available</div>\n                                )}\n                                {/* {providerMapSelect.service_type ?? \"---\"}\n                                {providerMapSelect.service_type ===\n                                  \"Specialists\" &&\n                                providerMapSelect.service_specialist\n                                  ? \" : \" + providerMapSelect.service_specialist\n                                  : \"\"} */}\n                              </div>\n                            </div>\n                            {/*  */}\n\n                            <div className=\"flex flex-row items-center text-xs my-3\">\n                              <div>\n                                <svg\n                                  xmlns=\"http://www.w3.org/2000/svg\"\n                                  fill=\"none\"\n                                  viewBox=\"0 0 24 24\"\n                                  stroke-width=\"1.5\"\n                                  stroke=\"currentColor\"\n                                  className=\"size-4\"\n                                >\n                                  <path\n                                    stroke-linecap=\"round\"\n                                    stroke-linejoin=\"round\"\n                                    d=\"M15.75 6a3.75 3.75 0 1 1-7.5 0 3.75 3.75 0 0 1 7.5 0ZM4.501 20.118a7.5 7.5 0 0 1 14.998 0A17.933 17.933 0 0 1 12 21.75c-2.676 0-5.216-.584-7.499-1.632Z\"\n                                  />\n                                </svg>\n                              </div>\n                              <div className=\"flex-1 px-2\">\n                                {providerMapSelect.full_name ?? \"---\"}\n                              </div>\n                            </div>\n                            {/*  */}\n                            {providerMapSelect.provider_infos && providerMapSelect.provider_infos.length > 0 ? (\n                              providerMapSelect.provider_infos.map(\n                                (item, index) => (\n                                  <div key={index}>\n                                    <div className=\"flex flex-row items-center text-xs my-3\">\n                                      <div>\n                                        {[\n                                          \"Main Phone\",\n                                          \"Whatsapp\",\n                                          \"Billing Phone\",\n                                        ].includes(item.info_type) ? (\n                                          <svg\n                                            xmlns=\"http://www.w3.org/2000/svg\"\n                                            fill=\"none\"\n                                            viewBox=\"0 0 24 24\"\n                                            stroke-width=\"1.5\"\n                                            stroke=\"currentColor\"\n                                            className=\"size-4\"\n                                          >\n                                            <path\n                                              stroke-linecap=\"round\"\n                                              stroke-linejoin=\"round\"\n                                              d=\"M2.25 6.75c0 8.284 6.716 15 15 15h2.25a2.25 2.25 0 0 0 2.25-2.25v-1.372c0-.516-.351-.966-.852-1.091l-4.423-1.106c-.44-.11-.902.055-1.173.417l-.97 1.293c-.282.376-.769.542-1.21.38a12.035 12.035 0 0 1-7.143-7.143c-.162-.441.004-.928.38-1.21l1.293-.97c.363-.271.527-.734.417-1.173L6.963 3.102a1.125 1.125 0 0 0-1.091-.852H4.5A2.25 2.25 0 0 0 2.25 4.5v2.25Z\"\n                                            />\n                                          </svg>\n                                        ) : (\n                                          <svg\n                                            xmlns=\"http://www.w3.org/2000/svg\"\n                                            fill=\"none\"\n                                            viewBox=\"0 0 24 24\"\n                                            stroke-width=\"1.5\"\n                                            stroke=\"currentColor\"\n                                            className=\"size-4\"\n                                          >\n                                            <path\n                                              stroke-linecap=\"round\"\n                                              stroke-linejoin=\"round\"\n                                              d=\"M21.75 6.75v10.5a2.25 2.25 0 0 1-2.25 2.25h-15a2.25 2.25 0 0 1-2.25-2.25V6.75m19.5 0A2.25 2.25 0 0 0 19.5 4.5h-15a2.25 2.25 0 0 0-2.25 2.25m19.5 0v.243a2.25 2.25 0 0 1-1.07 1.916l-7.5 4.615a2.25 2.25 0 0 1-2.36 0L3.32 8.91a2.25 2.25 0 0 1-1.07-1.916V6.75\"\n                                            />\n                                          </svg>\n                                        )}\n                                      </div>\n                                      <div className=\"flex-1 px-2\">\n                                        {item.info_type} : {item.info_value}\n                                      </div>\n                                    </div>\n                                  </div>\n                                )\n                              )\n                            ) : null}\n                            {/* <div className=\"flex flex-row items-center text-xs my-3\">\n                              <div>\n                                <svg\n                                  xmlns=\"http://www.w3.org/2000/svg\"\n                                  fill=\"none\"\n                                  viewBox=\"0 0 24 24\"\n                                  stroke-width=\"1.5\"\n                                  stroke=\"currentColor\"\n                                  className=\"size-5\"\n                                >\n                                  <path\n                                    stroke-linecap=\"round\"\n                                    stroke-linejoin=\"round\"\n                                    d=\"M21.75 6.75v10.5a2.25 2.25 0 0 1-2.25 2.25h-15a2.25 2.25 0 0 1-2.25-2.25V6.75m19.5 0A2.25 2.25 0 0 0 19.5 4.5h-15a2.25 2.25 0 0 0-2.25 2.25m19.5 0v.243a2.25 2.25 0 0 1-1.07 1.916l-7.5 4.615a2.25 2.25 0 0 1-2.36 0L3.32 8.91a2.25 2.25 0 0 1-1.07-1.916V6.75\"\n                                  />\n                                </svg>\n                              </div>\n                              <div className=\"flex-1 px-2\">\n                                {providerMapSelect.email ?? \"---\"}\n                              </div>\n                            </div> */}\n                            {/*  */}\n                            {/* <div className=\"flex flex-row items-center text-xs my-3\">\n                              <div>\n                                <svg\n                                  xmlns=\"http://www.w3.org/2000/svg\"\n                                  fill=\"none\"\n                                  viewBox=\"0 0 24 24\"\n                                  stroke-width=\"1.5\"\n                                  stroke=\"currentColor\"\n                                  className=\"size-5\"\n                                >\n                                  <path\n                                    stroke-linecap=\"round\"\n                                    stroke-linejoin=\"round\"\n                                    d=\"M2.25 6.75c0 8.284 6.716 15 15 15h2.25a2.25 2.25 0 0 0 2.25-2.25v-1.372c0-.516-.351-.966-.852-1.091l-4.423-1.106c-.44-.11-.902.055-1.173.417l-.97 1.293c-.282.376-.769.542-1.21.38a12.035 12.035 0 0 1-7.143-7.143c-.162-.441.004-.928.38-1.21l1.293-.97c.363-.271.527-.734.417-1.173L6.963 3.102a1.125 1.125 0 0 0-1.091-.852H4.5A2.25 2.25 0 0 0 2.25 4.5v2.25Z\"\n                                  />\n                                </svg>\n                              </div>\n                              <div className=\"flex-1 px-2\">\n                                {providerMapSelect.phone ?? \"---\"}\n                              </div>\n                            </div> */}\n                            {/*  */}\n                            <div className=\"flex flex-row items-center text-xs my-3\">\n                              <div>\n                                <svg\n                                  xmlns=\"http://www.w3.org/2000/svg\"\n                                  fill=\"none\"\n                                  viewBox=\"0 0 24 24\"\n                                  stroke-width=\"1.5\"\n                                  stroke=\"currentColor\"\n                                  className=\"size-4\"\n                                >\n                                  <path\n                                    stroke-linecap=\"round\"\n                                    stroke-linejoin=\"round\"\n                                    d=\"M15 10.5a3 3 0 1 1-6 0 3 3 0 0 1 6 0Z\"\n                                  />\n                                  <path\n                                    stroke-linecap=\"round\"\n                                    stroke-linejoin=\"round\"\n                                    d=\"M19.5 10.5c0 7.142-7.5 11.25-7.5 11.25S4.5 17.642 4.5 10.5a7.5 7.5 0 1 1 15 0Z\"\n                                  />\n                                </svg>\n                              </div>\n                              <div className=\"flex-1 px-2\">\n                                {providerMapSelect.address ?? \"---\"}\n                              </div>\n                            </div>\n                            <div className=\"flex flex-row items-center text-xs my-3\">\n                              <div>\n                                <svg\n                                  xmlns=\"http://www.w3.org/2000/svg\"\n                                  fill=\"none\"\n                                  viewBox=\"0 0 24 24\"\n                                  stroke-width=\"1.5\"\n                                  stroke=\"currentColor\"\n                                  class=\"size-4\"\n                                >\n                                  <path\n                                    stroke-linecap=\"round\"\n                                    stroke-linejoin=\"round\"\n                                    d=\"M2.25 8.25h19.5M2.25 9h19.5m-16.5 5.25h6m-6 2.25h3m-3.75 3h15a2.25 2.25 0 0 0 2.25-2.25V6.75A2.25 2.25 0 0 0 19.5 4.5h-15a2.25 2.25 0 0 0-2.25 2.25v10.5A2.25 2.25 0 0 0 4.5 19.5Z\"\n                                  />\n                                </svg>\n                              </div>\n                              <div className=\"flex-1 px-2\">\n                                {providerMapSelect.payment_method ?? \"---\"}\n                              </div>\n                            </div>\n                            <p className=\"text-black  text-xs w-max flex flex-row my-4 \">\n                              <Link\n                                className=\"mx-1 update-class \"\n                                to={\n                                  \"/providers-list/edit/\" + providerMapSelect.id\n                                }\n                              >\n                                <svg\n                                  xmlns=\"http://www.w3.org/2000/svg\"\n                                  fill=\"none\"\n                                  viewBox=\"0 0 24 24\"\n                                  strokeWidth=\"1.5\"\n                                  stroke=\"currentColor\"\n                                  className=\"w-8 h-5 bg-primary rounded p-1 text-white text-center text-xs\"\n                                >\n                                  <path\n                                    strokeLinecap=\"round\"\n                                    strokeLinejoin=\"round\"\n                                    d=\"m16.862 4.487 1.687-1.688a1.875 1.875 0 1 1 2.652 2.652L10.582 16.07a4.5 4.5 0 0 1-1.897 1.13L6 18l.8-2.685a4.5 4.5 0 0 1 1.13-1.897l8.932-8.931Zm0 0L19.5 7.125M18 14v4.75A2.25 2.25 0 0 1 15.75 21H5.25A2.25 2.25 0 0 1 3 18.75V8.25A2.25 2.25 0 0 1 5.25 6H10\"\n                                  />\n                                </svg>\n                              </Link>\n                              <div\n                                onClick={() => {\n                                  setEventType(\"delete\");\n                                  setProviderId(providerMapSelect.id);\n                                  setIsDelete(true);\n                                }}\n                                className=\"mx-1 delete-class cursor-pointer\"\n                              >\n                                <svg\n                                  xmlns=\"http://www.w3.org/2000/svg\"\n                                  fill=\"none\"\n                                  viewBox=\"0 0 24 24\"\n                                  stroke-width=\"1.5\"\n                                  stroke=\"currentColor\"\n                                  className=\"w-8 h-5 bg-danger rounded p-1 text-white text-center text-xs\"\n                                >\n                                  <path\n                                    stroke-linecap=\"round\"\n                                    stroke-linejoin=\"round\"\n                                    d=\"m20.25 7.5-.625 10.632a2.25 2.25 0 0 1-2.247 2.118H6.622a2.25 2.25 0 0 1-2.247-2.118L3.75 7.5m6 4.125 2.25 2.25m0 0 2.25 2.25M12 13.875l2.25-2.25M12 13.875l-2.25 2.25M3.375 7.5h17.25c.621 0 1.125-.504 1.125-1.125v-1.5c0-.621-.504-1.125-1.125-1.125H3.375c-.621 0-1.125.504-1.125 1.125v1.5c0 .621.504 1.125 1.125 1.125Z\"\n                                  />\n                                </svg>\n                              </div>\n                              <Link\n                                className=\"mx-1 profile-class\"\n                                to={\n                                  \"/providers-list/profile/\" +\n                                  providerMapSelect.id\n                                }\n                              >\n                                <svg\n                                  xmlns=\"http://www.w3.org/2000/svg\"\n                                  fill=\"none\"\n                                  viewBox=\"0 0 24 24\"\n                                  stroke-width=\"1.5\"\n                                  stroke=\"currentColor\"\n                                  className=\"w-5 h-5 bg-success rounded p-1 text-white text-center text-xs\"\n                                >\n                                  <path\n                                    stroke-linecap=\"round\"\n                                    stroke-linejoin=\"round\"\n                                    d=\"M8.25 6.75h12M8.25 12h12m-12 5.25h12M3.75 6.75h.007v.008H3.75V6.75Zm.375 0a.375.375 0 1 1-.75 0 .375.375 0 0 1 .75 0ZM3.75 12h.007v.008H3.75V12Zm.375 0a.375.375 0 1 1-.75 0 .375.375 0 0 1 .75 0Zm-.375 5.25h.007v.008H3.75v-.008Zm.375 0a.375.375 0 1 1-.75 0 .375.375 0 0 1 .75 0Z\"\n                                  />\n                                </svg>\n                              </Link>\n                            </p>\n                          </div>\n                        )}\n                      </div>\n                    </div>\n                  </div>\n                ) : null}\n              </div>\n            ) : (\n              // <iframe\n              //   title=\"Providers List\"\n              //   src=\"https://www.google.com/maps/d/u/0/embed?mid=1KH5CWcxgH2OO_t1rr6OqMCS-pCVTaik&ehbc=2E312F\"\n              //   className=\"min-h-[500px] w-full\"\n              // ></iframe>\n              <div>\n                {loadingProviders ? (\n                  <Loader />\n                ) : errorProviders ? (\n                  <Alert type=\"error\" message={errorProviders} />\n                ) : (\n                  <div className=\"max-w-full overflow-x-auto \">\n                    <table className=\"w-full table-auto\">\n                      <thead>\n                        <tr className=\" bg-[#F3F5FB] text-left \">\n                          <th className=\"min-w-[30px] py-4 px-4 font-bold text-[#8E8E8E] text-xs w-max\">\n                            #\n                          </th>\n                          <th className=\"min-w-[60px] py-4 px-4 font-bold text-[#8E8E8E] text-xs w-max \">\n                            Provider\n                          </th>\n                          <th className=\"min-w-[60px] py-4 px-4 font-bold text-[#8E8E8E] text-xs w-max \">\n                            Services Type\n                          </th>\n                          {/* <th className=\"min-w-[120px] py-4 px-4 font-bold text-[#8E8E8E] text-xs w-max\">\n                            Email\n                          </th>\n                          <th className=\"min-w-[120px] py-4 px-4 font-bold text-[#8E8E8E] text-xs w-max\">\n                            Phone\n                          </th> */}\n                          <th className=\"min-w-[120px] py-4 px-4 font-bold text-[#8E8E8E] text-xs w-max\">\n                            Country\n                          </th>\n                          <th className=\"min-w-[120px] py-4 px-4 font-bold text-[#8E8E8E] text-xs w-max\">\n                            City\n                          </th>\n                          <th className=\"min-w-[120px] py-4 px-4 font-bold text-[#8E8E8E] text-xs w-max\">\n                            Address\n                          </th>\n\n                          <th className=\"py-4 px-4 font-bold text-[#8E8E8E] text-xs w-max\">\n                            Operation\n                          </th>\n                        </tr>\n                      </thead>\n                      {/*  */}\n                      <tbody>\n                        {providers?.map((item, index) => (\n                          <tr key={index}>\n                            <td className=\" py-3 px-4 max-w-[200px]  \">\n                              <p className=\"text-black  text-xs truncate  \">\n                                {item.id}\n                              </p>\n                            </td>\n                            <td className=\" py-3 px-4 max-w-[200px]  \">\n                              <p className=\"text-black  text-xs truncate  \">\n                                <a href={\"/providers-list/profile/\" + item.id}>\n                                  <p className=\"text-black  text-xs w-max  \">\n                                    {item.full_name}\n                                  </p>\n                                </a>\n                              </p>\n                            </td>\n                            <td className=\" py-3 px-4 max-w-[200px]  \">\n                              <p className=\"text-black  text-xs truncate  \">\n                                <a href={\"/providers-list/profile/\" + item.id}>\n                                  {item.services?.length || 0}{\" \"}\n                                  {(item.services?.length || 0) > 1\n                                    ? \"Services\"\n                                    : \"Service\"}\n                                </a>\n                                {/* {item.service_type}\n                                {item.service_type === \"Specialists\" &&\n                                item.service_specialist\n                                  ? \" : \" + item.service_specialist\n                                  : \"\"} */}\n                              </p>\n                            </td>\n                            {/* <td className=\" py-3 px-4 max-w-[200px]  \">\n                              <p className=\"text-black  text-xs truncate  \">\n                                {item.email ?? \"----\"}\n                              </p>\n                            </td>\n                            <td className=\" py-3 px-4 max-w-[200px]  \">\n                              <p className=\"text-black  text-xs truncate  \">\n                                {item.phone ?? \"----\"}\n                              </p>\n                            </td> */}\n                            <td className=\" py-3 px-4 max-w-[200px]  \">\n                              <p className=\"text-black  text-xs truncate  \">\n                                {item.country ?? \"----\"}\n                              </p>\n                            </td>\n                            <td className=\" py-3 px-4 max-w-[200px]  \">\n                              <p className=\"text-black  text-xs truncate  \">\n                                {item.city ?? \"----\"}\n                              </p>\n                            </td>\n                            <td className=\" py-3 px-4 max-w-[200px]  \">\n                              <p className=\"text-black  text-xs truncate  \">\n                                {item.address}\n                              </p>\n                            </td>\n\n                            <td className=\" py-3 px-4 min-w-[120px]  \">\n                              <p className=\"text-black  text-xs w-max flex flex-row  \">\n                                <Link\n                                  className=\"mx-1 update-class\"\n                                  to={\"/providers-list/edit/\" + item.id}\n                                >\n                                  <svg\n                                    xmlns=\"http://www.w3.org/2000/svg\"\n                                    fill=\"none\"\n                                    viewBox=\"0 0 24 24\"\n                                    strokeWidth=\"1.5\"\n                                    stroke=\"currentColor\"\n                                    className=\"w-5 h-5 bg-primary rounded p-1 text-white text-center text-xs\"\n                                  >\n                                    <path\n                                      strokeLinecap=\"round\"\n                                      strokeLinejoin=\"round\"\n                                      d=\"m16.862 4.487 1.687-1.688a1.875 1.875 0 1 1 2.652 2.652L10.582 16.07a4.5 4.5 0 0 1-1.897 1.13L6 18l.8-2.685a4.5 4.5 0 0 1 1.13-1.897l8.932-8.931Zm0 0L19.5 7.125M18 14v4.75A2.25 2.25 0 0 1 15.75 21H5.25A2.25 2.25 0 0 1 3 18.75V8.25A2.25 2.25 0 0 1 5.25 6H10\"\n                                    />\n                                  </svg>\n                                </Link>\n                                <div\n                                  onClick={() => {\n                                    setEventType(\"delete\");\n                                    setProviderId(item.id);\n                                    setIsDelete(true);\n                                  }}\n                                  className=\"mx-1 delete-class cursor-pointer\"\n                                >\n                                  <svg\n                                    xmlns=\"http://www.w3.org/2000/svg\"\n                                    fill=\"none\"\n                                    viewBox=\"0 0 24 24\"\n                                    stroke-width=\"1.5\"\n                                    stroke=\"currentColor\"\n                                    className=\"w-5 h-5 bg-danger rounded p-1 text-white text-center text-xs\"\n                                  >\n                                    <path\n                                      stroke-linecap=\"round\"\n                                      stroke-linejoin=\"round\"\n                                      d=\"m20.25 7.5-.625 10.632a2.25 2.25 0 0 1-2.247 2.118H6.622a2.25 2.25 0 0 1-2.247-2.118L3.75 7.5m6 4.125 2.25 2.25m0 0 2.25 2.25M12 13.875l2.25-2.25M12 13.875l-2.25 2.25M3.375 7.5h17.25c.621 0 1.125-.504 1.125-1.125v-1.5c0-.621-.504-1.125-1.125-1.125H3.375c-.621 0-1.125.504-1.125 1.125v1.5c0 .621.504 1.125 1.125 1.125Z\"\n                                    />\n                                  </svg>\n                                </div>\n                                <Link\n                                  className=\"mx-1 profile-class\"\n                                  to={\"/providers-list/profile/\" + item.id}\n                                >\n                                  <svg\n                                    xmlns=\"http://www.w3.org/2000/svg\"\n                                    fill=\"none\"\n                                    viewBox=\"0 0 24 24\"\n                                    stroke-width=\"1.5\"\n                                    stroke=\"currentColor\"\n                                    className=\"w-5 h-5 bg-success rounded p-1 text-white text-center text-xs\"\n                                  >\n                                    <path\n                                      stroke-linecap=\"round\"\n                                      stroke-linejoin=\"round\"\n                                      d=\"M8.25 6.75h12M8.25 12h12m-12 5.25h12M3.75 6.75h.007v.008H3.75V6.75Zm.375 0a.375.375 0 1 1-.75 0 .375.375 0 0 1 .75 0ZM3.75 12h.007v.008H3.75V12Zm.375 0a.375.375 0 1 1-.75 0 .375.375 0 0 1 .75 0Zm-.375 5.25h.007v.008H3.75v-.008Zm.375 0a.375.375 0 1 1-.75 0 .375.375 0 0 1 .75 0Z\"\n                                    />\n                                  </svg>\n                                </Link>\n                              </p>\n                            </td>\n                          </tr>\n                        ))}\n                      </tbody>\n                    </table>\n                  </div>\n                )}\n                <div className=\"\">\n                  <Paginate\n                    route={`/providers-list?searchname=${searchName}&searchtype=${searchType}&searchcity=${searchCity}&searchcountry=${searchCountry}&`}\n                    search={\"\"}\n                    page={page}\n                    pages={pages}\n                  />\n                </div>\n              </div>\n            )}\n          </div>\n          <div className=\"my-5\"></div>\n        </div>\n        <ConfirmationModal\n          isOpen={isDelete}\n          message={\n            eventType === \"delete\"\n              ? \"Are you sure you want to delete this Provider?\"\n              : \"Are you sure ?\"\n          }\n          onConfirm={async () => {\n            if (eventType === \"cancel\") {\n              setIsDelete(false);\n              setEventType(\"\");\n              setLoadEvent(false);\n            } else if (eventType === \"delete\" && providerId !== \"\") {\n              setLoadEvent(true);\n              dispatch(deleteProvider(providerId));\n              setIsDelete(false);\n              setEventType(\"\");\n              setLoadEvent(false);\n            } else {\n              setIsDelete(false);\n              setEventType(\"\");\n              setLoadEvent(false);\n            }\n          }}\n          onCancel={() => {\n            setIsDelete(false);\n            setEventType(\"\");\n            setLoadEvent(false);\n          }}\n          loadEvent={loadEvent}\n        />\n        <div className=\"grid md:grid-cols-2 w-full container mt-5\"></div>\n      </div>\n    </DefaultLayout>\n  );\n}\n\nexport default ProvidersMapScreen;\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,EAAIC,SAAS,CAAEC,MAAM,CAAEC,QAAQ,KAAQ,OAAO,CAC1D,OAASC,WAAW,CAAEC,WAAW,KAAQ,aAAa,CACtD,OACEC,IAAI,CACJC,WAAW,CACXC,WAAW,CACXC,eAAe,KACV,kBAAkB,CACzB,MAAO,CAAAC,aAAa,KAAM,6BAA6B,CACvD,OACEC,cAAc,CACdC,aAAa,KACR,qCAAqC,CAC5C,MAAO,CAAAC,MAAM,KAAM,yBAAyB,CAC5C,MAAO,CAAAC,KAAK,KAAM,wBAAwB,CAC1C,OAASC,YAAY,CAAEC,SAAS,CAAEC,MAAM,CAAEC,KAAK,CAAEC,MAAM,KAAQ,eAAe,CAC9E,MAAO,0BAA0B,CACjC,MAAO,CAAAC,CAAC,KAAM,SAAS,CACvB,MAAO,CAAAC,iBAAiB,KAAM,oCAAoC,CAClE,MAAO,CAAAC,MAAM,KAAM,cAAc,CACjC,OAASC,SAAS,CAAEC,WAAW,KAAQ,iBAAiB,CACxD,MAAO,CAAAC,eAAe,KAAM,2BAA2B,CACvD,MAAO,CAAAC,QAAQ,KAAM,2BAA2B,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAEjD,MAAO,CAAAV,CAAC,CAACW,IAAI,CAACC,OAAO,CAACC,SAAS,CAACC,WAAW,CAC3Cd,CAAC,CAACW,IAAI,CAACC,OAAO,CAACG,YAAY,CAAC,CAC1BC,aAAa,CACX,gEAAgE,CAClEC,OAAO,CAAE,6DAA6D,CACtEC,SAAS,CAAE,+DACb,CAAC,CAAC,CAEF,QAAS,CAAAC,kBAAkBA,CAAA,CAAG,KAAAC,qBAAA,CAAAC,qBAAA,CAAAC,qBAAA,CAC5B,KAAM,CAAAC,QAAQ,CAAGnC,WAAW,CAAC,CAAC,CAC9B,KAAM,CAAAoC,QAAQ,CAAGrC,WAAW,CAAC,CAAC,CAC9B,KAAM,CAACsC,YAAY,CAAC,CAAGpC,eAAe,CAAC,CAAC,CACxC,KAAM,CAAAqC,QAAQ,CAAG1C,WAAW,CAAC,CAAC,CAC9B,KAAM,CAAA2C,IAAI,CAAGF,YAAY,CAACG,GAAG,CAAC,MAAM,CAAC,EAAI,GAAG,CAE5C,KAAM,CAACC,MAAM,CAAEC,SAAS,CAAC,CAAG/C,QAAQ,CAAC,KAAK,CAAC,CAE3C,KAAM,CAACgD,iBAAiB,CAAEC,oBAAoB,CAAC,CAAGjD,QAAQ,CAAC,IAAI,CAAC,CAChE,KAAM,CAACkD,SAAS,CAAEC,YAAY,CAAC,CAAGnD,QAAQ,CAAC,KAAK,CAAC,CAEjD,KAAM,CAACoD,QAAQ,CAAEC,WAAW,CAAC,CAAGrD,QAAQ,CAAC,KAAK,CAAC,CAC/C,KAAM,CAACsD,SAAS,CAAEC,YAAY,CAAC,CAAGvD,QAAQ,CAAC,KAAK,CAAC,CACjD,KAAM,CAACwD,SAAS,CAAEC,YAAY,CAAC,CAAGzD,QAAQ,CAAC,EAAE,CAAC,CAC9C,KAAM,CAAC0D,UAAU,CAAEC,aAAa,CAAC,CAAG3D,QAAQ,CAAC,EAAE,CAAC,CAEhD,KAAM,CAAC4D,YAAY,CAAEC,eAAe,CAAC,CAAG7D,QAAQ,CAAC,KAAK,CAAC,CAEvD,KAAM,CAAC8D,UAAU,CAAEC,aAAa,CAAC,CAAG/D,QAAQ,CAC1C0C,YAAY,CAACG,GAAG,CAAC,YAAY,CAAC,EAAI,EACpC,CAAC,CACD,KAAM,CAACmB,UAAU,CAAEC,aAAa,CAAC,CAAGjE,QAAQ,CAC1C0C,YAAY,CAACG,GAAG,CAAC,YAAY,CAAC,EAAI,EACpC,CAAC,CACD,KAAM,CAACqB,UAAU,CAAEC,aAAa,CAAC,CAAGnE,QAAQ,CAC1C0C,YAAY,CAACG,GAAG,CAAC,YAAY,CAAC,EAAI,EACpC,CAAC,CACD,KAAM,CAACuB,mBAAmB,CAAEC,sBAAsB,CAAC,CAAGrE,QAAQ,CAAC,EAAE,CAAC,CAClE,KAAM,CAACsE,aAAa,CAAEC,gBAAgB,CAAC,CAAGvE,QAAQ,CAChD0C,YAAY,CAACG,GAAG,CAAC,eAAe,CAAC,EAAI,EACvC,CAAC,CAED,KAAM,CAAC2B,KAAK,CAAEC,QAAQ,CAAC,CAAGzE,QAAQ,CAAC,CAAE0E,GAAG,CAAE,CAAC,CAAEC,GAAG,CAAE,CAAE,CAAC,CAAC,CAEtD,KAAM,CAAAC,SAAS,CAAG1E,WAAW,CAAE2E,KAAK,EAAKA,KAAK,CAACD,SAAS,CAAC,CACzD,KAAM,CAAEE,QAAS,CAAC,CAAGF,SAAS,CAE9B,KAAM,CAAAG,aAAa,CAAG7E,WAAW,CAAE2E,KAAK,EAAKA,KAAK,CAACG,YAAY,CAAC,CAChE,KAAM,CAAEC,SAAS,CAAEC,gBAAgB,CAAEC,cAAc,CAAEC,KAAM,CAAC,CAAGL,aAAa,CAE5E,KAAM,CAAAM,cAAc,CAAGnF,WAAW,CAAE2E,KAAK,EAAKA,KAAK,CAACrE,cAAc,CAAC,CACnE,KAAM,CAAE8E,qBAAqB,CAAEC,mBAAmB,CAAEC,qBAAsB,CAAC,CACzEH,cAAc,CAEhB,KAAM,CAAAI,QAAQ,CAAG,GAAG,CAEpB3F,SAAS,CAAC,IAAM,CACd,GAAI,CAACgF,QAAQ,CAAE,CACbtC,QAAQ,CAACiD,QAAQ,CAAC,CACpB,CAAC,IAAM,CACL9C,QAAQ,CACNlC,aAAa,CACXqC,MAAM,CAAG,GAAG,CAAGF,IAAI,CACnBkB,UAAU,CACVI,UAAU,CACVF,UAAU,CACVM,aAAa,CACbE,KAAK,CAACG,GACR,CACF,CAAC,CACH,CACF,CAAC,CAAE,CACDnC,QAAQ,CACRsC,QAAQ,CACRnC,QAAQ,CACR;AACA;AACA;AACA;AACAC,IAAI,CACL,CAAC,CAEF9C,SAAS,CAAC,IAAM,CACd,GAAI0F,qBAAqB,CAAE,CACzB7C,QAAQ,CACNlC,aAAa,CACXqC,MAAM,CAAG,GAAG,CAAG,CAAC,CAChBgB,UAAU,CACVI,UAAU,CACVF,UAAU,CACVM,aAAa,CACbE,KAAK,CAACG,GAAG,CACT,EAAE,CACF,EACF,CACF,CAAC,CACDxB,YAAY,CAAC,KAAK,CAAC,CACnBF,oBAAoB,CAAC,IAAI,CAAC,CAC5B,CACF,CAAC,CAAE,CACDuC,qBACA;AACA;AACA;AACA;AAAA,CACD,CAAC,CAEF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AAEA,KAAM,CAACE,UAAU,CAAEC,aAAa,CAAC,CAAG3F,QAAQ,CAAC,CAAC,CAAC,CAC/C,KAAM,CAAC4F,UAAU,CAAEC,aAAa,CAAC,CAAG7F,QAAQ,CAAC,CAAC,CAAC,CAC/C,KAAM,CAAC8F,SAAS,CAAEC,YAAY,CAAC,CAAG/F,QAAQ,CAAC,CAAC,CAAC,CAAE,CAAC,CAAC,CAAC,CAClD,KAAM,CAACgG,OAAO,CAAEC,UAAU,CAAC,CAAGjG,QAAQ,CAAC,CAAC,CAAC,CAAE;AAC3C,KAAM,CAAAkG,MAAM,CAAGnG,MAAM,CAAC,IAAI,CAAC,CAE3B,KAAM,CAAAoG,aAAa,CAAGC,IAAA,EAAsB,IAArB,CAAEC,MAAM,CAAEC,IAAK,CAAC,CAAAF,IAAA,CACrC,KAAM,CAAAG,GAAG,CAAGvF,MAAM,CAAC,CAAC,CACpBuF,GAAG,CAACC,OAAO,CAACH,MAAM,CAAEC,IAAI,CAAC,CACzB,MAAO,KAAI,CACb,CAAC,CAED,KAAM,CAAAG,eAAe,CAAIC,CAAC,EAAK,CAC7B,KAAM,CAAAC,KAAK,CAAGC,QAAQ,CAACF,CAAC,CAACG,MAAM,CAACF,KAAK,CAAE,EAAE,CAAC,CAC1ClC,QAAQ,CAAEqC,IAAI,GAAM,CAAE,GAAGA,IAAI,CAAEpC,GAAG,CAAEiC,KAAM,CAAC,CAAC,CAAC,CAC/C,CAAC,CAED,KAAM,CAAAI,eAAe,CAAIL,CAAC,EAAK,CAC7B,KAAM,CAAAC,KAAK,CAAGC,QAAQ,CAACF,CAAC,CAACG,MAAM,CAACF,KAAK,CAAE,EAAE,CAAC,CAC1ClC,QAAQ,CAAEqC,IAAI,GAAM,CAAE,GAAGA,IAAI,CAAEnC,GAAG,CAAEgC,KAAM,CAAC,CAAC,CAAC,CAC/C,CAAC,CAED,mBACElF,IAAA,CAAClB,aAAa,EAAAyG,QAAA,cACZrF,KAAA,QAAAqF,QAAA,eACErF,KAAA,QAAKsF,SAAS,CAAC,yCAAyC,CAAAD,QAAA,eAEtDvF,IAAA,MAAGyF,IAAI,CAAC,YAAY,CAAAF,QAAA,cAClBrF,KAAA,QAAKsF,SAAS,CAAC,+CAA+C,CAAAD,QAAA,eAC5DvF,IAAA,QACE0F,KAAK,CAAC,4BAA4B,CAClCC,IAAI,CAAC,MAAM,CACXC,OAAO,CAAC,WAAW,CACnB,eAAa,KAAK,CAClBC,MAAM,CAAC,cAAc,CACrBL,SAAS,CAAC,SAAS,CAAAD,QAAA,cAEnBvF,IAAA,SACE8F,aAAa,CAAC,OAAO,CACrBC,cAAc,CAAC,OAAO,CACtBC,CAAC,CAAC,4OAA4O,CAC/O,CAAC,CACC,CAAC,cACNhG,IAAA,SAAMwF,SAAS,CAAC,MAAM,CAAAD,QAAA,CAAC,WAAS,CAAM,CAAC,EACpC,CAAC,CACL,CAAC,cACJvF,IAAA,SAAAuF,QAAA,cACEvF,IAAA,QACE0F,KAAK,CAAC,4BAA4B,CAClCC,IAAI,CAAC,MAAM,CACXC,OAAO,CAAC,WAAW,CACnB,eAAa,KAAK,CAClBC,MAAM,CAAC,cAAc,CACrBL,SAAS,CAAC,SAAS,CAAAD,QAAA,cAEnBvF,IAAA,SACE8F,aAAa,CAAC,OAAO,CACrBC,cAAc,CAAC,OAAO,CACtBC,CAAC,CAAC,2BAA2B,CAC9B,CAAC,CACC,CAAC,CACF,CAAC,cACPhG,IAAA,QAAKwF,SAAS,CAAC,EAAE,CAAAD,QAAA,CAAC,gBAAc,CAAK,CAAC,EACnC,CAAC,cAGNrF,KAAA,QAAKsF,SAAS,CAAC,mDAAmD,CAAAD,QAAA,eAChEvF,IAAA,QAAKwF,SAAS,CAAC,4BAA4B,CAAAD,QAAA,CAAC,gBAAc,CAAK,CAAC,cAEhErF,KAAA,QAAKsF,SAAS,CAAC,wCAAwC,CAAAD,QAAA,eACrDvF,IAAA,QAAKwF,SAAS,CAAC,OAAO,CAAAD,QAAA,cACpBvF,IAAA,WACEiG,OAAO,CAAEA,CAAA,GAAM,CACb7D,eAAe,CAAC,CAACD,YAAY,CAAC,CAChC,CAAE,CACFqD,SAAS,CAAC,0GAA0G,CAAAD,QAAA,cAEpHvF,IAAA,QACE0F,KAAK,CAAC,4BAA4B,CAClCC,IAAI,CAAC,MAAM,CACXC,OAAO,CAAC,WAAW,CACnB,eAAa,KAAK,CAClBC,MAAM,CAAC,cAAc,CACrBL,SAAS,CAAC,QAAQ,CAAAD,QAAA,cAElBvF,IAAA,SACE,iBAAe,OAAO,CACtB,kBAAgB,OAAO,CACvBgG,CAAC,CAAC,oNAAoN,CACvN,CAAC,CACC,CAAC,CACA,CAAC,CACN,CAAC,cACNhG,IAAA,QAAKwF,SAAS,CAAC,OAAO,CAAAD,QAAA,cACpBvF,IAAA,WACEiG,OAAO,CAAEA,CAAA,GAAM,CACb3E,SAAS,CAAC,CAACD,MAAM,CAAC,CAClBH,QAAQ,CACNlC,aAAa,CACX,CAACqC,MAAM,CAAG,GAAG,CAAG,GAAG,CACnBgB,UAAU,CACVI,UAAU,CACVF,UAAU,CACVM,aAAa,CACbE,KAAK,CAACG,GAAG,CACTe,UAAU,GAAK,CAAC,CAAG,EAAE,CAAGA,UAAU,CAClCE,UAAU,GAAK,CAAC,CAAG,EAAE,CAAGA,UAC1B,CACF,CAAC,CACH,CAAE,CACFqB,SAAS,CAAC,0GAA0G,CAAAD,QAAA,CAEnHlE,MAAM,cACLrB,IAAA,QACE0F,KAAK,CAAC,4BAA4B,CAClCC,IAAI,CAAC,MAAM,CACXC,OAAO,CAAC,WAAW,CACnB,eAAa,KAAK,CAClBC,MAAM,CAAC,cAAc,CACrBL,SAAS,CAAC,QAAQ,CAAAD,QAAA,cAElBvF,IAAA,SACE,iBAAe,OAAO,CACtB,kBAAgB,OAAO,CACvBgG,CAAC,CAAC,uHAAuH,CAC1H,CAAC,CACC,CAAC,cAENhG,IAAA,QACE0F,KAAK,CAAC,4BAA4B,CAClCC,IAAI,CAAC,MAAM,CACXC,OAAO,CAAC,WAAW,CACnB,eAAa,KAAK,CAClBC,MAAM,CAAC,cAAc,CACrBL,SAAS,CAAC,QAAQ,CAAAD,QAAA,cAElBvF,IAAA,SACE,iBAAe,OAAO,CACtB,kBAAgB,OAAO,CACvBgG,CAAC,CAAC,sUAAsU,CACzU,CAAC,CACC,CACN,CACK,CAAC,CACN,CAAC,cACN9F,KAAA,MACEuF,IAAI,CAAC,8BAA8B,CACnCD,SAAS,CAAC,wFAAwF,CAAAD,QAAA,eAElGvF,IAAA,QACE0F,KAAK,CAAC,4BAA4B,CAClCC,IAAI,CAAC,MAAM,CACXC,OAAO,CAAC,WAAW,CACnB,eAAa,KAAK,CAClBC,MAAM,CAAC,cAAc,CACrBL,SAAS,CAAC,aAAa,CAAAD,QAAA,cAEvBvF,IAAA,SACE,iBAAe,OAAO,CACtB,kBAAgB,OAAO,CACvBgG,CAAC,CAAC,wBAAwB,CAC3B,CAAC,CACC,CAAC,cAENhG,IAAA,QAAAuF,QAAA,CAAK,cAAY,CAAK,CAAC,EACtB,CAAC,EACD,CAAC,EACH,CAAC,CACLpD,YAAY,cACXnC,IAAA,QAAKwF,SAAS,CAAC,oGAAoG,CAAAD,QAAA,cACjHvF,IAAA,QAAKwF,SAAS,CAAC,8CAA8C,CAAAD,QAAA,cAC3DrF,KAAA,QAAKsF,SAAS,CAAC,qBAAqB,CAAAD,QAAA,eAElCrF,KAAA,QAAKsF,SAAS,CAAC,iBAAiB,CAAAD,QAAA,eAC9BvF,IAAA,QAAKwF,SAAS,CAAC,4BAA4B,CAAAD,QAAA,cACzCvF,IAAA,WACEwF,SAAS,CAAC,iEAAiE,CAC3ES,OAAO,CAAEA,CAAA,GAAM7D,eAAe,CAAC,KAAK,CAAE,CAAAmD,QAAA,cAEtCvF,IAAA,QACE0F,KAAK,CAAC,4BAA4B,CAClCC,IAAI,CAAC,MAAM,CACXC,OAAO,CAAC,WAAW,CACnB,eAAa,KAAK,CAClBC,MAAM,CAAC,cAAc,CACrBK,KAAK,CAAC,QAAQ,CAAAX,QAAA,cAEdvF,IAAA,SACE,iBAAe,OAAO,CACtB,kBAAgB,OAAO,CACvBgG,CAAC,CAAC,sBAAsB,CACzB,CAAC,CACC,CAAC,CACA,CAAC,CACN,CAAC,cACN9F,KAAA,QAAKsF,SAAS,CAAC,uBAAuB,CAAAD,QAAA,eACpCvF,IAAA,QAAKwF,SAAS,CAAC,0CAA0C,CAAAD,QAAA,CAAC,eAE1D,CAAK,CAAC,cACNvF,IAAA,QAAAuF,QAAA,cACEvF,IAAA,UACEwF,SAAS,CAAC,wEAAwE,CAClFW,IAAI,CAAC,MAAM,CACXC,WAAW,CAAC,mBAAmB,CAC/BlB,KAAK,CAAE7C,UAAW,CAClBgE,QAAQ,CAAGC,CAAC,EAAK,CACfhE,aAAa,CAACgE,CAAC,CAAClB,MAAM,CAACF,KAAK,CAAC,CAC7B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACF,CAAE,CACH,CAAC,CACC,CAAC,EACH,CAAC,EACH,CAAC,cAENlF,IAAA,QAAKwF,SAAS,CAAC,iBAAiB,CAAAD,QAAA,cAC9BrF,KAAA,QAAKsF,SAAS,CAAC,uBAAuB,CAAAD,QAAA,eACpCvF,IAAA,QAAKwF,SAAS,CAAC,0CAA0C,CAAAD,QAAA,CAAC,cAE1D,CAAK,CAAC,cACNvF,IAAA,QAAAuF,QAAA,cACErF,KAAA,WACEmG,QAAQ,CAAGC,CAAC,EAAK,CACf5D,aAAa,CAAC4D,CAAC,CAAClB,MAAM,CAACF,KAAK,CAAC,CAC7B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACF,CAAE,CACFA,KAAK,CAAEzC,UAAW,CAClB+C,SAAS,CAAC,wEAAwE,CAAAD,QAAA,eAElFvF,IAAA,WAAQkF,KAAK,CAAE,EAAG,CAAAK,QAAA,CAAC,eAAa,CAAQ,CAAC,CACxC3F,WAAW,SAAXA,WAAW,iBAAXA,WAAW,CAAEkF,GAAG,CAAC,CAACyB,IAAI,CAAEC,KAAK,gBAC5BxG,IAAA,WAAQkF,KAAK,CAAEqB,IAAK,CAAAhB,QAAA,CAAEgB,IAAI,CAAS,CACpC,CAAC,EACI,CAAC,CACN,CAAC,EACH,CAAC,CACH,CAAC,cACNvG,IAAA,QAAKwF,SAAS,CAAC,iBAAiB,CAAAD,QAAA,cAC9BrF,KAAA,QAAKsF,SAAS,CAAC,uBAAuB,CAAAD,QAAA,eACpCvF,IAAA,QAAKwF,SAAS,CAAC,0CAA0C,CAAAD,QAAA,CAAC,SAE1D,CAAK,CAAC,cACNvF,IAAA,QAAAuF,QAAA,cACEvF,IAAA,CAACN,MAAM,EACLwF,KAAK,CAAEvC,mBAAoB,CAC3B0D,QAAQ,CAAGI,MAAM,EAAK,CACpB3D,gBAAgB,CAAC2D,MAAM,CAACvB,KAAK,CAAC,CAC9BtC,sBAAsB,CAAC6D,MAAM,CAAC,CAChC,CAAE,CACFjB,SAAS,CAAC,uEAAuE,CACjFkB,OAAO,CAAE/G,SAAS,SAATA,SAAS,iBAATA,SAAS,CAAEmF,GAAG,CAAE6B,OAAO,GAAM,CACpCzB,KAAK,CAAEyB,OAAO,CAACC,KAAK,CACpBC,KAAK,cACH3G,KAAA,QACEsF,SAAS,CAAG,GACVmB,OAAO,CAACC,KAAK,GAAK,EAAE,CAAG,EAAE,CAAG,EAC7B,6BAA6B,CAAArB,QAAA,eAE9BvF,IAAA,SAAMwF,SAAS,CAAC,MAAM,CAAAD,QAAA,CAAEoB,OAAO,CAACG,IAAI,CAAO,CAAC,cAC5C9G,IAAA,SAAAuF,QAAA,CAAOoB,OAAO,CAACC,KAAK,CAAO,CAAC,EACzB,CAET,CAAC,CAAC,CAAE,CACJR,WAAW,CAAC,qBAAqB,CACjCW,YAAY,MACZC,MAAM,CAAE,CACNC,OAAO,CAAEA,CAACC,IAAI,CAAE9D,KAAK,IAAM,CACzB,GAAG8D,IAAI,CACPC,UAAU,CAAE,MAAM,CAClBC,MAAM,CAAE,mBAAmB,CAC3BC,SAAS,CAAEjE,KAAK,CAACkE,SAAS,CAAG,MAAM,CAAG,MAAM,CAC5C,SAAS,CAAE,CACTF,MAAM,CAAE,mBACV,CAAC,CACDG,QAAQ,CAAE,OACZ,CAAC,CAAC,CACFd,MAAM,CAAGS,IAAI,GAAM,CACjB,GAAGA,IAAI,CACPM,OAAO,CAAE,MAAM,CACfC,UAAU,CAAE,QACd,CAAC,CAAC,CACFC,WAAW,CAAGR,IAAI,GAAM,CACtB,GAAGA,IAAI,CACPM,OAAO,CAAE,MAAM,CACfC,UAAU,CAAE,QACd,CAAC,CACH,CAAE,CACH,CAAC,CACC,CAAC,EACH,CAAC,CACH,CAAC,cACNzH,IAAA,QAAKwF,SAAS,CAAC,iBAAiB,CAAAD,QAAA,cAC9BrF,KAAA,QAAKsF,SAAS,CAAC,uBAAuB,CAAAD,QAAA,eACpCvF,IAAA,QAAKwF,SAAS,CAAC,0CAA0C,CAAAD,QAAA,CAAC,MAE1D,CAAK,CAAC,cACNvF,IAAA,QAAAuF,QAAA,cACEvF,IAAA,CAACH,eAAe,EACd8H,MAAM,CAAC,yCAAyC,CAChDnC,SAAS,CAAG,yBACV,CAAC,EAAI,CAAC,CAAG,eAAe,CAAG,kBAC5B,oCAAoC,CACrCa,QAAQ,CAAGC,CAAC,EAAK,CACf9D,aAAa,CAAC8D,CAAC,CAAClB,MAAM,CAACF,KAAK,CAAC,CAC/B,CAAE,CACF0C,eAAe,CAAGC,KAAK,EAAK,CAC1B,GAAIA,KAAK,EAAIA,KAAK,CAACC,QAAQ,CAAE,KAAAC,qBAAA,CAC3BvF,aAAa,EAAAuF,qBAAA,CAACF,KAAK,CAACG,iBAAiB,UAAAD,qBAAA,UAAAA,qBAAA,CAAI,EAAE,CAAC,CAC5C,KAAM,CAAAE,QAAQ,CAAGJ,KAAK,CAACC,QAAQ,CAAC9G,QAAQ,CAACkH,GAAG,CAAC,CAAC,CAC9C,KAAM,CAAAC,SAAS,CAAGN,KAAK,CAACC,QAAQ,CAAC9G,QAAQ,CAACoH,GAAG,CAAC,CAAC,CAC/ClE,aAAa,CAAC+D,QAAQ,CAAC,CACvB7D,aAAa,CAAC+D,SAAS,CAAC,CACxB;AACA;AACF,CACF,CAAE,CACFE,YAAY,CAAE9F,UAAW,CACzB+F,KAAK,CAAE,CAAC,MAAM,CAAE,CAChBC,QAAQ,CAAC,IAAI,CACd,CAAC,CACC,CAAC,EACH,CAAC,CACH,CAAC,cACNvI,IAAA,QAAKwF,SAAS,CAAC,iBAAiB,CAAAD,QAAA,cAC9BrF,KAAA,QAAKsF,SAAS,CAAC,uBAAuB,CAAAD,QAAA,eACpCrF,KAAA,QAAKsF,SAAS,CAAC,0CAA0C,CAAAD,QAAA,EAAC,SACjD,CAACxC,KAAK,CAACG,GAAG,CAAC,MACpB,EAAK,CAAC,cACNlD,IAAA,QAAAuF,QAAA,cACEvF,IAAA,UACEmG,IAAI,CAAC,OAAO,CACZX,SAAS,CAAC,wEAAwE,CAClFN,KAAK,CAAEnC,KAAK,CAACG,GAAI,CACjBmD,QAAQ,CAAEf,eAAgB,CAC1BrC,GAAG,CAAC,GAAG,CACPC,GAAG,CAAC,KAAK,CACV,CAAC,CACC,CAAC,EACH,CAAC,CACH,CAAC,cAENhD,KAAA,QAAKsF,SAAS,CAAC,iBAAiB,CAAAD,QAAA,eAC9BrF,KAAA,WACE+F,OAAO,CAAEA,CAAA,GAAM,CACb;AACA;AACA;AACA;AACA;AACA;AACA;AAEA/E,QAAQ,CACNlC,aAAa,CACXqC,MAAM,CAAG,GAAG,CAAG,GAAG,CAClBgB,UAAU,CACVI,UAAU,CACVF,UAAU,CACVM,aAAa,CACbE,KAAK,CAACG,GAAG,CACTe,UAAU,CACVE,UACF,CACF,CAAC,CACD,GAAI5B,UAAU,GAAK,EAAE,CAAE,CACrB+B,YAAY,CAAC,CAACL,UAAU,CAAEE,UAAU,CAAC,CAAC,CAAE;AACxCK,UAAU,CAAC,EAAE,CAAC,CAChB,CAEApC,eAAe,CAAC,KAAK,CAAC,CACxB,CAAE,CACFoD,SAAS,CAAC,iFAAiF,CAAAD,QAAA,eAE3FvF,IAAA,QACE0F,KAAK,CAAC,4BAA4B,CAClCC,IAAI,CAAC,MAAM,CACXC,OAAO,CAAC,WAAW,CACnB,eAAa,KAAK,CAClBC,MAAM,CAAC,cAAc,CACrBK,KAAK,CAAC,aAAa,CAAAX,QAAA,cAEnBvF,IAAA,SACE,iBAAe,OAAO,CACtB,kBAAgB,OAAO,CACvBgG,CAAC,CAAC,uBAAuB,CAC1B,CAAC,CACC,CAAC,cAENhG,IAAA,QAAAuF,QAAA,CAAK,SAAO,CAAK,CAAC,EACZ,CAAC,cACTrF,KAAA,WACE+F,OAAO,CAAEA,CAAA,GAAM,CACbzD,aAAa,CAAC,EAAE,CAAC,CACjBF,aAAa,CAAC,EAAE,CAAC,CACjBQ,gBAAgB,CAAC,EAAE,CAAC,CACpBF,sBAAsB,CAAC,EAAE,CAAC,CAC1BF,aAAa,CAAC,EAAE,CAAC,CACjB4B,YAAY,CAAC,CAAC,CAAC,CAAE,CAAC,CAAC,CAAC,CAAE;AACtBE,UAAU,CAAC,CAAC,CAAC,CACbtD,QAAQ,CACNlC,aAAa,CACXqC,MAAM,CAAG,GAAG,CAAG,GAAG,CAClB,EAAE,CACF,EAAE,CACF,EAAE,CACF,EAAE,CACF,GAAG,CACH,EAAE,CACF,EACF,CACF,CAAC,CACDe,eAAe,CAAC,KAAK,CAAC,CACxB,CAAE,CACFoD,SAAS,CAAC,gFAAgF,CAAAD,QAAA,eAE1FvF,IAAA,QACE0F,KAAK,CAAC,4BAA4B,CAClCC,IAAI,CAAC,MAAM,CACXC,OAAO,CAAC,WAAW,CACnB,eAAa,KAAK,CAClBC,MAAM,CAAC,cAAc,CACrBL,SAAS,CAAC,aAAa,CAAAD,QAAA,cAEvBvF,IAAA,SACE,iBAAe,OAAO,CACtB,kBAAgB,OAAO,CACvBgG,CAAC,CAAC,yKAAyK,CAC5K,CAAC,CACC,CAAC,cACNhG,IAAA,QAAAuF,QAAA,CAAK,QAAM,CAAK,CAAC,EACX,CAAC,EACN,CAAC,EACH,CAAC,CACH,CAAC,CACH,CAAC,CACJ,IAAI,cAERrF,KAAA,QAAKsF,SAAS,CAAC,2EAA2E,CAAAD,QAAA,eACxFvF,IAAA,QAAKwF,SAAS,CAAC,wBAAwB,CAAAD,QAAA,CACpClE,MAAM,cACLnB,KAAA,QAAKsF,SAAS,CAAC,WAAW,CAAAD,QAAA,eACxBrF,KAAA,CAACf,YAAY,EACXyF,MAAM,CAAEP,SAAU,CAClBQ,IAAI,CAAEN,OAAQ,CACdiE,KAAK,CAAE,CAAEC,MAAM,CAAE,OAAO,CAAEC,KAAK,CAAE,MAAO,CAAE,CAC1CC,WAAW,CAAGC,WAAW,EAAMnE,MAAM,CAACoE,OAAO,CAAGD,WAAc;AAAA,CAAArD,QAAA,eAE9DvF,IAAA,CAAC0E,aAAa,EAACE,MAAM,CAAEP,SAAU,CAACQ,IAAI,CAAEN,OAAQ,CAAE,CAAC,cACnDvE,IAAA,CAACZ,SAAS,EACR0J,GAAG,CAAC,oDAAoD,CACxDC,WAAW,CAAC,yFAAyF,CACtG,CAAC,CACDvF,SAAS,SAATA,SAAS,iBAATA,SAAS,CACNwF,MAAM,CACLC,QAAQ,EAAKA,QAAQ,CAACC,UAAU,EAAID,QAAQ,CAACE,UAChD,CAAC,CACArE,GAAG,CAAC,CAACmE,QAAQ,CAAEzC,KAAK,gBACnBxG,IAAA,CAACX,MAAM,EACL+J,aAAa,CAAE,CACbC,KAAK,CAAEA,CAAA,GAAM,CACX3H,YAAY,CAAC,IAAI,CAAC,CAClBF,oBAAoB,CAACyH,QAAQ,CAAC,CAChC,CACF,CAAE,CAEFK,QAAQ,CAAE,CAACL,QAAQ,CAACC,UAAU,CAAED,QAAQ,CAACE,UAAU,CAAE,CAAA5D,QAAA,cAErDrF,KAAA,CAACZ,KAAK,EAAAiG,QAAA,EACH0D,QAAQ,CAACM,SAAS,cACnBvJ,IAAA,QAAK,CAAC,EACD,CAAC,EANHwG,KAOC,CACT,CAAC,EACQ,CAAC,CAiCd/E,SAAS,cACRzB,IAAA,QAAKwF,SAAS,CAAC,4DAA4D,CAAAD,QAAA,cACzErF,KAAA,QAAKsF,SAAS,CAAC,iCAAiC,CAAAD,QAAA,eAC9CvF,IAAA,QAAKwF,SAAS,CAAC,mBAAmB,CAAAD,QAAA,cAChCvF,IAAA,WACEiG,OAAO,CAAEA,CAAA,GAAM,CACbvE,YAAY,CAAC,KAAK,CAAC,CACnBF,oBAAoB,CAAC,IAAI,CAAC,CAC5B,CAAE,CACFgE,SAAS,CAAC,yEAAyE,CAAAD,QAAA,cAEnFvF,IAAA,QACE0F,KAAK,CAAC,4BAA4B,CAClCC,IAAI,CAAC,MAAM,CACXC,OAAO,CAAC,WAAW,CACnB,eAAa,KAAK,CAClBC,MAAM,CAAC,cAAc,CACrBL,SAAS,CAAC,QAAQ,CAAAD,QAAA,cAElBvF,IAAA,SACE,iBAAe,OAAO,CACtB,kBAAgB,OAAO,CACvBgG,CAAC,CAAC,sBAAsB,CACzB,CAAC,CACC,CAAC,CACA,CAAC,CACN,CAAC,cACNhG,IAAA,QAAKwF,SAAS,CAAC,iBAAiB,CAAAD,QAAA,CAC7BhE,iBAAiB,eAChBrB,KAAA,QAAAqF,QAAA,eACErF,KAAA,QAAKsF,SAAS,CAAC,yCAAyC,CAAAD,QAAA,eACtDvF,IAAA,QAAAuF,QAAA,cACEvF,IAAA,QACE0F,KAAK,CAAC,4BAA4B,CAClCC,IAAI,CAAC,MAAM,CACXC,OAAO,CAAC,WAAW,CACnB,eAAa,KAAK,CAClBC,MAAM,CAAC,cAAc,CACrBL,SAAS,CAAC,QAAQ,CAAAD,QAAA,cAElBvF,IAAA,SACE,iBAAe,OAAO,CACtB,kBAAgB,OAAO,CACvBgG,CAAC,CAAC,2gBAA2gB,CAC9gB,CAAC,CACC,CAAC,CACH,CAAC,cACNhG,IAAA,QAAKwF,SAAS,CAAC,aAAa,CAAAD,QAAA,CACzBhE,iBAAiB,CAACiI,QAAQ,EAAIjI,iBAAiB,CAACiI,QAAQ,CAACC,MAAM,CAAG,CAAC,CAClElI,iBAAiB,CAACiI,QAAQ,CAAC1E,GAAG,CAC5B,CAAC4E,OAAO,CAAElD,KAAK,gBACbtG,KAAA,QAAiBsF,SAAS,CAAC,MAAM,CAAAD,QAAA,EAAC,GAC/B,CAAC,GAAG,CACJmE,OAAO,CAACC,YAAY,EAClBD,OAAO,CAACE,kBAAkB,GAAK,EAAE,EAClCF,OAAO,CAACE,kBAAkB,GAAK,IAAI,CAC/B,IAAI,CAAGF,OAAO,CAACE,kBAAkB,CACjC,EAAE,CAAC,GANDpD,KAOL,CAET,CAAC,cAEDxG,IAAA,QAAKwF,SAAS,CAAC,MAAM,CAAAD,QAAA,CAAC,uBAAqB,CAAK,CACjD,CAOE,CAAC,EACH,CAAC,cAGNrF,KAAA,QAAKsF,SAAS,CAAC,yCAAyC,CAAAD,QAAA,eACtDvF,IAAA,QAAAuF,QAAA,cACEvF,IAAA,QACE0F,KAAK,CAAC,4BAA4B,CAClCC,IAAI,CAAC,MAAM,CACXC,OAAO,CAAC,WAAW,CACnB,eAAa,KAAK,CAClBC,MAAM,CAAC,cAAc,CACrBL,SAAS,CAAC,QAAQ,CAAAD,QAAA,cAElBvF,IAAA,SACE,iBAAe,OAAO,CACtB,kBAAgB,OAAO,CACvBgG,CAAC,CAAC,yJAAyJ,CAC5J,CAAC,CACC,CAAC,CACH,CAAC,cACNhG,IAAA,QAAKwF,SAAS,CAAC,aAAa,CAAAD,QAAA,EAAA3E,qBAAA,CACzBW,iBAAiB,CAACgI,SAAS,UAAA3I,qBAAA,UAAAA,qBAAA,CAAI,KAAK,CAClC,CAAC,EACH,CAAC,CAELW,iBAAiB,CAACsI,cAAc,EAAItI,iBAAiB,CAACsI,cAAc,CAACJ,MAAM,CAAG,CAAC,CAC9ElI,iBAAiB,CAACsI,cAAc,CAAC/E,GAAG,CAClC,CAACyB,IAAI,CAAEC,KAAK,gBACVxG,IAAA,QAAAuF,QAAA,cACErF,KAAA,QAAKsF,SAAS,CAAC,yCAAyC,CAAAD,QAAA,eACtDvF,IAAA,QAAAuF,QAAA,CACG,CACC,YAAY,CACZ,UAAU,CACV,eAAe,CAChB,CAACuE,QAAQ,CAACvD,IAAI,CAACwD,SAAS,CAAC,cACxB/J,IAAA,QACE0F,KAAK,CAAC,4BAA4B,CAClCC,IAAI,CAAC,MAAM,CACXC,OAAO,CAAC,WAAW,CACnB,eAAa,KAAK,CAClBC,MAAM,CAAC,cAAc,CACrBL,SAAS,CAAC,QAAQ,CAAAD,QAAA,cAElBvF,IAAA,SACE,iBAAe,OAAO,CACtB,kBAAgB,OAAO,CACvBgG,CAAC,CAAC,mWAAmW,CACtW,CAAC,CACC,CAAC,cAENhG,IAAA,QACE0F,KAAK,CAAC,4BAA4B,CAClCC,IAAI,CAAC,MAAM,CACXC,OAAO,CAAC,WAAW,CACnB,eAAa,KAAK,CAClBC,MAAM,CAAC,cAAc,CACrBL,SAAS,CAAC,QAAQ,CAAAD,QAAA,cAElBvF,IAAA,SACE,iBAAe,OAAO,CACtB,kBAAgB,OAAO,CACvBgG,CAAC,CAAC,gQAAgQ,CACnQ,CAAC,CACC,CACN,CACE,CAAC,cACN9F,KAAA,QAAKsF,SAAS,CAAC,aAAa,CAAAD,QAAA,EACzBgB,IAAI,CAACwD,SAAS,CAAC,KAAG,CAACxD,IAAI,CAACyD,UAAU,EAChC,CAAC,EACH,CAAC,EA1CExD,KA2CL,CAET,CAAC,CACC,IAAI,cA6CRtG,KAAA,QAAKsF,SAAS,CAAC,yCAAyC,CAAAD,QAAA,eACtDvF,IAAA,QAAAuF,QAAA,cACErF,KAAA,QACEwF,KAAK,CAAC,4BAA4B,CAClCC,IAAI,CAAC,MAAM,CACXC,OAAO,CAAC,WAAW,CACnB,eAAa,KAAK,CAClBC,MAAM,CAAC,cAAc,CACrBL,SAAS,CAAC,QAAQ,CAAAD,QAAA,eAElBvF,IAAA,SACE,iBAAe,OAAO,CACtB,kBAAgB,OAAO,CACvBgG,CAAC,CAAC,uCAAuC,CAC1C,CAAC,cACFhG,IAAA,SACE,iBAAe,OAAO,CACtB,kBAAgB,OAAO,CACvBgG,CAAC,CAAC,gFAAgF,CACnF,CAAC,EACC,CAAC,CACH,CAAC,cACNhG,IAAA,QAAKwF,SAAS,CAAC,aAAa,CAAAD,QAAA,EAAA1E,qBAAA,CACzBU,iBAAiB,CAAC0I,OAAO,UAAApJ,qBAAA,UAAAA,qBAAA,CAAI,KAAK,CAChC,CAAC,EACH,CAAC,cACNX,KAAA,QAAKsF,SAAS,CAAC,yCAAyC,CAAAD,QAAA,eACtDvF,IAAA,QAAAuF,QAAA,cACEvF,IAAA,QACE0F,KAAK,CAAC,4BAA4B,CAClCC,IAAI,CAAC,MAAM,CACXC,OAAO,CAAC,WAAW,CACnB,eAAa,KAAK,CAClBC,MAAM,CAAC,cAAc,CACrBK,KAAK,CAAC,QAAQ,CAAAX,QAAA,cAEdvF,IAAA,SACE,iBAAe,OAAO,CACtB,kBAAgB,OAAO,CACvBgG,CAAC,CAAC,oLAAoL,CACvL,CAAC,CACC,CAAC,CACH,CAAC,cACNhG,IAAA,QAAKwF,SAAS,CAAC,aAAa,CAAAD,QAAA,EAAAzE,qBAAA,CACzBS,iBAAiB,CAAC2I,cAAc,UAAApJ,qBAAA,UAAAA,qBAAA,CAAI,KAAK,CACvC,CAAC,EACH,CAAC,cACNZ,KAAA,MAAGsF,SAAS,CAAC,+CAA+C,CAAAD,QAAA,eAC1DvF,IAAA,CAACtB,IAAI,EACH8G,SAAS,CAAC,oBAAoB,CAC9B2E,EAAE,CACA,uBAAuB,CAAG5I,iBAAiB,CAAC6I,EAC7C,CAAA7E,QAAA,cAEDvF,IAAA,QACE0F,KAAK,CAAC,4BAA4B,CAClCC,IAAI,CAAC,MAAM,CACXC,OAAO,CAAC,WAAW,CACnByE,WAAW,CAAC,KAAK,CACjBxE,MAAM,CAAC,cAAc,CACrBL,SAAS,CAAC,+DAA+D,CAAAD,QAAA,cAEzEvF,IAAA,SACE8F,aAAa,CAAC,OAAO,CACrBC,cAAc,CAAC,OAAO,CACtBC,CAAC,CAAC,kQAAkQ,CACrQ,CAAC,CACC,CAAC,CACF,CAAC,cACPhG,IAAA,QACEiG,OAAO,CAAEA,CAAA,GAAM,CACbjE,YAAY,CAAC,QAAQ,CAAC,CACtBE,aAAa,CAACX,iBAAiB,CAAC6I,EAAE,CAAC,CACnCxI,WAAW,CAAC,IAAI,CAAC,CACnB,CAAE,CACF4D,SAAS,CAAC,kCAAkC,CAAAD,QAAA,cAE5CvF,IAAA,QACE0F,KAAK,CAAC,4BAA4B,CAClCC,IAAI,CAAC,MAAM,CACXC,OAAO,CAAC,WAAW,CACnB,eAAa,KAAK,CAClBC,MAAM,CAAC,cAAc,CACrBL,SAAS,CAAC,8DAA8D,CAAAD,QAAA,cAExEvF,IAAA,SACE,iBAAe,OAAO,CACtB,kBAAgB,OAAO,CACvBgG,CAAC,CAAC,+TAA+T,CAClU,CAAC,CACC,CAAC,CACH,CAAC,cACNhG,IAAA,CAACtB,IAAI,EACH8G,SAAS,CAAC,oBAAoB,CAC9B2E,EAAE,CACA,0BAA0B,CAC1B5I,iBAAiB,CAAC6I,EACnB,CAAA7E,QAAA,cAEDvF,IAAA,QACE0F,KAAK,CAAC,4BAA4B,CAClCC,IAAI,CAAC,MAAM,CACXC,OAAO,CAAC,WAAW,CACnB,eAAa,KAAK,CAClBC,MAAM,CAAC,cAAc,CACrBL,SAAS,CAAC,+DAA+D,CAAAD,QAAA,cAEzEvF,IAAA,SACE,iBAAe,OAAO,CACtB,kBAAgB,OAAO,CACvBgG,CAAC,CAAC,uRAAuR,CAC1R,CAAC,CACC,CAAC,CACF,CAAC,EACN,CAAC,EACD,CACN,CACE,CAAC,EACH,CAAC,CACH,CAAC,CACJ,IAAI,EACL,CAAC,eAEN;AACA;AACA;AACA;AACA;AACA9F,KAAA,QAAAqF,QAAA,EACG9B,gBAAgB,cACfzD,IAAA,CAACf,MAAM,GAAE,CAAC,CACRyE,cAAc,cAChB1D,IAAA,CAACd,KAAK,EAACiH,IAAI,CAAC,OAAO,CAACmE,OAAO,CAAE5G,cAAe,CAAE,CAAC,cAE/C1D,IAAA,QAAKwF,SAAS,CAAC,6BAA6B,CAAAD,QAAA,cAC1CrF,KAAA,UAAOsF,SAAS,CAAC,mBAAmB,CAAAD,QAAA,eAClCvF,IAAA,UAAAuF,QAAA,cACErF,KAAA,OAAIsF,SAAS,CAAC,0BAA0B,CAAAD,QAAA,eACtCvF,IAAA,OAAIwF,SAAS,CAAC,+DAA+D,CAAAD,QAAA,CAAC,GAE9E,CAAI,CAAC,cACLvF,IAAA,OAAIwF,SAAS,CAAC,gEAAgE,CAAAD,QAAA,CAAC,UAE/E,CAAI,CAAC,cACLvF,IAAA,OAAIwF,SAAS,CAAC,gEAAgE,CAAAD,QAAA,CAAC,eAE/E,CAAI,CAAC,cAOLvF,IAAA,OAAIwF,SAAS,CAAC,gEAAgE,CAAAD,QAAA,CAAC,SAE/E,CAAI,CAAC,cACLvF,IAAA,OAAIwF,SAAS,CAAC,gEAAgE,CAAAD,QAAA,CAAC,MAE/E,CAAI,CAAC,cACLvF,IAAA,OAAIwF,SAAS,CAAC,gEAAgE,CAAAD,QAAA,CAAC,SAE/E,CAAI,CAAC,cAELvF,IAAA,OAAIwF,SAAS,CAAC,kDAAkD,CAAAD,QAAA,CAAC,WAEjE,CAAI,CAAC,EACH,CAAC,CACA,CAAC,cAERvF,IAAA,UAAAuF,QAAA,CACG/B,SAAS,SAATA,SAAS,iBAATA,SAAS,CAAEsB,GAAG,CAAC,CAACyB,IAAI,CAAEC,KAAK,QAAA+D,cAAA,CAAAC,eAAA,CAAAC,aAAA,CAAAC,UAAA,oBAC1BxK,KAAA,OAAAqF,QAAA,eACEvF,IAAA,OAAIwF,SAAS,CAAC,4BAA4B,CAAAD,QAAA,cACxCvF,IAAA,MAAGwF,SAAS,CAAC,gCAAgC,CAAAD,QAAA,CAC1CgB,IAAI,CAAC6D,EAAE,CACP,CAAC,CACF,CAAC,cACLpK,IAAA,OAAIwF,SAAS,CAAC,4BAA4B,CAAAD,QAAA,cACxCvF,IAAA,MAAGwF,SAAS,CAAC,gCAAgC,CAAAD,QAAA,cAC3CvF,IAAA,MAAGyF,IAAI,CAAE,0BAA0B,CAAGc,IAAI,CAAC6D,EAAG,CAAA7E,QAAA,cAC5CvF,IAAA,MAAGwF,SAAS,CAAC,6BAA6B,CAAAD,QAAA,CACvCgB,IAAI,CAACgD,SAAS,CACd,CAAC,CACH,CAAC,CACH,CAAC,CACF,CAAC,cACLvJ,IAAA,OAAIwF,SAAS,CAAC,4BAA4B,CAAAD,QAAA,cACxCvF,IAAA,MAAGwF,SAAS,CAAC,gCAAgC,CAAAD,QAAA,cAC3CrF,KAAA,MAAGuF,IAAI,CAAE,0BAA0B,CAAGc,IAAI,CAAC6D,EAAG,CAAA7E,QAAA,EAC3C,EAAAgF,cAAA,CAAAhE,IAAI,CAACiD,QAAQ,UAAAe,cAAA,iBAAbA,cAAA,CAAed,MAAM,GAAI,CAAC,CAAE,GAAG,CAC/B,CAAC,EAAAe,eAAA,CAAAjE,IAAI,CAACiD,QAAQ,UAAAgB,eAAA,iBAAbA,eAAA,CAAef,MAAM,GAAI,CAAC,EAAI,CAAC,CAC7B,UAAU,CACV,SAAS,EACZ,CAAC,CAMH,CAAC,CACF,CAAC,cAWLzJ,IAAA,OAAIwF,SAAS,CAAC,4BAA4B,CAAAD,QAAA,cACxCvF,IAAA,MAAGwF,SAAS,CAAC,gCAAgC,CAAAD,QAAA,EAAAkF,aAAA,CAC1ClE,IAAI,CAACI,OAAO,UAAA8D,aAAA,UAAAA,aAAA,CAAI,MAAM,CACtB,CAAC,CACF,CAAC,cACLzK,IAAA,OAAIwF,SAAS,CAAC,4BAA4B,CAAAD,QAAA,cACxCvF,IAAA,MAAGwF,SAAS,CAAC,gCAAgC,CAAAD,QAAA,EAAAmF,UAAA,CAC1CnE,IAAI,CAACoE,IAAI,UAAAD,UAAA,UAAAA,UAAA,CAAI,MAAM,CACnB,CAAC,CACF,CAAC,cACL1K,IAAA,OAAIwF,SAAS,CAAC,4BAA4B,CAAAD,QAAA,cACxCvF,IAAA,MAAGwF,SAAS,CAAC,gCAAgC,CAAAD,QAAA,CAC1CgB,IAAI,CAAC0D,OAAO,CACZ,CAAC,CACF,CAAC,cAELjK,IAAA,OAAIwF,SAAS,CAAC,4BAA4B,CAAAD,QAAA,cACxCrF,KAAA,MAAGsF,SAAS,CAAC,2CAA2C,CAAAD,QAAA,eACtDvF,IAAA,CAACtB,IAAI,EACH8G,SAAS,CAAC,mBAAmB,CAC7B2E,EAAE,CAAE,uBAAuB,CAAG5D,IAAI,CAAC6D,EAAG,CAAA7E,QAAA,cAEtCvF,IAAA,QACE0F,KAAK,CAAC,4BAA4B,CAClCC,IAAI,CAAC,MAAM,CACXC,OAAO,CAAC,WAAW,CACnByE,WAAW,CAAC,KAAK,CACjBxE,MAAM,CAAC,cAAc,CACrBL,SAAS,CAAC,+DAA+D,CAAAD,QAAA,cAEzEvF,IAAA,SACE8F,aAAa,CAAC,OAAO,CACrBC,cAAc,CAAC,OAAO,CACtBC,CAAC,CAAC,kQAAkQ,CACrQ,CAAC,CACC,CAAC,CACF,CAAC,cACPhG,IAAA,QACEiG,OAAO,CAAEA,CAAA,GAAM,CACbjE,YAAY,CAAC,QAAQ,CAAC,CACtBE,aAAa,CAACqE,IAAI,CAAC6D,EAAE,CAAC,CACtBxI,WAAW,CAAC,IAAI,CAAC,CACnB,CAAE,CACF4D,SAAS,CAAC,kCAAkC,CAAAD,QAAA,cAE5CvF,IAAA,QACE0F,KAAK,CAAC,4BAA4B,CAClCC,IAAI,CAAC,MAAM,CACXC,OAAO,CAAC,WAAW,CACnB,eAAa,KAAK,CAClBC,MAAM,CAAC,cAAc,CACrBL,SAAS,CAAC,8DAA8D,CAAAD,QAAA,cAExEvF,IAAA,SACE,iBAAe,OAAO,CACtB,kBAAgB,OAAO,CACvBgG,CAAC,CAAC,+TAA+T,CAClU,CAAC,CACC,CAAC,CACH,CAAC,cACNhG,IAAA,CAACtB,IAAI,EACH8G,SAAS,CAAC,oBAAoB,CAC9B2E,EAAE,CAAE,0BAA0B,CAAG5D,IAAI,CAAC6D,EAAG,CAAA7E,QAAA,cAEzCvF,IAAA,QACE0F,KAAK,CAAC,4BAA4B,CAClCC,IAAI,CAAC,MAAM,CACXC,OAAO,CAAC,WAAW,CACnB,eAAa,KAAK,CAClBC,MAAM,CAAC,cAAc,CACrBL,SAAS,CAAC,+DAA+D,CAAAD,QAAA,cAEzEvF,IAAA,SACE,iBAAe,OAAO,CACtB,kBAAgB,OAAO,CACvBgG,CAAC,CAAC,uRAAuR,CAC1R,CAAC,CACC,CAAC,CACF,CAAC,EACN,CAAC,CACF,CAAC,GAxHEQ,KAyHL,CAAC,EACN,CAAC,CACG,CAAC,EACH,CAAC,CACL,CACN,cACDxG,IAAA,QAAKwF,SAAS,CAAC,EAAE,CAAAD,QAAA,cACfvF,IAAA,CAACF,QAAQ,EACP8K,KAAK,CAAG,8BAA6BvI,UAAW,eAAcI,UAAW,eAAcF,UAAW,kBAAiBM,aAAc,GAAG,CACpIgI,MAAM,CAAE,EAAG,CACX1J,IAAI,CAAEA,IAAK,CACXwC,KAAK,CAAEA,KAAM,CACd,CAAC,CACC,CAAC,EACH,CACN,CACE,CAAC,cACN3D,IAAA,QAAKwF,SAAS,CAAC,MAAM,CAAM,CAAC,EACzB,CAAC,cACNxF,IAAA,CAACP,iBAAiB,EAChBqL,MAAM,CAAEnJ,QAAS,CACjB2I,OAAO,CACLvI,SAAS,GAAK,QAAQ,CAClB,gDAAgD,CAChD,gBACL,CACDgJ,SAAS,CAAE,KAAAA,CAAA,GAAY,CACrB,GAAIhJ,SAAS,GAAK,QAAQ,CAAE,CAC1BH,WAAW,CAAC,KAAK,CAAC,CAClBI,YAAY,CAAC,EAAE,CAAC,CAChBF,YAAY,CAAC,KAAK,CAAC,CACrB,CAAC,IAAM,IAAIC,SAAS,GAAK,QAAQ,EAAIE,UAAU,GAAK,EAAE,CAAE,CACtDH,YAAY,CAAC,IAAI,CAAC,CAClBZ,QAAQ,CAACnC,cAAc,CAACkD,UAAU,CAAC,CAAC,CACpCL,WAAW,CAAC,KAAK,CAAC,CAClBI,YAAY,CAAC,EAAE,CAAC,CAChBF,YAAY,CAAC,KAAK,CAAC,CACrB,CAAC,IAAM,CACLF,WAAW,CAAC,KAAK,CAAC,CAClBI,YAAY,CAAC,EAAE,CAAC,CAChBF,YAAY,CAAC,KAAK,CAAC,CACrB,CACF,CAAE,CACFkJ,QAAQ,CAAEA,CAAA,GAAM,CACdpJ,WAAW,CAAC,KAAK,CAAC,CAClBI,YAAY,CAAC,EAAE,CAAC,CAChBF,YAAY,CAAC,KAAK,CAAC,CACrB,CAAE,CACFD,SAAS,CAAEA,SAAU,CACtB,CAAC,cACF7B,IAAA,QAAKwF,SAAS,CAAC,2CAA2C,CAAM,CAAC,EAC9D,CAAC,CACO,CAAC,CAEpB,CAEA,cAAe,CAAA7E,kBAAkB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}