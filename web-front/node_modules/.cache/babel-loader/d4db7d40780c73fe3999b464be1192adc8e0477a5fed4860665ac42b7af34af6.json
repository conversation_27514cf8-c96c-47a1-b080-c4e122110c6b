{"ast": null, "code": "import React,{useEffect,useState}from\"react\";import{useDispatch,useSelector}from\"react-redux\";import{Link,useLocation,useNavigate,useSearchParams}from\"react-router-dom\";import{casesListDashboard,casesListMap,deleteCase}from\"../../redux/actions/caseActions\";import ConfirmationModal from\"../../components/ConfirmationModal\";import Paginate from\"../../components/Paginate\";import Alert from\"../../components/Alert\";import Loader from\"../../components/Loader\";import DefaultLayout from\"../../layouts/DefaultLayout\";import{<PERSON><PERSON><PERSON><PERSON>,<PERSON><PERSON><PERSON><PERSON><PERSON>,<PERSON><PERSON>,<PERSON><PERSON>}from\"react-leaflet\";import\"leaflet/dist/leaflet.css\";import L from\"leaflet\";import Select from\"react-select\";import{getListCoordinators}from\"../../redux/actions/userActions\";import{providersListDashboard}from\"../../redux/actions/providerActions\";import{insurancesListDashboard}from\"../../redux/actions/insuranceActions\";import{UAParser}from\"ua-parser-js\";import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";delete L.Icon.Default.prototype._getIconUrl;L.Icon.Default.mergeOptions({iconRetinaUrl:\"https://unpkg.com/leaflet@1.7.1/dist/images/marker-icon-2x.png\",iconUrl:\"https://unpkg.com/leaflet@1.7.1/dist/images/marker-icon.png\",shadowUrl:\"https://unpkg.com/leaflet@1.7.1/dist/images/marker-shadow.png\"});function DashboardScreen(){var _providerMapSelect$pr,_providerMapSelect$pr2,_providerMapSelect$pr3;const navigate=useNavigate();const location=useLocation();const[searchParams]=useSearchParams();const page=searchParams.get(\"page\")||\"1\";const dispatch=useDispatch();const[providerMapSelect,setProviderMapSelect]=useState(null);const[isOpenMap,setIsOpenMap]=useState(false);const[idFilter,setIdFilter]=useState(searchParams.get(\"filterid\")||\"\");const[ciaIdFilter,setCiaIdFilter]=useState(searchParams.get(\"filterciaid\")||\"\");const[patientFilter,setPatientFilter]=useState(searchParams.get(\"filterpatient\")||\"\");const[insuranceFilter,setInsuranceFilter]=useState(searchParams.get(\"filterinsurance\")||\"\");const[typeFilter,setTypeFilter]=useState(searchParams.get(\"filtertype\")||\"\");const[providerFilter,setProviderFilter]=useState(searchParams.get(\"filterprovider\")||\"\");const[coordinationFilter,setCoordinatorFilter]=useState(searchParams.get(\"filtercoordination\")||\"\");const[statusFilter,setStatusrFilter]=useState(searchParams.get(\"filterstatus\")||\"\");useEffect(()=>{const params=new URLSearchParams();if(idFilter)params.set(\"filterid\",idFilter);if(ciaIdFilter)params.set(\"filterciaid\",ciaIdFilter);if(patientFilter)params.set(\"filterpatient\",patientFilter);if(insuranceFilter)params.set(\"filterinsurance\",insuranceFilter);if(typeFilter)params.set(\"filtertype\",typeFilter);if(providerFilter)params.set(\"filterprovider\",providerFilter);if(coordinationFilter)params.set(\"filtercoordination\",coordinationFilter);if(statusFilter)params.set(\"filterstatus\",statusFilter);// Add default page\nparams.set(\"page\",\"1\");// Update URL\nnavigate({pathname:location.pathname,search:params.toString()});},[idFilter,patientFilter,statusFilter,insuranceFilter,providerFilter,coordinationFilter,typeFilter,ciaIdFilter,dispatch,navigate,location.pathname]);const[isDelete,setIsDelete]=useState(false);const[loadEvent,setLoadEvent]=useState(false);const[eventType,setEventType]=useState(\"\");const[caseId,setCaseId]=useState(\"\");const userLogin=useSelector(state=>state.userLogin);const{userInfo}=userLogin;const listCases=useSelector(state=>state.caseList);const{cases,loadingCases,errorCases,pages}=listCases;const listCasesMap=useSelector(state=>state.caseListMap);const{casesMap,loadingCasesMap,errorCasesMap}=listCasesMap;const caseDelete=useSelector(state=>state.deleteCase);const{loadingCaseDelete,errorCaseDelete,successCaseDelete}=caseDelete;const listProviders=useSelector(state=>state.providerList);const{providers,loadingProviders,errorProviders}=listProviders;const listInsurances=useSelector(state=>state.insuranceList);const{insurances,loadingInsurances,errorInsurances}=listInsurances;const listCoordinators=useSelector(state=>state.coordinatorsList);const{coordinators,loadingCoordinators,errorCoordinators}=listCoordinators;const redirect=\"/\";useEffect(()=>{if(!userInfo){navigate(redirect);}else{var _insuranceFilter$valu,_providerFilter$value,_insuranceFilter$valu2,_providerFilter$value2;const parser=new UAParser();const result=parser.getResult();const browser=result.browser.name||\"Unknown browser\";const device=result.device.model||result.device.type||\"Unknown device\";// get list cases (optimized for dashboard)\ndispatch(casesListDashboard(page,\"\",idFilter,patientFilter,statusFilter,insuranceFilter!==\"\"?(_insuranceFilter$valu=insuranceFilter.value)!==null&&_insuranceFilter$valu!==void 0?_insuranceFilter$valu:\"\":\"\",providerFilter!==\"\"?(_providerFilter$value=providerFilter.value)!==null&&_providerFilter$value!==void 0?_providerFilter$value:\"\":\"\",coordinationFilter,typeFilter,ciaIdFilter));// get list case maps \ndispatch(casesListMap(\"0\",\"\",idFilter,patientFilter,statusFilter,insuranceFilter!==\"\"?(_insuranceFilter$valu2=insuranceFilter.value)!==null&&_insuranceFilter$valu2!==void 0?_insuranceFilter$valu2:\"\":\"\",providerFilter!==\"\"?(_providerFilter$value2=providerFilter.value)!==null&&_providerFilter$value2!==void 0?_providerFilter$value2:\"\":\"\",coordinationFilter,typeFilter,ciaIdFilter));// get List Coordinators\n// dispatch(getListCoordinators(\"0\"));\n// get providers List (optimized for dashboard)\ndispatch(providersListDashboard(\"0\"));// get Insuranes List (optimized for dashboard)\ndispatch(insurancesListDashboard(\"0\"));// \n}},[navigate,userInfo,dispatch,page// idFilter,\n// patientFilter,\n// statusFilter,\n// insuranceFilter,\n// providerFilter,\n// coordinationFilter,\n// typeFilter,\n]);useEffect(()=>{if(successCaseDelete){var _insuranceFilter$valu3,_providerFilter$value3,_insuranceFilter$valu4,_providerFilter$value4;dispatch(casesListDashboard(\"1\",\"\",idFilter,patientFilter,statusFilter,insuranceFilter!==\"\"?(_insuranceFilter$valu3=insuranceFilter.value)!==null&&_insuranceFilter$valu3!==void 0?_insuranceFilter$valu3:\"\":\"\",providerFilter!==\"\"?(_providerFilter$value3=providerFilter.value)!==null&&_providerFilter$value3!==void 0?_providerFilter$value3:\"\":\"\",coordinationFilter,typeFilter,ciaIdFilter));dispatch(casesListMap(\"0\",\"\",idFilter,patientFilter,statusFilter,insuranceFilter!==\"\"?(_insuranceFilter$valu4=insuranceFilter.value)!==null&&_insuranceFilter$valu4!==void 0?_insuranceFilter$valu4:\"\":\"\",providerFilter!==\"\"?(_providerFilter$value4=providerFilter.value)!==null&&_providerFilter$value4!==void 0?_providerFilter$value4:\"\":\"\",coordinationFilter,typeFilter,ciaIdFilter));}},[successCaseDelete]);const formatDate=dateString=>{if(dateString&&dateString!==\"\"){const date=new Date(dateString);return date.toLocaleDateString(\"en-US\",{year:\"numeric\",month:\"long\",day:\"numeric\"});}else{return dateString&&dateString!==\"\"?dateString:\"----\";}};const caseStatus=casestatus=>{switch(casestatus){case\"pending-coordination\":return\"Pending Coordination\";case\"coordinated-missing-m-r\":return\"Coordinated, Missing M.R.\";case\"coordinated-missing-invoice\":return\"Coordinated, Missing Invoice\";case\"waiting-for-insurance-authorization\":return\"Waiting for Insurance Authorization\";case\"coordinated-patient-not-seen-yet\":return\"Coordinated, Patient not seen yet\";case\"fully-coordinated\":return\"Fully Coordinated\";case\"coordination-fee\":return\"Coordination Fee\";case\"coordinated-missing-payment\":return\"Coordinated, Missing Payment\";case\"failed\":return\"Failed\";default:return casestatus;}};return/*#__PURE__*/_jsx(DefaultLayout,{children:/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"div\",{className:\"flex flex-row text-sm items-center my-1\",children:/*#__PURE__*/_jsx(\"a\",{href:\"/dashboard\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"flex flex-row  items-center hover:text-black \",children:[/*#__PURE__*/_jsx(\"svg\",{xmlns:\"http://www.w3.org/2000/svg\",fill:\"none\",viewBox:\"0 0 24 24\",\"stroke-width\":\"1.5\",stroke:\"currentColor\",className:\"w-4 h-4\",children:/*#__PURE__*/_jsx(\"path\",{strokeLinecap:\"round\",strokeLinejoin:\"round\",d:\"m2.25 12 8.954-8.955c.44-.439 1.152-.439 1.591 0L21.75 12M4.5 9.75v10.125c0 .621.504 1.125 1.125 1.125H9.75v-4.875c0-.621.504-1.125 1.125-1.125h2.25c.621 0 1.125.504 1.125 1.125V21h4.125c.621 0 1.125-.504 1.125-1.125V9.75M8.25 21h8.25\"})}),/*#__PURE__*/_jsx(\"span\",{className:\"mx-1\",children:\"Dashboard\"})]})})}),/*#__PURE__*/_jsxs(\"div\",{className:\"rounded-sm border border-stroke px-2 pt-6 pb-2.5 shadow-default   dark:bg-boxdark \",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"py-2 px-4 md:px-6 xl:px-7.5 flex md:flex-row flex-col justify-between\",children:[/*#__PURE__*/_jsx(\"h4\",{className:\" uppercase font-semibold text-black  text-xs w-max\",children:\"Cases list\"}),/*#__PURE__*/_jsx(\"div\",{className:\"flex flex-row justify-end\",children:/*#__PURE__*/_jsxs(\"a\",{href:\"/cases-list/add\",className:\"px-4 py-3 rounded-full text-white bg-[#0388A6] flex flex-row text-xs items-center\",children:[/*#__PURE__*/_jsx(\"svg\",{xmlns:\"http://www.w3.org/2000/svg\",fill:\"none\",viewBox:\"0 0 24 24\",\"stroke-width\":\"1.5\",stroke:\"currentColor\",class:\"size-4\",children:/*#__PURE__*/_jsx(\"path\",{\"stroke-linecap\":\"round\",\"stroke-linejoin\":\"round\",d:\"M12 4.5v15m7.5-7.5h-15\"})}),/*#__PURE__*/_jsx(\"div\",{className:\"mx-2\",children:\"Create new case\"})]})})]}),/*#__PURE__*/_jsx(\"div\",{className:\"bg-gray-50 p-2 rounded-lg mb-2\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"relative group\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none\",children:/*#__PURE__*/_jsx(\"svg\",{className:\"h-5 w-5 text-gray-400 group-focus-within:text-blue-500 transition-colors\",fill:\"none\",viewBox:\"0 0 24 24\",stroke:\"currentColor\",children:/*#__PURE__*/_jsx(\"path\",{strokeLinecap:\"round\",strokeLinejoin:\"round\",strokeWidth:2,d:\"M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z\"})})}),/*#__PURE__*/_jsx(\"input\",{className:\"w-full pl-10 pr-4 py-3 rounded-lg bg-white text-sm text-gray-700 placeholder-gray-400 focus:outline-none focus:bg-blue-50 transition-all duration-200\",placeholder:\"Search Case ID...\",type:\"text\",value:idFilter,onChange:v=>{var _insuranceFilter$valu5,_providerFilter$value5,_insuranceFilter$valu6,_providerFilter$value6;setIdFilter(v.target.value);dispatch(casesListDashboard(\"1\",\"\",v.target.value,patientFilter,statusFilter,insuranceFilter!==\"\"?(_insuranceFilter$valu5=insuranceFilter.value)!==null&&_insuranceFilter$valu5!==void 0?_insuranceFilter$valu5:\"\":\"\",providerFilter!==\"\"?(_providerFilter$value5=providerFilter.value)!==null&&_providerFilter$value5!==void 0?_providerFilter$value5:\"\":\"\",coordinationFilter,typeFilter,ciaIdFilter));dispatch(casesListMap(\"0\",\"\",v.target.value,patientFilter,statusFilter,insuranceFilter!==\"\"?(_insuranceFilter$valu6=insuranceFilter.value)!==null&&_insuranceFilter$valu6!==void 0?_insuranceFilter$valu6:\"\":\"\",providerFilter!==\"\"?(_providerFilter$value6=providerFilter.value)!==null&&_providerFilter$value6!==void 0?_providerFilter$value6:\"\":\"\",coordinationFilter,typeFilter,ciaIdFilter));}})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"relative group\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none\",children:/*#__PURE__*/_jsx(\"svg\",{className:\"h-5 w-5 text-gray-400 group-focus-within:text-blue-500 transition-colors\",fill:\"none\",viewBox:\"0 0 24 24\",stroke:\"currentColor\",children:/*#__PURE__*/_jsx(\"path\",{strokeLinecap:\"round\",strokeLinejoin:\"round\",strokeWidth:2,d:\"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z\"})})}),/*#__PURE__*/_jsx(\"input\",{className:\"w-full pl-10 pr-4 py-3 rounded-lg bg-white text-sm text-gray-700 placeholder-gray-400 focus:outline-none focus:bg-blue-50 transition-all duration-200\",placeholder:\"CIA Reference...\",type:\"text\",value:ciaIdFilter,onChange:v=>{var _insuranceFilter$valu7,_providerFilter$value7,_insuranceFilter$valu8,_providerFilter$value8;setCiaIdFilter(v.target.value);dispatch(casesListDashboard(\"1\",\"\",idFilter,patientFilter,statusFilter,insuranceFilter!==\"\"?(_insuranceFilter$valu7=insuranceFilter.value)!==null&&_insuranceFilter$valu7!==void 0?_insuranceFilter$valu7:\"\":\"\",providerFilter!==\"\"?(_providerFilter$value7=providerFilter.value)!==null&&_providerFilter$value7!==void 0?_providerFilter$value7:\"\":\"\",coordinationFilter,typeFilter,v.target.value));dispatch(casesListMap(\"0\",\"\",idFilter,patientFilter,statusFilter,insuranceFilter!==\"\"?(_insuranceFilter$valu8=insuranceFilter.value)!==null&&_insuranceFilter$valu8!==void 0?_insuranceFilter$valu8:\"\":\"\",providerFilter!==\"\"?(_providerFilter$value8=providerFilter.value)!==null&&_providerFilter$value8!==void 0?_providerFilter$value8:\"\":\"\",coordinationFilter,typeFilter,v.target.value));}})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"relative group\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none\",children:/*#__PURE__*/_jsx(\"svg\",{className:\"h-5 w-5 text-gray-400 group-focus-within:text-blue-500 transition-colors\",fill:\"none\",viewBox:\"0 0 24 24\",stroke:\"currentColor\",children:/*#__PURE__*/_jsx(\"path\",{strokeLinecap:\"round\",strokeLinejoin:\"round\",strokeWidth:2,d:\"M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z\"})})}),/*#__PURE__*/_jsx(\"input\",{className:\"w-full pl-10 pr-4 py-3 rounded-lg bg-white text-sm text-gray-700 placeholder-gray-400 focus:outline-none focus:bg-blue-50 transition-all duration-200\",placeholder:\"Patient Name...\",type:\"text\",value:patientFilter,onChange:v=>{var _insuranceFilter$valu9,_providerFilter$value9,_insuranceFilter$valu10,_providerFilter$value10;setPatientFilter(v.target.value);dispatch(casesListDashboard(\"1\",\"\",idFilter,v.target.value,statusFilter,insuranceFilter!==\"\"?(_insuranceFilter$valu9=insuranceFilter.value)!==null&&_insuranceFilter$valu9!==void 0?_insuranceFilter$valu9:\"\":\"\",providerFilter!==\"\"?(_providerFilter$value9=providerFilter.value)!==null&&_providerFilter$value9!==void 0?_providerFilter$value9:\"\":\"\",coordinationFilter,typeFilter,ciaIdFilter));dispatch(casesListMap(\"0\",\"\",idFilter,v.target.value,statusFilter,insuranceFilter!==\"\"?(_insuranceFilter$valu10=insuranceFilter.value)!==null&&_insuranceFilter$valu10!==void 0?_insuranceFilter$valu10:\"\":\"\",providerFilter!==\"\"?(_providerFilter$value10=providerFilter.value)!==null&&_providerFilter$value10!==void 0?_providerFilter$value10:\"\":\"\",coordinationFilter,typeFilter,ciaIdFilter));}})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"relative group\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none z-10\",children:/*#__PURE__*/_jsx(\"svg\",{className:\"h-5 w-5 text-gray-400 group-focus-within:text-blue-500 transition-colors\",fill:\"none\",viewBox:\"0 0 24 24\",stroke:\"currentColor\",children:/*#__PURE__*/_jsx(\"path\",{strokeLinecap:\"round\",strokeLinejoin:\"round\",strokeWidth:2,d:\"M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10\"})})}),/*#__PURE__*/_jsxs(\"select\",{value:typeFilter,onChange:v=>{var _insuranceFilter$valu11,_providerFilter$value11,_insuranceFilter$valu12,_providerFilter$value12;setTypeFilter(v.target.value);dispatch(casesListDashboard(\"1\",\"\",idFilter,patientFilter,statusFilter,insuranceFilter!==\"\"?(_insuranceFilter$valu11=insuranceFilter.value)!==null&&_insuranceFilter$valu11!==void 0?_insuranceFilter$valu11:\"\":\"\",providerFilter!==\"\"?(_providerFilter$value11=providerFilter.value)!==null&&_providerFilter$value11!==void 0?_providerFilter$value11:\"\":\"\",coordinationFilter,v.target.value,ciaIdFilter));dispatch(casesListMap(\"0\",\"\",idFilter,patientFilter,statusFilter,insuranceFilter!==\"\"?(_insuranceFilter$valu12=insuranceFilter.value)!==null&&_insuranceFilter$valu12!==void 0?_insuranceFilter$valu12:\"\":\"\",providerFilter!==\"\"?(_providerFilter$value12=providerFilter.value)!==null&&_providerFilter$value12!==void 0?_providerFilter$value12:\"\":\"\",coordinationFilter,v.target.value,ciaIdFilter));},className:\"w-full pl-10 pr-8 py-3 rounded-lg bg-white text-sm text-gray-700 focus:outline-none focus:bg-blue-50 transition-all duration-200 appearance-none cursor-pointer\",children:[/*#__PURE__*/_jsx(\"option\",{value:\"\",children:\"All Types\"}),/*#__PURE__*/_jsx(\"option\",{value:\"Medical\",children:\"Medical\"}),/*#__PURE__*/_jsx(\"option\",{value:\"Technical\",children:\"Technical\"})]}),/*#__PURE__*/_jsx(\"div\",{className:\"absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none\",children:/*#__PURE__*/_jsx(\"svg\",{className:\"h-4 w-4 text-gray-400\",fill:\"none\",viewBox:\"0 0 24 24\",stroke:\"currentColor\",children:/*#__PURE__*/_jsx(\"path\",{strokeLinecap:\"round\",strokeLinejoin:\"round\",strokeWidth:2,d:\"M19 9l-7 7-7-7\"})})})]})]})}),/*#__PURE__*/_jsx(\"div\",{className:\" bg-gray-50 p-2 rounded-lg mb-4\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"relative group\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none z-10\",children:/*#__PURE__*/_jsx(\"svg\",{className:\"h-5 w-5 text-gray-400 group-focus-within:text-blue-500 transition-colors\",fill:\"none\",viewBox:\"0 0 24 24\",stroke:\"currentColor\",children:/*#__PURE__*/_jsx(\"path\",{strokeLinecap:\"round\",strokeLinejoin:\"round\",strokeWidth:2,d:\"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z\"})})}),/*#__PURE__*/_jsxs(\"select\",{value:statusFilter,onChange:v=>{var _insuranceFilter$valu13,_providerFilter$value13,_insuranceFilter$valu14,_providerFilter$value14;setStatusrFilter(v.target.value);dispatch(casesListDashboard(\"1\",\"\",idFilter,patientFilter,v.target.value,insuranceFilter!==\"\"?(_insuranceFilter$valu13=insuranceFilter.value)!==null&&_insuranceFilter$valu13!==void 0?_insuranceFilter$valu13:\"\":\"\",providerFilter!==\"\"?(_providerFilter$value13=providerFilter.value)!==null&&_providerFilter$value13!==void 0?_providerFilter$value13:\"\":\"\",coordinationFilter,typeFilter,ciaIdFilter));dispatch(casesListMap(\"0\",\"\",idFilter,patientFilter,v.target.value,insuranceFilter!==\"\"?(_insuranceFilter$valu14=insuranceFilter.value)!==null&&_insuranceFilter$valu14!==void 0?_insuranceFilter$valu14:\"\":\"\",providerFilter!==\"\"?(_providerFilter$value14=providerFilter.value)!==null&&_providerFilter$value14!==void 0?_providerFilter$value14:\"\":\"\",coordinationFilter,typeFilter,ciaIdFilter));},className:\"w-full pl-10 pr-8 py-[14px] rounded-lg bg-gray-50 text-sm text-gray-700 focus:outline-none focus:bg-blue-50 transition-all duration-200 appearance-none cursor-pointer\",children:[/*#__PURE__*/_jsx(\"option\",{value:\"\",children:\"All Status\"}),/*#__PURE__*/_jsx(\"option\",{value:\"pending-coordination\",children:\"Pending Coordination\"}),/*#__PURE__*/_jsx(\"option\",{value:\"coordinated-missing-m-r\",children:\"Coordinated, Missing M.R.\"}),/*#__PURE__*/_jsx(\"option\",{value:\"coordinated-missing-invoice\",children:\"Coordinated, Missing Invoice\"}),/*#__PURE__*/_jsx(\"option\",{value:\"waiting-for-insurance-authorization\",children:\"Waiting for Insurance Authorization\"}),/*#__PURE__*/_jsx(\"option\",{value:\"coordinated-patient-not-seen-yet\",children:\"Coordinated, Patient not seen yet\"}),/*#__PURE__*/_jsx(\"option\",{value:\"fully-coordinated\",children:\"Fully Coordinated\"}),/*#__PURE__*/_jsx(\"option\",{value:\"coordinated-missing-payment\",children:\"Coordinated, Missing Payment\"}),/*#__PURE__*/_jsx(\"option\",{value:\"coordination-fee\",children:\"Coordination Fee\"}),/*#__PURE__*/_jsx(\"option\",{value:\"failed\",children:\"Failed\"})]}),/*#__PURE__*/_jsx(\"div\",{className:\"absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none\",children:/*#__PURE__*/_jsx(\"svg\",{className:\"h-4 w-4 text-gray-400\",fill:\"none\",viewBox:\"0 0 24 24\",stroke:\"currentColor\",children:/*#__PURE__*/_jsx(\"path\",{strokeLinecap:\"round\",strokeLinejoin:\"round\",strokeWidth:2,d:\"M19 9l-7 7-7-7\"})})})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"relative group\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none z-20\",children:/*#__PURE__*/_jsx(\"svg\",{className:\"h-5 w-5 text-gray-400 group-focus-within:text-blue-500 transition-colors\",fill:\"none\",viewBox:\"0 0 24 24\",stroke:\"currentColor\",children:/*#__PURE__*/_jsx(\"path\",{strokeLinecap:\"round\",strokeLinejoin:\"round\",strokeWidth:2,d:\"M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z\"})})}),/*#__PURE__*/_jsx(Select,{value:insuranceFilter,onChange:option=>{setInsuranceFilter(option);if(option&&option.value){var _providerFilter$value15,_providerFilter$value16;dispatch(casesListDashboard(\"1\",\"\",idFilter,patientFilter,statusFilter,option.value,providerFilter!==\"\"?(_providerFilter$value15=providerFilter.value)!==null&&_providerFilter$value15!==void 0?_providerFilter$value15:\"\":\"\",coordinationFilter,typeFilter,ciaIdFilter));dispatch(casesListMap(\"0\",\"\",idFilter,patientFilter,statusFilter,option.value,providerFilter!==\"\"?(_providerFilter$value16=providerFilter.value)!==null&&_providerFilter$value16!==void 0?_providerFilter$value16:\"\":\"\",coordinationFilter,typeFilter,ciaIdFilter));}else{var _providerFilter$value17,_providerFilter$value18;dispatch(casesListDashboard(\"1\",\"\",idFilter,patientFilter,statusFilter,\"\",providerFilter!==\"\"?(_providerFilter$value17=providerFilter.value)!==null&&_providerFilter$value17!==void 0?_providerFilter$value17:\"\":\"\",coordinationFilter,typeFilter,ciaIdFilter));dispatch(casesListMap(\"0\",\"\",idFilter,patientFilter,statusFilter,\"\",providerFilter!==\"\"?(_providerFilter$value18=providerFilter.value)!==null&&_providerFilter$value18!==void 0?_providerFilter$value18:\"\":\"\",coordinationFilter,typeFilter,ciaIdFilter));}},options:insurances&&insurances.length>0?insurances.map(assurance=>({value:assurance.id,label:assurance.assurance_name||\"\"})):[],filterOption:(option,inputValue)=>option.label.toLowerCase().includes(inputValue.toLowerCase()),placeholder:\"Select Insurance...\",isSearchable:true,isClearable:true,className:\"react-select-container\",classNamePrefix:\"react-select\",styles:{control:(base,state)=>({...base,minHeight:'48px',paddingLeft:'32px',border:'none',borderRadius:'8px',backgroundColor:'#ffffff',boxShadow:'none','&:hover':{backgroundColor:'#eff6ff'},transition:'all 0.2s'}),placeholder:base=>({...base,color:'#9ca3af',fontSize:'14px'}),singleValue:base=>({...base,color:'#374151',fontSize:'14px'}),option:(base,state)=>({...base,backgroundColor:state.isSelected?'#3b82f6':state.isFocused?'#eff6ff':'white',color:state.isSelected?'white':'#374151',fontSize:'14px'})}})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"relative group\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none z-20\",children:/*#__PURE__*/_jsx(\"svg\",{className:\"h-5 w-5 text-gray-400 group-focus-within:text-blue-500 transition-colors\",fill:\"none\",viewBox:\"0 0 24 24\",stroke:\"currentColor\",children:/*#__PURE__*/_jsx(\"path\",{strokeLinecap:\"round\",strokeLinejoin:\"round\",strokeWidth:2,d:\"M17.982 18.725A7.488 7.488 0 0012 15.75a7.488 7.488 0 00-5.982 2.975m11.963 0a9 9 0 10-11.963 0m11.963 0A8.966 8.966 0 0112 21a8.966 8.966 0 01-5.982-2.275M15 9.75a3 3 0 11-6 0 3 3 0 016 0z\"})})}),/*#__PURE__*/_jsx(Select,{value:providerFilter,onChange:option=>{setProviderFilter(option);if(option&&option.value){var _insuranceFilter$valu15,_insuranceFilter$valu16;dispatch(casesListDashboard(\"1\",\"\",idFilter,patientFilter,statusFilter,insuranceFilter!==\"\"?(_insuranceFilter$valu15=insuranceFilter.value)!==null&&_insuranceFilter$valu15!==void 0?_insuranceFilter$valu15:\"\":\"\",option.value,coordinationFilter,typeFilter,ciaIdFilter));dispatch(casesListMap(\"0\",\"\",idFilter,patientFilter,statusFilter,insuranceFilter!==\"\"?(_insuranceFilter$valu16=insuranceFilter.value)!==null&&_insuranceFilter$valu16!==void 0?_insuranceFilter$valu16:\"\":\"\",option.value,coordinationFilter,typeFilter,ciaIdFilter));}else{var _insuranceFilter$valu17,_insuranceFilter$valu18;dispatch(casesListDashboard(\"1\",\"\",idFilter,patientFilter,statusFilter,insuranceFilter!==\"\"?(_insuranceFilter$valu17=insuranceFilter.value)!==null&&_insuranceFilter$valu17!==void 0?_insuranceFilter$valu17:\"\":\"\",\"\",coordinationFilter,typeFilter,ciaIdFilter));dispatch(casesListMap(\"0\",\"\",idFilter,patientFilter,statusFilter,insuranceFilter!==\"\"?(_insuranceFilter$valu18=insuranceFilter.value)!==null&&_insuranceFilter$valu18!==void 0?_insuranceFilter$valu18:\"\":\"\",\"\",coordinationFilter,typeFilter,ciaIdFilter));}},options:providers&&providers.length>0?providers.map(provider=>({value:provider.id,label:provider.full_name||\"\"})):[],filterOption:(option,inputValue)=>option.label.toLowerCase().includes(inputValue.toLowerCase()),placeholder:\"Select Provider...\",isSearchable:true,isClearable:true,className:\"react-select-container\",classNamePrefix:\"react-select\",styles:{control:(base,state)=>({...base,minHeight:'48px',paddingLeft:'32px',border:'none',borderRadius:'8px',backgroundColor:'#ffffff',boxShadow:'none','&:hover':{backgroundColor:'#eff6ff'},transition:'all 0.2s'}),placeholder:base=>({...base,color:'#9ca3af',fontSize:'14px'}),singleValue:base=>({...base,color:'#374151',fontSize:'14px'}),option:(base,state)=>({...base,backgroundColor:state.isSelected?'#3b82f6':state.isFocused?'#eff6ff':'white',color:state.isSelected?'white':'#374151',fontSize:'14px'})}})]}),/*#__PURE__*/_jsx(\"div\",{className:\"flex items-end\",children:/*#__PURE__*/_jsxs(\"button\",{onClick:()=>{setIdFilter(\"\");setInsuranceFilter(\"\");setProviderFilter(\"\");setStatusrFilter(\"\");setTypeFilter(\"\");setPatientFilter(\"\");setCiaIdFilter(\"\");},className:\"w-full flex items-center justify-center gap-2 bg-danger hover:bg-danger text-white px-4 py-3 rounded-lg font-medium text-sm transition-all duration-200\",children:[/*#__PURE__*/_jsx(\"svg\",{xmlns:\"http://www.w3.org/2000/svg\",fill:\"none\",viewBox:\"0 0 24 24\",strokeWidth:\"1.5\",stroke:\"currentColor\",className:\"w-4 h-4\",children:/*#__PURE__*/_jsx(\"path\",{strokeLinecap:\"round\",strokeLinejoin:\"round\",d:\"M16.023 9.348h4.992v-.001M2.985 19.644v-4.992m0 0h4.992m-4.993 0 3.181 3.183a8.25 8.25 0 0 0 13.803-3.7M4.031 9.865a8.25 8.25 0 0 1 13.803-3.7l3.181 3.182m0-4.991v4.99\"})}),\"Reset Filters\"]})})]})}),/*#__PURE__*/_jsx(\"div\",{className:\" w-full  px-1 py-3 \",children:/*#__PURE__*/_jsx(\"div\",{className:\"py-4 px-2 shadow-1 bg-white\",children:loadingCases?/*#__PURE__*/_jsx(Loader,{}):errorCases?/*#__PURE__*/_jsx(Alert,{type:\"error\",message:errorCases}):/*#__PURE__*/_jsxs(\"div\",{className:\"max-w-full overflow-x-auto \",children:[/*#__PURE__*/_jsxs(\"table\",{className:\"w-full table-auto\",children:[/*#__PURE__*/_jsx(\"thead\",{children:/*#__PURE__*/_jsxs(\"tr\",{className:\" bg-[#F3F5FB] text-left \",children:[/*#__PURE__*/_jsx(\"th\",{className:\"min-w-[60px] py-4 px-4 font-bold text-[#8E8E8E] text-xs w-max \",children:\"ID\"}),/*#__PURE__*/_jsx(\"th\",{className:\"min-w-[60px] py-4 px-4 font-bold text-[#8E8E8E] text-xs w-max \",children:\"Client\"}),/*#__PURE__*/_jsx(\"th\",{className:\"min-w-[60px] py-4 px-4 font-bold text-[#8E8E8E] text-xs w-max \",children:\"Patient Name\"}),/*#__PURE__*/_jsx(\"th\",{className:\"min-w-[30px] py-4 px-4 font-bold text-[#8E8E8E] text-xs w-max\",children:\"Type\"}),/*#__PURE__*/_jsx(\"th\",{className:\"min-w-[120px] py-4 px-4 font-bold text-[#8E8E8E] text-xs w-max\",children:\"Assigned Provider\"}),/*#__PURE__*/_jsx(\"th\",{className:\"min-w-[120px] py-4 px-4 font-bold text-[#8E8E8E] text-xs w-max\",children:\"Status\"}),/*#__PURE__*/_jsx(\"th\",{className:\"min-w-[120px] py-4 px-4 font-bold text-[#8E8E8E] text-xs w-max\",children:\"Date Created\"}),/*#__PURE__*/_jsx(\"th\",{className:\"min-w-[120px] py-4 px-4 font-bold text-[#8E8E8E] text-xs w-max\"})]})}),/*#__PURE__*/_jsxs(\"tbody\",{children:[cases===null||cases===void 0?void 0:cases.map((item,index)=>{var _item$assurance$assur,_item$assurance,_item$patient$full_na,_item$patient,_item$case_type;return/*#__PURE__*/ (//  <a href={`/cases/detail/${item.id}`}></a>\n_jsxs(\"tr\",{children:[/*#__PURE__*/_jsx(\"td\",{onClick:()=>{navigate(\"/cases-list/detail/\"+item.id);},className:\" py-3 px-4 min-w-[120px] cursor-pointer  \",children:/*#__PURE__*/_jsxs(\"p\",{className:\"text-black  text-xs w-max  \",children:[\"#\",item.id]})}),/*#__PURE__*/_jsx(\"td\",{onClick:()=>{navigate(\"/cases-list/detail/\"+item.id);},className:\" py-3 px-4 min-w-[120px] cursor-pointer  \",children:/*#__PURE__*/_jsx(\"p\",{className:\"text-black  text-xs w-max  \",children:(_item$assurance$assur=(_item$assurance=item.assurance)===null||_item$assurance===void 0?void 0:_item$assurance.assurance_name)!==null&&_item$assurance$assur!==void 0?_item$assurance$assur:\"---\"})}),/*#__PURE__*/_jsx(\"td\",{onClick:()=>{navigate(\"/cases-list/detail/\"+item.id);},className:\" py-3 px-4 min-w-[120px] cursor-pointer  \",children:/*#__PURE__*/_jsx(\"p\",{className:\"text-black  text-xs w-max  \",children:(_item$patient$full_na=(_item$patient=item.patient)===null||_item$patient===void 0?void 0:_item$patient.full_name)!==null&&_item$patient$full_na!==void 0?_item$patient$full_na:\"---\"})}),/*#__PURE__*/_jsx(\"td\",{onClick:()=>{navigate(\"/cases-list/detail/\"+item.id);},className:\" py-3 px-4 min-w-[120px] cursor-pointer  \",children:/*#__PURE__*/_jsx(\"p\",{className:\"text-black  text-xs w-max  \",children:(_item$case_type=item.case_type)!==null&&_item$case_type!==void 0?_item$case_type:\"---\"})}),/*#__PURE__*/_jsx(\"td\",{onClick:()=>{navigate(\"/cases-list/detail/\"+item.id);},className:\" py-3 px-4 min-w-[120px] cursor-pointer  \",children:/*#__PURE__*/_jsxs(\"p\",{className:\"text-black  text-xs w-max  \",children:[item.provider_services||0,\" Providers\"]})}),/*#__PURE__*/_jsx(\"td\",{onClick:()=>{navigate(\"/cases-list/detail/\"+item.id);},className:\" py-3 px-4 min-w-[120px] cursor-pointer  \",children:/*#__PURE__*/_jsx(\"p\",{className:\"text-black   text-xs  text-[10px]\",children:item.case_status&&item.case_status.length>0?item.case_status.map((stat,index)=>/*#__PURE__*/_jsxs(\"span\",{children:[caseStatus(stat.status_coordination),\"- \"]},index)):\"---\"})}),/*#__PURE__*/_jsx(\"td\",{onClick:()=>{navigate(\"/cases-list/detail/\"+item.id);},className:\" py-3 px-4 min-w-[120px] cursor-pointer  \",children:/*#__PURE__*/_jsx(\"p\",{className:\"text-black  text-xs w-max  \",children:formatDate(item.case_date)})}),/*#__PURE__*/_jsx(\"td\",{className:\" py-3 px-4 min-w-[120px]  \",children:/*#__PURE__*/_jsxs(\"p\",{className:\"text-black  text-xs w-max flex flex-row  \",children:[/*#__PURE__*/_jsx(Link,{className:\"mx-1 detail-class\",to:\"/cases-list/detail/\"+item.id,children:/*#__PURE__*/_jsxs(\"svg\",{xmlns:\"http://www.w3.org/2000/svg\",fill:\"none\",viewBox:\"0 0 24 24\",\"stroke-width\":\"1.5\",stroke:\"currentColor\",className:\"w-5 h-5 bg-warning rounded p-1 text-white text-center text-xs\",children:[/*#__PURE__*/_jsx(\"path\",{\"stroke-linecap\":\"round\",\"stroke-linejoin\":\"round\",d:\"M2.036 12.322a1.012 1.012 0 0 1 0-.639C3.423 7.51 7.36 4.5 12 4.5c4.638 0 8.573 3.007 9.963 7.178.07.207.07.431 0 .639C20.577 16.49 16.64 19.5 12 19.5c-4.638 0-8.573-3.007-9.963-7.178Z\"}),/*#__PURE__*/_jsx(\"path\",{\"stroke-linecap\":\"round\",\"stroke-linejoin\":\"round\",d:\"M15 12a3 3 0 1 1-6 0 3 3 0 0 1 6 0Z\"})]})}),/*#__PURE__*/_jsx(Link,{className:\"mx-1 update-class\",to:\"/cases-list/edit/\"+item.id,children:/*#__PURE__*/_jsx(\"svg\",{xmlns:\"http://www.w3.org/2000/svg\",fill:\"none\",viewBox:\"0 0 24 24\",strokeWidth:\"1.5\",stroke:\"currentColor\",className:\"w-5 h-5 bg-primary rounded p-1 text-white text-center text-xs\",children:/*#__PURE__*/_jsx(\"path\",{strokeLinecap:\"round\",strokeLinejoin:\"round\",d:\"m16.862 4.487 1.687-1.688a1.875 1.875 0 1 1 2.652 2.652L10.582 16.07a4.5 4.5 0 0 1-1.897 1.13L6 18l.8-2.685a4.5 4.5 0 0 1 1.13-1.897l8.932-8.931Zm0 0L19.5 7.125M18 14v4.75A2.25 2.25 0 0 1 15.75 21H5.25A2.25 2.25 0 0 1 3 18.75V8.25A2.25 2.25 0 0 1 5.25 6H10\"})})}),/*#__PURE__*/_jsx(\"div\",{onClick:()=>{setEventType(\"delete\");setCaseId(item.id);setIsDelete(true);},className:\"mx-1 delete-class cursor-pointer\",children:/*#__PURE__*/_jsx(\"svg\",{xmlns:\"http://www.w3.org/2000/svg\",fill:\"none\",viewBox:\"0 0 24 24\",\"stroke-width\":\"1.5\",stroke:\"currentColor\",className:\"w-5 h-5 bg-danger rounded p-1 text-white text-center text-xs\",children:/*#__PURE__*/_jsx(\"path\",{\"stroke-linecap\":\"round\",\"stroke-linejoin\":\"round\",d:\"m20.25 7.5-.625 10.632a2.25 2.25 0 0 1-2.247 2.118H6.622a2.25 2.25 0 0 1-2.247-2.118L3.75 7.5m6 4.125 2.25 2.25m0 0 2.25 2.25M12 13.875l2.25-2.25M12 13.875l-2.25 2.25M3.375 7.5h17.25c.621 0 1.125-.504 1.125-1.125v-1.5c0-.621-.504-1.125-1.125-1.125H3.375c-.621 0-1.125.504-1.125 1.125v1.5c0 .621.504 1.125 1.125 1.125Z\"})})})]})})]},index));}),/*#__PURE__*/_jsx(\"tr\",{className:\"h-5\"})]})]}),/*#__PURE__*/_jsx(\"div\",{className:\"\",children:/*#__PURE__*/_jsx(Paginate,{route:\"/dashboard?\",search:\"\",page:page,pages:pages})})]})})}),/*#__PURE__*/_jsx(\"div\",{className:\"py-2 px-4 md:px-6 xl:px-7.5 flex justify-between\",children:/*#__PURE__*/_jsx(\"h4\",{className:\" uppercase font-semibold text-black  text-xs w-max\",children:\"Providers Map\"})}),/*#__PURE__*/_jsx(\"div\",{className:\" w-full  px-1 py-3 \",children:/*#__PURE__*/_jsx(\"div\",{className:\"py-4 px-2 shadow-1 bg-white\",children:/*#__PURE__*/_jsxs(\"div\",{className:\" relative\",children:[/*#__PURE__*/_jsxs(MapContainer,{center:[0,0],zoom:2,style:{height:\"500px\",width:\"100%\"},children:[/*#__PURE__*/_jsx(TileLayer,{url:\"https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png\",attribution:\"\\xA9 <a href=\\\"https://www.openstreetmap.org/copyright\\\">OpenStreetMap</a> contributors\"}),casesMap&&casesMap.length>0?casesMap.map((caseitem,caseIndex)=>/*#__PURE__*/_jsx(\"div\",{children:caseitem.provider_services&&caseitem.provider_services.length>0?caseitem.provider_services.filter(provider=>provider.provider&&provider.provider.location_x&&provider.provider.location_y).map((provider,index)=>/*#__PURE__*/_jsx(Marker,{eventHandlers:{click:()=>{setIsOpenMap(true);setProviderMapSelect(provider);}// Trigger onClick event\n},position:[provider.provider.location_x,provider.provider.location_y],children:/*#__PURE__*/_jsxs(Popup,{children:[provider.provider.full_name,/*#__PURE__*/_jsx(\"br\",{})]})},`${caseIndex}-${index}`)):null},caseIndex)):null]}),isOpenMap?/*#__PURE__*/_jsx(\"div\",{className:\" absolute top-0 left-0 z-99999  p-2 md:w-1/3 w-2/3 h-full \",children:/*#__PURE__*/_jsxs(\"div\",{className:\"bg-white shadow-1 w-full h-full\",children:[/*#__PURE__*/_jsx(\"div\",{className:\" p-3 float-right \",children:/*#__PURE__*/_jsx(\"button\",{onClick:()=>{setIsOpenMap(false);setProviderMapSelect(null);},className:\"rounded-full p-1 bg-danger shadow-1 text-white flex items-center w-max \",children:/*#__PURE__*/_jsx(\"svg\",{xmlns:\"http://www.w3.org/2000/svg\",fill:\"none\",viewBox:\"0 0 24 24\",\"stroke-width\":\"1.5\",stroke:\"currentColor\",class:\"size-4\",children:/*#__PURE__*/_jsx(\"path\",{\"stroke-linecap\":\"round\",\"stroke-linejoin\":\"round\",d:\"M6 18 18 6M6 6l12 12\"})})})}),/*#__PURE__*/_jsx(\"div\",{className:\"pt-10 py-4 px-3\",children:providerMapSelect&&/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsxs(\"div\",{className:\"flex flex-row items-center text-xs my-3\",children:[/*#__PURE__*/_jsx(\"div\",{children:/*#__PURE__*/_jsx(\"svg\",{xmlns:\"http://www.w3.org/2000/svg\",fill:\"none\",viewBox:\"0 0 24 24\",\"stroke-width\":\"1.5\",stroke:\"currentColor\",className:\"size-5\",children:/*#__PURE__*/_jsx(\"path\",{\"stroke-linecap\":\"round\",\"stroke-linejoin\":\"round\",d:\"M11.42 15.17 17.25 21A2.652 2.652 0 0 0 21 17.25l-5.877-5.877M11.42 15.17l2.496-3.03c.317-.384.74-.626 1.208-.766M11.42 15.17l-4.655 5.653a2.548 2.548 0 1 1-3.586-3.586l6.837-5.63m5.108-.233c.55-.164 1.163-.188 1.743-.14a4.5 4.5 0 0 0 4.486-6.336l-3.276 3.277a3.004 3.004 0 0 1-2.25-2.25l3.276-3.276a4.5 4.5 0 0 0-6.336 4.486c.091 1.076-.071 2.264-.904 2.95l-.102.085m-1.745 1.437L5.909 7.5H4.5L2.25 3.75l1.5-1.5L7.5 4.5v1.409l4.26 4.26m-1.745 1.437 1.745-1.437m6.615 8.206L15.75 15.75M4.867 19.125h.008v.008h-.008v-.008Z\"})})}),/*#__PURE__*/_jsx(\"div\",{className:\"flex-1 px-2\",children:providerMapSelect.provider.services&&providerMapSelect.provider.services.length>0?providerMapSelect.provider.services.map((service,index)=>/*#__PURE__*/_jsxs(\"div\",{className:\"my-1\",children:[\"-\",\" \",service.service_type+(service.service_specialist!==\"\"&&service.service_specialist!==null?\": \"+service.service_specialist:\"\")]},index)):/*#__PURE__*/_jsx(\"div\",{className:\"my-1\",children:\"No services available\"})})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex flex-row items-center text-xs my-3\",children:[/*#__PURE__*/_jsx(\"div\",{children:/*#__PURE__*/_jsx(\"svg\",{xmlns:\"http://www.w3.org/2000/svg\",fill:\"none\",viewBox:\"0 0 24 24\",\"stroke-width\":\"1.5\",stroke:\"currentColor\",class:\"size-5\",children:/*#__PURE__*/_jsx(\"path\",{\"stroke-linecap\":\"round\",\"stroke-linejoin\":\"round\",d:\"M15.75 6a3.75 3.75 0 1 1-7.5 0 3.75 3.75 0 0 1 7.5 0ZM4.501 20.118a7.5 7.5 0 0 1 14.998 0A17.933 17.933 0 0 1 12 21.75c-2.676 0-5.216-.584-7.499-1.632Z\"})})}),/*#__PURE__*/_jsx(\"div\",{className:\"flex-1 px-2\",children:(_providerMapSelect$pr=providerMapSelect.provider.full_name)!==null&&_providerMapSelect$pr!==void 0?_providerMapSelect$pr:\"---\"})]}),providerMapSelect.provider.provider_infos&&providerMapSelect.provider.provider_infos.length>0?providerMapSelect.provider.provider_infos.map((item,index)=>/*#__PURE__*/_jsx(\"div\",{children:/*#__PURE__*/_jsxs(\"div\",{className:\"flex flex-row items-center text-xs my-3\",children:[/*#__PURE__*/_jsx(\"div\",{children:[\"Main Phone\",\"Whatsapp\",\"Billing Phone\"].includes(item.info_type)?/*#__PURE__*/_jsx(\"svg\",{xmlns:\"http://www.w3.org/2000/svg\",fill:\"none\",viewBox:\"0 0 24 24\",\"stroke-width\":\"1.5\",stroke:\"currentColor\",className:\"size-4\",children:/*#__PURE__*/_jsx(\"path\",{\"stroke-linecap\":\"round\",\"stroke-linejoin\":\"round\",d:\"M2.25 6.75c0 8.284 6.716 15 15 15h2.25a2.25 2.25 0 0 0 2.25-2.25v-1.372c0-.516-.351-.966-.852-1.091l-4.423-1.106c-.44-.11-.902.055-1.173.417l-.97 1.293c-.282.376-.769.542-1.21.38a12.035 12.035 0 0 1-7.143-7.143c-.162-.441.004-.928.38-1.21l1.293-.97c.363-.271.527-.734.417-1.173L6.963 3.102a1.125 1.125 0 0 0-1.091-.852H4.5A2.25 2.25 0 0 0 2.25 4.5v2.25Z\"})}):/*#__PURE__*/_jsx(\"svg\",{xmlns:\"http://www.w3.org/2000/svg\",fill:\"none\",viewBox:\"0 0 24 24\",\"stroke-width\":\"1.5\",stroke:\"currentColor\",className:\"size-4\",children:/*#__PURE__*/_jsx(\"path\",{\"stroke-linecap\":\"round\",\"stroke-linejoin\":\"round\",d:\"M21.75 6.75v10.5a2.25 2.25 0 0 1-2.25 2.25h-15a2.25 2.25 0 0 1-2.25-2.25V6.75m19.5 0A2.25 2.25 0 0 0 19.5 4.5h-15a2.25 2.25 0 0 0-2.25 2.25m19.5 0v.243a2.25 2.25 0 0 1-1.07 1.916l-7.5 4.615a2.25 2.25 0 0 1-2.36 0L3.32 8.91a2.25 2.25 0 0 1-1.07-1.916V6.75\"})})}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex-1 px-2\",children:[item.info_type,\" : \",item.info_value]})]})},index)):null,/*#__PURE__*/_jsxs(\"div\",{className:\"flex flex-row items-center text-xs my-3\",children:[/*#__PURE__*/_jsx(\"div\",{children:/*#__PURE__*/_jsxs(\"svg\",{xmlns:\"http://www.w3.org/2000/svg\",fill:\"none\",viewBox:\"0 0 24 24\",\"stroke-width\":\"1.5\",stroke:\"currentColor\",class:\"size-5\",children:[/*#__PURE__*/_jsx(\"path\",{\"stroke-linecap\":\"round\",\"stroke-linejoin\":\"round\",d:\"M15 10.5a3 3 0 1 1-6 0 3 3 0 0 1 6 0Z\"}),/*#__PURE__*/_jsx(\"path\",{\"stroke-linecap\":\"round\",\"stroke-linejoin\":\"round\",d:\"M19.5 10.5c0 7.142-7.5 11.25-7.5 11.25S4.5 17.642 4.5 10.5a7.5 7.5 0 1 1 15 0Z\"})]})}),/*#__PURE__*/_jsx(\"div\",{className:\"flex-1 px-2\",children:(_providerMapSelect$pr2=providerMapSelect.provider.address)!==null&&_providerMapSelect$pr2!==void 0?_providerMapSelect$pr2:\"---\"})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex flex-row items-center text-xs my-3\",children:[/*#__PURE__*/_jsx(\"div\",{children:/*#__PURE__*/_jsx(\"svg\",{xmlns:\"http://www.w3.org/2000/svg\",fill:\"none\",viewBox:\"0 0 24 24\",\"stroke-width\":\"1.5\",stroke:\"currentColor\",class:\"size-4\",children:/*#__PURE__*/_jsx(\"path\",{\"stroke-linecap\":\"round\",\"stroke-linejoin\":\"round\",d:\"M2.25 8.25h19.5M2.25 9h19.5m-16.5 5.25h6m-6 2.25h3m-3.75 3h15a2.25 2.25 0 0 0 2.25-2.25V6.75A2.25 2.25 0 0 0 19.5 4.5h-15a2.25 2.25 0 0 0-2.25 2.25v10.5A2.25 2.25 0 0 0 4.5 19.5Z\"})})}),/*#__PURE__*/_jsx(\"div\",{className:\"flex-1 px-2\",children:(_providerMapSelect$pr3=providerMapSelect.provider.payment_method)!==null&&_providerMapSelect$pr3!==void 0?_providerMapSelect$pr3:\"---\"})]}),/*#__PURE__*/_jsx(\"p\",{className:\"text-black  text-xs w-max flex flex-row my-4 \",children:/*#__PURE__*/_jsx(Link,{className:\"mx-1 update-class \",to:\"/providers-list/edit/\"+providerMapSelect.provider.id,children:/*#__PURE__*/_jsx(\"svg\",{xmlns:\"http://www.w3.org/2000/svg\",fill:\"none\",viewBox:\"0 0 24 24\",strokeWidth:\"1.5\",stroke:\"currentColor\",className:\"w-8 h-5 bg-primary rounded p-1 text-white text-center text-xs\",children:/*#__PURE__*/_jsx(\"path\",{strokeLinecap:\"round\",strokeLinejoin:\"round\",d:\"m16.862 4.487 1.687-1.688a1.875 1.875 0 1 1 2.652 2.652L10.582 16.07a4.5 4.5 0 0 1-1.897 1.13L6 18l.8-2.685a4.5 4.5 0 0 1 1.13-1.897l8.932-8.931Zm0 0L19.5 7.125M18 14v4.75A2.25 2.25 0 0 1 15.75 21H5.25A2.25 2.25 0 0 1 3 18.75V8.25A2.25 2.25 0 0 1 5.25 6H10\"})})})})]})})]})}):null]})})})]}),/*#__PURE__*/_jsx(ConfirmationModal,{isOpen:isDelete,message:eventType===\"delete\"?\"Are you sure you want to delete this case?\":\"Are you sure ?\",onConfirm:async()=>{if(eventType===\"cancel\"){setIsDelete(false);setEventType(\"\");setLoadEvent(false);}else if(eventType===\"delete\"&&caseId!==\"\"){setLoadEvent(true);dispatch(deleteCase(caseId));setIsDelete(false);setEventType(\"\");setLoadEvent(false);}else{setIsDelete(false);setEventType(\"\");setLoadEvent(false);}},onCancel:()=>{setIsDelete(false);setEventType(\"\");setLoadEvent(false);},loadEvent:loadEvent}),/*#__PURE__*/_jsx(\"div\",{className:\"grid md:grid-cols-2 w-full container mt-5\"})]})});}export default DashboardScreen;", "map": {"version": 3, "names": ["React", "useEffect", "useState", "useDispatch", "useSelector", "Link", "useLocation", "useNavigate", "useSearchParams", "casesListDashboard", "casesListMap", "deleteCase", "ConfirmationModal", "Paginate", "<PERSON><PERSON>", "Loader", "DefaultLayout", "MapContainer", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Popup", "L", "Select", "getListCoordinators", "providersListDashboard", "insurancesListDashboard", "<PERSON><PERSON><PERSON><PERSON>", "jsx", "_jsx", "jsxs", "_jsxs", "Icon", "<PERSON><PERSON><PERSON>", "prototype", "_getIconUrl", "mergeOptions", "iconRetinaUrl", "iconUrl", "shadowUrl", "DashboardScreen", "_providerMapSelect$pr", "_providerMapSelect$pr2", "_providerMapSelect$pr3", "navigate", "location", "searchParams", "page", "get", "dispatch", "providerMapSelect", "setProviderMapSelect", "isOpenMap", "setIsOpenMap", "idFilter", "setIdFilter", "ciaIdFilter", "setCiaIdFilter", "patientFilter", "set<PERSON>atient<PERSON><PERSON>er", "insuranceFilter", "setInsuranceFilter", "typeFilter", "setTypeFilter", "providerFilter", "setProviderFilter", "<PERSON><PERSON><PERSON>er", "setCoordinator<PERSON><PERSON><PERSON>", "statusFilter", "set<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "params", "URLSearchParams", "set", "pathname", "search", "toString", "isDelete", "setIsDelete", "loadEvent", "setLoadEvent", "eventType", "setEventType", "caseId", "setCaseId", "userLogin", "state", "userInfo", "listCases", "caseList", "cases", "loadingCases", "errorCases", "pages", "listCasesMap", "caseListMap", "casesMap", "loadingCasesMap", "errorCasesMap", "caseDelete", "loadingCaseDelete", "errorCaseDelete", "successCaseDelete", "listProviders", "providerList", "providers", "loadingProviders", "errorProviders", "listInsurances", "insuranceList", "insurances", "loadingInsurances", "errorInsurances", "listCoordinators", "coordinatorsList", "coordinators", "loadingCoordinators", "errorCoordinators", "redirect", "_insuranceFilter$valu", "_providerFilter$value", "_insuranceFilter$valu2", "_providerFilter$value2", "parser", "result", "getResult", "browser", "name", "device", "model", "type", "value", "_insuranceFilter$valu3", "_providerFilter$value3", "_insuranceFilter$valu4", "_providerFilter$value4", "formatDate", "dateString", "date", "Date", "toLocaleDateString", "year", "month", "day", "caseStatus", "casestatus", "children", "className", "href", "xmlns", "fill", "viewBox", "stroke", "strokeLinecap", "strokeLinejoin", "d", "class", "strokeWidth", "placeholder", "onChange", "v", "_insuranceFilter$valu5", "_providerFilter$value5", "_insuranceFilter$valu6", "_providerFilter$value6", "target", "_insuranceFilter$valu7", "_providerFilter$value7", "_insuranceFilter$valu8", "_providerFilter$value8", "_insuranceFilter$valu9", "_providerFilter$value9", "_insuranceFilter$valu10", "_providerFilter$value10", "_insuranceFilter$valu11", "_providerFilter$value11", "_insuranceFilter$valu12", "_providerFilter$value12", "_insuranceFilter$valu13", "_providerFilter$value13", "_insuranceFilter$valu14", "_providerFilter$value14", "option", "_providerFilter$value15", "_providerFilter$value16", "_providerFilter$value17", "_providerFilter$value18", "options", "length", "map", "assurance", "id", "label", "assurance_name", "filterOption", "inputValue", "toLowerCase", "includes", "isSearchable", "isClearable", "classNamePrefix", "styles", "control", "base", "minHeight", "paddingLeft", "border", "borderRadius", "backgroundColor", "boxShadow", "transition", "color", "fontSize", "singleValue", "isSelected", "isFocused", "_insuranceFilter$valu15", "_insuranceFilter$valu16", "_insuranceFilter$valu17", "_insuranceFilter$valu18", "provider", "full_name", "onClick", "message", "item", "index", "_item$assurance$assur", "_item$assurance", "_item$patient$full_na", "_item$patient", "_item$case_type", "patient", "case_type", "provider_services", "case_status", "stat", "status_coordination", "case_date", "to", "route", "center", "zoom", "style", "height", "width", "url", "attribution", "caseitem", "caseIndex", "filter", "location_x", "location_y", "eventHandlers", "click", "position", "services", "service", "service_type", "service_specialist", "provider_infos", "info_type", "info_value", "address", "payment_method", "isOpen", "onConfirm", "onCancel"], "sources": ["/Users/<USER>/Desktop/Freelances/<PERSON><PERSON><PERSON>/UNIMEDCARE/web-front/src/screens/dashboard/DashboardScreen.js"], "sourcesContent": ["import React, { useEffect, useState } from \"react\";\nimport { useDispatch, useSelector } from \"react-redux\";\nimport {\n  Link,\n  useLocation,\n  useNavigate,\n  useSearchParams,\n} from \"react-router-dom\";\nimport {\n  casesListDashboard,\n  casesListMap,\n  deleteCase,\n} from \"../../redux/actions/caseActions\";\nimport ConfirmationModal from \"../../components/ConfirmationModal\";\nimport Paginate from \"../../components/Paginate\";\nimport Alert from \"../../components/Alert\";\nimport Loader from \"../../components/Loader\";\nimport DefaultLayout from \"../../layouts/DefaultLayout\";\n\nimport { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON> } from \"react-leaflet\";\nimport \"leaflet/dist/leaflet.css\";\nimport L from \"leaflet\";\nimport Select from \"react-select\";\nimport { getListCoordinators } from \"../../redux/actions/userActions\";\nimport { providersListDashboard } from \"../../redux/actions/providerActions\";\nimport { insurancesListDashboard } from \"../../redux/actions/insuranceActions\";\nimport { UAParser } from \"ua-parser-js\";\n\ndelete L.Icon.Default.prototype._getIconUrl;\nL.Icon.Default.mergeOptions({\n  iconRetinaUrl:\n    \"https://unpkg.com/leaflet@1.7.1/dist/images/marker-icon-2x.png\",\n  iconUrl: \"https://unpkg.com/leaflet@1.7.1/dist/images/marker-icon.png\",\n  shadowUrl: \"https://unpkg.com/leaflet@1.7.1/dist/images/marker-shadow.png\",\n});\n\nfunction DashboardScreen() {\n  const navigate = useNavigate();\n  const location = useLocation();\n  const [searchParams] = useSearchParams();\n  const page = searchParams.get(\"page\") || \"1\";\n  const dispatch = useDispatch();\n\n  const [providerMapSelect, setProviderMapSelect] = useState(null);\n  const [isOpenMap, setIsOpenMap] = useState(false);\n\n  const [idFilter, setIdFilter] = useState(searchParams.get(\"filterid\") || \"\");\n  const [ciaIdFilter, setCiaIdFilter] = useState(\n    searchParams.get(\"filterciaid\") || \"\"\n  );\n  const [patientFilter, setPatientFilter] = useState(\n    searchParams.get(\"filterpatient\") || \"\"\n  );\n  const [insuranceFilter, setInsuranceFilter] = useState(\n    searchParams.get(\"filterinsurance\") || \"\"\n  );\n  const [typeFilter, setTypeFilter] = useState(\n    searchParams.get(\"filtertype\") || \"\"\n  );\n  const [providerFilter, setProviderFilter] = useState(\n    searchParams.get(\"filterprovider\") || \"\"\n  );\n  const [coordinationFilter, setCoordinatorFilter] = useState(\n    searchParams.get(\"filtercoordination\") || \"\"\n  );\n  const [statusFilter, setStatusrFilter] = useState(\n    searchParams.get(\"filterstatus\") || \"\"\n  );\n\n  useEffect(() => {\n    const params = new URLSearchParams();\n\n    if (idFilter) params.set(\"filterid\", idFilter);\n    if (ciaIdFilter) params.set(\"filterciaid\", ciaIdFilter);\n    if (patientFilter) params.set(\"filterpatient\", patientFilter);\n    if (insuranceFilter) params.set(\"filterinsurance\", insuranceFilter);\n    if (typeFilter) params.set(\"filtertype\", typeFilter);\n    if (providerFilter) params.set(\"filterprovider\", providerFilter);\n    if (coordinationFilter)\n      params.set(\"filtercoordination\", coordinationFilter);\n    if (statusFilter) params.set(\"filterstatus\", statusFilter);\n\n    // Add default page\n    params.set(\"page\", \"1\");\n\n    // Update URL\n    navigate({\n      pathname: location.pathname,\n      search: params.toString(),\n    });\n  }, [\n    idFilter,\n    patientFilter,\n    statusFilter,\n    insuranceFilter,\n    providerFilter,\n    coordinationFilter,\n    typeFilter,\n    ciaIdFilter,\n    dispatch,\n    navigate,\n    location.pathname,\n  ]);\n\n  const [isDelete, setIsDelete] = useState(false);\n  const [loadEvent, setLoadEvent] = useState(false);\n  const [eventType, setEventType] = useState(\"\");\n  const [caseId, setCaseId] = useState(\"\");\n\n  const userLogin = useSelector((state) => state.userLogin);\n  const { userInfo } = userLogin;\n\n  const listCases = useSelector((state) => state.caseList);\n  const { cases, loadingCases, errorCases, pages } = listCases;\n\n  const listCasesMap = useSelector((state) => state.caseListMap);\n  const { casesMap, loadingCasesMap, errorCasesMap } = listCasesMap;\n\n  const caseDelete = useSelector((state) => state.deleteCase);\n  const { loadingCaseDelete, errorCaseDelete, successCaseDelete } = caseDelete;\n\n  const listProviders = useSelector((state) => state.providerList);\n  const { providers, loadingProviders, errorProviders } = listProviders;\n\n  const listInsurances = useSelector((state) => state.insuranceList);\n  const { insurances, loadingInsurances, errorInsurances } = listInsurances;\n\n  const listCoordinators = useSelector((state) => state.coordinatorsList);\n  const { coordinators, loadingCoordinators, errorCoordinators } =\n    listCoordinators;\n\n  const redirect = \"/\";\n\n  useEffect(() => {\n    if (!userInfo) {\n      navigate(redirect);\n    } else {\n      const parser = new UAParser();\n      const result = parser.getResult();\n\n      const browser = result.browser.name || \"Unknown browser\";\n      const device =\n        result.device.model || result.device.type || \"Unknown device\";\n\n      // get list cases (optimized for dashboard)\n      dispatch(\n        casesListDashboard(\n          page,\n          \"\",\n          idFilter,\n          patientFilter,\n          statusFilter,\n          insuranceFilter !== \"\" ? insuranceFilter.value ?? \"\" : \"\",\n          providerFilter !== \"\" ? providerFilter.value ?? \"\" : \"\",\n          coordinationFilter,\n          typeFilter,\n          ciaIdFilter\n        )\n      );\n      // get list case maps \n      dispatch(\n        casesListMap(\n          \"0\",\n          \"\",\n          idFilter,\n          patientFilter,\n          statusFilter,\n          insuranceFilter !== \"\" ? insuranceFilter.value ?? \"\" : \"\",\n          providerFilter !== \"\" ? providerFilter.value ?? \"\" : \"\",\n          coordinationFilter,\n          typeFilter,\n          ciaIdFilter\n        )\n      );\n      // get List Coordinators\n      // dispatch(getListCoordinators(\"0\"));\n      // get providers List (optimized for dashboard)\n      dispatch(providersListDashboard(\"0\"));\n      // get Insuranes List (optimized for dashboard)\n      dispatch(insurancesListDashboard(\"0\"));\n      // \n    }\n  }, [\n    navigate,\n    userInfo,\n    dispatch,\n    page,\n    // idFilter,\n    // patientFilter,\n    // statusFilter,\n    // insuranceFilter,\n    // providerFilter,\n    // coordinationFilter,\n    // typeFilter,\n  ]);\n\n  useEffect(() => {\n    if (successCaseDelete) {\n      dispatch(\n        casesListDashboard(\n          \"1\",\n          \"\",\n          idFilter,\n          patientFilter,\n          statusFilter,\n          insuranceFilter !== \"\" ? insuranceFilter.value ?? \"\" : \"\",\n          providerFilter !== \"\" ? providerFilter.value ?? \"\" : \"\",\n          coordinationFilter,\n          typeFilter,\n          ciaIdFilter\n        )\n      );\n      dispatch(\n        casesListMap(\n          \"0\",\n          \"\",\n          idFilter,\n          patientFilter,\n          statusFilter,\n          insuranceFilter !== \"\" ? insuranceFilter.value ?? \"\" : \"\",\n          providerFilter !== \"\" ? providerFilter.value ?? \"\" : \"\",\n          coordinationFilter,\n          typeFilter,\n          ciaIdFilter\n        )\n      );\n    }\n  }, [successCaseDelete]);\n\n  const formatDate = (dateString) => {\n    if (dateString && dateString !== \"\") {\n      const date = new Date(dateString);\n      return date.toLocaleDateString(\"en-US\", {\n        year: \"numeric\",\n        month: \"long\",\n        day: \"numeric\",\n      });\n    } else {\n      return dateString && dateString !== \"\" ? dateString : \"----\";\n    }\n  };\n\n  const caseStatus = (casestatus) => {\n    switch (casestatus) {\n      case \"pending-coordination\":\n        return \"Pending Coordination\";\n      case \"coordinated-missing-m-r\":\n        return \"Coordinated, Missing M.R.\";\n      case \"coordinated-missing-invoice\":\n        return \"Coordinated, Missing Invoice\";\n      case \"waiting-for-insurance-authorization\":\n        return \"Waiting for Insurance Authorization\";\n      case \"coordinated-patient-not-seen-yet\":\n        return \"Coordinated, Patient not seen yet\";\n      case \"fully-coordinated\":\n        return \"Fully Coordinated\";\n      case \"coordination-fee\":\n        return \"Coordination Fee\";\n      case \"coordinated-missing-payment\":\n        return \"Coordinated, Missing Payment\";\n      case \"failed\":\n        return \"Failed\";\n      default:\n        return casestatus;\n    }\n  };\n\n  return (\n    <DefaultLayout>\n      <div>\n        <div className=\"flex flex-row text-sm items-center my-1\">\n          {/* home */}\n          <a href=\"/dashboard\">\n            <div className=\"flex flex-row  items-center hover:text-black \">\n              <svg\n                xmlns=\"http://www.w3.org/2000/svg\"\n                fill=\"none\"\n                viewBox=\"0 0 24 24\"\n                stroke-width=\"1.5\"\n                stroke=\"currentColor\"\n                className=\"w-4 h-4\"\n              >\n                <path\n                  strokeLinecap=\"round\"\n                  strokeLinejoin=\"round\"\n                  d=\"m2.25 12 8.954-8.955c.44-.439 1.152-.439 1.591 0L21.75 12M4.5 9.75v10.125c0 .621.504 1.125 1.125 1.125H9.75v-4.875c0-.621.504-1.125 1.125-1.125h2.25c.621 0 1.125.504 1.125 1.125V21h4.125c.621 0 1.125-.504 1.125-1.125V9.75M8.25 21h8.25\"\n                />\n              </svg>\n              <span className=\"mx-1\">Dashboard</span>\n            </div>\n          </a>\n        </div>\n\n        \n        {/*  */}\n        <div className=\"rounded-sm border border-stroke px-2 pt-6 pb-2.5 shadow-default   dark:bg-boxdark \">\n          <div className=\"py-2 px-4 md:px-6 xl:px-7.5 flex md:flex-row flex-col justify-between\">\n            <h4 className=\" uppercase font-semibold text-black  text-xs w-max\">\n              Cases list\n            </h4>\n            <div className=\"flex flex-row justify-end\">\n              <a\n                href=\"/cases-list/add\"\n                className=\"px-4 py-3 rounded-full text-white bg-[#0388A6] flex flex-row text-xs items-center\"\n              >\n                <svg\n                  xmlns=\"http://www.w3.org/2000/svg\"\n                  fill=\"none\"\n                  viewBox=\"0 0 24 24\"\n                  stroke-width=\"1.5\"\n                  stroke=\"currentColor\"\n                  class=\"size-4\"\n                >\n                  <path\n                    stroke-linecap=\"round\"\n                    stroke-linejoin=\"round\"\n                    d=\"M12 4.5v15m7.5-7.5h-15\"\n                  />\n                </svg>\n\n                <div className=\"mx-2\">Create new case</div>\n              </a>\n            </div>\n          </div>\n          {/* Clean Filter Section */}\n          <div className=\"bg-gray-50 p-2 rounded-lg mb-2\">\n            <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4\">\n              {/* Search ID Case */}\n              <div className=\"relative group\">\n                <div className=\"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none\">\n                  <svg className=\"h-5 w-5 text-gray-400 group-focus-within:text-blue-500 transition-colors\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z\" />\n                  </svg>\n                </div>\n                <input\n                  className=\"w-full pl-10 pr-4 py-3 rounded-lg bg-white text-sm text-gray-700 placeholder-gray-400 focus:outline-none focus:bg-blue-50 transition-all duration-200\"\n                  placeholder=\"Search Case ID...\"\n                  type=\"text\"\n                  value={idFilter}\n                  onChange={(v) => {\n                    setIdFilter(v.target.value);\n                    dispatch(\n                      casesListDashboard(\n                        \"1\",\n                        \"\",\n                        v.target.value,\n                        patientFilter,\n                        statusFilter,\n                        insuranceFilter !== \"\"\n                          ? insuranceFilter.value ?? \"\"\n                          : \"\",\n                        providerFilter !== \"\" ? providerFilter.value ?? \"\" : \"\",\n                        coordinationFilter,\n                        typeFilter,\n                        ciaIdFilter\n                      )\n                    );\n                    dispatch(\n                      casesListMap(\n                        \"0\",\n                        \"\",\n                        v.target.value,\n                        patientFilter,\n                        statusFilter,\n                        insuranceFilter !== \"\"\n                          ? insuranceFilter.value ?? \"\"\n                          : \"\",\n                        providerFilter !== \"\" ? providerFilter.value ?? \"\" : \"\",\n                        coordinationFilter,\n                        typeFilter,\n                        ciaIdFilter\n                      )\n                    );\n                  }}\n                />\n              </div>\n\n              {/* CIA Reference */}\n              <div className=\"relative group\">\n                <div className=\"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none\">\n                  <svg className=\"h-5 w-5 text-gray-400 group-focus-within:text-blue-500 transition-colors\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z\" />\n                  </svg>\n                </div>\n                <input\n                  className=\"w-full pl-10 pr-4 py-3 rounded-lg bg-white text-sm text-gray-700 placeholder-gray-400 focus:outline-none focus:bg-blue-50 transition-all duration-200\"\n                  placeholder=\"CIA Reference...\"\n                  type=\"text\"\n                  value={ciaIdFilter}\n                  onChange={(v) => {\n                    setCiaIdFilter(v.target.value);\n                    dispatch(\n                      casesListDashboard(\n                        \"1\",\n                        \"\",\n                        idFilter,\n                        patientFilter,\n                        statusFilter,\n                        insuranceFilter !== \"\"\n                          ? insuranceFilter.value ?? \"\"\n                          : \"\",\n                        providerFilter !== \"\" ? providerFilter.value ?? \"\" : \"\",\n                        coordinationFilter,\n                        typeFilter,\n                        v.target.value\n                      )\n                    );\n                    dispatch(\n                      casesListMap(\n                        \"0\",\n                        \"\",\n                        idFilter,\n                        patientFilter,\n                        statusFilter,\n                        insuranceFilter !== \"\"\n                          ? insuranceFilter.value ?? \"\"\n                          : \"\",\n                        providerFilter !== \"\" ? providerFilter.value ?? \"\" : \"\",\n                        coordinationFilter,\n                        typeFilter,\n                        v.target.value\n                      )\n                    );\n                  }}\n                />\n              </div>\n\n              {/* Patient Name */}\n              <div className=\"relative group\">\n                <div className=\"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none\">\n                  <svg className=\"h-5 w-5 text-gray-400 group-focus-within:text-blue-500 transition-colors\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z\" />\n                  </svg>\n                </div>\n                <input\n                  className=\"w-full pl-10 pr-4 py-3 rounded-lg bg-white text-sm text-gray-700 placeholder-gray-400 focus:outline-none focus:bg-blue-50 transition-all duration-200\"\n                  placeholder=\"Patient Name...\"\n                  type=\"text\"\n                  value={patientFilter}\n                  onChange={(v) => {\n                    setPatientFilter(v.target.value);\n                    dispatch(\n                      casesListDashboard(\n                        \"1\",\n                        \"\",\n                        idFilter,\n                        v.target.value,\n                        statusFilter,\n                        insuranceFilter !== \"\"\n                          ? insuranceFilter.value ?? \"\"\n                          : \"\",\n                        providerFilter !== \"\" ? providerFilter.value ?? \"\" : \"\",\n                        coordinationFilter,\n                        typeFilter,\n                        ciaIdFilter\n                      )\n                    );\n                    dispatch(\n                      casesListMap(\n                        \"0\",\n                        \"\",\n                        idFilter,\n                        v.target.value,\n                        statusFilter,\n                        insuranceFilter !== \"\"\n                          ? insuranceFilter.value ?? \"\"\n                          : \"\",\n                        providerFilter !== \"\" ? providerFilter.value ?? \"\" : \"\",\n                        coordinationFilter,\n                        typeFilter,\n                        ciaIdFilter\n                      )\n                    );\n                  }}\n                />\n              </div>\n\n              {/* Case Type */}\n              <div className=\"relative group\">\n                <div className=\"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none z-10\">\n                  <svg className=\"h-5 w-5 text-gray-400 group-focus-within:text-blue-500 transition-colors\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10\" />\n                  </svg>\n                </div>\n                <select\n                  value={typeFilter}\n                  onChange={(v) => {\n                    setTypeFilter(v.target.value);\n                    dispatch(\n                      casesListDashboard(\n                        \"1\",\n                        \"\",\n                        idFilter,\n                        patientFilter,\n                        statusFilter,\n                        insuranceFilter !== \"\"\n                          ? insuranceFilter.value ?? \"\"\n                          : \"\",\n                        providerFilter !== \"\" ? providerFilter.value ?? \"\" : \"\",\n                        coordinationFilter,\n                        v.target.value,\n                        ciaIdFilter\n                      )\n                    );\n                    dispatch(\n                      casesListMap(\n                        \"0\",\n                        \"\",\n                        idFilter,\n                        patientFilter,\n                        statusFilter,\n                        insuranceFilter !== \"\"\n                          ? insuranceFilter.value ?? \"\"\n                          : \"\",\n                        providerFilter !== \"\" ? providerFilter.value ?? \"\" : \"\",\n                        coordinationFilter,\n                        v.target.value,\n                        ciaIdFilter\n                      )\n                    );\n                  }}\n                  className=\"w-full pl-10 pr-8 py-3 rounded-lg bg-white text-sm text-gray-700 focus:outline-none focus:bg-blue-50 transition-all duration-200 appearance-none cursor-pointer\"\n                >\n                  <option value=\"\">All Types</option>\n                  <option value=\"Medical\">Medical</option>\n                  <option value=\"Technical\">Technical</option>\n                </select>\n                <div className=\"absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none\">\n                  <svg className=\"h-4 w-4 text-gray-400\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M19 9l-7 7-7-7\" />\n                  </svg>\n                </div>\n              </div>\n            </div>\n          </div>\n          {/* Advanced Filters Row */}\n          <div className=\" bg-gray-50 p-2 rounded-lg mb-4\">\n            <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4\">\n              {/* Status Filter */}\n              <div className=\"relative group\">\n                <div className=\"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none z-10\">\n                  <svg className=\"h-5 w-5 text-gray-400 group-focus-within:text-blue-500 transition-colors\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z\" />\n                  </svg>\n                </div>\n                <select\n                  value={statusFilter}\n                  onChange={(v) => {\n                    setStatusrFilter(v.target.value);\n                    dispatch(\n                      casesListDashboard(\n                        \"1\",\n                        \"\",\n                        idFilter,\n                        patientFilter,\n                        v.target.value,\n                        insuranceFilter !== \"\"\n                          ? insuranceFilter.value ?? \"\"\n                          : \"\",\n                        providerFilter !== \"\" ? providerFilter.value ?? \"\" : \"\",\n                        coordinationFilter,\n                        typeFilter,\n                        ciaIdFilter\n                      )\n                    );\n                    dispatch(\n                      casesListMap(\n                        \"0\",\n                        \"\",\n                        idFilter,\n                        patientFilter,\n                        v.target.value,\n                        insuranceFilter !== \"\"\n                          ? insuranceFilter.value ?? \"\"\n                          : \"\",\n                        providerFilter !== \"\" ? providerFilter.value ?? \"\" : \"\",\n                        coordinationFilter,\n                        typeFilter,\n                        ciaIdFilter\n                      )\n                    );\n                  }}\n                  className=\"w-full pl-10 pr-8 py-[14px] rounded-lg bg-gray-50 text-sm text-gray-700 focus:outline-none focus:bg-blue-50 transition-all duration-200 appearance-none cursor-pointer\"\n                >\n                  <option value=\"\">All Status</option>\n                  <option value=\"pending-coordination\">Pending Coordination</option>\n                  <option value=\"coordinated-missing-m-r\">Coordinated, Missing M.R.</option>\n                  <option value=\"coordinated-missing-invoice\">Coordinated, Missing Invoice</option>\n                  <option value=\"waiting-for-insurance-authorization\">Waiting for Insurance Authorization</option>\n                  <option value=\"coordinated-patient-not-seen-yet\">Coordinated, Patient not seen yet</option>\n                  <option value=\"fully-coordinated\">Fully Coordinated</option>\n                  <option value=\"coordinated-missing-payment\">Coordinated, Missing Payment</option>\n                  <option value=\"coordination-fee\">Coordination Fee</option>\n                  <option value=\"failed\">Failed</option>\n                </select>\n                <div className=\"absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none\">\n                  <svg className=\"h-4 w-4 text-gray-400\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M19 9l-7 7-7-7\" />\n                  </svg>\n                </div>\n              </div>\n\n              {/* Insurance Filter */}\n              <div className=\"relative group\">\n                <div className=\"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none z-20\">\n                  <svg className=\"h-5 w-5 text-gray-400 group-focus-within:text-blue-500 transition-colors\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z\" />\n                  </svg>\n                </div>\n                <Select\n                  value={insuranceFilter}\n                  onChange={(option) => {\n                    setInsuranceFilter(option);\n                    if (option && option.value) {\n                      dispatch(\n                        casesListDashboard(\n                          \"1\",\n                          \"\",\n                          idFilter,\n                          patientFilter,\n                          statusFilter,\n                          option.value,\n                          providerFilter !== \"\"\n                            ? providerFilter.value ?? \"\"\n                            : \"\",\n                          coordinationFilter,\n                          typeFilter,\n                          ciaIdFilter\n                        )\n                      );\n                      dispatch(\n                        casesListMap(\n                          \"0\",\n                          \"\",\n                          idFilter,\n                          patientFilter,\n                          statusFilter,\n                          option.value,\n                          providerFilter !== \"\"\n                            ? providerFilter.value ?? \"\"\n                            : \"\",\n                          coordinationFilter,\n                          typeFilter,\n                          ciaIdFilter\n                        )\n                      );\n                    } else {\n                      dispatch(\n                        casesListDashboard(\n                          \"1\",\n                          \"\",\n                          idFilter,\n                          patientFilter,\n                          statusFilter,\n                          \"\",\n                          providerFilter !== \"\"\n                            ? providerFilter.value ?? \"\"\n                            : \"\",\n                          coordinationFilter,\n                          typeFilter,\n                          ciaIdFilter\n                        )\n                      );\n                      dispatch(\n                        casesListMap(\n                          \"0\",\n                          \"\",\n                          idFilter,\n                          patientFilter,\n                          statusFilter,\n                          \"\",\n                          providerFilter !== \"\"\n                            ? providerFilter.value ?? \"\"\n                            : \"\",\n                          coordinationFilter,\n                          typeFilter,\n                          ciaIdFilter\n                        )\n                      );\n                    }\n                  }}\n                  options={insurances && insurances.length > 0 ? insurances.map((assurance) => ({\n                    value: assurance.id,\n                    label: assurance.assurance_name || \"\",\n                  })) : []}\n                  filterOption={(option, inputValue) =>\n                    option.label\n                      .toLowerCase()\n                      .includes(inputValue.toLowerCase())\n                  }\n                  placeholder=\"Select Insurance...\"\n                  isSearchable\n                  isClearable\n                  className=\"react-select-container\"\n                  classNamePrefix=\"react-select\"\n                  styles={{\n                    control: (base, state) => ({\n                      ...base,\n                      minHeight: '48px',\n                      paddingLeft: '32px',\n                      border: 'none',\n                      borderRadius: '8px',\n                      backgroundColor: '#ffffff',\n                      boxShadow: 'none',\n                      '&:hover': {\n                        backgroundColor: '#eff6ff',\n                      },\n                      transition: 'all 0.2s',\n                    }),\n                    placeholder: (base) => ({\n                      ...base,\n                      color: '#9ca3af',\n                      fontSize: '14px',\n                    }),\n                    singleValue: (base) => ({\n                      ...base,\n                      color: '#374151',\n                      fontSize: '14px',\n                    }),\n                    option: (base, state) => ({\n                      ...base,\n                      backgroundColor: state.isSelected ? '#3b82f6' : state.isFocused ? '#eff6ff' : 'white',\n                      color: state.isSelected ? 'white' : '#374151',\n                      fontSize: '14px',\n                    }),\n                  }}\n                />\n              </div>\n\n              {/* Provider Filter */}\n              <div className=\"relative group\">\n                <div className=\"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none z-20\">\n                  <svg className=\"h-5 w-5 text-gray-400 group-focus-within:text-blue-500 transition-colors\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M17.982 18.725A7.488 7.488 0 0012 15.75a7.488 7.488 0 00-5.982 2.975m11.963 0a9 9 0 10-11.963 0m11.963 0A8.966 8.966 0 0112 21a8.966 8.966 0 01-5.982-2.275M15 9.75a3 3 0 11-6 0 3 3 0 016 0z\" />\n                  </svg>\n                </div>\n                <Select\n                  value={providerFilter}\n                  onChange={(option) => {\n                    setProviderFilter(option);\n                    if (option && option.value) {\n                      dispatch(\n                        casesListDashboard(\n                          \"1\",\n                          \"\",\n                          idFilter,\n                          patientFilter,\n                          statusFilter,\n                          insuranceFilter !== \"\" ? insuranceFilter.value ?? \"\" : \"\",\n                          option.value,\n                          coordinationFilter,\n                          typeFilter,\n                          ciaIdFilter\n                        )\n                      );\n                      dispatch(\n                        casesListMap(\n                          \"0\",\n                          \"\",\n                          idFilter,\n                          patientFilter,\n                          statusFilter,\n                          insuranceFilter !== \"\" ? insuranceFilter.value ?? \"\" : \"\",\n                          option.value,\n                          coordinationFilter,\n                          typeFilter,\n                          ciaIdFilter\n                        )\n                      );\n                    } else {\n                      dispatch(\n                        casesListDashboard(\n                          \"1\",\n                          \"\",\n                          idFilter,\n                          patientFilter,\n                          statusFilter,\n                          insuranceFilter !== \"\" ? insuranceFilter.value ?? \"\" : \"\",\n                          \"\",\n                          coordinationFilter,\n                          typeFilter,\n                          ciaIdFilter\n                        )\n                      );\n                      dispatch(\n                        casesListMap(\n                          \"0\",\n                          \"\",\n                          idFilter,\n                          patientFilter,\n                          statusFilter,\n                          insuranceFilter !== \"\" ? insuranceFilter.value ?? \"\" : \"\",\n                          \"\",\n                          coordinationFilter,\n                          typeFilter,\n                          ciaIdFilter\n                        )\n                      );\n                    }\n                  }}\n                  options={providers && providers.length > 0 ? providers.map((provider) => ({\n                    value: provider.id,\n                    label: provider.full_name || \"\",\n                  })) : []}\n                  filterOption={(option, inputValue) =>\n                    option.label\n                      .toLowerCase()\n                      .includes(inputValue.toLowerCase())\n                  }\n                  placeholder=\"Select Provider...\"\n                  isSearchable\n                  isClearable\n                  className=\"react-select-container\"\n                  classNamePrefix=\"react-select\"\n                  styles={{\n                    control: (base, state) => ({\n                      ...base,\n                      minHeight: '48px',\n                      paddingLeft: '32px',\n                      border: 'none',\n                      borderRadius: '8px',\n                      backgroundColor: '#ffffff',\n                      boxShadow: 'none',\n                      '&:hover': {\n                        backgroundColor: '#eff6ff',\n                      },\n                      transition: 'all 0.2s',\n                    }),\n                    placeholder: (base) => ({\n                      ...base,\n                      color: '#9ca3af',\n                      fontSize: '14px',\n                    }),\n                    singleValue: (base) => ({\n                      ...base,\n                      color: '#374151',\n                      fontSize: '14px',\n                    }),\n                    option: (base, state) => ({\n                      ...base,\n                      backgroundColor: state.isSelected ? '#3b82f6' : state.isFocused ? '#eff6ff' : 'white',\n                      color: state.isSelected ? 'white' : '#374151',\n                      fontSize: '14px',\n                    }),\n                  }}\n                />\n              </div>\n\n              {/* Reset Button */}\n              <div className=\"flex items-end\">\n                <button\n                  onClick={() => {\n                    setIdFilter(\"\");\n                    setInsuranceFilter(\"\");\n                    setProviderFilter(\"\");\n                    setStatusrFilter(\"\");\n                    setTypeFilter(\"\");\n                    setPatientFilter(\"\");\n                    setCiaIdFilter(\"\");\n                  }}\n                  className=\"w-full flex items-center justify-center gap-2 bg-danger hover:bg-danger text-white px-4 py-3 rounded-lg font-medium text-sm transition-all duration-200\"\n                >\n                  <svg\n                    xmlns=\"http://www.w3.org/2000/svg\"\n                    fill=\"none\"\n                    viewBox=\"0 0 24 24\"\n                    strokeWidth=\"1.5\"\n                    stroke=\"currentColor\"\n                    className=\"w-4 h-4\"\n                  >\n                    <path\n                      strokeLinecap=\"round\"\n                      strokeLinejoin=\"round\"\n                      d=\"M16.023 9.348h4.992v-.001M2.985 19.644v-4.992m0 0h4.992m-4.993 0 3.181 3.183a8.25 8.25 0 0 0 13.803-3.7M4.031 9.865a8.25 8.25 0 0 1 13.803-3.7l3.181 3.182m0-4.991v4.99\"\n                    />\n                  </svg>\n                  Reset Filters\n                </button>\n              </div>\n            </div>\n          </div>\n          <div className=\" w-full  px-1 py-3 \">\n            <div className=\"py-4 px-2 shadow-1 bg-white\">\n              {loadingCases ? (\n                <Loader />\n              ) : errorCases ? (\n                <Alert type=\"error\" message={errorCases} />\n              ) : (\n                <div className=\"max-w-full overflow-x-auto \">\n                  <table className=\"w-full table-auto\">\n                    <thead>\n                      <tr className=\" bg-[#F3F5FB] text-left \">\n                        <th className=\"min-w-[60px] py-4 px-4 font-bold text-[#8E8E8E] text-xs w-max \">\n                          ID\n                        </th>\n                        <th className=\"min-w-[60px] py-4 px-4 font-bold text-[#8E8E8E] text-xs w-max \">\n                          Client\n                        </th>\n                        <th className=\"min-w-[60px] py-4 px-4 font-bold text-[#8E8E8E] text-xs w-max \">\n                          Patient Name\n                        </th>\n                        <th className=\"min-w-[30px] py-4 px-4 font-bold text-[#8E8E8E] text-xs w-max\">\n                          Type\n                        </th>\n                        <th className=\"min-w-[120px] py-4 px-4 font-bold text-[#8E8E8E] text-xs w-max\">\n                          Assigned Provider\n                        </th>\n                        <th className=\"min-w-[120px] py-4 px-4 font-bold text-[#8E8E8E] text-xs w-max\">\n                          Status\n                        </th>\n                        <th className=\"min-w-[120px] py-4 px-4 font-bold text-[#8E8E8E] text-xs w-max\">\n                          Date Created\n                        </th>\n                        <th className=\"min-w-[120px] py-4 px-4 font-bold text-[#8E8E8E] text-xs w-max\"></th>\n                      </tr>\n                    </thead>\n                    {/*  */}\n                    <tbody>\n                      {cases?.map((item, index) => (\n                        //  <a href={`/cases/detail/${item.id}`}></a>\n                        <tr key={index}>\n                          <td\n                            onClick={() => {\n                              navigate(\"/cases-list/detail/\" + item.id);\n                            }}\n                            className=\" py-3 px-4 min-w-[120px] cursor-pointer  \"\n                          >\n                            <p className=\"text-black  text-xs w-max  \">\n                              #{item.id}\n                            </p>\n                          </td>\n                          <td\n                            onClick={() => {\n                              navigate(\"/cases-list/detail/\" + item.id);\n                            }}\n                            className=\" py-3 px-4 min-w-[120px] cursor-pointer  \"\n                          >\n                            <p className=\"text-black  text-xs w-max  \">\n                              {item.assurance?.assurance_name ?? \"---\"}\n                            </p>\n                          </td>\n                          <td\n                            onClick={() => {\n                              navigate(\"/cases-list/detail/\" + item.id);\n                            }}\n                            className=\" py-3 px-4 min-w-[120px] cursor-pointer  \"\n                          >\n                            <p className=\"text-black  text-xs w-max  \">\n                              {item.patient?.full_name ?? \"---\"}\n                            </p>\n                          </td>\n                          <td\n                            onClick={() => {\n                              navigate(\"/cases-list/detail/\" + item.id);\n                            }}\n                            className=\" py-3 px-4 min-w-[120px] cursor-pointer  \"\n                          >\n                            <p className=\"text-black  text-xs w-max  \">\n                              {item.case_type ?? \"---\"}\n                            </p>\n                          </td>\n                          <td\n                            onClick={() => {\n                              navigate(\"/cases-list/detail/\" + item.id);\n                            }}\n                            className=\" py-3 px-4 min-w-[120px] cursor-pointer  \"\n                          >\n                            <p className=\"text-black  text-xs w-max  \">\n                              {/* {item.provider?.full_name ?? \"---\"} */}\n                              {item.provider_services || 0} Providers\n                            </p>\n                          </td>\n                          <td\n                            onClick={() => {\n                              navigate(\"/cases-list/detail/\" + item.id);\n                            }}\n                            className=\" py-3 px-4 min-w-[120px] cursor-pointer  \"\n                          >\n                            <p className=\"text-black   text-xs  text-[10px]\">\n                              {item.case_status && item.case_status.length > 0 ? (\n                                item.case_status.map((stat, index) => (\n                                  <span key={index}>{caseStatus(stat.status_coordination)}- </span>\n                                ))\n                              ) : (\n                                \"---\"\n                              )}\n                            </p>\n                          </td>\n                          <td\n                            onClick={() => {\n                              navigate(\"/cases-list/detail/\" + item.id);\n                            }}\n                            className=\" py-3 px-4 min-w-[120px] cursor-pointer  \"\n                          >\n                            <p className=\"text-black  text-xs w-max  \">\n                              {formatDate(item.case_date)}\n                            </p>\n                          </td>\n                          <td className=\" py-3 px-4 min-w-[120px]  \">\n                            <p className=\"text-black  text-xs w-max flex flex-row  \">\n                              <Link\n                                className=\"mx-1 detail-class\"\n                                to={\"/cases-list/detail/\" + item.id}\n                              >\n                                <svg\n                                  xmlns=\"http://www.w3.org/2000/svg\"\n                                  fill=\"none\"\n                                  viewBox=\"0 0 24 24\"\n                                  stroke-width=\"1.5\"\n                                  stroke=\"currentColor\"\n                                  className=\"w-5 h-5 bg-warning rounded p-1 text-white text-center text-xs\"\n                                >\n                                  <path\n                                    stroke-linecap=\"round\"\n                                    stroke-linejoin=\"round\"\n                                    d=\"M2.036 12.322a1.012 1.012 0 0 1 0-.639C3.423 7.51 7.36 4.5 12 4.5c4.638 0 8.573 3.007 9.963 7.178.07.207.07.431 0 .639C20.577 16.49 16.64 19.5 12 19.5c-4.638 0-8.573-3.007-9.963-7.178Z\"\n                                  />\n                                  <path\n                                    stroke-linecap=\"round\"\n                                    stroke-linejoin=\"round\"\n                                    d=\"M15 12a3 3 0 1 1-6 0 3 3 0 0 1 6 0Z\"\n                                  />\n                                </svg>\n                              </Link>\n                              <Link\n                                className=\"mx-1 update-class\"\n                                to={\"/cases-list/edit/\" + item.id}\n                              >\n                                <svg\n                                  xmlns=\"http://www.w3.org/2000/svg\"\n                                  fill=\"none\"\n                                  viewBox=\"0 0 24 24\"\n                                  strokeWidth=\"1.5\"\n                                  stroke=\"currentColor\"\n                                  className=\"w-5 h-5 bg-primary rounded p-1 text-white text-center text-xs\"\n                                >\n                                  <path\n                                    strokeLinecap=\"round\"\n                                    strokeLinejoin=\"round\"\n                                    d=\"m16.862 4.487 1.687-1.688a1.875 1.875 0 1 1 2.652 2.652L10.582 16.07a4.5 4.5 0 0 1-1.897 1.13L6 18l.8-2.685a4.5 4.5 0 0 1 1.13-1.897l8.932-8.931Zm0 0L19.5 7.125M18 14v4.75A2.25 2.25 0 0 1 15.75 21H5.25A2.25 2.25 0 0 1 3 18.75V8.25A2.25 2.25 0 0 1 5.25 6H10\"\n                                  />\n                                </svg>\n                              </Link>\n                              <div\n                                onClick={() => {\n                                  setEventType(\"delete\");\n                                  setCaseId(item.id);\n                                  setIsDelete(true);\n                                }}\n                                className=\"mx-1 delete-class cursor-pointer\"\n                              >\n                                <svg\n                                  xmlns=\"http://www.w3.org/2000/svg\"\n                                  fill=\"none\"\n                                  viewBox=\"0 0 24 24\"\n                                  stroke-width=\"1.5\"\n                                  stroke=\"currentColor\"\n                                  className=\"w-5 h-5 bg-danger rounded p-1 text-white text-center text-xs\"\n                                >\n                                  <path\n                                    stroke-linecap=\"round\"\n                                    stroke-linejoin=\"round\"\n                                    d=\"m20.25 7.5-.625 10.632a2.25 2.25 0 0 1-2.247 2.118H6.622a2.25 2.25 0 0 1-2.247-2.118L3.75 7.5m6 4.125 2.25 2.25m0 0 2.25 2.25M12 13.875l2.25-2.25M12 13.875l-2.25 2.25M3.375 7.5h17.25c.621 0 1.125-.504 1.125-1.125v-1.5c0-.621-.504-1.125-1.125-1.125H3.375c-.621 0-1.125.504-1.125 1.125v1.5c0 .621.504 1.125 1.125 1.125Z\"\n                                  />\n                                </svg>\n                              </div>\n                            </p>\n                          </td>\n                        </tr>\n                      ))}\n                      <tr className=\"h-5\"></tr>\n                    </tbody>\n                  </table>\n                  <div className=\"\">\n                    <Paginate\n                      route={\"/dashboard?\"}\n                      search={\"\"}\n                      page={page}\n                      pages={pages}\n                    />\n                  </div>\n                </div>\n              )}\n            </div>\n          </div>\n          <div className=\"py-2 px-4 md:px-6 xl:px-7.5 flex justify-between\">\n            <h4 className=\" uppercase font-semibold text-black  text-xs w-max\">\n              Providers Map \n            </h4>\n          </div>\n\n          <div className=\" w-full  px-1 py-3 \">\n            <div className=\"py-4 px-2 shadow-1 bg-white\">\n              <div className=\" relative\">\n                <MapContainer\n                  center={[0, 0]}\n                  zoom={2}\n                  style={{ height: \"500px\", width: \"100%\" }}\n                >\n                  <TileLayer\n                    url=\"https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png\"\n                    attribution='&copy; <a href=\"https://www.openstreetmap.org/copyright\">OpenStreetMap</a> contributors'\n                  />\n                  {casesMap && casesMap.length > 0 ? casesMap.map((caseitem, caseIndex) => (\n                    <div key={caseIndex}>\n                      {caseitem.provider_services && caseitem.provider_services.length > 0 ? (\n                        caseitem.provider_services\n                          .filter(\n                            (provider) =>\n                              provider.provider &&\n                              provider.provider.location_x &&\n                              provider.provider.location_y\n                          )\n                          .map((provider, index) => (\n                            <Marker\n                              eventHandlers={{\n                                click: () => {\n                                  setIsOpenMap(true);\n                                  setProviderMapSelect(provider);\n                                }, // Trigger onClick event\n                              }}\n                              key={`${caseIndex}-${index}`}\n                              position={[\n                                provider.provider.location_x,\n                                provider.provider.location_y,\n                              ]}\n                            >\n                              <Popup>\n                                {provider.provider.full_name}\n                                <br />\n                              </Popup>\n                            </Marker>\n                          ))\n                      ) : null}\n                    </div>\n                  )) : null}\n                  \n                </MapContainer>\n                {isOpenMap ? (\n                  <div className=\" absolute top-0 left-0 z-99999  p-2 md:w-1/3 w-2/3 h-full \">\n                    <div className=\"bg-white shadow-1 w-full h-full\">\n                      <div className=\" p-3 float-right \">\n                        <button\n                          onClick={() => {\n                            setIsOpenMap(false);\n                            setProviderMapSelect(null);\n                          }}\n                          className=\"rounded-full p-1 bg-danger shadow-1 text-white flex items-center w-max \"\n                        >\n                          <svg\n                            xmlns=\"http://www.w3.org/2000/svg\"\n                            fill=\"none\"\n                            viewBox=\"0 0 24 24\"\n                            stroke-width=\"1.5\"\n                            stroke=\"currentColor\"\n                            class=\"size-4\"\n                          >\n                            <path\n                              stroke-linecap=\"round\"\n                              stroke-linejoin=\"round\"\n                              d=\"M6 18 18 6M6 6l12 12\"\n                            />\n                          </svg>\n                        </button>\n                      </div>\n                      <div className=\"pt-10 py-4 px-3\">\n                        {providerMapSelect && (\n                          <div>\n                            <div className=\"flex flex-row items-center text-xs my-3\">\n                              <div>\n                                <svg\n                                  xmlns=\"http://www.w3.org/2000/svg\"\n                                  fill=\"none\"\n                                  viewBox=\"0 0 24 24\"\n                                  stroke-width=\"1.5\"\n                                  stroke=\"currentColor\"\n                                  className=\"size-5\"\n                                >\n                                  <path\n                                    stroke-linecap=\"round\"\n                                    stroke-linejoin=\"round\"\n                                    d=\"M11.42 15.17 17.25 21A2.652 2.652 0 0 0 21 17.25l-5.877-5.877M11.42 15.17l2.496-3.03c.317-.384.74-.626 1.208-.766M11.42 15.17l-4.655 5.653a2.548 2.548 0 1 1-3.586-3.586l6.837-5.63m5.108-.233c.55-.164 1.163-.188 1.743-.14a4.5 4.5 0 0 0 4.486-6.336l-3.276 3.277a3.004 3.004 0 0 1-2.25-2.25l3.276-3.276a4.5 4.5 0 0 0-6.336 4.486c.091 1.076-.071 2.264-.904 2.95l-.102.085m-1.745 1.437L5.909 7.5H4.5L2.25 3.75l1.5-1.5L7.5 4.5v1.409l4.26 4.26m-1.745 1.437 1.745-1.437m6.615 8.206L15.75 15.75M4.867 19.125h.008v.008h-.008v-.008Z\"\n                                  />\n                                </svg>\n                              </div>\n\n                              <div className=\"flex-1 px-2\">\n                                {providerMapSelect.provider.services && providerMapSelect.provider.services.length > 0 ? (\n                                  providerMapSelect.provider.services.map(\n                                    (service, index) => (\n                                      <div key={index} className=\"my-1\">\n                                        -{\" \"}\n                                        {service.service_type +\n                                          (service.service_specialist !== \"\" &&\n                                          service.service_specialist !== null\n                                            ? \": \" + service.service_specialist\n                                            : \"\")}\n                                      </div>\n                                    )\n                                  )\n                                ) : (\n                                  <div className=\"my-1\">No services available</div>\n                                )}\n\n                              </div>\n                            </div>\n                            {/*  */}\n                            <div className=\"flex flex-row items-center text-xs my-3\">\n                              <div>\n                                <svg\n                                  xmlns=\"http://www.w3.org/2000/svg\"\n                                  fill=\"none\"\n                                  viewBox=\"0 0 24 24\"\n                                  stroke-width=\"1.5\"\n                                  stroke=\"currentColor\"\n                                  class=\"size-5\"\n                                >\n                                  <path\n                                    stroke-linecap=\"round\"\n                                    stroke-linejoin=\"round\"\n                                    d=\"M15.75 6a3.75 3.75 0 1 1-7.5 0 3.75 3.75 0 0 1 7.5 0ZM4.501 20.118a7.5 7.5 0 0 1 14.998 0A17.933 17.933 0 0 1 12 21.75c-2.676 0-5.216-.584-7.499-1.632Z\"\n                                  />\n                                </svg>\n                              </div>\n                              <div className=\"flex-1 px-2\">\n                                {providerMapSelect.provider.full_name ?? \"---\"}\n                              </div>\n                            </div>\n                            {/*  */}\n                            {providerMapSelect.provider.provider_infos && providerMapSelect.provider.provider_infos.length > 0 ? (\n                              providerMapSelect.provider.provider_infos.map(\n                                (item, index) => (\n                                  <div key={index}>\n                                    <div className=\"flex flex-row items-center text-xs my-3\">\n                                      <div>\n                                        {[\n                                          \"Main Phone\",\n                                          \"Whatsapp\",\n                                          \"Billing Phone\",\n                                        ].includes(item.info_type) ? (\n                                          <svg\n                                            xmlns=\"http://www.w3.org/2000/svg\"\n                                            fill=\"none\"\n                                            viewBox=\"0 0 24 24\"\n                                            stroke-width=\"1.5\"\n                                            stroke=\"currentColor\"\n                                            className=\"size-4\"\n                                          >\n                                            <path\n                                              stroke-linecap=\"round\"\n                                              stroke-linejoin=\"round\"\n                                              d=\"M2.25 6.75c0 8.284 6.716 15 15 15h2.25a2.25 2.25 0 0 0 2.25-2.25v-1.372c0-.516-.351-.966-.852-1.091l-4.423-1.106c-.44-.11-.902.055-1.173.417l-.97 1.293c-.282.376-.769.542-1.21.38a12.035 12.035 0 0 1-7.143-7.143c-.162-.441.004-.928.38-1.21l1.293-.97c.363-.271.527-.734.417-1.173L6.963 3.102a1.125 1.125 0 0 0-1.091-.852H4.5A2.25 2.25 0 0 0 2.25 4.5v2.25Z\"\n                                            />\n                                          </svg>\n                                        ) : (\n                                          <svg\n                                            xmlns=\"http://www.w3.org/2000/svg\"\n                                            fill=\"none\"\n                                            viewBox=\"0 0 24 24\"\n                                            stroke-width=\"1.5\"\n                                            stroke=\"currentColor\"\n                                            className=\"size-4\"\n                                          >\n                                            <path\n                                              stroke-linecap=\"round\"\n                                              stroke-linejoin=\"round\"\n                                              d=\"M21.75 6.75v10.5a2.25 2.25 0 0 1-2.25 2.25h-15a2.25 2.25 0 0 1-2.25-2.25V6.75m19.5 0A2.25 2.25 0 0 0 19.5 4.5h-15a2.25 2.25 0 0 0-2.25 2.25m19.5 0v.243a2.25 2.25 0 0 1-1.07 1.916l-7.5 4.615a2.25 2.25 0 0 1-2.36 0L3.32 8.91a2.25 2.25 0 0 1-1.07-1.916V6.75\"\n                                            />\n                                          </svg>\n                                        )}\n                                      </div>\n                                      <div className=\"flex-1 px-2\">\n                                        {item.info_type} : {item.info_value}\n                                      </div>\n                                    </div>\n                                  </div>\n                                )\n                              )\n                            ) : null}\n                            \n                            {/*  */}\n                            <div className=\"flex flex-row items-center text-xs my-3\">\n                              <div>\n                                <svg\n                                  xmlns=\"http://www.w3.org/2000/svg\"\n                                  fill=\"none\"\n                                  viewBox=\"0 0 24 24\"\n                                  stroke-width=\"1.5\"\n                                  stroke=\"currentColor\"\n                                  class=\"size-5\"\n                                >\n                                  <path\n                                    stroke-linecap=\"round\"\n                                    stroke-linejoin=\"round\"\n                                    d=\"M15 10.5a3 3 0 1 1-6 0 3 3 0 0 1 6 0Z\"\n                                  />\n                                  <path\n                                    stroke-linecap=\"round\"\n                                    stroke-linejoin=\"round\"\n                                    d=\"M19.5 10.5c0 7.142-7.5 11.25-7.5 11.25S4.5 17.642 4.5 10.5a7.5 7.5 0 1 1 15 0Z\"\n                                  />\n                                </svg>\n                              </div>\n                              <div className=\"flex-1 px-2\">\n                                {providerMapSelect.provider.address ?? \"---\"}\n                              </div>\n                            </div>\n                            <div className=\"flex flex-row items-center text-xs my-3\">\n                              <div>\n                                <svg\n                                  xmlns=\"http://www.w3.org/2000/svg\"\n                                  fill=\"none\"\n                                  viewBox=\"0 0 24 24\"\n                                  stroke-width=\"1.5\"\n                                  stroke=\"currentColor\"\n                                  class=\"size-4\"\n                                >\n                                  <path\n                                    stroke-linecap=\"round\"\n                                    stroke-linejoin=\"round\"\n                                    d=\"M2.25 8.25h19.5M2.25 9h19.5m-16.5 5.25h6m-6 2.25h3m-3.75 3h15a2.25 2.25 0 0 0 2.25-2.25V6.75A2.25 2.25 0 0 0 19.5 4.5h-15a2.25 2.25 0 0 0-2.25 2.25v10.5A2.25 2.25 0 0 0 4.5 19.5Z\"\n                                  />\n                                </svg>\n                              </div>\n                              <div className=\"flex-1 px-2\">\n                                {providerMapSelect.provider.payment_method ??\n                                  \"---\"}\n                              </div>\n                            </div>\n                            <p className=\"text-black  text-xs w-max flex flex-row my-4 \">\n                              <Link\n                                className=\"mx-1 update-class \"\n                                to={\n                                  \"/providers-list/edit/\" +\n                                  providerMapSelect.provider.id\n                                }\n                              >\n                                <svg\n                                  xmlns=\"http://www.w3.org/2000/svg\"\n                                  fill=\"none\"\n                                  viewBox=\"0 0 24 24\"\n                                  strokeWidth=\"1.5\"\n                                  stroke=\"currentColor\"\n                                  className=\"w-8 h-5 bg-primary rounded p-1 text-white text-center text-xs\"\n                                >\n                                  <path\n                                    strokeLinecap=\"round\"\n                                    strokeLinejoin=\"round\"\n                                    d=\"m16.862 4.487 1.687-1.688a1.875 1.875 0 1 1 2.652 2.652L10.582 16.07a4.5 4.5 0 0 1-1.897 1.13L6 18l.8-2.685a4.5 4.5 0 0 1 1.13-1.897l8.932-8.931Zm0 0L19.5 7.125M18 14v4.75A2.25 2.25 0 0 1 15.75 21H5.25A2.25 2.25 0 0 1 3 18.75V8.25A2.25 2.25 0 0 1 5.25 6H10\"\n                                  />\n                                </svg>\n                              </Link>\n                            </p>\n                          </div>\n                        )}\n                      </div>\n                    </div>\n                  </div>\n                ) : null}\n              </div>\n            </div>\n          </div>\n        </div>\n\n        <ConfirmationModal\n          isOpen={isDelete}\n          message={\n            eventType === \"delete\"\n              ? \"Are you sure you want to delete this case?\"\n              : \"Are you sure ?\"\n          }\n          onConfirm={async () => {\n            if (eventType === \"cancel\") {\n              setIsDelete(false);\n              setEventType(\"\");\n              setLoadEvent(false);\n            } else if (eventType === \"delete\" && caseId !== \"\") {\n              setLoadEvent(true);\n              dispatch(deleteCase(caseId));\n              setIsDelete(false);\n              setEventType(\"\");\n              setLoadEvent(false);\n            } else {\n              setIsDelete(false);\n              setEventType(\"\");\n              setLoadEvent(false);\n            }\n          }}\n          onCancel={() => {\n            setIsDelete(false);\n            setEventType(\"\");\n            setLoadEvent(false);\n          }}\n          loadEvent={loadEvent}\n        />\n\n        <div className=\"grid md:grid-cols-2 w-full container mt-5\"></div>\n      </div>\n    </DefaultLayout>\n  );\n}\n\nexport default DashboardScreen;\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,EAAIC,SAAS,CAAEC,QAAQ,KAAQ,OAAO,CAClD,OAASC,WAAW,CAAEC,WAAW,KAAQ,aAAa,CACtD,OACEC,IAAI,CACJC,WAAW,CACXC,WAAW,CACXC,eAAe,KACV,kBAAkB,CACzB,OACEC,kBAAkB,CAClBC,YAAY,CACZC,UAAU,KACL,iCAAiC,CACxC,MAAO,CAAAC,iBAAiB,KAAM,oCAAoC,CAClE,MAAO,CAAAC,QAAQ,KAAM,2BAA2B,CAChD,MAAO,CAAAC,KAAK,KAAM,wBAAwB,CAC1C,MAAO,CAAAC,MAAM,KAAM,yBAAyB,CAC5C,MAAO,CAAAC,aAAa,KAAM,6BAA6B,CAEvD,OAASC,YAAY,CAAEC,SAAS,CAAEC,MAAM,CAAEC,KAAK,KAAQ,eAAe,CACtE,MAAO,0BAA0B,CACjC,MAAO,CAAAC,CAAC,KAAM,SAAS,CACvB,MAAO,CAAAC,MAAM,KAAM,cAAc,CACjC,OAASC,mBAAmB,KAAQ,iCAAiC,CACrE,OAASC,sBAAsB,KAAQ,qCAAqC,CAC5E,OAASC,uBAAuB,KAAQ,sCAAsC,CAC9E,OAASC,QAAQ,KAAQ,cAAc,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAExC,MAAO,CAAAT,CAAC,CAACU,IAAI,CAACC,OAAO,CAACC,SAAS,CAACC,WAAW,CAC3Cb,CAAC,CAACU,IAAI,CAACC,OAAO,CAACG,YAAY,CAAC,CAC1BC,aAAa,CACX,gEAAgE,CAClEC,OAAO,CAAE,6DAA6D,CACtEC,SAAS,CAAE,+DACb,CAAC,CAAC,CAEF,QAAS,CAAAC,eAAeA,CAAA,CAAG,KAAAC,qBAAA,CAAAC,sBAAA,CAAAC,sBAAA,CACzB,KAAM,CAAAC,QAAQ,CAAGpC,WAAW,CAAC,CAAC,CAC9B,KAAM,CAAAqC,QAAQ,CAAGtC,WAAW,CAAC,CAAC,CAC9B,KAAM,CAACuC,YAAY,CAAC,CAAGrC,eAAe,CAAC,CAAC,CACxC,KAAM,CAAAsC,IAAI,CAAGD,YAAY,CAACE,GAAG,CAAC,MAAM,CAAC,EAAI,GAAG,CAC5C,KAAM,CAAAC,QAAQ,CAAG7C,WAAW,CAAC,CAAC,CAE9B,KAAM,CAAC8C,iBAAiB,CAAEC,oBAAoB,CAAC,CAAGhD,QAAQ,CAAC,IAAI,CAAC,CAChE,KAAM,CAACiD,SAAS,CAAEC,YAAY,CAAC,CAAGlD,QAAQ,CAAC,KAAK,CAAC,CAEjD,KAAM,CAACmD,QAAQ,CAAEC,WAAW,CAAC,CAAGpD,QAAQ,CAAC2C,YAAY,CAACE,GAAG,CAAC,UAAU,CAAC,EAAI,EAAE,CAAC,CAC5E,KAAM,CAACQ,WAAW,CAAEC,cAAc,CAAC,CAAGtD,QAAQ,CAC5C2C,YAAY,CAACE,GAAG,CAAC,aAAa,CAAC,EAAI,EACrC,CAAC,CACD,KAAM,CAACU,aAAa,CAAEC,gBAAgB,CAAC,CAAGxD,QAAQ,CAChD2C,YAAY,CAACE,GAAG,CAAC,eAAe,CAAC,EAAI,EACvC,CAAC,CACD,KAAM,CAACY,eAAe,CAAEC,kBAAkB,CAAC,CAAG1D,QAAQ,CACpD2C,YAAY,CAACE,GAAG,CAAC,iBAAiB,CAAC,EAAI,EACzC,CAAC,CACD,KAAM,CAACc,UAAU,CAAEC,aAAa,CAAC,CAAG5D,QAAQ,CAC1C2C,YAAY,CAACE,GAAG,CAAC,YAAY,CAAC,EAAI,EACpC,CAAC,CACD,KAAM,CAACgB,cAAc,CAAEC,iBAAiB,CAAC,CAAG9D,QAAQ,CAClD2C,YAAY,CAACE,GAAG,CAAC,gBAAgB,CAAC,EAAI,EACxC,CAAC,CACD,KAAM,CAACkB,kBAAkB,CAAEC,oBAAoB,CAAC,CAAGhE,QAAQ,CACzD2C,YAAY,CAACE,GAAG,CAAC,oBAAoB,CAAC,EAAI,EAC5C,CAAC,CACD,KAAM,CAACoB,YAAY,CAAEC,gBAAgB,CAAC,CAAGlE,QAAQ,CAC/C2C,YAAY,CAACE,GAAG,CAAC,cAAc,CAAC,EAAI,EACtC,CAAC,CAED9C,SAAS,CAAC,IAAM,CACd,KAAM,CAAAoE,MAAM,CAAG,GAAI,CAAAC,eAAe,CAAC,CAAC,CAEpC,GAAIjB,QAAQ,CAAEgB,MAAM,CAACE,GAAG,CAAC,UAAU,CAAElB,QAAQ,CAAC,CAC9C,GAAIE,WAAW,CAAEc,MAAM,CAACE,GAAG,CAAC,aAAa,CAAEhB,WAAW,CAAC,CACvD,GAAIE,aAAa,CAAEY,MAAM,CAACE,GAAG,CAAC,eAAe,CAAEd,aAAa,CAAC,CAC7D,GAAIE,eAAe,CAAEU,MAAM,CAACE,GAAG,CAAC,iBAAiB,CAAEZ,eAAe,CAAC,CACnE,GAAIE,UAAU,CAAEQ,MAAM,CAACE,GAAG,CAAC,YAAY,CAAEV,UAAU,CAAC,CACpD,GAAIE,cAAc,CAAEM,MAAM,CAACE,GAAG,CAAC,gBAAgB,CAAER,cAAc,CAAC,CAChE,GAAIE,kBAAkB,CACpBI,MAAM,CAACE,GAAG,CAAC,oBAAoB,CAAEN,kBAAkB,CAAC,CACtD,GAAIE,YAAY,CAAEE,MAAM,CAACE,GAAG,CAAC,cAAc,CAAEJ,YAAY,CAAC,CAE1D;AACAE,MAAM,CAACE,GAAG,CAAC,MAAM,CAAE,GAAG,CAAC,CAEvB;AACA5B,QAAQ,CAAC,CACP6B,QAAQ,CAAE5B,QAAQ,CAAC4B,QAAQ,CAC3BC,MAAM,CAAEJ,MAAM,CAACK,QAAQ,CAAC,CAC1B,CAAC,CAAC,CACJ,CAAC,CAAE,CACDrB,QAAQ,CACRI,aAAa,CACbU,YAAY,CACZR,eAAe,CACfI,cAAc,CACdE,kBAAkB,CAClBJ,UAAU,CACVN,WAAW,CACXP,QAAQ,CACRL,QAAQ,CACRC,QAAQ,CAAC4B,QAAQ,CAClB,CAAC,CAEF,KAAM,CAACG,QAAQ,CAAEC,WAAW,CAAC,CAAG1E,QAAQ,CAAC,KAAK,CAAC,CAC/C,KAAM,CAAC2E,SAAS,CAAEC,YAAY,CAAC,CAAG5E,QAAQ,CAAC,KAAK,CAAC,CACjD,KAAM,CAAC6E,SAAS,CAAEC,YAAY,CAAC,CAAG9E,QAAQ,CAAC,EAAE,CAAC,CAC9C,KAAM,CAAC+E,MAAM,CAAEC,SAAS,CAAC,CAAGhF,QAAQ,CAAC,EAAE,CAAC,CAExC,KAAM,CAAAiF,SAAS,CAAG/E,WAAW,CAAEgF,KAAK,EAAKA,KAAK,CAACD,SAAS,CAAC,CACzD,KAAM,CAAEE,QAAS,CAAC,CAAGF,SAAS,CAE9B,KAAM,CAAAG,SAAS,CAAGlF,WAAW,CAAEgF,KAAK,EAAKA,KAAK,CAACG,QAAQ,CAAC,CACxD,KAAM,CAAEC,KAAK,CAAEC,YAAY,CAAEC,UAAU,CAAEC,KAAM,CAAC,CAAGL,SAAS,CAE5D,KAAM,CAAAM,YAAY,CAAGxF,WAAW,CAAEgF,KAAK,EAAKA,KAAK,CAACS,WAAW,CAAC,CAC9D,KAAM,CAAEC,QAAQ,CAAEC,eAAe,CAAEC,aAAc,CAAC,CAAGJ,YAAY,CAEjE,KAAM,CAAAK,UAAU,CAAG7F,WAAW,CAAEgF,KAAK,EAAKA,KAAK,CAACzE,UAAU,CAAC,CAC3D,KAAM,CAAEuF,iBAAiB,CAAEC,eAAe,CAAEC,iBAAkB,CAAC,CAAGH,UAAU,CAE5E,KAAM,CAAAI,aAAa,CAAGjG,WAAW,CAAEgF,KAAK,EAAKA,KAAK,CAACkB,YAAY,CAAC,CAChE,KAAM,CAAEC,SAAS,CAAEC,gBAAgB,CAAEC,cAAe,CAAC,CAAGJ,aAAa,CAErE,KAAM,CAAAK,cAAc,CAAGtG,WAAW,CAAEgF,KAAK,EAAKA,KAAK,CAACuB,aAAa,CAAC,CAClE,KAAM,CAAEC,UAAU,CAAEC,iBAAiB,CAAEC,eAAgB,CAAC,CAAGJ,cAAc,CAEzE,KAAM,CAAAK,gBAAgB,CAAG3G,WAAW,CAAEgF,KAAK,EAAKA,KAAK,CAAC4B,gBAAgB,CAAC,CACvE,KAAM,CAAEC,YAAY,CAAEC,mBAAmB,CAAEC,iBAAkB,CAAC,CAC5DJ,gBAAgB,CAElB,KAAM,CAAAK,QAAQ,CAAG,GAAG,CAEpBnH,SAAS,CAAC,IAAM,CACd,GAAI,CAACoF,QAAQ,CAAE,CACb1C,QAAQ,CAACyE,QAAQ,CAAC,CACpB,CAAC,IAAM,KAAAC,qBAAA,CAAAC,qBAAA,CAAAC,sBAAA,CAAAC,sBAAA,CACL,KAAM,CAAAC,MAAM,CAAG,GAAI,CAAA/F,QAAQ,CAAC,CAAC,CAC7B,KAAM,CAAAgG,MAAM,CAAGD,MAAM,CAACE,SAAS,CAAC,CAAC,CAEjC,KAAM,CAAAC,OAAO,CAAGF,MAAM,CAACE,OAAO,CAACC,IAAI,EAAI,iBAAiB,CACxD,KAAM,CAAAC,MAAM,CACVJ,MAAM,CAACI,MAAM,CAACC,KAAK,EAAIL,MAAM,CAACI,MAAM,CAACE,IAAI,EAAI,gBAAgB,CAE/D;AACAhF,QAAQ,CACNvC,kBAAkB,CAChBqC,IAAI,CACJ,EAAE,CACFO,QAAQ,CACRI,aAAa,CACbU,YAAY,CACZR,eAAe,GAAK,EAAE,EAAA0D,qBAAA,CAAG1D,eAAe,CAACsE,KAAK,UAAAZ,qBAAA,UAAAA,qBAAA,CAAI,EAAE,CAAG,EAAE,CACzDtD,cAAc,GAAK,EAAE,EAAAuD,qBAAA,CAAGvD,cAAc,CAACkE,KAAK,UAAAX,qBAAA,UAAAA,qBAAA,CAAI,EAAE,CAAG,EAAE,CACvDrD,kBAAkB,CAClBJ,UAAU,CACVN,WACF,CACF,CAAC,CACD;AACAP,QAAQ,CACNtC,YAAY,CACV,GAAG,CACH,EAAE,CACF2C,QAAQ,CACRI,aAAa,CACbU,YAAY,CACZR,eAAe,GAAK,EAAE,EAAA4D,sBAAA,CAAG5D,eAAe,CAACsE,KAAK,UAAAV,sBAAA,UAAAA,sBAAA,CAAI,EAAE,CAAG,EAAE,CACzDxD,cAAc,GAAK,EAAE,EAAAyD,sBAAA,CAAGzD,cAAc,CAACkE,KAAK,UAAAT,sBAAA,UAAAA,sBAAA,CAAI,EAAE,CAAG,EAAE,CACvDvD,kBAAkB,CAClBJ,UAAU,CACVN,WACF,CACF,CAAC,CACD;AACA;AACA;AACAP,QAAQ,CAACxB,sBAAsB,CAAC,GAAG,CAAC,CAAC,CACrC;AACAwB,QAAQ,CAACvB,uBAAuB,CAAC,GAAG,CAAC,CAAC,CACtC;AACF,CACF,CAAC,CAAE,CACDkB,QAAQ,CACR0C,QAAQ,CACRrC,QAAQ,CACRF,IACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA,CACD,CAAC,CAEF7C,SAAS,CAAC,IAAM,CACd,GAAImG,iBAAiB,CAAE,KAAA8B,sBAAA,CAAAC,sBAAA,CAAAC,sBAAA,CAAAC,sBAAA,CACrBrF,QAAQ,CACNvC,kBAAkB,CAChB,GAAG,CACH,EAAE,CACF4C,QAAQ,CACRI,aAAa,CACbU,YAAY,CACZR,eAAe,GAAK,EAAE,EAAAuE,sBAAA,CAAGvE,eAAe,CAACsE,KAAK,UAAAC,sBAAA,UAAAA,sBAAA,CAAI,EAAE,CAAG,EAAE,CACzDnE,cAAc,GAAK,EAAE,EAAAoE,sBAAA,CAAGpE,cAAc,CAACkE,KAAK,UAAAE,sBAAA,UAAAA,sBAAA,CAAI,EAAE,CAAG,EAAE,CACvDlE,kBAAkB,CAClBJ,UAAU,CACVN,WACF,CACF,CAAC,CACDP,QAAQ,CACNtC,YAAY,CACV,GAAG,CACH,EAAE,CACF2C,QAAQ,CACRI,aAAa,CACbU,YAAY,CACZR,eAAe,GAAK,EAAE,EAAAyE,sBAAA,CAAGzE,eAAe,CAACsE,KAAK,UAAAG,sBAAA,UAAAA,sBAAA,CAAI,EAAE,CAAG,EAAE,CACzDrE,cAAc,GAAK,EAAE,EAAAsE,sBAAA,CAAGtE,cAAc,CAACkE,KAAK,UAAAI,sBAAA,UAAAA,sBAAA,CAAI,EAAE,CAAG,EAAE,CACvDpE,kBAAkB,CAClBJ,UAAU,CACVN,WACF,CACF,CAAC,CACH,CACF,CAAC,CAAE,CAAC6C,iBAAiB,CAAC,CAAC,CAEvB,KAAM,CAAAkC,UAAU,CAAIC,UAAU,EAAK,CACjC,GAAIA,UAAU,EAAIA,UAAU,GAAK,EAAE,CAAE,CACnC,KAAM,CAAAC,IAAI,CAAG,GAAI,CAAAC,IAAI,CAACF,UAAU,CAAC,CACjC,MAAO,CAAAC,IAAI,CAACE,kBAAkB,CAAC,OAAO,CAAE,CACtCC,IAAI,CAAE,SAAS,CACfC,KAAK,CAAE,MAAM,CACbC,GAAG,CAAE,SACP,CAAC,CAAC,CACJ,CAAC,IAAM,CACL,MAAO,CAAAN,UAAU,EAAIA,UAAU,GAAK,EAAE,CAAGA,UAAU,CAAG,MAAM,CAC9D,CACF,CAAC,CAED,KAAM,CAAAO,UAAU,CAAIC,UAAU,EAAK,CACjC,OAAQA,UAAU,EAChB,IAAK,sBAAsB,CACzB,MAAO,sBAAsB,CAC/B,IAAK,yBAAyB,CAC5B,MAAO,2BAA2B,CACpC,IAAK,6BAA6B,CAChC,MAAO,8BAA8B,CACvC,IAAK,qCAAqC,CACxC,MAAO,qCAAqC,CAC9C,IAAK,kCAAkC,CACrC,MAAO,mCAAmC,CAC5C,IAAK,mBAAmB,CACtB,MAAO,mBAAmB,CAC5B,IAAK,kBAAkB,CACrB,MAAO,kBAAkB,CAC3B,IAAK,6BAA6B,CAChC,MAAO,8BAA8B,CACvC,IAAK,QAAQ,CACX,MAAO,QAAQ,CACjB,QACE,MAAO,CAAAA,UAAU,CACrB,CACF,CAAC,CAED,mBACEnH,IAAA,CAACZ,aAAa,EAAAgI,QAAA,cACZlH,KAAA,QAAAkH,QAAA,eACEpH,IAAA,QAAKqH,SAAS,CAAC,yCAAyC,CAAAD,QAAA,cAEtDpH,IAAA,MAAGsH,IAAI,CAAC,YAAY,CAAAF,QAAA,cAClBlH,KAAA,QAAKmH,SAAS,CAAC,+CAA+C,CAAAD,QAAA,eAC5DpH,IAAA,QACEuH,KAAK,CAAC,4BAA4B,CAClCC,IAAI,CAAC,MAAM,CACXC,OAAO,CAAC,WAAW,CACnB,eAAa,KAAK,CAClBC,MAAM,CAAC,cAAc,CACrBL,SAAS,CAAC,SAAS,CAAAD,QAAA,cAEnBpH,IAAA,SACE2H,aAAa,CAAC,OAAO,CACrBC,cAAc,CAAC,OAAO,CACtBC,CAAC,CAAC,4OAA4O,CAC/O,CAAC,CACC,CAAC,cACN7H,IAAA,SAAMqH,SAAS,CAAC,MAAM,CAAAD,QAAA,CAAC,WAAS,CAAM,CAAC,EACpC,CAAC,CACL,CAAC,CACD,CAAC,cAINlH,KAAA,QAAKmH,SAAS,CAAC,oFAAoF,CAAAD,QAAA,eACjGlH,KAAA,QAAKmH,SAAS,CAAC,uEAAuE,CAAAD,QAAA,eACpFpH,IAAA,OAAIqH,SAAS,CAAC,oDAAoD,CAAAD,QAAA,CAAC,YAEnE,CAAI,CAAC,cACLpH,IAAA,QAAKqH,SAAS,CAAC,2BAA2B,CAAAD,QAAA,cACxClH,KAAA,MACEoH,IAAI,CAAC,iBAAiB,CACtBD,SAAS,CAAC,mFAAmF,CAAAD,QAAA,eAE7FpH,IAAA,QACEuH,KAAK,CAAC,4BAA4B,CAClCC,IAAI,CAAC,MAAM,CACXC,OAAO,CAAC,WAAW,CACnB,eAAa,KAAK,CAClBC,MAAM,CAAC,cAAc,CACrBI,KAAK,CAAC,QAAQ,CAAAV,QAAA,cAEdpH,IAAA,SACE,iBAAe,OAAO,CACtB,kBAAgB,OAAO,CACvB6H,CAAC,CAAC,wBAAwB,CAC3B,CAAC,CACC,CAAC,cAEN7H,IAAA,QAAKqH,SAAS,CAAC,MAAM,CAAAD,QAAA,CAAC,iBAAe,CAAK,CAAC,EAC1C,CAAC,CACD,CAAC,EACH,CAAC,cAENpH,IAAA,QAAKqH,SAAS,CAAC,gCAAgC,CAAAD,QAAA,cAC7ClH,KAAA,QAAKmH,SAAS,CAAC,sDAAsD,CAAAD,QAAA,eAEnElH,KAAA,QAAKmH,SAAS,CAAC,gBAAgB,CAAAD,QAAA,eAC7BpH,IAAA,QAAKqH,SAAS,CAAC,sEAAsE,CAAAD,QAAA,cACnFpH,IAAA,QAAKqH,SAAS,CAAC,0EAA0E,CAACG,IAAI,CAAC,MAAM,CAACC,OAAO,CAAC,WAAW,CAACC,MAAM,CAAC,cAAc,CAAAN,QAAA,cAC7IpH,IAAA,SAAM2H,aAAa,CAAC,OAAO,CAACC,cAAc,CAAC,OAAO,CAACG,WAAW,CAAE,CAAE,CAACF,CAAC,CAAC,6CAA6C,CAAE,CAAC,CAClH,CAAC,CACH,CAAC,cACN7H,IAAA,UACEqH,SAAS,CAAC,uJAAuJ,CACjKW,WAAW,CAAC,mBAAmB,CAC/B5B,IAAI,CAAC,MAAM,CACXC,KAAK,CAAE5E,QAAS,CAChBwG,QAAQ,CAAGC,CAAC,EAAK,KAAAC,sBAAA,CAAAC,sBAAA,CAAAC,sBAAA,CAAAC,sBAAA,CACf5G,WAAW,CAACwG,CAAC,CAACK,MAAM,CAAClC,KAAK,CAAC,CAC3BjF,QAAQ,CACNvC,kBAAkB,CAChB,GAAG,CACH,EAAE,CACFqJ,CAAC,CAACK,MAAM,CAAClC,KAAK,CACdxE,aAAa,CACbU,YAAY,CACZR,eAAe,GAAK,EAAE,EAAAoG,sBAAA,CAClBpG,eAAe,CAACsE,KAAK,UAAA8B,sBAAA,UAAAA,sBAAA,CAAI,EAAE,CAC3B,EAAE,CACNhG,cAAc,GAAK,EAAE,EAAAiG,sBAAA,CAAGjG,cAAc,CAACkE,KAAK,UAAA+B,sBAAA,UAAAA,sBAAA,CAAI,EAAE,CAAG,EAAE,CACvD/F,kBAAkB,CAClBJ,UAAU,CACVN,WACF,CACF,CAAC,CACDP,QAAQ,CACNtC,YAAY,CACV,GAAG,CACH,EAAE,CACFoJ,CAAC,CAACK,MAAM,CAAClC,KAAK,CACdxE,aAAa,CACbU,YAAY,CACZR,eAAe,GAAK,EAAE,EAAAsG,sBAAA,CAClBtG,eAAe,CAACsE,KAAK,UAAAgC,sBAAA,UAAAA,sBAAA,CAAI,EAAE,CAC3B,EAAE,CACNlG,cAAc,GAAK,EAAE,EAAAmG,sBAAA,CAAGnG,cAAc,CAACkE,KAAK,UAAAiC,sBAAA,UAAAA,sBAAA,CAAI,EAAE,CAAG,EAAE,CACvDjG,kBAAkB,CAClBJ,UAAU,CACVN,WACF,CACF,CAAC,CACH,CAAE,CACH,CAAC,EACC,CAAC,cAGNzB,KAAA,QAAKmH,SAAS,CAAC,gBAAgB,CAAAD,QAAA,eAC7BpH,IAAA,QAAKqH,SAAS,CAAC,sEAAsE,CAAAD,QAAA,cACnFpH,IAAA,QAAKqH,SAAS,CAAC,0EAA0E,CAACG,IAAI,CAAC,MAAM,CAACC,OAAO,CAAC,WAAW,CAACC,MAAM,CAAC,cAAc,CAAAN,QAAA,cAC7IpH,IAAA,SAAM2H,aAAa,CAAC,OAAO,CAACC,cAAc,CAAC,OAAO,CAACG,WAAW,CAAE,CAAE,CAACF,CAAC,CAAC,sHAAsH,CAAE,CAAC,CAC3L,CAAC,CACH,CAAC,cACN7H,IAAA,UACEqH,SAAS,CAAC,uJAAuJ,CACjKW,WAAW,CAAC,kBAAkB,CAC9B5B,IAAI,CAAC,MAAM,CACXC,KAAK,CAAE1E,WAAY,CACnBsG,QAAQ,CAAGC,CAAC,EAAK,KAAAM,sBAAA,CAAAC,sBAAA,CAAAC,sBAAA,CAAAC,sBAAA,CACf/G,cAAc,CAACsG,CAAC,CAACK,MAAM,CAAClC,KAAK,CAAC,CAC9BjF,QAAQ,CACNvC,kBAAkB,CAChB,GAAG,CACH,EAAE,CACF4C,QAAQ,CACRI,aAAa,CACbU,YAAY,CACZR,eAAe,GAAK,EAAE,EAAAyG,sBAAA,CAClBzG,eAAe,CAACsE,KAAK,UAAAmC,sBAAA,UAAAA,sBAAA,CAAI,EAAE,CAC3B,EAAE,CACNrG,cAAc,GAAK,EAAE,EAAAsG,sBAAA,CAAGtG,cAAc,CAACkE,KAAK,UAAAoC,sBAAA,UAAAA,sBAAA,CAAI,EAAE,CAAG,EAAE,CACvDpG,kBAAkB,CAClBJ,UAAU,CACViG,CAAC,CAACK,MAAM,CAAClC,KACX,CACF,CAAC,CACDjF,QAAQ,CACNtC,YAAY,CACV,GAAG,CACH,EAAE,CACF2C,QAAQ,CACRI,aAAa,CACbU,YAAY,CACZR,eAAe,GAAK,EAAE,EAAA2G,sBAAA,CAClB3G,eAAe,CAACsE,KAAK,UAAAqC,sBAAA,UAAAA,sBAAA,CAAI,EAAE,CAC3B,EAAE,CACNvG,cAAc,GAAK,EAAE,EAAAwG,sBAAA,CAAGxG,cAAc,CAACkE,KAAK,UAAAsC,sBAAA,UAAAA,sBAAA,CAAI,EAAE,CAAG,EAAE,CACvDtG,kBAAkB,CAClBJ,UAAU,CACViG,CAAC,CAACK,MAAM,CAAClC,KACX,CACF,CAAC,CACH,CAAE,CACH,CAAC,EACC,CAAC,cAGNnG,KAAA,QAAKmH,SAAS,CAAC,gBAAgB,CAAAD,QAAA,eAC7BpH,IAAA,QAAKqH,SAAS,CAAC,sEAAsE,CAAAD,QAAA,cACnFpH,IAAA,QAAKqH,SAAS,CAAC,0EAA0E,CAACG,IAAI,CAAC,MAAM,CAACC,OAAO,CAAC,WAAW,CAACC,MAAM,CAAC,cAAc,CAAAN,QAAA,cAC7IpH,IAAA,SAAM2H,aAAa,CAAC,OAAO,CAACC,cAAc,CAAC,OAAO,CAACG,WAAW,CAAE,CAAE,CAACF,CAAC,CAAC,qEAAqE,CAAE,CAAC,CAC1I,CAAC,CACH,CAAC,cACN7H,IAAA,UACEqH,SAAS,CAAC,uJAAuJ,CACjKW,WAAW,CAAC,iBAAiB,CAC7B5B,IAAI,CAAC,MAAM,CACXC,KAAK,CAAExE,aAAc,CACrBoG,QAAQ,CAAGC,CAAC,EAAK,KAAAU,sBAAA,CAAAC,sBAAA,CAAAC,uBAAA,CAAAC,uBAAA,CACfjH,gBAAgB,CAACoG,CAAC,CAACK,MAAM,CAAClC,KAAK,CAAC,CAChCjF,QAAQ,CACNvC,kBAAkB,CAChB,GAAG,CACH,EAAE,CACF4C,QAAQ,CACRyG,CAAC,CAACK,MAAM,CAAClC,KAAK,CACd9D,YAAY,CACZR,eAAe,GAAK,EAAE,EAAA6G,sBAAA,CAClB7G,eAAe,CAACsE,KAAK,UAAAuC,sBAAA,UAAAA,sBAAA,CAAI,EAAE,CAC3B,EAAE,CACNzG,cAAc,GAAK,EAAE,EAAA0G,sBAAA,CAAG1G,cAAc,CAACkE,KAAK,UAAAwC,sBAAA,UAAAA,sBAAA,CAAI,EAAE,CAAG,EAAE,CACvDxG,kBAAkB,CAClBJ,UAAU,CACVN,WACF,CACF,CAAC,CACDP,QAAQ,CACNtC,YAAY,CACV,GAAG,CACH,EAAE,CACF2C,QAAQ,CACRyG,CAAC,CAACK,MAAM,CAAClC,KAAK,CACd9D,YAAY,CACZR,eAAe,GAAK,EAAE,EAAA+G,uBAAA,CAClB/G,eAAe,CAACsE,KAAK,UAAAyC,uBAAA,UAAAA,uBAAA,CAAI,EAAE,CAC3B,EAAE,CACN3G,cAAc,GAAK,EAAE,EAAA4G,uBAAA,CAAG5G,cAAc,CAACkE,KAAK,UAAA0C,uBAAA,UAAAA,uBAAA,CAAI,EAAE,CAAG,EAAE,CACvD1G,kBAAkB,CAClBJ,UAAU,CACVN,WACF,CACF,CAAC,CACH,CAAE,CACH,CAAC,EACC,CAAC,cAGNzB,KAAA,QAAKmH,SAAS,CAAC,gBAAgB,CAAAD,QAAA,eAC7BpH,IAAA,QAAKqH,SAAS,CAAC,2EAA2E,CAAAD,QAAA,cACxFpH,IAAA,QAAKqH,SAAS,CAAC,0EAA0E,CAACG,IAAI,CAAC,MAAM,CAACC,OAAO,CAAC,WAAW,CAACC,MAAM,CAAC,cAAc,CAAAN,QAAA,cAC7IpH,IAAA,SAAM2H,aAAa,CAAC,OAAO,CAACC,cAAc,CAAC,OAAO,CAACG,WAAW,CAAE,CAAE,CAACF,CAAC,CAAC,wJAAwJ,CAAE,CAAC,CAC7N,CAAC,CACH,CAAC,cACN3H,KAAA,WACEmG,KAAK,CAAEpE,UAAW,CAClBgG,QAAQ,CAAGC,CAAC,EAAK,KAAAc,uBAAA,CAAAC,uBAAA,CAAAC,uBAAA,CAAAC,uBAAA,CACfjH,aAAa,CAACgG,CAAC,CAACK,MAAM,CAAClC,KAAK,CAAC,CAC7BjF,QAAQ,CACNvC,kBAAkB,CAChB,GAAG,CACH,EAAE,CACF4C,QAAQ,CACRI,aAAa,CACbU,YAAY,CACZR,eAAe,GAAK,EAAE,EAAAiH,uBAAA,CAClBjH,eAAe,CAACsE,KAAK,UAAA2C,uBAAA,UAAAA,uBAAA,CAAI,EAAE,CAC3B,EAAE,CACN7G,cAAc,GAAK,EAAE,EAAA8G,uBAAA,CAAG9G,cAAc,CAACkE,KAAK,UAAA4C,uBAAA,UAAAA,uBAAA,CAAI,EAAE,CAAG,EAAE,CACvD5G,kBAAkB,CAClB6F,CAAC,CAACK,MAAM,CAAClC,KAAK,CACd1E,WACF,CACF,CAAC,CACDP,QAAQ,CACNtC,YAAY,CACV,GAAG,CACH,EAAE,CACF2C,QAAQ,CACRI,aAAa,CACbU,YAAY,CACZR,eAAe,GAAK,EAAE,EAAAmH,uBAAA,CAClBnH,eAAe,CAACsE,KAAK,UAAA6C,uBAAA,UAAAA,uBAAA,CAAI,EAAE,CAC3B,EAAE,CACN/G,cAAc,GAAK,EAAE,EAAAgH,uBAAA,CAAGhH,cAAc,CAACkE,KAAK,UAAA8C,uBAAA,UAAAA,uBAAA,CAAI,EAAE,CAAG,EAAE,CACvD9G,kBAAkB,CAClB6F,CAAC,CAACK,MAAM,CAAClC,KAAK,CACd1E,WACF,CACF,CAAC,CACH,CAAE,CACF0F,SAAS,CAAC,iKAAiK,CAAAD,QAAA,eAE3KpH,IAAA,WAAQqG,KAAK,CAAC,EAAE,CAAAe,QAAA,CAAC,WAAS,CAAQ,CAAC,cACnCpH,IAAA,WAAQqG,KAAK,CAAC,SAAS,CAAAe,QAAA,CAAC,SAAO,CAAQ,CAAC,cACxCpH,IAAA,WAAQqG,KAAK,CAAC,WAAW,CAAAe,QAAA,CAAC,WAAS,CAAQ,CAAC,EACtC,CAAC,cACTpH,IAAA,QAAKqH,SAAS,CAAC,uEAAuE,CAAAD,QAAA,cACpFpH,IAAA,QAAKqH,SAAS,CAAC,uBAAuB,CAACG,IAAI,CAAC,MAAM,CAACC,OAAO,CAAC,WAAW,CAACC,MAAM,CAAC,cAAc,CAAAN,QAAA,cAC1FpH,IAAA,SAAM2H,aAAa,CAAC,OAAO,CAACC,cAAc,CAAC,OAAO,CAACG,WAAW,CAAE,CAAE,CAACF,CAAC,CAAC,gBAAgB,CAAE,CAAC,CACrF,CAAC,CACH,CAAC,EACH,CAAC,EACH,CAAC,CACH,CAAC,cAEN7H,IAAA,QAAKqH,SAAS,CAAC,iCAAiC,CAAAD,QAAA,cAC9ClH,KAAA,QAAKmH,SAAS,CAAC,sDAAsD,CAAAD,QAAA,eAEnElH,KAAA,QAAKmH,SAAS,CAAC,gBAAgB,CAAAD,QAAA,eAC7BpH,IAAA,QAAKqH,SAAS,CAAC,2EAA2E,CAAAD,QAAA,cACxFpH,IAAA,QAAKqH,SAAS,CAAC,0EAA0E,CAACG,IAAI,CAAC,MAAM,CAACC,OAAO,CAAC,WAAW,CAACC,MAAM,CAAC,cAAc,CAAAN,QAAA,cAC7IpH,IAAA,SAAM2H,aAAa,CAAC,OAAO,CAACC,cAAc,CAAC,OAAO,CAACG,WAAW,CAAE,CAAE,CAACF,CAAC,CAAC,+CAA+C,CAAE,CAAC,CACpH,CAAC,CACH,CAAC,cACN3H,KAAA,WACEmG,KAAK,CAAE9D,YAAa,CACpB0F,QAAQ,CAAGC,CAAC,EAAK,KAAAkB,uBAAA,CAAAC,uBAAA,CAAAC,uBAAA,CAAAC,uBAAA,CACf/G,gBAAgB,CAAC0F,CAAC,CAACK,MAAM,CAAClC,KAAK,CAAC,CAChCjF,QAAQ,CACNvC,kBAAkB,CAChB,GAAG,CACH,EAAE,CACF4C,QAAQ,CACRI,aAAa,CACbqG,CAAC,CAACK,MAAM,CAAClC,KAAK,CACdtE,eAAe,GAAK,EAAE,EAAAqH,uBAAA,CAClBrH,eAAe,CAACsE,KAAK,UAAA+C,uBAAA,UAAAA,uBAAA,CAAI,EAAE,CAC3B,EAAE,CACNjH,cAAc,GAAK,EAAE,EAAAkH,uBAAA,CAAGlH,cAAc,CAACkE,KAAK,UAAAgD,uBAAA,UAAAA,uBAAA,CAAI,EAAE,CAAG,EAAE,CACvDhH,kBAAkB,CAClBJ,UAAU,CACVN,WACF,CACF,CAAC,CACDP,QAAQ,CACNtC,YAAY,CACV,GAAG,CACH,EAAE,CACF2C,QAAQ,CACRI,aAAa,CACbqG,CAAC,CAACK,MAAM,CAAClC,KAAK,CACdtE,eAAe,GAAK,EAAE,EAAAuH,uBAAA,CAClBvH,eAAe,CAACsE,KAAK,UAAAiD,uBAAA,UAAAA,uBAAA,CAAI,EAAE,CAC3B,EAAE,CACNnH,cAAc,GAAK,EAAE,EAAAoH,uBAAA,CAAGpH,cAAc,CAACkE,KAAK,UAAAkD,uBAAA,UAAAA,uBAAA,CAAI,EAAE,CAAG,EAAE,CACvDlH,kBAAkB,CAClBJ,UAAU,CACVN,WACF,CACF,CAAC,CACH,CAAE,CACF0F,SAAS,CAAC,wKAAwK,CAAAD,QAAA,eAElLpH,IAAA,WAAQqG,KAAK,CAAC,EAAE,CAAAe,QAAA,CAAC,YAAU,CAAQ,CAAC,cACpCpH,IAAA,WAAQqG,KAAK,CAAC,sBAAsB,CAAAe,QAAA,CAAC,sBAAoB,CAAQ,CAAC,cAClEpH,IAAA,WAAQqG,KAAK,CAAC,yBAAyB,CAAAe,QAAA,CAAC,2BAAyB,CAAQ,CAAC,cAC1EpH,IAAA,WAAQqG,KAAK,CAAC,6BAA6B,CAAAe,QAAA,CAAC,8BAA4B,CAAQ,CAAC,cACjFpH,IAAA,WAAQqG,KAAK,CAAC,qCAAqC,CAAAe,QAAA,CAAC,qCAAmC,CAAQ,CAAC,cAChGpH,IAAA,WAAQqG,KAAK,CAAC,kCAAkC,CAAAe,QAAA,CAAC,mCAAiC,CAAQ,CAAC,cAC3FpH,IAAA,WAAQqG,KAAK,CAAC,mBAAmB,CAAAe,QAAA,CAAC,mBAAiB,CAAQ,CAAC,cAC5DpH,IAAA,WAAQqG,KAAK,CAAC,6BAA6B,CAAAe,QAAA,CAAC,8BAA4B,CAAQ,CAAC,cACjFpH,IAAA,WAAQqG,KAAK,CAAC,kBAAkB,CAAAe,QAAA,CAAC,kBAAgB,CAAQ,CAAC,cAC1DpH,IAAA,WAAQqG,KAAK,CAAC,QAAQ,CAAAe,QAAA,CAAC,QAAM,CAAQ,CAAC,EAChC,CAAC,cACTpH,IAAA,QAAKqH,SAAS,CAAC,uEAAuE,CAAAD,QAAA,cACpFpH,IAAA,QAAKqH,SAAS,CAAC,uBAAuB,CAACG,IAAI,CAAC,MAAM,CAACC,OAAO,CAAC,WAAW,CAACC,MAAM,CAAC,cAAc,CAAAN,QAAA,cAC1FpH,IAAA,SAAM2H,aAAa,CAAC,OAAO,CAACC,cAAc,CAAC,OAAO,CAACG,WAAW,CAAE,CAAE,CAACF,CAAC,CAAC,gBAAgB,CAAE,CAAC,CACrF,CAAC,CACH,CAAC,EACH,CAAC,cAGN3H,KAAA,QAAKmH,SAAS,CAAC,gBAAgB,CAAAD,QAAA,eAC7BpH,IAAA,QAAKqH,SAAS,CAAC,2EAA2E,CAAAD,QAAA,cACxFpH,IAAA,QAAKqH,SAAS,CAAC,0EAA0E,CAACG,IAAI,CAAC,MAAM,CAACC,OAAO,CAAC,WAAW,CAACC,MAAM,CAAC,cAAc,CAAAN,QAAA,cAC7IpH,IAAA,SAAM2H,aAAa,CAAC,OAAO,CAACC,cAAc,CAAC,OAAO,CAACG,WAAW,CAAE,CAAE,CAACF,CAAC,CAAC,gMAAgM,CAAE,CAAC,CACrQ,CAAC,CACH,CAAC,cACN7H,IAAA,CAACN,MAAM,EACL2G,KAAK,CAAEtE,eAAgB,CACvBkG,QAAQ,CAAGuB,MAAM,EAAK,CACpBxH,kBAAkB,CAACwH,MAAM,CAAC,CAC1B,GAAIA,MAAM,EAAIA,MAAM,CAACnD,KAAK,CAAE,KAAAoD,uBAAA,CAAAC,uBAAA,CAC1BtI,QAAQ,CACNvC,kBAAkB,CAChB,GAAG,CACH,EAAE,CACF4C,QAAQ,CACRI,aAAa,CACbU,YAAY,CACZiH,MAAM,CAACnD,KAAK,CACZlE,cAAc,GAAK,EAAE,EAAAsH,uBAAA,CACjBtH,cAAc,CAACkE,KAAK,UAAAoD,uBAAA,UAAAA,uBAAA,CAAI,EAAE,CAC1B,EAAE,CACNpH,kBAAkB,CAClBJ,UAAU,CACVN,WACF,CACF,CAAC,CACDP,QAAQ,CACNtC,YAAY,CACV,GAAG,CACH,EAAE,CACF2C,QAAQ,CACRI,aAAa,CACbU,YAAY,CACZiH,MAAM,CAACnD,KAAK,CACZlE,cAAc,GAAK,EAAE,EAAAuH,uBAAA,CACjBvH,cAAc,CAACkE,KAAK,UAAAqD,uBAAA,UAAAA,uBAAA,CAAI,EAAE,CAC1B,EAAE,CACNrH,kBAAkB,CAClBJ,UAAU,CACVN,WACF,CACF,CAAC,CACH,CAAC,IAAM,KAAAgI,uBAAA,CAAAC,uBAAA,CACLxI,QAAQ,CACNvC,kBAAkB,CAChB,GAAG,CACH,EAAE,CACF4C,QAAQ,CACRI,aAAa,CACbU,YAAY,CACZ,EAAE,CACFJ,cAAc,GAAK,EAAE,EAAAwH,uBAAA,CACjBxH,cAAc,CAACkE,KAAK,UAAAsD,uBAAA,UAAAA,uBAAA,CAAI,EAAE,CAC1B,EAAE,CACNtH,kBAAkB,CAClBJ,UAAU,CACVN,WACF,CACF,CAAC,CACDP,QAAQ,CACNtC,YAAY,CACV,GAAG,CACH,EAAE,CACF2C,QAAQ,CACRI,aAAa,CACbU,YAAY,CACZ,EAAE,CACFJ,cAAc,GAAK,EAAE,EAAAyH,uBAAA,CACjBzH,cAAc,CAACkE,KAAK,UAAAuD,uBAAA,UAAAA,uBAAA,CAAI,EAAE,CAC1B,EAAE,CACNvH,kBAAkB,CAClBJ,UAAU,CACVN,WACF,CACF,CAAC,CACH,CACF,CAAE,CACFkI,OAAO,CAAE7E,UAAU,EAAIA,UAAU,CAAC8E,MAAM,CAAG,CAAC,CAAG9E,UAAU,CAAC+E,GAAG,CAAEC,SAAS,GAAM,CAC5E3D,KAAK,CAAE2D,SAAS,CAACC,EAAE,CACnBC,KAAK,CAAEF,SAAS,CAACG,cAAc,EAAI,EACrC,CAAC,CAAC,CAAC,CAAG,EAAG,CACTC,YAAY,CAAEA,CAACZ,MAAM,CAAEa,UAAU,GAC/Bb,MAAM,CAACU,KAAK,CACTI,WAAW,CAAC,CAAC,CACbC,QAAQ,CAACF,UAAU,CAACC,WAAW,CAAC,CAAC,CACrC,CACDtC,WAAW,CAAC,qBAAqB,CACjCwC,YAAY,MACZC,WAAW,MACXpD,SAAS,CAAC,wBAAwB,CAClCqD,eAAe,CAAC,cAAc,CAC9BC,MAAM,CAAE,CACNC,OAAO,CAAEA,CAACC,IAAI,CAAErH,KAAK,IAAM,CACzB,GAAGqH,IAAI,CACPC,SAAS,CAAE,MAAM,CACjBC,WAAW,CAAE,MAAM,CACnBC,MAAM,CAAE,MAAM,CACdC,YAAY,CAAE,KAAK,CACnBC,eAAe,CAAE,SAAS,CAC1BC,SAAS,CAAE,MAAM,CACjB,SAAS,CAAE,CACTD,eAAe,CAAE,SACnB,CAAC,CACDE,UAAU,CAAE,UACd,CAAC,CAAC,CACFpD,WAAW,CAAG6C,IAAI,GAAM,CACtB,GAAGA,IAAI,CACPQ,KAAK,CAAE,SAAS,CAChBC,QAAQ,CAAE,MACZ,CAAC,CAAC,CACFC,WAAW,CAAGV,IAAI,GAAM,CACtB,GAAGA,IAAI,CACPQ,KAAK,CAAE,SAAS,CAChBC,QAAQ,CAAE,MACZ,CAAC,CAAC,CACF9B,MAAM,CAAEA,CAACqB,IAAI,CAAErH,KAAK,IAAM,CACxB,GAAGqH,IAAI,CACPK,eAAe,CAAE1H,KAAK,CAACgI,UAAU,CAAG,SAAS,CAAGhI,KAAK,CAACiI,SAAS,CAAG,SAAS,CAAG,OAAO,CACrFJ,KAAK,CAAE7H,KAAK,CAACgI,UAAU,CAAG,OAAO,CAAG,SAAS,CAC7CF,QAAQ,CAAE,MACZ,CAAC,CACH,CAAE,CACH,CAAC,EACC,CAAC,cAGNpL,KAAA,QAAKmH,SAAS,CAAC,gBAAgB,CAAAD,QAAA,eAC7BpH,IAAA,QAAKqH,SAAS,CAAC,2EAA2E,CAAAD,QAAA,cACxFpH,IAAA,QAAKqH,SAAS,CAAC,0EAA0E,CAACG,IAAI,CAAC,MAAM,CAACC,OAAO,CAAC,WAAW,CAACC,MAAM,CAAC,cAAc,CAAAN,QAAA,cAC7IpH,IAAA,SAAM2H,aAAa,CAAC,OAAO,CAACC,cAAc,CAAC,OAAO,CAACG,WAAW,CAAE,CAAE,CAACF,CAAC,CAAC,+LAA+L,CAAE,CAAC,CACpQ,CAAC,CACH,CAAC,cACN7H,IAAA,CAACN,MAAM,EACL2G,KAAK,CAAElE,cAAe,CACtB8F,QAAQ,CAAGuB,MAAM,EAAK,CACpBpH,iBAAiB,CAACoH,MAAM,CAAC,CACzB,GAAIA,MAAM,EAAIA,MAAM,CAACnD,KAAK,CAAE,KAAAqF,uBAAA,CAAAC,uBAAA,CAC1BvK,QAAQ,CACNvC,kBAAkB,CAChB,GAAG,CACH,EAAE,CACF4C,QAAQ,CACRI,aAAa,CACbU,YAAY,CACZR,eAAe,GAAK,EAAE,EAAA2J,uBAAA,CAAG3J,eAAe,CAACsE,KAAK,UAAAqF,uBAAA,UAAAA,uBAAA,CAAI,EAAE,CAAG,EAAE,CACzDlC,MAAM,CAACnD,KAAK,CACZhE,kBAAkB,CAClBJ,UAAU,CACVN,WACF,CACF,CAAC,CACDP,QAAQ,CACNtC,YAAY,CACV,GAAG,CACH,EAAE,CACF2C,QAAQ,CACRI,aAAa,CACbU,YAAY,CACZR,eAAe,GAAK,EAAE,EAAA4J,uBAAA,CAAG5J,eAAe,CAACsE,KAAK,UAAAsF,uBAAA,UAAAA,uBAAA,CAAI,EAAE,CAAG,EAAE,CACzDnC,MAAM,CAACnD,KAAK,CACZhE,kBAAkB,CAClBJ,UAAU,CACVN,WACF,CACF,CAAC,CACH,CAAC,IAAM,KAAAiK,uBAAA,CAAAC,uBAAA,CACLzK,QAAQ,CACNvC,kBAAkB,CAChB,GAAG,CACH,EAAE,CACF4C,QAAQ,CACRI,aAAa,CACbU,YAAY,CACZR,eAAe,GAAK,EAAE,EAAA6J,uBAAA,CAAG7J,eAAe,CAACsE,KAAK,UAAAuF,uBAAA,UAAAA,uBAAA,CAAI,EAAE,CAAG,EAAE,CACzD,EAAE,CACFvJ,kBAAkB,CAClBJ,UAAU,CACVN,WACF,CACF,CAAC,CACDP,QAAQ,CACNtC,YAAY,CACV,GAAG,CACH,EAAE,CACF2C,QAAQ,CACRI,aAAa,CACbU,YAAY,CACZR,eAAe,GAAK,EAAE,EAAA8J,uBAAA,CAAG9J,eAAe,CAACsE,KAAK,UAAAwF,uBAAA,UAAAA,uBAAA,CAAI,EAAE,CAAG,EAAE,CACzD,EAAE,CACFxJ,kBAAkB,CAClBJ,UAAU,CACVN,WACF,CACF,CAAC,CACH,CACF,CAAE,CACFkI,OAAO,CAAElF,SAAS,EAAIA,SAAS,CAACmF,MAAM,CAAG,CAAC,CAAGnF,SAAS,CAACoF,GAAG,CAAE+B,QAAQ,GAAM,CACxEzF,KAAK,CAAEyF,QAAQ,CAAC7B,EAAE,CAClBC,KAAK,CAAE4B,QAAQ,CAACC,SAAS,EAAI,EAC/B,CAAC,CAAC,CAAC,CAAG,EAAG,CACT3B,YAAY,CAAEA,CAACZ,MAAM,CAAEa,UAAU,GAC/Bb,MAAM,CAACU,KAAK,CACTI,WAAW,CAAC,CAAC,CACbC,QAAQ,CAACF,UAAU,CAACC,WAAW,CAAC,CAAC,CACrC,CACDtC,WAAW,CAAC,oBAAoB,CAChCwC,YAAY,MACZC,WAAW,MACXpD,SAAS,CAAC,wBAAwB,CAClCqD,eAAe,CAAC,cAAc,CAC9BC,MAAM,CAAE,CACNC,OAAO,CAAEA,CAACC,IAAI,CAAErH,KAAK,IAAM,CACzB,GAAGqH,IAAI,CACPC,SAAS,CAAE,MAAM,CACjBC,WAAW,CAAE,MAAM,CACnBC,MAAM,CAAE,MAAM,CACdC,YAAY,CAAE,KAAK,CACnBC,eAAe,CAAE,SAAS,CAC1BC,SAAS,CAAE,MAAM,CACjB,SAAS,CAAE,CACTD,eAAe,CAAE,SACnB,CAAC,CACDE,UAAU,CAAE,UACd,CAAC,CAAC,CACFpD,WAAW,CAAG6C,IAAI,GAAM,CACtB,GAAGA,IAAI,CACPQ,KAAK,CAAE,SAAS,CAChBC,QAAQ,CAAE,MACZ,CAAC,CAAC,CACFC,WAAW,CAAGV,IAAI,GAAM,CACtB,GAAGA,IAAI,CACPQ,KAAK,CAAE,SAAS,CAChBC,QAAQ,CAAE,MACZ,CAAC,CAAC,CACF9B,MAAM,CAAEA,CAACqB,IAAI,CAAErH,KAAK,IAAM,CACxB,GAAGqH,IAAI,CACPK,eAAe,CAAE1H,KAAK,CAACgI,UAAU,CAAG,SAAS,CAAGhI,KAAK,CAACiI,SAAS,CAAG,SAAS,CAAG,OAAO,CACrFJ,KAAK,CAAE7H,KAAK,CAACgI,UAAU,CAAG,OAAO,CAAG,SAAS,CAC7CF,QAAQ,CAAE,MACZ,CAAC,CACH,CAAE,CACH,CAAC,EACC,CAAC,cAGNtL,IAAA,QAAKqH,SAAS,CAAC,gBAAgB,CAAAD,QAAA,cAC7BlH,KAAA,WACE8L,OAAO,CAAEA,CAAA,GAAM,CACbtK,WAAW,CAAC,EAAE,CAAC,CACfM,kBAAkB,CAAC,EAAE,CAAC,CACtBI,iBAAiB,CAAC,EAAE,CAAC,CACrBI,gBAAgB,CAAC,EAAE,CAAC,CACpBN,aAAa,CAAC,EAAE,CAAC,CACjBJ,gBAAgB,CAAC,EAAE,CAAC,CACpBF,cAAc,CAAC,EAAE,CAAC,CACpB,CAAE,CACFyF,SAAS,CAAC,yJAAyJ,CAAAD,QAAA,eAEnKpH,IAAA,QACEuH,KAAK,CAAC,4BAA4B,CAClCC,IAAI,CAAC,MAAM,CACXC,OAAO,CAAC,WAAW,CACnBM,WAAW,CAAC,KAAK,CACjBL,MAAM,CAAC,cAAc,CACrBL,SAAS,CAAC,SAAS,CAAAD,QAAA,cAEnBpH,IAAA,SACE2H,aAAa,CAAC,OAAO,CACrBC,cAAc,CAAC,OAAO,CACtBC,CAAC,CAAC,yKAAyK,CAC5K,CAAC,CACC,CAAC,gBAER,EAAQ,CAAC,CACN,CAAC,EACH,CAAC,CACH,CAAC,cACN7H,IAAA,QAAKqH,SAAS,CAAC,qBAAqB,CAAAD,QAAA,cAClCpH,IAAA,QAAKqH,SAAS,CAAC,6BAA6B,CAAAD,QAAA,CACzCvD,YAAY,cACX7D,IAAA,CAACb,MAAM,GAAE,CAAC,CACR2E,UAAU,cACZ9D,IAAA,CAACd,KAAK,EAACkH,IAAI,CAAC,OAAO,CAAC6F,OAAO,CAAEnI,UAAW,CAAE,CAAC,cAE3C5D,KAAA,QAAKmH,SAAS,CAAC,6BAA6B,CAAAD,QAAA,eAC1ClH,KAAA,UAAOmH,SAAS,CAAC,mBAAmB,CAAAD,QAAA,eAClCpH,IAAA,UAAAoH,QAAA,cACElH,KAAA,OAAImH,SAAS,CAAC,0BAA0B,CAAAD,QAAA,eACtCpH,IAAA,OAAIqH,SAAS,CAAC,gEAAgE,CAAAD,QAAA,CAAC,IAE/E,CAAI,CAAC,cACLpH,IAAA,OAAIqH,SAAS,CAAC,gEAAgE,CAAAD,QAAA,CAAC,QAE/E,CAAI,CAAC,cACLpH,IAAA,OAAIqH,SAAS,CAAC,gEAAgE,CAAAD,QAAA,CAAC,cAE/E,CAAI,CAAC,cACLpH,IAAA,OAAIqH,SAAS,CAAC,+DAA+D,CAAAD,QAAA,CAAC,MAE9E,CAAI,CAAC,cACLpH,IAAA,OAAIqH,SAAS,CAAC,gEAAgE,CAAAD,QAAA,CAAC,mBAE/E,CAAI,CAAC,cACLpH,IAAA,OAAIqH,SAAS,CAAC,gEAAgE,CAAAD,QAAA,CAAC,QAE/E,CAAI,CAAC,cACLpH,IAAA,OAAIqH,SAAS,CAAC,gEAAgE,CAAAD,QAAA,CAAC,cAE/E,CAAI,CAAC,cACLpH,IAAA,OAAIqH,SAAS,CAAC,gEAAgE,CAAK,CAAC,EAClF,CAAC,CACA,CAAC,cAERnH,KAAA,UAAAkH,QAAA,EACGxD,KAAK,SAALA,KAAK,iBAALA,KAAK,CAAEmG,GAAG,CAAC,CAACmC,IAAI,CAAEC,KAAK,QAAAC,qBAAA,CAAAC,eAAA,CAAAC,qBAAA,CAAAC,aAAA,CAAAC,eAAA,qBACtB;AACAtM,KAAA,OAAAkH,QAAA,eACEpH,IAAA,OACEgM,OAAO,CAAEA,CAAA,GAAM,CACbjL,QAAQ,CAAC,qBAAqB,CAAGmL,IAAI,CAACjC,EAAE,CAAC,CAC3C,CAAE,CACF5C,SAAS,CAAC,2CAA2C,CAAAD,QAAA,cAErDlH,KAAA,MAAGmH,SAAS,CAAC,6BAA6B,CAAAD,QAAA,EAAC,GACxC,CAAC8E,IAAI,CAACjC,EAAE,EACR,CAAC,CACF,CAAC,cACLjK,IAAA,OACEgM,OAAO,CAAEA,CAAA,GAAM,CACbjL,QAAQ,CAAC,qBAAqB,CAAGmL,IAAI,CAACjC,EAAE,CAAC,CAC3C,CAAE,CACF5C,SAAS,CAAC,2CAA2C,CAAAD,QAAA,cAErDpH,IAAA,MAAGqH,SAAS,CAAC,6BAA6B,CAAAD,QAAA,EAAAgF,qBAAA,EAAAC,eAAA,CACvCH,IAAI,CAAClC,SAAS,UAAAqC,eAAA,iBAAdA,eAAA,CAAgBlC,cAAc,UAAAiC,qBAAA,UAAAA,qBAAA,CAAI,KAAK,CACvC,CAAC,CACF,CAAC,cACLpM,IAAA,OACEgM,OAAO,CAAEA,CAAA,GAAM,CACbjL,QAAQ,CAAC,qBAAqB,CAAGmL,IAAI,CAACjC,EAAE,CAAC,CAC3C,CAAE,CACF5C,SAAS,CAAC,2CAA2C,CAAAD,QAAA,cAErDpH,IAAA,MAAGqH,SAAS,CAAC,6BAA6B,CAAAD,QAAA,EAAAkF,qBAAA,EAAAC,aAAA,CACvCL,IAAI,CAACO,OAAO,UAAAF,aAAA,iBAAZA,aAAA,CAAcR,SAAS,UAAAO,qBAAA,UAAAA,qBAAA,CAAI,KAAK,CAChC,CAAC,CACF,CAAC,cACLtM,IAAA,OACEgM,OAAO,CAAEA,CAAA,GAAM,CACbjL,QAAQ,CAAC,qBAAqB,CAAGmL,IAAI,CAACjC,EAAE,CAAC,CAC3C,CAAE,CACF5C,SAAS,CAAC,2CAA2C,CAAAD,QAAA,cAErDpH,IAAA,MAAGqH,SAAS,CAAC,6BAA6B,CAAAD,QAAA,EAAAoF,eAAA,CACvCN,IAAI,CAACQ,SAAS,UAAAF,eAAA,UAAAA,eAAA,CAAI,KAAK,CACvB,CAAC,CACF,CAAC,cACLxM,IAAA,OACEgM,OAAO,CAAEA,CAAA,GAAM,CACbjL,QAAQ,CAAC,qBAAqB,CAAGmL,IAAI,CAACjC,EAAE,CAAC,CAC3C,CAAE,CACF5C,SAAS,CAAC,2CAA2C,CAAAD,QAAA,cAErDlH,KAAA,MAAGmH,SAAS,CAAC,6BAA6B,CAAAD,QAAA,EAEvC8E,IAAI,CAACS,iBAAiB,EAAI,CAAC,CAAC,YAC/B,EAAG,CAAC,CACF,CAAC,cACL3M,IAAA,OACEgM,OAAO,CAAEA,CAAA,GAAM,CACbjL,QAAQ,CAAC,qBAAqB,CAAGmL,IAAI,CAACjC,EAAE,CAAC,CAC3C,CAAE,CACF5C,SAAS,CAAC,2CAA2C,CAAAD,QAAA,cAErDpH,IAAA,MAAGqH,SAAS,CAAC,mCAAmC,CAAAD,QAAA,CAC7C8E,IAAI,CAACU,WAAW,EAAIV,IAAI,CAACU,WAAW,CAAC9C,MAAM,CAAG,CAAC,CAC9CoC,IAAI,CAACU,WAAW,CAAC7C,GAAG,CAAC,CAAC8C,IAAI,CAAEV,KAAK,gBAC/BjM,KAAA,SAAAkH,QAAA,EAAmBF,UAAU,CAAC2F,IAAI,CAACC,mBAAmB,CAAC,CAAC,IAAE,GAA/CX,KAAqD,CACjE,CAAC,CAEF,KACD,CACA,CAAC,CACF,CAAC,cACLnM,IAAA,OACEgM,OAAO,CAAEA,CAAA,GAAM,CACbjL,QAAQ,CAAC,qBAAqB,CAAGmL,IAAI,CAACjC,EAAE,CAAC,CAC3C,CAAE,CACF5C,SAAS,CAAC,2CAA2C,CAAAD,QAAA,cAErDpH,IAAA,MAAGqH,SAAS,CAAC,6BAA6B,CAAAD,QAAA,CACvCV,UAAU,CAACwF,IAAI,CAACa,SAAS,CAAC,CAC1B,CAAC,CACF,CAAC,cACL/M,IAAA,OAAIqH,SAAS,CAAC,4BAA4B,CAAAD,QAAA,cACxClH,KAAA,MAAGmH,SAAS,CAAC,2CAA2C,CAAAD,QAAA,eACtDpH,IAAA,CAACvB,IAAI,EACH4I,SAAS,CAAC,mBAAmB,CAC7B2F,EAAE,CAAE,qBAAqB,CAAGd,IAAI,CAACjC,EAAG,CAAA7C,QAAA,cAEpClH,KAAA,QACEqH,KAAK,CAAC,4BAA4B,CAClCC,IAAI,CAAC,MAAM,CACXC,OAAO,CAAC,WAAW,CACnB,eAAa,KAAK,CAClBC,MAAM,CAAC,cAAc,CACrBL,SAAS,CAAC,+DAA+D,CAAAD,QAAA,eAEzEpH,IAAA,SACE,iBAAe,OAAO,CACtB,kBAAgB,OAAO,CACvB6H,CAAC,CAAC,0LAA0L,CAC7L,CAAC,cACF7H,IAAA,SACE,iBAAe,OAAO,CACtB,kBAAgB,OAAO,CACvB6H,CAAC,CAAC,qCAAqC,CACxC,CAAC,EACC,CAAC,CACF,CAAC,cACP7H,IAAA,CAACvB,IAAI,EACH4I,SAAS,CAAC,mBAAmB,CAC7B2F,EAAE,CAAE,mBAAmB,CAAGd,IAAI,CAACjC,EAAG,CAAA7C,QAAA,cAElCpH,IAAA,QACEuH,KAAK,CAAC,4BAA4B,CAClCC,IAAI,CAAC,MAAM,CACXC,OAAO,CAAC,WAAW,CACnBM,WAAW,CAAC,KAAK,CACjBL,MAAM,CAAC,cAAc,CACrBL,SAAS,CAAC,+DAA+D,CAAAD,QAAA,cAEzEpH,IAAA,SACE2H,aAAa,CAAC,OAAO,CACrBC,cAAc,CAAC,OAAO,CACtBC,CAAC,CAAC,kQAAkQ,CACrQ,CAAC,CACC,CAAC,CACF,CAAC,cACP7H,IAAA,QACEgM,OAAO,CAAEA,CAAA,GAAM,CACb5I,YAAY,CAAC,QAAQ,CAAC,CACtBE,SAAS,CAAC4I,IAAI,CAACjC,EAAE,CAAC,CAClBjH,WAAW,CAAC,IAAI,CAAC,CACnB,CAAE,CACFqE,SAAS,CAAC,kCAAkC,CAAAD,QAAA,cAE5CpH,IAAA,QACEuH,KAAK,CAAC,4BAA4B,CAClCC,IAAI,CAAC,MAAM,CACXC,OAAO,CAAC,WAAW,CACnB,eAAa,KAAK,CAClBC,MAAM,CAAC,cAAc,CACrBL,SAAS,CAAC,8DAA8D,CAAAD,QAAA,cAExEpH,IAAA,SACE,iBAAe,OAAO,CACtB,kBAAgB,OAAO,CACvB6H,CAAC,CAAC,+TAA+T,CAClU,CAAC,CACC,CAAC,CACH,CAAC,EACL,CAAC,CACF,CAAC,GAnJEsE,KAoJL,CAAC,GACN,CAAC,cACFnM,IAAA,OAAIqH,SAAS,CAAC,KAAK,CAAK,CAAC,EACpB,CAAC,EACH,CAAC,cACRrH,IAAA,QAAKqH,SAAS,CAAC,EAAE,CAAAD,QAAA,cACfpH,IAAA,CAACf,QAAQ,EACPgO,KAAK,CAAE,aAAc,CACrBpK,MAAM,CAAE,EAAG,CACX3B,IAAI,CAAEA,IAAK,CACX6C,KAAK,CAAEA,KAAM,CACd,CAAC,CACC,CAAC,EACH,CACN,CACE,CAAC,CACH,CAAC,cACN/D,IAAA,QAAKqH,SAAS,CAAC,kDAAkD,CAAAD,QAAA,cAC/DpH,IAAA,OAAIqH,SAAS,CAAC,oDAAoD,CAAAD,QAAA,CAAC,eAEnE,CAAI,CAAC,CACF,CAAC,cAENpH,IAAA,QAAKqH,SAAS,CAAC,qBAAqB,CAAAD,QAAA,cAClCpH,IAAA,QAAKqH,SAAS,CAAC,6BAA6B,CAAAD,QAAA,cAC1ClH,KAAA,QAAKmH,SAAS,CAAC,WAAW,CAAAD,QAAA,eACxBlH,KAAA,CAACb,YAAY,EACX6N,MAAM,CAAE,CAAC,CAAC,CAAE,CAAC,CAAE,CACfC,IAAI,CAAE,CAAE,CACRC,KAAK,CAAE,CAAEC,MAAM,CAAE,OAAO,CAAEC,KAAK,CAAE,MAAO,CAAE,CAAAlG,QAAA,eAE1CpH,IAAA,CAACV,SAAS,EACRiO,GAAG,CAAC,oDAAoD,CACxDC,WAAW,CAAC,yFAAyF,CACtG,CAAC,CACDtJ,QAAQ,EAAIA,QAAQ,CAAC4F,MAAM,CAAG,CAAC,CAAG5F,QAAQ,CAAC6F,GAAG,CAAC,CAAC0D,QAAQ,CAAEC,SAAS,gBAClE1N,IAAA,QAAAoH,QAAA,CACGqG,QAAQ,CAACd,iBAAiB,EAAIc,QAAQ,CAACd,iBAAiB,CAAC7C,MAAM,CAAG,CAAC,CAClE2D,QAAQ,CAACd,iBAAiB,CACvBgB,MAAM,CACJ7B,QAAQ,EACPA,QAAQ,CAACA,QAAQ,EACjBA,QAAQ,CAACA,QAAQ,CAAC8B,UAAU,EAC5B9B,QAAQ,CAACA,QAAQ,CAAC+B,UACtB,CAAC,CACA9D,GAAG,CAAC,CAAC+B,QAAQ,CAAEK,KAAK,gBACnBnM,IAAA,CAACT,MAAM,EACLuO,aAAa,CAAE,CACbC,KAAK,CAAEA,CAAA,GAAM,CACXvM,YAAY,CAAC,IAAI,CAAC,CAClBF,oBAAoB,CAACwK,QAAQ,CAAC,CAChC,CAAG;AACL,CAAE,CAEFkC,QAAQ,CAAE,CACRlC,QAAQ,CAACA,QAAQ,CAAC8B,UAAU,CAC5B9B,QAAQ,CAACA,QAAQ,CAAC+B,UAAU,CAC5B,CAAAzG,QAAA,cAEFlH,KAAA,CAACV,KAAK,EAAA4H,QAAA,EACH0E,QAAQ,CAACA,QAAQ,CAACC,SAAS,cAC5B/L,IAAA,QAAK,CAAC,EACD,CAAC,EATF,GAAE0N,SAAU,IAAGvB,KAAM,EAUrB,CACT,CAAC,CACF,IAAI,EA7BAuB,SA8BL,CACN,CAAC,CAAG,IAAI,EAEG,CAAC,CACdnM,SAAS,cACRvB,IAAA,QAAKqH,SAAS,CAAC,4DAA4D,CAAAD,QAAA,cACzElH,KAAA,QAAKmH,SAAS,CAAC,iCAAiC,CAAAD,QAAA,eAC9CpH,IAAA,QAAKqH,SAAS,CAAC,mBAAmB,CAAAD,QAAA,cAChCpH,IAAA,WACEgM,OAAO,CAAEA,CAAA,GAAM,CACbxK,YAAY,CAAC,KAAK,CAAC,CACnBF,oBAAoB,CAAC,IAAI,CAAC,CAC5B,CAAE,CACF+F,SAAS,CAAC,yEAAyE,CAAAD,QAAA,cAEnFpH,IAAA,QACEuH,KAAK,CAAC,4BAA4B,CAClCC,IAAI,CAAC,MAAM,CACXC,OAAO,CAAC,WAAW,CACnB,eAAa,KAAK,CAClBC,MAAM,CAAC,cAAc,CACrBI,KAAK,CAAC,QAAQ,CAAAV,QAAA,cAEdpH,IAAA,SACE,iBAAe,OAAO,CACtB,kBAAgB,OAAO,CACvB6H,CAAC,CAAC,sBAAsB,CACzB,CAAC,CACC,CAAC,CACA,CAAC,CACN,CAAC,cACN7H,IAAA,QAAKqH,SAAS,CAAC,iBAAiB,CAAAD,QAAA,CAC7B/F,iBAAiB,eAChBnB,KAAA,QAAAkH,QAAA,eACElH,KAAA,QAAKmH,SAAS,CAAC,yCAAyC,CAAAD,QAAA,eACtDpH,IAAA,QAAAoH,QAAA,cACEpH,IAAA,QACEuH,KAAK,CAAC,4BAA4B,CAClCC,IAAI,CAAC,MAAM,CACXC,OAAO,CAAC,WAAW,CACnB,eAAa,KAAK,CAClBC,MAAM,CAAC,cAAc,CACrBL,SAAS,CAAC,QAAQ,CAAAD,QAAA,cAElBpH,IAAA,SACE,iBAAe,OAAO,CACtB,kBAAgB,OAAO,CACvB6H,CAAC,CAAC,2gBAA2gB,CAC9gB,CAAC,CACC,CAAC,CACH,CAAC,cAEN7H,IAAA,QAAKqH,SAAS,CAAC,aAAa,CAAAD,QAAA,CACzB/F,iBAAiB,CAACyK,QAAQ,CAACmC,QAAQ,EAAI5M,iBAAiB,CAACyK,QAAQ,CAACmC,QAAQ,CAACnE,MAAM,CAAG,CAAC,CACpFzI,iBAAiB,CAACyK,QAAQ,CAACmC,QAAQ,CAAClE,GAAG,CACrC,CAACmE,OAAO,CAAE/B,KAAK,gBACbjM,KAAA,QAAiBmH,SAAS,CAAC,MAAM,CAAAD,QAAA,EAAC,GAC/B,CAAC,GAAG,CACJ8G,OAAO,CAACC,YAAY,EAClBD,OAAO,CAACE,kBAAkB,GAAK,EAAE,EAClCF,OAAO,CAACE,kBAAkB,GAAK,IAAI,CAC/B,IAAI,CAAGF,OAAO,CAACE,kBAAkB,CACjC,EAAE,CAAC,GANDjC,KAOL,CAET,CAAC,cAEDnM,IAAA,QAAKqH,SAAS,CAAC,MAAM,CAAAD,QAAA,CAAC,uBAAqB,CAAK,CACjD,CAEE,CAAC,EACH,CAAC,cAENlH,KAAA,QAAKmH,SAAS,CAAC,yCAAyC,CAAAD,QAAA,eACtDpH,IAAA,QAAAoH,QAAA,cACEpH,IAAA,QACEuH,KAAK,CAAC,4BAA4B,CAClCC,IAAI,CAAC,MAAM,CACXC,OAAO,CAAC,WAAW,CACnB,eAAa,KAAK,CAClBC,MAAM,CAAC,cAAc,CACrBI,KAAK,CAAC,QAAQ,CAAAV,QAAA,cAEdpH,IAAA,SACE,iBAAe,OAAO,CACtB,kBAAgB,OAAO,CACvB6H,CAAC,CAAC,yJAAyJ,CAC5J,CAAC,CACC,CAAC,CACH,CAAC,cACN7H,IAAA,QAAKqH,SAAS,CAAC,aAAa,CAAAD,QAAA,EAAAxG,qBAAA,CACzBS,iBAAiB,CAACyK,QAAQ,CAACC,SAAS,UAAAnL,qBAAA,UAAAA,qBAAA,CAAI,KAAK,CAC3C,CAAC,EACH,CAAC,CAELS,iBAAiB,CAACyK,QAAQ,CAACuC,cAAc,EAAIhN,iBAAiB,CAACyK,QAAQ,CAACuC,cAAc,CAACvE,MAAM,CAAG,CAAC,CAChGzI,iBAAiB,CAACyK,QAAQ,CAACuC,cAAc,CAACtE,GAAG,CAC3C,CAACmC,IAAI,CAAEC,KAAK,gBACVnM,IAAA,QAAAoH,QAAA,cACElH,KAAA,QAAKmH,SAAS,CAAC,yCAAyC,CAAAD,QAAA,eACtDpH,IAAA,QAAAoH,QAAA,CACG,CACC,YAAY,CACZ,UAAU,CACV,eAAe,CAChB,CAACmD,QAAQ,CAAC2B,IAAI,CAACoC,SAAS,CAAC,cACxBtO,IAAA,QACEuH,KAAK,CAAC,4BAA4B,CAClCC,IAAI,CAAC,MAAM,CACXC,OAAO,CAAC,WAAW,CACnB,eAAa,KAAK,CAClBC,MAAM,CAAC,cAAc,CACrBL,SAAS,CAAC,QAAQ,CAAAD,QAAA,cAElBpH,IAAA,SACE,iBAAe,OAAO,CACtB,kBAAgB,OAAO,CACvB6H,CAAC,CAAC,mWAAmW,CACtW,CAAC,CACC,CAAC,cAEN7H,IAAA,QACEuH,KAAK,CAAC,4BAA4B,CAClCC,IAAI,CAAC,MAAM,CACXC,OAAO,CAAC,WAAW,CACnB,eAAa,KAAK,CAClBC,MAAM,CAAC,cAAc,CACrBL,SAAS,CAAC,QAAQ,CAAAD,QAAA,cAElBpH,IAAA,SACE,iBAAe,OAAO,CACtB,kBAAgB,OAAO,CACvB6H,CAAC,CAAC,gQAAgQ,CACnQ,CAAC,CACC,CACN,CACE,CAAC,cACN3H,KAAA,QAAKmH,SAAS,CAAC,aAAa,CAAAD,QAAA,EACzB8E,IAAI,CAACoC,SAAS,CAAC,KAAG,CAACpC,IAAI,CAACqC,UAAU,EAChC,CAAC,EACH,CAAC,EA1CEpC,KA2CL,CAET,CAAC,CACC,IAAI,cAGRjM,KAAA,QAAKmH,SAAS,CAAC,yCAAyC,CAAAD,QAAA,eACtDpH,IAAA,QAAAoH,QAAA,cACElH,KAAA,QACEqH,KAAK,CAAC,4BAA4B,CAClCC,IAAI,CAAC,MAAM,CACXC,OAAO,CAAC,WAAW,CACnB,eAAa,KAAK,CAClBC,MAAM,CAAC,cAAc,CACrBI,KAAK,CAAC,QAAQ,CAAAV,QAAA,eAEdpH,IAAA,SACE,iBAAe,OAAO,CACtB,kBAAgB,OAAO,CACvB6H,CAAC,CAAC,uCAAuC,CAC1C,CAAC,cACF7H,IAAA,SACE,iBAAe,OAAO,CACtB,kBAAgB,OAAO,CACvB6H,CAAC,CAAC,gFAAgF,CACnF,CAAC,EACC,CAAC,CACH,CAAC,cACN7H,IAAA,QAAKqH,SAAS,CAAC,aAAa,CAAAD,QAAA,EAAAvG,sBAAA,CACzBQ,iBAAiB,CAACyK,QAAQ,CAAC0C,OAAO,UAAA3N,sBAAA,UAAAA,sBAAA,CAAI,KAAK,CACzC,CAAC,EACH,CAAC,cACNX,KAAA,QAAKmH,SAAS,CAAC,yCAAyC,CAAAD,QAAA,eACtDpH,IAAA,QAAAoH,QAAA,cACEpH,IAAA,QACEuH,KAAK,CAAC,4BAA4B,CAClCC,IAAI,CAAC,MAAM,CACXC,OAAO,CAAC,WAAW,CACnB,eAAa,KAAK,CAClBC,MAAM,CAAC,cAAc,CACrBI,KAAK,CAAC,QAAQ,CAAAV,QAAA,cAEdpH,IAAA,SACE,iBAAe,OAAO,CACtB,kBAAgB,OAAO,CACvB6H,CAAC,CAAC,oLAAoL,CACvL,CAAC,CACC,CAAC,CACH,CAAC,cACN7H,IAAA,QAAKqH,SAAS,CAAC,aAAa,CAAAD,QAAA,EAAAtG,sBAAA,CACzBO,iBAAiB,CAACyK,QAAQ,CAAC2C,cAAc,UAAA3N,sBAAA,UAAAA,sBAAA,CACxC,KAAK,CACJ,CAAC,EACH,CAAC,cACNd,IAAA,MAAGqH,SAAS,CAAC,+CAA+C,CAAAD,QAAA,cAC1DpH,IAAA,CAACvB,IAAI,EACH4I,SAAS,CAAC,oBAAoB,CAC9B2F,EAAE,CACA,uBAAuB,CACvB3L,iBAAiB,CAACyK,QAAQ,CAAC7B,EAC5B,CAAA7C,QAAA,cAEDpH,IAAA,QACEuH,KAAK,CAAC,4BAA4B,CAClCC,IAAI,CAAC,MAAM,CACXC,OAAO,CAAC,WAAW,CACnBM,WAAW,CAAC,KAAK,CACjBL,MAAM,CAAC,cAAc,CACrBL,SAAS,CAAC,+DAA+D,CAAAD,QAAA,cAEzEpH,IAAA,SACE2H,aAAa,CAAC,OAAO,CACrBC,cAAc,CAAC,OAAO,CACtBC,CAAC,CAAC,kQAAkQ,CACrQ,CAAC,CACC,CAAC,CACF,CAAC,CACN,CAAC,EACD,CACN,CACE,CAAC,EACH,CAAC,CACH,CAAC,CACJ,IAAI,EACL,CAAC,CACH,CAAC,CACH,CAAC,EACH,CAAC,cAEN7H,IAAA,CAAChB,iBAAiB,EAChB0P,MAAM,CAAE3L,QAAS,CACjBkJ,OAAO,CACL9I,SAAS,GAAK,QAAQ,CAClB,4CAA4C,CAC5C,gBACL,CACDwL,SAAS,CAAE,KAAAA,CAAA,GAAY,CACrB,GAAIxL,SAAS,GAAK,QAAQ,CAAE,CAC1BH,WAAW,CAAC,KAAK,CAAC,CAClBI,YAAY,CAAC,EAAE,CAAC,CAChBF,YAAY,CAAC,KAAK,CAAC,CACrB,CAAC,IAAM,IAAIC,SAAS,GAAK,QAAQ,EAAIE,MAAM,GAAK,EAAE,CAAE,CAClDH,YAAY,CAAC,IAAI,CAAC,CAClB9B,QAAQ,CAACrC,UAAU,CAACsE,MAAM,CAAC,CAAC,CAC5BL,WAAW,CAAC,KAAK,CAAC,CAClBI,YAAY,CAAC,EAAE,CAAC,CAChBF,YAAY,CAAC,KAAK,CAAC,CACrB,CAAC,IAAM,CACLF,WAAW,CAAC,KAAK,CAAC,CAClBI,YAAY,CAAC,EAAE,CAAC,CAChBF,YAAY,CAAC,KAAK,CAAC,CACrB,CACF,CAAE,CACF0L,QAAQ,CAAEA,CAAA,GAAM,CACd5L,WAAW,CAAC,KAAK,CAAC,CAClBI,YAAY,CAAC,EAAE,CAAC,CAChBF,YAAY,CAAC,KAAK,CAAC,CACrB,CAAE,CACFD,SAAS,CAAEA,SAAU,CACtB,CAAC,cAEFjD,IAAA,QAAKqH,SAAS,CAAC,2CAA2C,CAAM,CAAC,EAC9D,CAAC,CACO,CAAC,CAEpB,CAEA,cAAe,CAAA1G,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}