{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Desktop/Freelances/Mohssin <PERSON>/UNIMEDCARE/web-front/src/screens/coordinator-space/CoordinatorSpaceScreen.js\",\n  _s = $RefreshSig$();\nimport React, { useEffect, useState } from \"react\";\nimport { useDispatch, useSelector } from \"react-redux\";\nimport { Link, useLocation, useNavigate, useSearchParams } from \"react-router-dom\";\nimport DefaultLayout from \"../../layouts/DefaultLayout\";\nimport { deleteUser, getListCoordinators } from \"../../redux/actions/userActions\";\nimport Loader from \"../../components/Loader\";\nimport Alert from \"../../components/Alert\";\nimport { baseURLFile } from \"../../constants\";\nimport ConfirmationModal from \"../../components/ConfirmationModal\";\nimport Paginate from \"../../components/Paginate\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction CoordinatorSpaceScreen() {\n  _s();\n  const navigate = useNavigate();\n  const location = useLocation();\n  const [searchParams] = useSearchParams();\n  const dispatch = useDispatch();\n  const page = searchParams.get(\"page\") || \"1\";\n  const [eventType, setEventType] = useState(\"\");\n  const [coordinatorId, setCoordinatorId] = useState(\"\");\n  const [isDelete, setIsDelete] = useState(false);\n  const [loadEvent, setLoadEvent] = useState(false);\n  const userLogin = useSelector(state => state.userLogin);\n  const {\n    userInfo\n  } = userLogin;\n  const listCoordinators = useSelector(state => state.coordinatorsList);\n  const {\n    coordinators,\n    loadingCoordinators,\n    errorCoordinators,\n    pages\n  } = listCoordinators;\n  const userDelete = useSelector(state => state.deleteUser);\n  const {\n    loadingUserDelete,\n    successUserDelete,\n    errorUsersDelete\n  } = userDelete;\n  const redirect = \"/\";\n  useEffect(() => {\n    if (!userInfo) {\n      navigate(redirect);\n    } else if (userInfo.role !== \"1\" && userInfo.role !== 1 && userInfo.role !== \"2\" && userInfo.role !== 2) {\n      navigate(redirect);\n    } else {\n      dispatch(getListCoordinators(page));\n    }\n  }, [navigate, userInfo, dispatch, page]);\n  useEffect(() => {\n    if (successUserDelete) {\n      dispatch(getListCoordinators(\"1\"));\n    }\n  }, [successUserDelete]);\n  return /*#__PURE__*/_jsxDEV(DefaultLayout, {\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex flex-row text-sm items-center my-1\",\n        children: [/*#__PURE__*/_jsxDEV(\"a\", {\n          href: \"/dashboard\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex flex-row  items-center hover:text-black \",\n            children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n              xmlns: \"http://www.w3.org/2000/svg\",\n              fill: \"none\",\n              viewBox: \"0 0 24 24\",\n              \"stroke-width\": \"1.5\",\n              stroke: \"currentColor\",\n              className: \"w-4 h-4\",\n              children: /*#__PURE__*/_jsxDEV(\"path\", {\n                strokeLinecap: \"round\",\n                strokeLinejoin: \"round\",\n                d: \"m2.25 12 8.954-8.955c.44-.439 1.152-.439 1.591 0L21.75 12M4.5 9.75v10.125c0 .621.504 1.125 1.125 1.125H9.75v-4.875c0-.621.504-1.125 1.125-1.125h2.25c.621 0 1.125.504 1.125 1.125V21h4.125c.621 0 1.125-.504 1.125-1.125V9.75M8.25 21h8.25\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 80,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 72,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"mx-1\",\n              children: \"Dashboard\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 86,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 71,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 70,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          children: /*#__PURE__*/_jsxDEV(\"svg\", {\n            xmlns: \"http://www.w3.org/2000/svg\",\n            fill: \"none\",\n            viewBox: \"0 0 24 24\",\n            \"stroke-width\": \"1.5\",\n            stroke: \"currentColor\",\n            className: \"w-4 h-4\",\n            children: /*#__PURE__*/_jsxDEV(\"path\", {\n              strokeLinecap: \"round\",\n              strokeLinejoin: \"round\",\n              d: \"m8.25 4.5 7.5 7.5-7.5 7.5\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 98,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 90,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 89,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"\",\n          children: \"Coordinator Space\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 105,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 68,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex flex-row justify-between  items-center my-3\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"mx-1 font-bold text-black \",\n          children: \"Coordinator Space\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 109,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex flex-row items-center justify-end\",\n          children: /*#__PURE__*/_jsxDEV(\"a\", {\n            href: \"/coordinator-space/new-coordinator\",\n            className: \"mx-2 flex flex-row bg-[#0388A6] text-white text-xs rounded-full px-5 py-3 items-center\",\n            children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n              xmlns: \"http://www.w3.org/2000/svg\",\n              fill: \"none\",\n              viewBox: \"0 0 24 24\",\n              \"stroke-width\": \"1.5\",\n              stroke: \"currentColor\",\n              className: \"size-4 mx-1\",\n              children: /*#__PURE__*/_jsxDEV(\"path\", {\n                \"stroke-linecap\": \"round\",\n                \"stroke-linejoin\": \"round\",\n                d: \"M12 4.5v15m7.5-7.5h-15\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 124,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 116,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: \"New Coordinator\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 131,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 112,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 111,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 108,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"rounded-sm border border-stroke bg-white px-5 pt-6 pb-2.5 shadow-default   dark:bg-boxdark sm:px-7.5 xl:pb-1\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"container mx-auto flex flex-col\",\n          children: [loadingCoordinators ? /*#__PURE__*/_jsxDEV(Loader, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 139,\n            columnNumber: 15\n          }, this) : errorCoordinators ? /*#__PURE__*/_jsxDEV(Alert, {\n            type: \"error\",\n            message: errorCoordinators\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 141,\n            columnNumber: 15\n          }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"max-w-full overflow-x-auto \",\n            children: /*#__PURE__*/_jsxDEV(\"table\", {\n              className: \"w-full table-auto\",\n              children: [/*#__PURE__*/_jsxDEV(\"thead\", {\n                children: /*#__PURE__*/_jsxDEV(\"tr\", {\n                  className: \" bg-[#F3F5FB] text-left \",\n                  children: [/*#__PURE__*/_jsxDEV(\"th\", {\n                    className: \"min-w-[30px] py-4 px-4 font-bold text-[#8E8E8E] text-xs w-max\",\n                    children: \"#\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 147,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                    className: \"min-w-[60px] py-4 px-4 font-bold text-[#8E8E8E] text-xs w-max \",\n                    children: \"Photo\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 150,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                    className: \"min-w-[60px] py-4 px-4 font-bold text-[#8E8E8E] text-xs w-max \",\n                    children: \"Coordinator Name\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 153,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                    className: \"min-w-[120px] py-4 px-4 font-bold text-[#8E8E8E] text-xs w-max\",\n                    children: \"Email\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 156,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                    className: \"min-w-[120px] py-4 px-4 font-bold text-[#8E8E8E] text-xs w-max\",\n                    children: \"Phone\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 159,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                    className: \"py-4 px-4 font-bold text-[#8E8E8E] text-xs w-max\",\n                    children: \"Operation\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 162,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 146,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 145,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"tbody\", {\n                children: coordinators === null || coordinators === void 0 ? void 0 : coordinators.map((item, index) => {\n                  var _item$first_name, _item$email, _item$phone;\n                  return /*#__PURE__*/_jsxDEV(\"tr\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"td\", {\n                      className: \" py-3 px-4 min-w-[120px]  \",\n                      children: /*#__PURE__*/_jsxDEV(\"p\", {\n                        className: \"text-black  text-xs w-max  \",\n                        children: [\"#\", item.id]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 172,\n                        columnNumber: 27\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 171,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                      className: \" py-3 px-4 min-w-[120px]  \",\n                      children: /*#__PURE__*/_jsxDEV(Link, {\n                        to: \"/coordinator-space/profile/\" + item.id,\n                        children: item.photo ? /*#__PURE__*/_jsxDEV(\"img\", {\n                          className: \"size-11 rounded\",\n                          src: baseURLFile + item.photo,\n                          alt: item.first_name + \" \" + item.last_name,\n                          onError: e => {\n                            e.target.onerror = null;\n                            e.target.src = \"/assets/placeholder.png\";\n                          }\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 179,\n                          columnNumber: 31\n                        }, this) : /*#__PURE__*/_jsxDEV(\"img\", {\n                          className: \"size-11 rounded\",\n                          src: \"/assets/placeholder.png\",\n                          alt: item.first_name + \" \" + item.last_name,\n                          onError: e => {\n                            e.target.onerror = null;\n                            e.target.src = \"/assets/placeholder.png\";\n                          }\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 189,\n                          columnNumber: 31\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 177,\n                        columnNumber: 27\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 176,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                      className: \" py-3 px-4 min-w-[120px]  \",\n                      children: /*#__PURE__*/_jsxDEV(\"p\", {\n                        className: \"text-black  text-xs w-max  \",\n                        children: [(_item$first_name = item.first_name) !== null && _item$first_name !== void 0 ? _item$first_name : \"---\", \" \", item.last_name]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 202,\n                        columnNumber: 27\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 201,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                      className: \" py-3 px-4 min-w-[120px]  \",\n                      children: /*#__PURE__*/_jsxDEV(\"p\", {\n                        className: \"text-black  text-xs w-max  \",\n                        children: (_item$email = item.email) !== null && _item$email !== void 0 ? _item$email : \"---\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 208,\n                        columnNumber: 27\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 207,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                      className: \" py-3 px-4 min-w-[120px]  \",\n                      children: /*#__PURE__*/_jsxDEV(\"p\", {\n                        className: \"text-black  text-xs w-max  \",\n                        children: (_item$phone = item.phone) !== null && _item$phone !== void 0 ? _item$phone : \"---\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 213,\n                        columnNumber: 27\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 212,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                      className: \" py-3 px-4 min-w-[120px]  \",\n                      children: /*#__PURE__*/_jsxDEV(\"p\", {\n                        className: \"text-black  text-xs w-max flex flex-row  \",\n                        children: [/*#__PURE__*/_jsxDEV(Link, {\n                          className: \"mx-1 update-class\",\n                          to: \"/coordinator-space/edit/\" + item.id,\n                          children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                            xmlns: \"http://www.w3.org/2000/svg\",\n                            fill: \"none\",\n                            viewBox: \"0 0 24 24\",\n                            strokeWidth: \"1.5\",\n                            stroke: \"currentColor\",\n                            className: \"w-5 h-5 bg-primary rounded p-1 text-white text-center text-xs\",\n                            children: /*#__PURE__*/_jsxDEV(\"path\", {\n                              strokeLinecap: \"round\",\n                              strokeLinejoin: \"round\",\n                              d: \"m16.862 4.487 1.687-1.688a1.875 1.875 0 1 1 2.652 2.652L10.582 16.07a4.5 4.5 0 0 1-1.897 1.13L6 18l.8-2.685a4.5 4.5 0 0 1 1.13-1.897l8.932-8.931Zm0 0L19.5 7.125M18 14v4.75A2.25 2.25 0 0 1 15.75 21H5.25A2.25 2.25 0 0 1 3 18.75V8.25A2.25 2.25 0 0 1 5.25 6H10\"\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 231,\n                              columnNumber: 33\n                            }, this)\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 223,\n                            columnNumber: 31\n                          }, this)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 219,\n                          columnNumber: 29\n                        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                          onClick: () => {\n                            setEventType(\"delete\");\n                            setCoordinatorId(item.id);\n                            setIsDelete(true);\n                          },\n                          className: \"mx-1 delete-class cursor-pointer\",\n                          children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                            xmlns: \"http://www.w3.org/2000/svg\",\n                            fill: \"none\",\n                            viewBox: \"0 0 24 24\",\n                            \"stroke-width\": \"1.5\",\n                            stroke: \"currentColor\",\n                            className: \"w-5 h-5 bg-danger rounded p-1 text-white text-center text-xs\",\n                            children: /*#__PURE__*/_jsxDEV(\"path\", {\n                              \"stroke-linecap\": \"round\",\n                              \"stroke-linejoin\": \"round\",\n                              d: \"m20.25 7.5-.625 10.632a2.25 2.25 0 0 1-2.247 2.118H6.622a2.25 2.25 0 0 1-2.247-2.118L3.75 7.5m6 4.125 2.25 2.25m0 0 2.25 2.25M12 13.875l2.25-2.25M12 13.875l-2.25 2.25M3.375 7.5h17.25c.621 0 1.125-.504 1.125-1.125v-1.5c0-.621-.504-1.125-1.125-1.125H3.375c-.621 0-1.125.504-1.125 1.125v1.5c0 .621.504 1.125 1.125 1.125Z\"\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 255,\n                              columnNumber: 33\n                            }, this)\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 247,\n                            columnNumber: 31\n                          }, this)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 239,\n                          columnNumber: 29\n                        }, this), /*#__PURE__*/_jsxDEV(Link, {\n                          className: \"mx-1 profile-class\",\n                          to: \"/coordinator-space/profile/\" + item.id,\n                          children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                            xmlns: \"http://www.w3.org/2000/svg\",\n                            fill: \"none\",\n                            viewBox: \"0 0 24 24\",\n                            \"stroke-width\": \"1.5\",\n                            stroke: \"currentColor\",\n                            className: \"w-5 h-5 bg-success rounded p-1 text-white text-center text-xs\",\n                            children: /*#__PURE__*/_jsxDEV(\"path\", {\n                              \"stroke-linecap\": \"round\",\n                              \"stroke-linejoin\": \"round\",\n                              d: \"M8.25 6.75h12M8.25 12h12m-12 5.25h12M3.75 6.75h.007v.008H3.75V6.75Zm.375 0a.375.375 0 1 1-.75 0 .375.375 0 0 1 .75 0ZM3.75 12h.007v.008H3.75V12Zm.375 0a.375.375 0 1 1-.75 0 .375.375 0 0 1 .75 0Zm-.375 5.25h.007v.008H3.75v-.008Zm.375 0a.375.375 0 1 1-.75 0 .375.375 0 0 1 .75 0Z\"\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 274,\n                              columnNumber: 33\n                            }, this)\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 266,\n                            columnNumber: 31\n                          }, this)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 262,\n                          columnNumber: 29\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 218,\n                        columnNumber: 27\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 217,\n                      columnNumber: 25\n                    }, this)]\n                  }, index, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 170,\n                    columnNumber: 23\n                  }, this);\n                })\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 168,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 144,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 143,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"mb-3\",\n            children: /*#__PURE__*/_jsxDEV(Paginate, {\n              route: \"/coordinator-space?\",\n              search: \"\",\n              page: page,\n              pages: pages\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 290,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 289,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 137,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 136,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(ConfirmationModal, {\n        isOpen: isDelete,\n        message: eventType === \"delete\" ? \"Are you sure you want to delete this Coorinator?\" : \"Are you sure ?\",\n        onConfirm: async () => {\n          if (eventType === \"cancel\") {\n            setIsDelete(false);\n            setEventType(\"\");\n            setLoadEvent(false);\n          } else if (eventType === \"delete\" && coordinatorId !== \"\") {\n            setLoadEvent(true);\n            dispatch(deleteUser(coordinatorId));\n            setIsDelete(false);\n            setEventType(\"\");\n            setLoadEvent(false);\n          } else {\n            setIsDelete(false);\n            setEventType(\"\");\n            setLoadEvent(false);\n          }\n        },\n        onCancel: () => {\n          setIsDelete(false);\n          setEventType(\"\");\n          setLoadEvent(false);\n        },\n        loadEvent: loadEvent\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 300,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"grid md:grid-cols-2 w-full container mt-5\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 331,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 67,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 66,\n    columnNumber: 5\n  }, this);\n}\n_s(CoordinatorSpaceScreen, \"NgtGsPgeQ3B1x0iMpFmNeBqOF3k=\", false, function () {\n  return [useNavigate, useLocation, useSearchParams, useDispatch, useSelector, useSelector, useSelector];\n});\n_c = CoordinatorSpaceScreen;\nexport default CoordinatorSpaceScreen;\nvar _c;\n$RefreshReg$(_c, \"CoordinatorSpaceScreen\");", "map": {"version": 3, "names": ["React", "useEffect", "useState", "useDispatch", "useSelector", "Link", "useLocation", "useNavigate", "useSearchParams", "DefaultLayout", "deleteUser", "getListCoordinators", "Loader", "<PERSON><PERSON>", "baseURLFile", "ConfirmationModal", "Paginate", "jsxDEV", "_jsxDEV", "CoordinatorSpaceScreen", "_s", "navigate", "location", "searchParams", "dispatch", "page", "get", "eventType", "setEventType", "coordinatorId", "setCoordinatorId", "isDelete", "setIsDelete", "loadEvent", "setLoadEvent", "userLogin", "state", "userInfo", "listCoordinators", "coordinatorsList", "coordinators", "loadingCoordinators", "errorCoordinators", "pages", "userDelete", "loadingUserDelete", "successUserDelete", "errorUsersDelete", "redirect", "role", "children", "className", "href", "xmlns", "fill", "viewBox", "stroke", "strokeLinecap", "strokeLinejoin", "d", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "type", "message", "map", "item", "index", "_item$first_name", "_item$email", "_item$phone", "id", "to", "photo", "src", "alt", "first_name", "last_name", "onError", "e", "target", "onerror", "email", "phone", "strokeWidth", "onClick", "route", "search", "isOpen", "onConfirm", "onCancel", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Desktop/Freelances/<PERSON><PERSON><PERSON>/UNIMEDCARE/web-front/src/screens/coordinator-space/CoordinatorSpaceScreen.js"], "sourcesContent": ["import React, { useEffect, useState } from \"react\";\nimport { useDispatch, useSelector } from \"react-redux\";\nimport {\n  Link,\n  useLocation,\n  useNavigate,\n  useSearchParams,\n} from \"react-router-dom\";\nimport DefaultLayout from \"../../layouts/DefaultLayout\";\nimport {\n  deleteUser,\n  getListCoordinators,\n} from \"../../redux/actions/userActions\";\nimport Loader from \"../../components/Loader\";\nimport Alert from \"../../components/Alert\";\nimport { baseURLFile } from \"../../constants\";\nimport ConfirmationModal from \"../../components/ConfirmationModal\";\nimport Paginate from \"../../components/Paginate\";\n\nfunction CoordinatorSpaceScreen() {\n  const navigate = useNavigate();\n  const location = useLocation();\n  const [searchParams] = useSearchParams();\n  const dispatch = useDispatch();\n  const page = searchParams.get(\"page\") || \"1\";\n\n  const [eventType, setEventType] = useState(\"\");\n  const [coordinatorId, setCoordinatorId] = useState(\"\");\n  const [isDelete, setIsDelete] = useState(false);\n  const [loadEvent, setLoadEvent] = useState(false);\n\n  const userLogin = useSelector((state) => state.userLogin);\n  const { userInfo } = userLogin;\n\n  const listCoordinators = useSelector((state) => state.coordinatorsList);\n  const { coordinators, loadingCoordinators, errorCoordinators, pages } =\n    listCoordinators;\n\n  const userDelete = useSelector((state) => state.deleteUser);\n  const { loadingUserDelete, successUserDelete, errorUsersDelete } = userDelete;\n\n  const redirect = \"/\";\n\n  useEffect(() => {\n    if (!userInfo) {\n      navigate(redirect);\n    } else if (\n      userInfo.role !== \"1\" &&\n      userInfo.role !== 1 &&\n      userInfo.role !== \"2\" &&\n      userInfo.role !== 2\n    ) {\n      navigate(redirect);\n    } else {\n      dispatch(getListCoordinators(page));\n    }\n  }, [navigate, userInfo, dispatch, page]);\n\n  useEffect(() => {\n    if (successUserDelete) {\n      dispatch(getListCoordinators(\"1\"));\n    }\n  }, [successUserDelete]);\n\n  return (\n    <DefaultLayout>\n      <div>\n        <div className=\"flex flex-row text-sm items-center my-1\">\n          {/* home */}\n          <a href=\"/dashboard\">\n            <div className=\"flex flex-row  items-center hover:text-black \">\n              <svg\n                xmlns=\"http://www.w3.org/2000/svg\"\n                fill=\"none\"\n                viewBox=\"0 0 24 24\"\n                stroke-width=\"1.5\"\n                stroke=\"currentColor\"\n                className=\"w-4 h-4\"\n              >\n                <path\n                  strokeLinecap=\"round\"\n                  strokeLinejoin=\"round\"\n                  d=\"m2.25 12 8.954-8.955c.44-.439 1.152-.439 1.591 0L21.75 12M4.5 9.75v10.125c0 .621.504 1.125 1.125 1.125H9.75v-4.875c0-.621.504-1.125 1.125-1.125h2.25c.621 0 1.125.504 1.125 1.125V21h4.125c.621 0 1.125-.504 1.125-1.125V9.75M8.25 21h8.25\"\n                />\n              </svg>\n              <span className=\"mx-1\">Dashboard</span>\n            </div>\n          </a>\n          <span>\n            <svg\n              xmlns=\"http://www.w3.org/2000/svg\"\n              fill=\"none\"\n              viewBox=\"0 0 24 24\"\n              stroke-width=\"1.5\"\n              stroke=\"currentColor\"\n              className=\"w-4 h-4\"\n            >\n              <path\n                strokeLinecap=\"round\"\n                strokeLinejoin=\"round\"\n                d=\"m8.25 4.5 7.5 7.5-7.5 7.5\"\n              />\n            </svg>\n          </span>\n          <div className=\"\">Coordinator Space</div>\n        </div>\n        {/*  */}\n        <div className=\"flex flex-row justify-between  items-center my-3\">\n          <div className=\"mx-1 font-bold text-black \">Coordinator Space</div>\n\n          <div className=\"flex flex-row items-center justify-end\">\n            <a\n              href=\"/coordinator-space/new-coordinator\"\n              className=\"mx-2 flex flex-row bg-[#0388A6] text-white text-xs rounded-full px-5 py-3 items-center\"\n            >\n              <svg\n                xmlns=\"http://www.w3.org/2000/svg\"\n                fill=\"none\"\n                viewBox=\"0 0 24 24\"\n                stroke-width=\"1.5\"\n                stroke=\"currentColor\"\n                className=\"size-4 mx-1\"\n              >\n                <path\n                  stroke-linecap=\"round\"\n                  stroke-linejoin=\"round\"\n                  d=\"M12 4.5v15m7.5-7.5h-15\"\n                />\n              </svg>\n\n              <div>New Coordinator</div>\n            </a>\n          </div>\n        </div>\n        {/*  */}\n        <div className=\"rounded-sm border border-stroke bg-white px-5 pt-6 pb-2.5 shadow-default   dark:bg-boxdark sm:px-7.5 xl:pb-1\">\n          <div className=\"container mx-auto flex flex-col\">\n            {loadingCoordinators ? (\n              <Loader />\n            ) : errorCoordinators ? (\n              <Alert type={\"error\"} message={errorCoordinators} />\n            ) : (\n              <div className=\"max-w-full overflow-x-auto \">\n                <table className=\"w-full table-auto\">\n                  <thead>\n                    <tr className=\" bg-[#F3F5FB] text-left \">\n                      <th className=\"min-w-[30px] py-4 px-4 font-bold text-[#8E8E8E] text-xs w-max\">\n                        #\n                      </th>\n                      <th className=\"min-w-[60px] py-4 px-4 font-bold text-[#8E8E8E] text-xs w-max \">\n                        Photo\n                      </th>\n                      <th className=\"min-w-[60px] py-4 px-4 font-bold text-[#8E8E8E] text-xs w-max \">\n                        Coordinator Name\n                      </th>\n                      <th className=\"min-w-[120px] py-4 px-4 font-bold text-[#8E8E8E] text-xs w-max\">\n                        Email\n                      </th>\n                      <th className=\"min-w-[120px] py-4 px-4 font-bold text-[#8E8E8E] text-xs w-max\">\n                        Phone\n                      </th>\n                      <th className=\"py-4 px-4 font-bold text-[#8E8E8E] text-xs w-max\">\n                        Operation\n                      </th>\n                    </tr>\n                  </thead>\n                  {/*  */}\n                  <tbody>\n                    {coordinators?.map((item, index) => (\n                      <tr key={index}>\n                        <td className=\" py-3 px-4 min-w-[120px]  \">\n                          <p className=\"text-black  text-xs w-max  \">\n                            #{item.id}\n                          </p>\n                        </td>\n                        <td className=\" py-3 px-4 min-w-[120px]  \">\n                          <Link to={\"/coordinator-space/profile/\" + item.id}>\n                            {item.photo ? (\n                              <img\n                                className=\"size-11 rounded\"\n                                src={baseURLFile + item.photo}\n                                alt={item.first_name + \" \" + item.last_name}\n                                onError={(e) => {\n                                  e.target.onerror = null;\n                                  e.target.src = \"/assets/placeholder.png\";\n                                }}\n                              />\n                            ) : (\n                              <img\n                                className=\"size-11 rounded\"\n                                src={\"/assets/placeholder.png\"}\n                                alt={item.first_name + \" \" + item.last_name}\n                                onError={(e) => {\n                                  e.target.onerror = null;\n                                  e.target.src = \"/assets/placeholder.png\";\n                                }}\n                              />\n                            )}\n                          </Link>\n                        </td>\n                        <td className=\" py-3 px-4 min-w-[120px]  \">\n                          <p className=\"text-black  text-xs w-max  \">\n                            {item.first_name ?? \"---\"} {item.last_name}\n                          </p>\n                        </td>\n\n                        <td className=\" py-3 px-4 min-w-[120px]  \">\n                          <p className=\"text-black  text-xs w-max  \">\n                            {item.email ?? \"---\"}\n                          </p>\n                        </td>\n                        <td className=\" py-3 px-4 min-w-[120px]  \">\n                          <p className=\"text-black  text-xs w-max  \">\n                            {item.phone ?? \"---\"}\n                          </p>\n                        </td>\n                        <td className=\" py-3 px-4 min-w-[120px]  \">\n                          <p className=\"text-black  text-xs w-max flex flex-row  \">\n                            <Link\n                              className=\"mx-1 update-class\"\n                              to={\"/coordinator-space/edit/\" + item.id}\n                            >\n                              <svg\n                                xmlns=\"http://www.w3.org/2000/svg\"\n                                fill=\"none\"\n                                viewBox=\"0 0 24 24\"\n                                strokeWidth=\"1.5\"\n                                stroke=\"currentColor\"\n                                className=\"w-5 h-5 bg-primary rounded p-1 text-white text-center text-xs\"\n                              >\n                                <path\n                                  strokeLinecap=\"round\"\n                                  strokeLinejoin=\"round\"\n                                  d=\"m16.862 4.487 1.687-1.688a1.875 1.875 0 1 1 2.652 2.652L10.582 16.07a4.5 4.5 0 0 1-1.897 1.13L6 18l.8-2.685a4.5 4.5 0 0 1 1.13-1.897l8.932-8.931Zm0 0L19.5 7.125M18 14v4.75A2.25 2.25 0 0 1 15.75 21H5.25A2.25 2.25 0 0 1 3 18.75V8.25A2.25 2.25 0 0 1 5.25 6H10\"\n                                />\n                              </svg>\n                            </Link>\n\n                            <div\n                              onClick={() => {\n                                setEventType(\"delete\");\n                                setCoordinatorId(item.id);\n                                setIsDelete(true);\n                              }}\n                              className=\"mx-1 delete-class cursor-pointer\"\n                            >\n                              <svg\n                                xmlns=\"http://www.w3.org/2000/svg\"\n                                fill=\"none\"\n                                viewBox=\"0 0 24 24\"\n                                stroke-width=\"1.5\"\n                                stroke=\"currentColor\"\n                                className=\"w-5 h-5 bg-danger rounded p-1 text-white text-center text-xs\"\n                              >\n                                <path\n                                  stroke-linecap=\"round\"\n                                  stroke-linejoin=\"round\"\n                                  d=\"m20.25 7.5-.625 10.632a2.25 2.25 0 0 1-2.247 2.118H6.622a2.25 2.25 0 0 1-2.247-2.118L3.75 7.5m6 4.125 2.25 2.25m0 0 2.25 2.25M12 13.875l2.25-2.25M12 13.875l-2.25 2.25M3.375 7.5h17.25c.621 0 1.125-.504 1.125-1.125v-1.5c0-.621-.504-1.125-1.125-1.125H3.375c-.621 0-1.125.504-1.125 1.125v1.5c0 .621.504 1.125 1.125 1.125Z\"\n                                />\n                              </svg>\n                            </div>\n                            <Link\n                              className=\"mx-1 profile-class\"\n                              to={\"/coordinator-space/profile/\" + item.id}\n                            >\n                              <svg\n                                xmlns=\"http://www.w3.org/2000/svg\"\n                                fill=\"none\"\n                                viewBox=\"0 0 24 24\"\n                                stroke-width=\"1.5\"\n                                stroke=\"currentColor\"\n                                className=\"w-5 h-5 bg-success rounded p-1 text-white text-center text-xs\"\n                              >\n                                <path\n                                  stroke-linecap=\"round\"\n                                  stroke-linejoin=\"round\"\n                                  d=\"M8.25 6.75h12M8.25 12h12m-12 5.25h12M3.75 6.75h.007v.008H3.75V6.75Zm.375 0a.375.375 0 1 1-.75 0 .375.375 0 0 1 .75 0ZM3.75 12h.007v.008H3.75V12Zm.375 0a.375.375 0 1 1-.75 0 .375.375 0 0 1 .75 0Zm-.375 5.25h.007v.008H3.75v-.008Zm.375 0a.375.375 0 1 1-.75 0 .375.375 0 0 1 .75 0Z\"\n                                />\n                              </svg>\n                            </Link>\n                          </p>\n                        </td>\n                      </tr>\n                    ))}\n                  </tbody>\n                </table>\n              </div>\n            )}\n            <div className=\"mb-3\">\n              <Paginate\n                route={\"/coordinator-space?\"}\n                search={\"\"}\n                page={page}\n                pages={pages}\n              />\n            </div>\n          </div>\n        </div>\n        {/*  */}\n        <ConfirmationModal\n          isOpen={isDelete}\n          message={\n            eventType === \"delete\"\n              ? \"Are you sure you want to delete this Coorinator?\"\n              : \"Are you sure ?\"\n          }\n          onConfirm={async () => {\n            if (eventType === \"cancel\") {\n              setIsDelete(false);\n              setEventType(\"\");\n              setLoadEvent(false);\n            } else if (eventType === \"delete\" && coordinatorId !== \"\") {\n              setLoadEvent(true);\n              dispatch(deleteUser(coordinatorId));\n              setIsDelete(false);\n              setEventType(\"\");\n              setLoadEvent(false);\n            } else {\n              setIsDelete(false);\n              setEventType(\"\");\n              setLoadEvent(false);\n            }\n          }}\n          onCancel={() => {\n            setIsDelete(false);\n            setEventType(\"\");\n            setLoadEvent(false);\n          }}\n          loadEvent={loadEvent}\n        />\n        <div className=\"grid md:grid-cols-2 w-full container mt-5\"></div>\n      </div>\n    </DefaultLayout>\n  );\n}\n\nexport default CoordinatorSpaceScreen;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,SAAS,EAAEC,QAAQ,QAAQ,OAAO;AAClD,SAASC,WAAW,EAAEC,WAAW,QAAQ,aAAa;AACtD,SACEC,IAAI,EACJC,WAAW,EACXC,WAAW,EACXC,eAAe,QACV,kBAAkB;AACzB,OAAOC,aAAa,MAAM,6BAA6B;AACvD,SACEC,UAAU,EACVC,mBAAmB,QACd,iCAAiC;AACxC,OAAOC,MAAM,MAAM,yBAAyB;AAC5C,OAAOC,KAAK,MAAM,wBAAwB;AAC1C,SAASC,WAAW,QAAQ,iBAAiB;AAC7C,OAAOC,iBAAiB,MAAM,oCAAoC;AAClE,OAAOC,QAAQ,MAAM,2BAA2B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEjD,SAASC,sBAAsBA,CAAA,EAAG;EAAAC,EAAA;EAChC,MAAMC,QAAQ,GAAGd,WAAW,CAAC,CAAC;EAC9B,MAAMe,QAAQ,GAAGhB,WAAW,CAAC,CAAC;EAC9B,MAAM,CAACiB,YAAY,CAAC,GAAGf,eAAe,CAAC,CAAC;EACxC,MAAMgB,QAAQ,GAAGrB,WAAW,CAAC,CAAC;EAC9B,MAAMsB,IAAI,GAAGF,YAAY,CAACG,GAAG,CAAC,MAAM,CAAC,IAAI,GAAG;EAE5C,MAAM,CAACC,SAAS,EAAEC,YAAY,CAAC,GAAG1B,QAAQ,CAAC,EAAE,CAAC;EAC9C,MAAM,CAAC2B,aAAa,EAAEC,gBAAgB,CAAC,GAAG5B,QAAQ,CAAC,EAAE,CAAC;EACtD,MAAM,CAAC6B,QAAQ,EAAEC,WAAW,CAAC,GAAG9B,QAAQ,CAAC,KAAK,CAAC;EAC/C,MAAM,CAAC+B,SAAS,EAAEC,YAAY,CAAC,GAAGhC,QAAQ,CAAC,KAAK,CAAC;EAEjD,MAAMiC,SAAS,GAAG/B,WAAW,CAAEgC,KAAK,IAAKA,KAAK,CAACD,SAAS,CAAC;EACzD,MAAM;IAAEE;EAAS,CAAC,GAAGF,SAAS;EAE9B,MAAMG,gBAAgB,GAAGlC,WAAW,CAAEgC,KAAK,IAAKA,KAAK,CAACG,gBAAgB,CAAC;EACvE,MAAM;IAAEC,YAAY;IAAEC,mBAAmB;IAAEC,iBAAiB;IAAEC;EAAM,CAAC,GACnEL,gBAAgB;EAElB,MAAMM,UAAU,GAAGxC,WAAW,CAAEgC,KAAK,IAAKA,KAAK,CAAC1B,UAAU,CAAC;EAC3D,MAAM;IAAEmC,iBAAiB;IAAEC,iBAAiB;IAAEC;EAAiB,CAAC,GAAGH,UAAU;EAE7E,MAAMI,QAAQ,GAAG,GAAG;EAEpB/C,SAAS,CAAC,MAAM;IACd,IAAI,CAACoC,QAAQ,EAAE;MACbhB,QAAQ,CAAC2B,QAAQ,CAAC;IACpB,CAAC,MAAM,IACLX,QAAQ,CAACY,IAAI,KAAK,GAAG,IACrBZ,QAAQ,CAACY,IAAI,KAAK,CAAC,IACnBZ,QAAQ,CAACY,IAAI,KAAK,GAAG,IACrBZ,QAAQ,CAACY,IAAI,KAAK,CAAC,EACnB;MACA5B,QAAQ,CAAC2B,QAAQ,CAAC;IACpB,CAAC,MAAM;MACLxB,QAAQ,CAACb,mBAAmB,CAACc,IAAI,CAAC,CAAC;IACrC;EACF,CAAC,EAAE,CAACJ,QAAQ,EAAEgB,QAAQ,EAAEb,QAAQ,EAAEC,IAAI,CAAC,CAAC;EAExCxB,SAAS,CAAC,MAAM;IACd,IAAI6C,iBAAiB,EAAE;MACrBtB,QAAQ,CAACb,mBAAmB,CAAC,GAAG,CAAC,CAAC;IACpC;EACF,CAAC,EAAE,CAACmC,iBAAiB,CAAC,CAAC;EAEvB,oBACE5B,OAAA,CAACT,aAAa;IAAAyC,QAAA,eACZhC,OAAA;MAAAgC,QAAA,gBACEhC,OAAA;QAAKiC,SAAS,EAAC,yCAAyC;QAAAD,QAAA,gBAEtDhC,OAAA;UAAGkC,IAAI,EAAC,YAAY;UAAAF,QAAA,eAClBhC,OAAA;YAAKiC,SAAS,EAAC,+CAA+C;YAAAD,QAAA,gBAC5DhC,OAAA;cACEmC,KAAK,EAAC,4BAA4B;cAClCC,IAAI,EAAC,MAAM;cACXC,OAAO,EAAC,WAAW;cACnB,gBAAa,KAAK;cAClBC,MAAM,EAAC,cAAc;cACrBL,SAAS,EAAC,SAAS;cAAAD,QAAA,eAEnBhC,OAAA;gBACEuC,aAAa,EAAC,OAAO;gBACrBC,cAAc,EAAC,OAAO;gBACtBC,CAAC,EAAC;cAA4O;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC/O;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eACN7C,OAAA;cAAMiC,SAAS,EAAC,MAAM;cAAAD,QAAA,EAAC;YAAS;cAAAU,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACpC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC,eACJ7C,OAAA;UAAAgC,QAAA,eACEhC,OAAA;YACEmC,KAAK,EAAC,4BAA4B;YAClCC,IAAI,EAAC,MAAM;YACXC,OAAO,EAAC,WAAW;YACnB,gBAAa,KAAK;YAClBC,MAAM,EAAC,cAAc;YACrBL,SAAS,EAAC,SAAS;YAAAD,QAAA,eAEnBhC,OAAA;cACEuC,aAAa,EAAC,OAAO;cACrBC,cAAc,EAAC,OAAO;cACtBC,CAAC,EAAC;YAA2B;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC9B;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC,eACP7C,OAAA;UAAKiC,SAAS,EAAC,EAAE;UAAAD,QAAA,EAAC;QAAiB;UAAAU,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACtC,CAAC,eAEN7C,OAAA;QAAKiC,SAAS,EAAC,kDAAkD;QAAAD,QAAA,gBAC/DhC,OAAA;UAAKiC,SAAS,EAAC,4BAA4B;UAAAD,QAAA,EAAC;QAAiB;UAAAU,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eAEnE7C,OAAA;UAAKiC,SAAS,EAAC,wCAAwC;UAAAD,QAAA,eACrDhC,OAAA;YACEkC,IAAI,EAAC,oCAAoC;YACzCD,SAAS,EAAC,wFAAwF;YAAAD,QAAA,gBAElGhC,OAAA;cACEmC,KAAK,EAAC,4BAA4B;cAClCC,IAAI,EAAC,MAAM;cACXC,OAAO,EAAC,WAAW;cACnB,gBAAa,KAAK;cAClBC,MAAM,EAAC,cAAc;cACrBL,SAAS,EAAC,aAAa;cAAAD,QAAA,eAEvBhC,OAAA;gBACE,kBAAe,OAAO;gBACtB,mBAAgB,OAAO;gBACvByC,CAAC,EAAC;cAAwB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC3B;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eAEN7C,OAAA;cAAAgC,QAAA,EAAK;YAAe;cAAAU,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACzB;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAEN7C,OAAA;QAAKiC,SAAS,EAAC,8GAA8G;QAAAD,QAAA,eAC3HhC,OAAA;UAAKiC,SAAS,EAAC,iCAAiC;UAAAD,QAAA,GAC7CT,mBAAmB,gBAClBvB,OAAA,CAACN,MAAM;YAAAgD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,GACRrB,iBAAiB,gBACnBxB,OAAA,CAACL,KAAK;YAACmD,IAAI,EAAE,OAAQ;YAACC,OAAO,EAAEvB;UAAkB;YAAAkB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,gBAEpD7C,OAAA;YAAKiC,SAAS,EAAC,6BAA6B;YAAAD,QAAA,eAC1ChC,OAAA;cAAOiC,SAAS,EAAC,mBAAmB;cAAAD,QAAA,gBAClChC,OAAA;gBAAAgC,QAAA,eACEhC,OAAA;kBAAIiC,SAAS,EAAC,0BAA0B;kBAAAD,QAAA,gBACtChC,OAAA;oBAAIiC,SAAS,EAAC,+DAA+D;oBAAAD,QAAA,EAAC;kBAE9E;oBAAAU,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACL7C,OAAA;oBAAIiC,SAAS,EAAC,gEAAgE;oBAAAD,QAAA,EAAC;kBAE/E;oBAAAU,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACL7C,OAAA;oBAAIiC,SAAS,EAAC,gEAAgE;oBAAAD,QAAA,EAAC;kBAE/E;oBAAAU,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACL7C,OAAA;oBAAIiC,SAAS,EAAC,gEAAgE;oBAAAD,QAAA,EAAC;kBAE/E;oBAAAU,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACL7C,OAAA;oBAAIiC,SAAS,EAAC,gEAAgE;oBAAAD,QAAA,EAAC;kBAE/E;oBAAAU,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACL7C,OAAA;oBAAIiC,SAAS,EAAC,kDAAkD;oBAAAD,QAAA,EAAC;kBAEjE;oBAAAU,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACA,CAAC,eAER7C,OAAA;gBAAAgC,QAAA,EACGV,YAAY,aAAZA,YAAY,uBAAZA,YAAY,CAAE0B,GAAG,CAAC,CAACC,IAAI,EAAEC,KAAK;kBAAA,IAAAC,gBAAA,EAAAC,WAAA,EAAAC,WAAA;kBAAA,oBAC7BrD,OAAA;oBAAAgC,QAAA,gBACEhC,OAAA;sBAAIiC,SAAS,EAAC,4BAA4B;sBAAAD,QAAA,eACxChC,OAAA;wBAAGiC,SAAS,EAAC,6BAA6B;wBAAAD,QAAA,GAAC,GACxC,EAACiB,IAAI,CAACK,EAAE;sBAAA;wBAAAZ,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACR;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACF,CAAC,eACL7C,OAAA;sBAAIiC,SAAS,EAAC,4BAA4B;sBAAAD,QAAA,eACxChC,OAAA,CAACb,IAAI;wBAACoE,EAAE,EAAE,6BAA6B,GAAGN,IAAI,CAACK,EAAG;wBAAAtB,QAAA,EAC/CiB,IAAI,CAACO,KAAK,gBACTxD,OAAA;0BACEiC,SAAS,EAAC,iBAAiB;0BAC3BwB,GAAG,EAAE7D,WAAW,GAAGqD,IAAI,CAACO,KAAM;0BAC9BE,GAAG,EAAET,IAAI,CAACU,UAAU,GAAG,GAAG,GAAGV,IAAI,CAACW,SAAU;0BAC5CC,OAAO,EAAGC,CAAC,IAAK;4BACdA,CAAC,CAACC,MAAM,CAACC,OAAO,GAAG,IAAI;4BACvBF,CAAC,CAACC,MAAM,CAACN,GAAG,GAAG,yBAAyB;0BAC1C;wBAAE;0BAAAf,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACH,CAAC,gBAEF7C,OAAA;0BACEiC,SAAS,EAAC,iBAAiB;0BAC3BwB,GAAG,EAAE,yBAA0B;0BAC/BC,GAAG,EAAET,IAAI,CAACU,UAAU,GAAG,GAAG,GAAGV,IAAI,CAACW,SAAU;0BAC5CC,OAAO,EAAGC,CAAC,IAAK;4BACdA,CAAC,CAACC,MAAM,CAACC,OAAO,GAAG,IAAI;4BACvBF,CAAC,CAACC,MAAM,CAACN,GAAG,GAAG,yBAAyB;0BAC1C;wBAAE;0BAAAf,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACH;sBACF;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACG;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACL,CAAC,eACL7C,OAAA;sBAAIiC,SAAS,EAAC,4BAA4B;sBAAAD,QAAA,eACxChC,OAAA;wBAAGiC,SAAS,EAAC,6BAA6B;wBAAAD,QAAA,IAAAmB,gBAAA,GACvCF,IAAI,CAACU,UAAU,cAAAR,gBAAA,cAAAA,gBAAA,GAAI,KAAK,EAAC,GAAC,EAACF,IAAI,CAACW,SAAS;sBAAA;wBAAAlB,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACzC;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACF,CAAC,eAEL7C,OAAA;sBAAIiC,SAAS,EAAC,4BAA4B;sBAAAD,QAAA,eACxChC,OAAA;wBAAGiC,SAAS,EAAC,6BAA6B;wBAAAD,QAAA,GAAAoB,WAAA,GACvCH,IAAI,CAACgB,KAAK,cAAAb,WAAA,cAAAA,WAAA,GAAI;sBAAK;wBAAAV,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACnB;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACF,CAAC,eACL7C,OAAA;sBAAIiC,SAAS,EAAC,4BAA4B;sBAAAD,QAAA,eACxChC,OAAA;wBAAGiC,SAAS,EAAC,6BAA6B;wBAAAD,QAAA,GAAAqB,WAAA,GACvCJ,IAAI,CAACiB,KAAK,cAAAb,WAAA,cAAAA,WAAA,GAAI;sBAAK;wBAAAX,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACnB;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACF,CAAC,eACL7C,OAAA;sBAAIiC,SAAS,EAAC,4BAA4B;sBAAAD,QAAA,eACxChC,OAAA;wBAAGiC,SAAS,EAAC,2CAA2C;wBAAAD,QAAA,gBACtDhC,OAAA,CAACb,IAAI;0BACH8C,SAAS,EAAC,mBAAmB;0BAC7BsB,EAAE,EAAE,0BAA0B,GAAGN,IAAI,CAACK,EAAG;0BAAAtB,QAAA,eAEzChC,OAAA;4BACEmC,KAAK,EAAC,4BAA4B;4BAClCC,IAAI,EAAC,MAAM;4BACXC,OAAO,EAAC,WAAW;4BACnB8B,WAAW,EAAC,KAAK;4BACjB7B,MAAM,EAAC,cAAc;4BACrBL,SAAS,EAAC,+DAA+D;4BAAAD,QAAA,eAEzEhC,OAAA;8BACEuC,aAAa,EAAC,OAAO;8BACrBC,cAAc,EAAC,OAAO;8BACtBC,CAAC,EAAC;4BAAkQ;8BAAAC,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OACrQ;0BAAC;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACC;wBAAC;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACF,CAAC,eAEP7C,OAAA;0BACEoE,OAAO,EAAEA,CAAA,KAAM;4BACb1D,YAAY,CAAC,QAAQ,CAAC;4BACtBE,gBAAgB,CAACqC,IAAI,CAACK,EAAE,CAAC;4BACzBxC,WAAW,CAAC,IAAI,CAAC;0BACnB,CAAE;0BACFmB,SAAS,EAAC,kCAAkC;0BAAAD,QAAA,eAE5ChC,OAAA;4BACEmC,KAAK,EAAC,4BAA4B;4BAClCC,IAAI,EAAC,MAAM;4BACXC,OAAO,EAAC,WAAW;4BACnB,gBAAa,KAAK;4BAClBC,MAAM,EAAC,cAAc;4BACrBL,SAAS,EAAC,8DAA8D;4BAAAD,QAAA,eAExEhC,OAAA;8BACE,kBAAe,OAAO;8BACtB,mBAAgB,OAAO;8BACvByC,CAAC,EAAC;4BAA+T;8BAAAC,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAClU;0BAAC;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACC;wBAAC;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACH,CAAC,eACN7C,OAAA,CAACb,IAAI;0BACH8C,SAAS,EAAC,oBAAoB;0BAC9BsB,EAAE,EAAE,6BAA6B,GAAGN,IAAI,CAACK,EAAG;0BAAAtB,QAAA,eAE5ChC,OAAA;4BACEmC,KAAK,EAAC,4BAA4B;4BAClCC,IAAI,EAAC,MAAM;4BACXC,OAAO,EAAC,WAAW;4BACnB,gBAAa,KAAK;4BAClBC,MAAM,EAAC,cAAc;4BACrBL,SAAS,EAAC,+DAA+D;4BAAAD,QAAA,eAEzEhC,OAAA;8BACE,kBAAe,OAAO;8BACtB,mBAAgB,OAAO;8BACvByC,CAAC,EAAC;4BAAuR;8BAAAC,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAC1R;0BAAC;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACC;wBAAC;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACF,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACN;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACF,CAAC;kBAAA,GAhHEK,KAAK;oBAAAR,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAiHV,CAAC;gBAAA,CACN;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CACN,eACD7C,OAAA;YAAKiC,SAAS,EAAC,MAAM;YAAAD,QAAA,eACnBhC,OAAA,CAACF,QAAQ;cACPuE,KAAK,EAAE,qBAAsB;cAC7BC,MAAM,EAAE,EAAG;cACX/D,IAAI,EAAEA,IAAK;cACXkB,KAAK,EAAEA;YAAM;cAAAiB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACd;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAEN7C,OAAA,CAACH,iBAAiB;QAChB0E,MAAM,EAAE1D,QAAS;QACjBkC,OAAO,EACLtC,SAAS,KAAK,QAAQ,GAClB,kDAAkD,GAClD,gBACL;QACD+D,SAAS,EAAE,MAAAA,CAAA,KAAY;UACrB,IAAI/D,SAAS,KAAK,QAAQ,EAAE;YAC1BK,WAAW,CAAC,KAAK,CAAC;YAClBJ,YAAY,CAAC,EAAE,CAAC;YAChBM,YAAY,CAAC,KAAK,CAAC;UACrB,CAAC,MAAM,IAAIP,SAAS,KAAK,QAAQ,IAAIE,aAAa,KAAK,EAAE,EAAE;YACzDK,YAAY,CAAC,IAAI,CAAC;YAClBV,QAAQ,CAACd,UAAU,CAACmB,aAAa,CAAC,CAAC;YACnCG,WAAW,CAAC,KAAK,CAAC;YAClBJ,YAAY,CAAC,EAAE,CAAC;YAChBM,YAAY,CAAC,KAAK,CAAC;UACrB,CAAC,MAAM;YACLF,WAAW,CAAC,KAAK,CAAC;YAClBJ,YAAY,CAAC,EAAE,CAAC;YAChBM,YAAY,CAAC,KAAK,CAAC;UACrB;QACF,CAAE;QACFyD,QAAQ,EAAEA,CAAA,KAAM;UACd3D,WAAW,CAAC,KAAK,CAAC;UAClBJ,YAAY,CAAC,EAAE,CAAC;UAChBM,YAAY,CAAC,KAAK,CAAC;QACrB,CAAE;QACFD,SAAS,EAAEA;MAAU;QAAA2B,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACtB,CAAC,eACF7C,OAAA;QAAKiC,SAAS,EAAC;MAA2C;QAAAS,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC9D;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACO,CAAC;AAEpB;AAAC3C,EAAA,CA3TQD,sBAAsB;EAAA,QACZZ,WAAW,EACXD,WAAW,EACLE,eAAe,EACrBL,WAAW,EAQVC,WAAW,EAGJA,WAAW,EAIjBA,WAAW;AAAA;AAAAwF,EAAA,GAnBvBzE,sBAAsB;AA6T/B,eAAeA,sBAAsB;AAAC,IAAAyE,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}