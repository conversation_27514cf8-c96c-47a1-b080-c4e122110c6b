{"ast": null, "code": "import axios from\"../../axios\";import{PROVIDER_LIST_REQUEST,PROVIDER_LIST_SUCCESS,PROVIDER_LIST_FAIL,//\nPROVIDER_ADD_REQUEST,PROVIDER_ADD_SUCCESS,PROVIDER_ADD_FAIL,//\nPROVIDER_DETAIL_REQUEST,PROVIDER_DETAIL_SUCCESS,PROVIDER_DETAIL_FAIL,//\nPROVIDER_UPDATE_REQUEST,PROVIDER_UPDATE_SUCCESS,PROVIDER_UPDATE_FAIL,//\nPROVIDER_DELETE_REQUEST,PROVIDER_DELETE_SUCCESS,PROVIDER_DELETE_FAIL//\n}from\"../constants/providerConstants\";export const updateProvider=(id,provider)=>async(dispatch,getState)=>{try{dispatch({type:PROVIDER_UPDATE_REQUEST});var{userLogin:{userInfo}}=getState();const config={headers:{\"Content-Type\":\"application/json\",Authorization:`Bearer ${userInfo.access}`}};const{data}=await axios.put(`/providers/update/${id}/`,provider,config);dispatch({type:PROVIDER_UPDATE_SUCCESS,payload:data});}catch(error){var err=error.response&&error.response.data.detail?error.response.data.detail:error.detail;if(err){if(err===\"Given token not valid for any token type\"){localStorage.removeItem(\"userInfoUnimedCare\");document.location.href=\"/\";}}dispatch({type:PROVIDER_UPDATE_FAIL,payload:error.response&&error.response.data.detail?error.response.data.detail:error.detail});}};// delete provider\nexport const deleteProvider=id=>async(dispatch,getState)=>{try{dispatch({type:PROVIDER_DELETE_REQUEST});var{userLogin:{userInfo}}=getState();const config={headers:{\"Content-Type\":\"multipart/form-data\",Authorization:`Bearer ${userInfo.access}`}};const{data}=await axios.delete(`/providers/delete/${id}/`,config);dispatch({type:PROVIDER_DELETE_SUCCESS,payload:data});}catch(error){var err=error.response&&error.response.data.detail?error.response.data.detail:error.detail;if(err){if(err===\"Given token not valid for any token type\"){localStorage.removeItem(\"userInfoUnimedCare\");document.location.href=\"/\";}}dispatch({type:PROVIDER_DELETE_FAIL,payload:error.response&&error.response.data.detail?error.response.data.detail:error.detail});}};// create provider\nexport const createNewProvider=provider=>async(dispatch,getState)=>{try{dispatch({type:PROVIDER_ADD_REQUEST});var{userLogin:{userInfo}}=getState();const config={headers:{\"Content-Type\":\"application/json\",Authorization:`Bearer ${userInfo.access}`}};const{data}=await axios.post(`/providers/create-new-provider/`,provider,config);dispatch({type:PROVIDER_ADD_SUCCESS,payload:data});}catch(error){var err=error.response&&error.response.data.detail?error.response.data.detail:error.detail;if(err){if(err===\"Given token not valid for any token type\"){localStorage.removeItem(\"userInfoUnimedCare\");document.location.href=\"/\";}}dispatch({type:PROVIDER_ADD_FAIL,payload:error.response&&error.response.data.detail?error.response.data.detail:error.detail});}};// detail case\nexport const detailProvider=id=>async(dispatch,getState)=>{try{dispatch({type:PROVIDER_DETAIL_REQUEST});var{userLogin:{userInfo}}=getState();const config={headers:{\"Content-Type\":\"application/json\",Authorization:`Bearer ${userInfo.access}`}};const{data}=await axios.get(`/providers/detail/${id}/`,config);dispatch({type:PROVIDER_DETAIL_SUCCESS,payload:data});}catch(error){var err=error.response&&error.response.data.detail?error.response.data.detail:error.detail;if(err){if(err===\"Given token not valid for any token type\"){localStorage.removeItem(\"userInfoUnimedCare\");document.location.href=\"/\";}}dispatch({type:PROVIDER_DETAIL_FAIL,payload:error.response&&error.response.data.detail?error.response.data.detail:error.detail});}};// get list cases\nexport const providersList=function(page){let filterName=arguments.length>1&&arguments[1]!==undefined?arguments[1]:\"\";let filterType=arguments.length>2&&arguments[2]!==undefined?arguments[2]:\"\";let filterCity=arguments.length>3&&arguments[3]!==undefined?arguments[3]:\"\";let filterCountry=arguments.length>4&&arguments[4]!==undefined?arguments[4]:\"\";let range_max=arguments.length>5&&arguments[5]!==undefined?arguments[5]:\"\";let location_x=arguments.length>6&&arguments[6]!==undefined?arguments[6]:\"\";let location_y=arguments.length>7&&arguments[7]!==undefined?arguments[7]:\"\";return async(dispatch,getState)=>{try{// Dispatch request action\ndispatch({type:PROVIDER_LIST_REQUEST});// Get user info from state\nvar{userLogin:{userInfo}}=getState();// Configure request\nconst config={headers:{\"Content-Type\":\"application/json\",Authorization:`Bearer ${userInfo.access}`},// Set a longer timeout specifically for this request\ntimeout:60000// 60 seconds timeout\n};console.log(\"Fetching providers data...\");// Create a promise with timeout\nconst fetchPromise=axios.get(`/providers/?page=${page}&filtername=${filterName}&filtertype=${filterType}&filtercity=${filterCity}&filtercountry=${filterCountry}&rangemax=${range_max}&locationx=${location_x}&locationy=${location_y}`,config);// Set a timeout promise\nconst timeoutPromise=new Promise((_,reject)=>{setTimeout(()=>{reject(new Error(\"Request timed out after 60 seconds\"));},60000);});// Race between fetch and timeout\nconst{data}=await Promise.race([fetchPromise,timeoutPromise]);console.log(\"Providers data fetched successfully!\");// Dispatch success action\ndispatch({type:PROVIDER_LIST_SUCCESS,payload:data});return data;// Return data for Promise chaining\n}catch(error){console.error(\"Error fetching providers:\",error);// Handle token validation error\nvar err=error.response&&error.response.data.detail?error.response.data.detail:error.detail||error.message||\"Unknown error\";if(err){if(err===\"Given token not valid for any token type\"){localStorage.removeItem(\"userInfoUnimedCare\");document.location.href=\"/\";}}// Dispatch failure action\ndispatch({type:PROVIDER_LIST_FAIL,payload:err});// Return empty array as fallback data\nreturn{results:[]};}};};// Optimized get list providers for Dashboard (lightweight)\nexport const providersListDashboard=function(){let page=arguments.length>0&&arguments[0]!==undefined?arguments[0]:\"0\";return async(dispatch,getState)=>{try{// Dispatch request action\ndispatch({type:PROVIDER_LIST_REQUEST});// Get user info from state\nvar{userLogin:{userInfo}}=getState();// Configure request\nconst config={headers:{\"Content-Type\":\"application/json\",Authorization:`Bearer ${userInfo.access}`}};// Fetch only essential provider data for dashboard dropdown\nconst{data}=await axios.get(`/providers/?page=${page}&isdashboard=true`,config);// Dispatch success action\ndispatch({type:PROVIDER_LIST_SUCCESS,payload:data});return data;}catch(error){var err=error.response&&error.response.data.detail?error.response.data.detail:error.message||\"An error occurred while fetching providers\";if(err===\"Given token not valid for any token type\"){localStorage.removeItem(\"userInfoUnimedCare\");document.location.href=\"/\";}dispatch({type:PROVIDER_LIST_FAIL,payload:err});return{results:[]};}};};// Optimized get list providers for EditCaseScreen (includes services)\nexport const providersListEditCase=function(){let page=arguments.length>0&&arguments[0]!==undefined?arguments[0]:\"0\";return async(dispatch,getState)=>{try{// Dispatch request action\ndispatch({type:PROVIDER_LIST_REQUEST});// Get user info from state\nvar{userLogin:{userInfo}}=getState();// Configure request\nconst config={headers:{\"Content-Type\":\"application/json\",Authorization:`Bearer ${userInfo.access}`}};// Fetch provider data with services for EditCaseScreen\nconst{data}=await axios.get(`/providers/?page=${page}&iseditcase=true`,config);// Dispatch success action\ndispatch({type:PROVIDER_LIST_SUCCESS,payload:data});return data;}catch(error){var err=error.response&&error.response.data.detail?error.response.data.detail:error.message||\"An error occurred while fetching providers\";if(err===\"Given token not valid for any token type\"){localStorage.removeItem(\"userInfoUnimedCare\");document.location.href=\"/\";}dispatch({type:PROVIDER_LIST_FAIL,payload:err});return{providers:[]};}};};// Optimized get list providers for ProvidersMapScreen (map display)\nexport const providersListMapScreen=function(page){let filterName=arguments.length>1&&arguments[1]!==undefined?arguments[1]:\"\";let filterType=arguments.length>2&&arguments[2]!==undefined?arguments[2]:\"\";let filterCity=arguments.length>3&&arguments[3]!==undefined?arguments[3]:\"\";let filterCountry=arguments.length>4&&arguments[4]!==undefined?arguments[4]:\"\";let range_max=arguments.length>5&&arguments[5]!==undefined?arguments[5]:\"\";let location_x=arguments.length>6&&arguments[6]!==undefined?arguments[6]:\"\";let location_y=arguments.length>7&&arguments[7]!==undefined?arguments[7]:\"\";return async(dispatch,getState)=>{try{// Dispatch request action\ndispatch({type:PROVIDER_LIST_REQUEST});// Get user info from state\nvar{userLogin:{userInfo}}=getState();// Configure request\nconst config={headers:{\"Content-Type\":\"application/json\",Authorization:`Bearer ${userInfo.access}`},// Set a longer timeout specifically for this request\ntimeout:60000// 60 seconds timeout\n};console.log(\"Fetching optimized providers data for map screen...\");// Create a promise with timeout\nconst fetchPromise=axios.get(`/providers/?page=${page}&filtername=${filterName}&filtertype=${filterType}&filtercity=${filterCity}&filtercountry=${filterCountry}&rangemax=${range_max}&locationx=${location_x}&locationy=${location_y}&ismapscreen=true`,config);// Set a timeout promise\nconst timeoutPromise=new Promise((_,reject)=>{setTimeout(()=>{reject(new Error(\"Request timed out after 60 seconds\"));},60000);});// Race between fetch and timeout\nconst{data}=await Promise.race([fetchPromise,timeoutPromise]);console.log(\"Optimized providers data fetched successfully!\");// Dispatch success action\ndispatch({type:PROVIDER_LIST_SUCCESS,payload:data});return data;// Return data for Promise chaining\n}catch(error){console.error(\"Error fetching providers for map screen:\",error);// Handle token validation error\nvar err=error.response&&error.response.data.detail?error.response.data.detail:error.detail||error.message||\"Unknown error\";if(err){if(err===\"Given token not valid for any token type\"){localStorage.removeItem(\"userInfoUnimedCare\");document.location.href=\"/\";}}// Dispatch failure action\ndispatch({type:PROVIDER_LIST_FAIL,payload:err});// Return empty array as fallback data\nreturn{results:[]};}};};", "map": {"version": 3, "names": ["axios", "PROVIDER_LIST_REQUEST", "PROVIDER_LIST_SUCCESS", "PROVIDER_LIST_FAIL", "PROVIDER_ADD_REQUEST", "PROVIDER_ADD_SUCCESS", "PROVIDER_ADD_FAIL", "PROVIDER_DETAIL_REQUEST", "PROVIDER_DETAIL_SUCCESS", "PROVIDER_DETAIL_FAIL", "PROVIDER_UPDATE_REQUEST", "PROVIDER_UPDATE_SUCCESS", "PROVIDER_UPDATE_FAIL", "PROVIDER_DELETE_REQUEST", "PROVIDER_DELETE_SUCCESS", "PROVIDER_DELETE_FAIL", "updateProvider", "id", "provider", "dispatch", "getState", "type", "userLogin", "userInfo", "config", "headers", "Authorization", "access", "data", "put", "payload", "error", "err", "response", "detail", "localStorage", "removeItem", "document", "location", "href", "deleteProvider", "delete", "createNewProvider", "post", "detail<PERSON>rovider", "get", "providersList", "page", "filterName", "arguments", "length", "undefined", "filterType", "filterCity", "filterCountry", "range_max", "location_x", "location_y", "timeout", "console", "log", "fetchPromise", "timeoutPromise", "Promise", "_", "reject", "setTimeout", "Error", "race", "message", "results", "providersListDashboard", "providersListEditCase", "providers", "providersListMapScreen"], "sources": ["/Users/<USER>/Desktop/Freelances/<PERSON><PERSON><PERSON>/UNIMEDCARE/web-front/src/redux/actions/providerActions.js"], "sourcesContent": ["import axios from \"../../axios\";\nimport {\n  PROVIDER_LIST_REQUEST,\n  PROVIDER_LIST_SUCCESS,\n  PROVIDER_LIST_FAIL,\n  //\n  PROVIDER_ADD_REQUEST,\n  PROVIDER_ADD_SUCCESS,\n  PROVIDER_ADD_FAIL,\n  //\n  PROVIDER_DETAIL_REQUEST,\n  PROVIDER_DETAIL_SUCCESS,\n  PROVIDER_DETAIL_FAIL,\n  //\n  PROVIDER_UPDATE_REQUEST,\n  PROVIDER_UPDATE_SUCCESS,\n  PROVIDER_UPDATE_FAIL,\n  //\n  PROVIDER_DELETE_REQUEST,\n  PROVIDER_DELETE_SUCCESS,\n  PROVIDER_DELETE_FAIL,\n  //\n} from \"../constants/providerConstants\";\n\nexport const updateProvider = (id, provider) => async (dispatch, getState) => {\n  try {\n    dispatch({\n      type: PROVIDER_UPDATE_REQUEST,\n    });\n    var {\n      userLogin: { userInfo },\n    } = getState();\n    const config = {\n      headers: {\n        \"Content-Type\": \"application/json\",\n        Authorization: `Bearer ${userInfo.access}`,\n      },\n    };\n    const { data } = await axios.put(\n      `/providers/update/${id}/`,\n      provider,\n      config\n    );\n\n    dispatch({\n      type: PROVIDER_UPDATE_SUCCESS,\n      payload: data,\n    });\n  } catch (error) {\n    var err =\n      error.response && error.response.data.detail\n        ? error.response.data.detail\n        : error.detail;\n    if (err) {\n      if (err === \"Given token not valid for any token type\") {\n        localStorage.removeItem(\"userInfoUnimedCare\");\n        document.location.href = \"/\";\n      }\n    }\n    dispatch({\n      type: PROVIDER_UPDATE_FAIL,\n      payload:\n        error.response && error.response.data.detail\n          ? error.response.data.detail\n          : error.detail,\n    });\n  }\n};\n\n// delete provider\nexport const deleteProvider = (id) => async (dispatch, getState) => {\n  try {\n    dispatch({\n      type: PROVIDER_DELETE_REQUEST,\n    });\n    var {\n      userLogin: { userInfo },\n    } = getState();\n    const config = {\n      headers: {\n        \"Content-Type\": \"multipart/form-data\",\n        Authorization: `Bearer ${userInfo.access}`,\n      },\n    };\n    const { data } = await axios.delete(`/providers/delete/${id}/`, config);\n\n    dispatch({\n      type: PROVIDER_DELETE_SUCCESS,\n      payload: data,\n    });\n  } catch (error) {\n    var err =\n      error.response && error.response.data.detail\n        ? error.response.data.detail\n        : error.detail;\n    if (err) {\n      if (err === \"Given token not valid for any token type\") {\n        localStorage.removeItem(\"userInfoUnimedCare\");\n        document.location.href = \"/\";\n      }\n    }\n    dispatch({\n      type: PROVIDER_DELETE_FAIL,\n      payload:\n        error.response && error.response.data.detail\n          ? error.response.data.detail\n          : error.detail,\n    });\n  }\n};\n\n// create provider\nexport const createNewProvider = (provider) => async (dispatch, getState) => {\n  try {\n    dispatch({\n      type: PROVIDER_ADD_REQUEST,\n    });\n    var {\n      userLogin: { userInfo },\n    } = getState();\n    const config = {\n      headers: {\n        \"Content-Type\": \"application/json\",\n        Authorization: `Bearer ${userInfo.access}`,\n      },\n    };\n    const { data } = await axios.post(\n      `/providers/create-new-provider/`,\n      provider,\n      config\n    );\n\n    dispatch({\n      type: PROVIDER_ADD_SUCCESS,\n      payload: data,\n    });\n  } catch (error) {\n    var err =\n      error.response && error.response.data.detail\n        ? error.response.data.detail\n        : error.detail;\n    if (err) {\n      if (err === \"Given token not valid for any token type\") {\n        localStorage.removeItem(\"userInfoUnimedCare\");\n        document.location.href = \"/\";\n      }\n    }\n    dispatch({\n      type: PROVIDER_ADD_FAIL,\n      payload:\n        error.response && error.response.data.detail\n          ? error.response.data.detail\n          : error.detail,\n    });\n  }\n};\n\n// detail case\nexport const detailProvider = (id) => async (dispatch, getState) => {\n  try {\n    dispatch({\n      type: PROVIDER_DETAIL_REQUEST,\n    });\n    var {\n      userLogin: { userInfo },\n    } = getState();\n    const config = {\n      headers: {\n        \"Content-Type\": \"application/json\",\n        Authorization: `Bearer ${userInfo.access}`,\n      },\n    };\n    const { data } = await axios.get(`/providers/detail/${id}/`, config);\n\n    dispatch({\n      type: PROVIDER_DETAIL_SUCCESS,\n      payload: data,\n    });\n  } catch (error) {\n    var err =\n      error.response && error.response.data.detail\n        ? error.response.data.detail\n        : error.detail;\n    if (err) {\n      if (err === \"Given token not valid for any token type\") {\n        localStorage.removeItem(\"userInfoUnimedCare\");\n        document.location.href = \"/\";\n      }\n    }\n    dispatch({\n      type: PROVIDER_DETAIL_FAIL,\n      payload:\n        error.response && error.response.data.detail\n          ? error.response.data.detail\n          : error.detail,\n    });\n  }\n};\n\n// get list cases\nexport const providersList =\n  (\n    page,\n    filterName = \"\",\n    filterType = \"\",\n    filterCity = \"\",\n    filterCountry = \"\",\n    range_max = \"\",\n    location_x = \"\",\n    location_y = \"\"\n  ) =>\n  async (dispatch, getState) => {\n    try {\n      // Dispatch request action\n      dispatch({\n        type: PROVIDER_LIST_REQUEST,\n      });\n\n      // Get user info from state\n      var {\n        userLogin: { userInfo },\n      } = getState();\n\n      // Configure request\n      const config = {\n        headers: {\n          \"Content-Type\": \"application/json\",\n          Authorization: `Bearer ${userInfo.access}`,\n        },\n        // Set a longer timeout specifically for this request\n        timeout: 60000, // 60 seconds timeout\n      };\n\n      console.log(\"Fetching providers data...\");\n\n      // Create a promise with timeout\n      const fetchPromise = axios.get(\n        `/providers/?page=${page}&filtername=${filterName}&filtertype=${filterType}&filtercity=${filterCity}&filtercountry=${filterCountry}&rangemax=${range_max}&locationx=${location_x}&locationy=${location_y}`,\n        config\n      );\n\n      // Set a timeout promise\n      const timeoutPromise = new Promise((_, reject) => {\n        setTimeout(() => {\n          reject(new Error(\"Request timed out after 60 seconds\"));\n        }, 60000);\n      });\n\n      // Race between fetch and timeout\n      const { data } = await Promise.race([fetchPromise, timeoutPromise]);\n\n      console.log(\"Providers data fetched successfully!\");\n\n      // Dispatch success action\n      dispatch({\n        type: PROVIDER_LIST_SUCCESS,\n        payload: data,\n      });\n\n      return data; // Return data for Promise chaining\n    } catch (error) {\n      console.error(\"Error fetching providers:\", error);\n\n      // Handle token validation error\n      var err =\n        error.response && error.response.data.detail\n          ? error.response.data.detail\n          : (error.detail || error.message || \"Unknown error\");\n\n      if (err) {\n        if (err === \"Given token not valid for any token type\") {\n          localStorage.removeItem(\"userInfoUnimedCare\");\n          document.location.href = \"/\";\n        }\n      }\n\n      // Dispatch failure action\n      dispatch({\n        type: PROVIDER_LIST_FAIL,\n        payload: err,\n      });\n\n      // Return empty array as fallback data\n      return { results: [] };\n    }\n  };\n\n// Optimized get list providers for Dashboard (lightweight)\nexport const providersListDashboard = (page = \"0\") => async (dispatch, getState) => {\n  try {\n    // Dispatch request action\n    dispatch({\n      type: PROVIDER_LIST_REQUEST,\n    });\n\n    // Get user info from state\n    var {\n      userLogin: { userInfo },\n    } = getState();\n\n    // Configure request\n    const config = {\n      headers: {\n        \"Content-Type\": \"application/json\",\n        Authorization: `Bearer ${userInfo.access}`,\n      },\n    };\n\n    // Fetch only essential provider data for dashboard dropdown\n    const { data } = await axios.get(\n      `/providers/?page=${page}&isdashboard=true`,\n      config\n    );\n\n    // Dispatch success action\n    dispatch({\n      type: PROVIDER_LIST_SUCCESS,\n      payload: data,\n    });\n\n    return data;\n  } catch (error) {\n    var err =\n      error.response && error.response.data.detail\n        ? error.response.data.detail\n        : error.message || \"An error occurred while fetching providers\";\n\n    if (err === \"Given token not valid for any token type\") {\n      localStorage.removeItem(\"userInfoUnimedCare\");\n      document.location.href = \"/\";\n    }\n\n    dispatch({\n      type: PROVIDER_LIST_FAIL,\n      payload: err,\n    });\n\n    return { results: [] };\n  }\n};\n\n// Optimized get list providers for EditCaseScreen (includes services)\nexport const providersListEditCase = (page = \"0\") => async (dispatch, getState) => {\n  try {\n    // Dispatch request action\n    dispatch({\n      type: PROVIDER_LIST_REQUEST,\n    });\n\n    // Get user info from state\n    var {\n      userLogin: { userInfo },\n    } = getState();\n\n    // Configure request\n    const config = {\n      headers: {\n        \"Content-Type\": \"application/json\",\n        Authorization: `Bearer ${userInfo.access}`,\n      },\n    };\n\n    // Fetch provider data with services for EditCaseScreen\n    const { data } = await axios.get(\n      `/providers/?page=${page}&iseditcase=true`,\n      config\n    );\n\n    // Dispatch success action\n    dispatch({\n      type: PROVIDER_LIST_SUCCESS,\n      payload: data,\n    });\n\n    return data;\n  } catch (error) {\n    var err =\n      error.response && error.response.data.detail\n        ? error.response.data.detail\n        : error.message || \"An error occurred while fetching providers\";\n\n    if (err === \"Given token not valid for any token type\") {\n      localStorage.removeItem(\"userInfoUnimedCare\");\n      document.location.href = \"/\";\n    }\n\n    dispatch({\n      type: PROVIDER_LIST_FAIL,\n      payload: err,\n    });\n\n    return { providers: [] };\n  }\n};\n\n// Optimized get list providers for ProvidersMapScreen (map display)\nexport const providersListMapScreen =\n  (\n    page,\n    filterName = \"\",\n    filterType = \"\",\n    filterCity = \"\",\n    filterCountry = \"\",\n    range_max = \"\",\n    location_x = \"\",\n    location_y = \"\"\n  ) =>\n  async (dispatch, getState) => {\n    try {\n      // Dispatch request action\n      dispatch({\n        type: PROVIDER_LIST_REQUEST,\n      });\n\n      // Get user info from state\n      var {\n        userLogin: { userInfo },\n      } = getState();\n\n      // Configure request\n      const config = {\n        headers: {\n          \"Content-Type\": \"application/json\",\n          Authorization: `Bearer ${userInfo.access}`,\n        },\n        // Set a longer timeout specifically for this request\n        timeout: 60000, // 60 seconds timeout\n      };\n\n      console.log(\"Fetching optimized providers data for map screen...\");\n\n      // Create a promise with timeout\n      const fetchPromise = axios.get(\n        `/providers/?page=${page}&filtername=${filterName}&filtertype=${filterType}&filtercity=${filterCity}&filtercountry=${filterCountry}&rangemax=${range_max}&locationx=${location_x}&locationy=${location_y}&ismapscreen=true`,\n        config\n      );\n\n      // Set a timeout promise\n      const timeoutPromise = new Promise((_, reject) => {\n        setTimeout(() => {\n          reject(new Error(\"Request timed out after 60 seconds\"));\n        }, 60000);\n      });\n\n      // Race between fetch and timeout\n      const { data } = await Promise.race([fetchPromise, timeoutPromise]);\n\n      console.log(\"Optimized providers data fetched successfully!\");\n\n      // Dispatch success action\n      dispatch({\n        type: PROVIDER_LIST_SUCCESS,\n        payload: data,\n      });\n\n      return data; // Return data for Promise chaining\n    } catch (error) {\n      console.error(\"Error fetching providers for map screen:\", error);\n\n      // Handle token validation error\n      var err =\n        error.response && error.response.data.detail\n          ? error.response.data.detail\n          : (error.detail || error.message || \"Unknown error\");\n\n      if (err) {\n        if (err === \"Given token not valid for any token type\") {\n          localStorage.removeItem(\"userInfoUnimedCare\");\n          document.location.href = \"/\";\n        }\n      }\n\n      // Dispatch failure action\n      dispatch({\n        type: PROVIDER_LIST_FAIL,\n        payload: err,\n      });\n\n      // Return empty array as fallback data\n      return { results: [] };\n    }\n  };\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,KAAM,aAAa,CAC/B,OACEC,qBAAqB,CACrBC,qBAAqB,CACrBC,kBAAkB,CAClB;AACAC,oBAAoB,CACpBC,oBAAoB,CACpBC,iBAAiB,CACjB;AACAC,uBAAuB,CACvBC,uBAAuB,CACvBC,oBAAoB,CACpB;AACAC,uBAAuB,CACvBC,uBAAuB,CACvBC,oBAAoB,CACpB;AACAC,uBAAuB,CACvBC,uBAAuB,CACvBC,oBACA;AAAA,KACK,gCAAgC,CAEvC,MAAO,MAAM,CAAAC,cAAc,CAAGA,CAACC,EAAE,CAAEC,QAAQ,GAAK,MAAOC,QAAQ,CAAEC,QAAQ,GAAK,CAC5E,GAAI,CACFD,QAAQ,CAAC,CACPE,IAAI,CAAEX,uBACR,CAAC,CAAC,CACF,GAAI,CACFY,SAAS,CAAE,CAAEC,QAAS,CACxB,CAAC,CAAGH,QAAQ,CAAC,CAAC,CACd,KAAM,CAAAI,MAAM,CAAG,CACbC,OAAO,CAAE,CACP,cAAc,CAAE,kBAAkB,CAClCC,aAAa,CAAG,UAASH,QAAQ,CAACI,MAAO,EAC3C,CACF,CAAC,CACD,KAAM,CAAEC,IAAK,CAAC,CAAG,KAAM,CAAA5B,KAAK,CAAC6B,GAAG,CAC7B,qBAAoBZ,EAAG,GAAE,CAC1BC,QAAQ,CACRM,MACF,CAAC,CAEDL,QAAQ,CAAC,CACPE,IAAI,CAAEV,uBAAuB,CAC7BmB,OAAO,CAAEF,IACX,CAAC,CAAC,CACJ,CAAE,MAAOG,KAAK,CAAE,CACd,GAAI,CAAAC,GAAG,CACLD,KAAK,CAACE,QAAQ,EAAIF,KAAK,CAACE,QAAQ,CAACL,IAAI,CAACM,MAAM,CACxCH,KAAK,CAACE,QAAQ,CAACL,IAAI,CAACM,MAAM,CAC1BH,KAAK,CAACG,MAAM,CAClB,GAAIF,GAAG,CAAE,CACP,GAAIA,GAAG,GAAK,0CAA0C,CAAE,CACtDG,YAAY,CAACC,UAAU,CAAC,oBAAoB,CAAC,CAC7CC,QAAQ,CAACC,QAAQ,CAACC,IAAI,CAAG,GAAG,CAC9B,CACF,CACApB,QAAQ,CAAC,CACPE,IAAI,CAAET,oBAAoB,CAC1BkB,OAAO,CACLC,KAAK,CAACE,QAAQ,EAAIF,KAAK,CAACE,QAAQ,CAACL,IAAI,CAACM,MAAM,CACxCH,KAAK,CAACE,QAAQ,CAACL,IAAI,CAACM,MAAM,CAC1BH,KAAK,CAACG,MACd,CAAC,CAAC,CACJ,CACF,CAAC,CAED;AACA,MAAO,MAAM,CAAAM,cAAc,CAAIvB,EAAE,EAAK,MAAOE,QAAQ,CAAEC,QAAQ,GAAK,CAClE,GAAI,CACFD,QAAQ,CAAC,CACPE,IAAI,CAAER,uBACR,CAAC,CAAC,CACF,GAAI,CACFS,SAAS,CAAE,CAAEC,QAAS,CACxB,CAAC,CAAGH,QAAQ,CAAC,CAAC,CACd,KAAM,CAAAI,MAAM,CAAG,CACbC,OAAO,CAAE,CACP,cAAc,CAAE,qBAAqB,CACrCC,aAAa,CAAG,UAASH,QAAQ,CAACI,MAAO,EAC3C,CACF,CAAC,CACD,KAAM,CAAEC,IAAK,CAAC,CAAG,KAAM,CAAA5B,KAAK,CAACyC,MAAM,CAAE,qBAAoBxB,EAAG,GAAE,CAAEO,MAAM,CAAC,CAEvEL,QAAQ,CAAC,CACPE,IAAI,CAAEP,uBAAuB,CAC7BgB,OAAO,CAAEF,IACX,CAAC,CAAC,CACJ,CAAE,MAAOG,KAAK,CAAE,CACd,GAAI,CAAAC,GAAG,CACLD,KAAK,CAACE,QAAQ,EAAIF,KAAK,CAACE,QAAQ,CAACL,IAAI,CAACM,MAAM,CACxCH,KAAK,CAACE,QAAQ,CAACL,IAAI,CAACM,MAAM,CAC1BH,KAAK,CAACG,MAAM,CAClB,GAAIF,GAAG,CAAE,CACP,GAAIA,GAAG,GAAK,0CAA0C,CAAE,CACtDG,YAAY,CAACC,UAAU,CAAC,oBAAoB,CAAC,CAC7CC,QAAQ,CAACC,QAAQ,CAACC,IAAI,CAAG,GAAG,CAC9B,CACF,CACApB,QAAQ,CAAC,CACPE,IAAI,CAAEN,oBAAoB,CAC1Be,OAAO,CACLC,KAAK,CAACE,QAAQ,EAAIF,KAAK,CAACE,QAAQ,CAACL,IAAI,CAACM,MAAM,CACxCH,KAAK,CAACE,QAAQ,CAACL,IAAI,CAACM,MAAM,CAC1BH,KAAK,CAACG,MACd,CAAC,CAAC,CACJ,CACF,CAAC,CAED;AACA,MAAO,MAAM,CAAAQ,iBAAiB,CAAIxB,QAAQ,EAAK,MAAOC,QAAQ,CAAEC,QAAQ,GAAK,CAC3E,GAAI,CACFD,QAAQ,CAAC,CACPE,IAAI,CAAEjB,oBACR,CAAC,CAAC,CACF,GAAI,CACFkB,SAAS,CAAE,CAAEC,QAAS,CACxB,CAAC,CAAGH,QAAQ,CAAC,CAAC,CACd,KAAM,CAAAI,MAAM,CAAG,CACbC,OAAO,CAAE,CACP,cAAc,CAAE,kBAAkB,CAClCC,aAAa,CAAG,UAASH,QAAQ,CAACI,MAAO,EAC3C,CACF,CAAC,CACD,KAAM,CAAEC,IAAK,CAAC,CAAG,KAAM,CAAA5B,KAAK,CAAC2C,IAAI,CAC9B,iCAAgC,CACjCzB,QAAQ,CACRM,MACF,CAAC,CAEDL,QAAQ,CAAC,CACPE,IAAI,CAAEhB,oBAAoB,CAC1ByB,OAAO,CAAEF,IACX,CAAC,CAAC,CACJ,CAAE,MAAOG,KAAK,CAAE,CACd,GAAI,CAAAC,GAAG,CACLD,KAAK,CAACE,QAAQ,EAAIF,KAAK,CAACE,QAAQ,CAACL,IAAI,CAACM,MAAM,CACxCH,KAAK,CAACE,QAAQ,CAACL,IAAI,CAACM,MAAM,CAC1BH,KAAK,CAACG,MAAM,CAClB,GAAIF,GAAG,CAAE,CACP,GAAIA,GAAG,GAAK,0CAA0C,CAAE,CACtDG,YAAY,CAACC,UAAU,CAAC,oBAAoB,CAAC,CAC7CC,QAAQ,CAACC,QAAQ,CAACC,IAAI,CAAG,GAAG,CAC9B,CACF,CACApB,QAAQ,CAAC,CACPE,IAAI,CAAEf,iBAAiB,CACvBwB,OAAO,CACLC,KAAK,CAACE,QAAQ,EAAIF,KAAK,CAACE,QAAQ,CAACL,IAAI,CAACM,MAAM,CACxCH,KAAK,CAACE,QAAQ,CAACL,IAAI,CAACM,MAAM,CAC1BH,KAAK,CAACG,MACd,CAAC,CAAC,CACJ,CACF,CAAC,CAED;AACA,MAAO,MAAM,CAAAU,cAAc,CAAI3B,EAAE,EAAK,MAAOE,QAAQ,CAAEC,QAAQ,GAAK,CAClE,GAAI,CACFD,QAAQ,CAAC,CACPE,IAAI,CAAEd,uBACR,CAAC,CAAC,CACF,GAAI,CACFe,SAAS,CAAE,CAAEC,QAAS,CACxB,CAAC,CAAGH,QAAQ,CAAC,CAAC,CACd,KAAM,CAAAI,MAAM,CAAG,CACbC,OAAO,CAAE,CACP,cAAc,CAAE,kBAAkB,CAClCC,aAAa,CAAG,UAASH,QAAQ,CAACI,MAAO,EAC3C,CACF,CAAC,CACD,KAAM,CAAEC,IAAK,CAAC,CAAG,KAAM,CAAA5B,KAAK,CAAC6C,GAAG,CAAE,qBAAoB5B,EAAG,GAAE,CAAEO,MAAM,CAAC,CAEpEL,QAAQ,CAAC,CACPE,IAAI,CAAEb,uBAAuB,CAC7BsB,OAAO,CAAEF,IACX,CAAC,CAAC,CACJ,CAAE,MAAOG,KAAK,CAAE,CACd,GAAI,CAAAC,GAAG,CACLD,KAAK,CAACE,QAAQ,EAAIF,KAAK,CAACE,QAAQ,CAACL,IAAI,CAACM,MAAM,CACxCH,KAAK,CAACE,QAAQ,CAACL,IAAI,CAACM,MAAM,CAC1BH,KAAK,CAACG,MAAM,CAClB,GAAIF,GAAG,CAAE,CACP,GAAIA,GAAG,GAAK,0CAA0C,CAAE,CACtDG,YAAY,CAACC,UAAU,CAAC,oBAAoB,CAAC,CAC7CC,QAAQ,CAACC,QAAQ,CAACC,IAAI,CAAG,GAAG,CAC9B,CACF,CACApB,QAAQ,CAAC,CACPE,IAAI,CAAEZ,oBAAoB,CAC1BqB,OAAO,CACLC,KAAK,CAACE,QAAQ,EAAIF,KAAK,CAACE,QAAQ,CAACL,IAAI,CAACM,MAAM,CACxCH,KAAK,CAACE,QAAQ,CAACL,IAAI,CAACM,MAAM,CAC1BH,KAAK,CAACG,MACd,CAAC,CAAC,CACJ,CACF,CAAC,CAED;AACA,MAAO,MAAM,CAAAY,aAAa,CACxB,QAAAA,CACEC,IAAI,KACJ,CAAAC,UAAU,CAAAC,SAAA,CAAAC,MAAA,IAAAD,SAAA,MAAAE,SAAA,CAAAF,SAAA,IAAG,EAAE,IACf,CAAAG,UAAU,CAAAH,SAAA,CAAAC,MAAA,IAAAD,SAAA,MAAAE,SAAA,CAAAF,SAAA,IAAG,EAAE,IACf,CAAAI,UAAU,CAAAJ,SAAA,CAAAC,MAAA,IAAAD,SAAA,MAAAE,SAAA,CAAAF,SAAA,IAAG,EAAE,IACf,CAAAK,aAAa,CAAAL,SAAA,CAAAC,MAAA,IAAAD,SAAA,MAAAE,SAAA,CAAAF,SAAA,IAAG,EAAE,IAClB,CAAAM,SAAS,CAAAN,SAAA,CAAAC,MAAA,IAAAD,SAAA,MAAAE,SAAA,CAAAF,SAAA,IAAG,EAAE,IACd,CAAAO,UAAU,CAAAP,SAAA,CAAAC,MAAA,IAAAD,SAAA,MAAAE,SAAA,CAAAF,SAAA,IAAG,EAAE,IACf,CAAAQ,UAAU,CAAAR,SAAA,CAAAC,MAAA,IAAAD,SAAA,MAAAE,SAAA,CAAAF,SAAA,IAAG,EAAE,OAEjB,OAAO9B,QAAQ,CAAEC,QAAQ,GAAK,CAC5B,GAAI,CACF;AACAD,QAAQ,CAAC,CACPE,IAAI,CAAEpB,qBACR,CAAC,CAAC,CAEF;AACA,GAAI,CACFqB,SAAS,CAAE,CAAEC,QAAS,CACxB,CAAC,CAAGH,QAAQ,CAAC,CAAC,CAEd;AACA,KAAM,CAAAI,MAAM,CAAG,CACbC,OAAO,CAAE,CACP,cAAc,CAAE,kBAAkB,CAClCC,aAAa,CAAG,UAASH,QAAQ,CAACI,MAAO,EAC3C,CAAC,CACD;AACA+B,OAAO,CAAE,KAAO;AAClB,CAAC,CAEDC,OAAO,CAACC,GAAG,CAAC,4BAA4B,CAAC,CAEzC;AACA,KAAM,CAAAC,YAAY,CAAG7D,KAAK,CAAC6C,GAAG,CAC3B,oBAAmBE,IAAK,eAAcC,UAAW,eAAcI,UAAW,eAAcC,UAAW,kBAAiBC,aAAc,aAAYC,SAAU,cAAaC,UAAW,cAAaC,UAAW,EAAC,CAC1MjC,MACF,CAAC,CAED;AACA,KAAM,CAAAsC,cAAc,CAAG,GAAI,CAAAC,OAAO,CAAC,CAACC,CAAC,CAAEC,MAAM,GAAK,CAChDC,UAAU,CAAC,IAAM,CACfD,MAAM,CAAC,GAAI,CAAAE,KAAK,CAAC,oCAAoC,CAAC,CAAC,CACzD,CAAC,CAAE,KAAK,CAAC,CACX,CAAC,CAAC,CAEF;AACA,KAAM,CAAEvC,IAAK,CAAC,CAAG,KAAM,CAAAmC,OAAO,CAACK,IAAI,CAAC,CAACP,YAAY,CAAEC,cAAc,CAAC,CAAC,CAEnEH,OAAO,CAACC,GAAG,CAAC,sCAAsC,CAAC,CAEnD;AACAzC,QAAQ,CAAC,CACPE,IAAI,CAAEnB,qBAAqB,CAC3B4B,OAAO,CAAEF,IACX,CAAC,CAAC,CAEF,MAAO,CAAAA,IAAI,CAAE;AACf,CAAE,MAAOG,KAAK,CAAE,CACd4B,OAAO,CAAC5B,KAAK,CAAC,2BAA2B,CAAEA,KAAK,CAAC,CAEjD;AACA,GAAI,CAAAC,GAAG,CACLD,KAAK,CAACE,QAAQ,EAAIF,KAAK,CAACE,QAAQ,CAACL,IAAI,CAACM,MAAM,CACxCH,KAAK,CAACE,QAAQ,CAACL,IAAI,CAACM,MAAM,CACzBH,KAAK,CAACG,MAAM,EAAIH,KAAK,CAACsC,OAAO,EAAI,eAAgB,CAExD,GAAIrC,GAAG,CAAE,CACP,GAAIA,GAAG,GAAK,0CAA0C,CAAE,CACtDG,YAAY,CAACC,UAAU,CAAC,oBAAoB,CAAC,CAC7CC,QAAQ,CAACC,QAAQ,CAACC,IAAI,CAAG,GAAG,CAC9B,CACF,CAEA;AACApB,QAAQ,CAAC,CACPE,IAAI,CAAElB,kBAAkB,CACxB2B,OAAO,CAAEE,GACX,CAAC,CAAC,CAEF;AACA,MAAO,CAAEsC,OAAO,CAAE,EAAG,CAAC,CACxB,CACF,CAAC,GAEH;AACA,MAAO,MAAM,CAAAC,sBAAsB,CAAG,QAAAA,CAAA,KAAC,CAAAxB,IAAI,CAAAE,SAAA,CAAAC,MAAA,IAAAD,SAAA,MAAAE,SAAA,CAAAF,SAAA,IAAG,GAAG,OAAK,OAAO9B,QAAQ,CAAEC,QAAQ,GAAK,CAClF,GAAI,CACF;AACAD,QAAQ,CAAC,CACPE,IAAI,CAAEpB,qBACR,CAAC,CAAC,CAEF;AACA,GAAI,CACFqB,SAAS,CAAE,CAAEC,QAAS,CACxB,CAAC,CAAGH,QAAQ,CAAC,CAAC,CAEd;AACA,KAAM,CAAAI,MAAM,CAAG,CACbC,OAAO,CAAE,CACP,cAAc,CAAE,kBAAkB,CAClCC,aAAa,CAAG,UAASH,QAAQ,CAACI,MAAO,EAC3C,CACF,CAAC,CAED;AACA,KAAM,CAAEC,IAAK,CAAC,CAAG,KAAM,CAAA5B,KAAK,CAAC6C,GAAG,CAC7B,oBAAmBE,IAAK,mBAAkB,CAC3CvB,MACF,CAAC,CAED;AACAL,QAAQ,CAAC,CACPE,IAAI,CAAEnB,qBAAqB,CAC3B4B,OAAO,CAAEF,IACX,CAAC,CAAC,CAEF,MAAO,CAAAA,IAAI,CACb,CAAE,MAAOG,KAAK,CAAE,CACd,GAAI,CAAAC,GAAG,CACLD,KAAK,CAACE,QAAQ,EAAIF,KAAK,CAACE,QAAQ,CAACL,IAAI,CAACM,MAAM,CACxCH,KAAK,CAACE,QAAQ,CAACL,IAAI,CAACM,MAAM,CAC1BH,KAAK,CAACsC,OAAO,EAAI,4CAA4C,CAEnE,GAAIrC,GAAG,GAAK,0CAA0C,CAAE,CACtDG,YAAY,CAACC,UAAU,CAAC,oBAAoB,CAAC,CAC7CC,QAAQ,CAACC,QAAQ,CAACC,IAAI,CAAG,GAAG,CAC9B,CAEApB,QAAQ,CAAC,CACPE,IAAI,CAAElB,kBAAkB,CACxB2B,OAAO,CAAEE,GACX,CAAC,CAAC,CAEF,MAAO,CAAEsC,OAAO,CAAE,EAAG,CAAC,CACxB,CACF,CAAC,GAED;AACA,MAAO,MAAM,CAAAE,qBAAqB,CAAG,QAAAA,CAAA,KAAC,CAAAzB,IAAI,CAAAE,SAAA,CAAAC,MAAA,IAAAD,SAAA,MAAAE,SAAA,CAAAF,SAAA,IAAG,GAAG,OAAK,OAAO9B,QAAQ,CAAEC,QAAQ,GAAK,CACjF,GAAI,CACF;AACAD,QAAQ,CAAC,CACPE,IAAI,CAAEpB,qBACR,CAAC,CAAC,CAEF;AACA,GAAI,CACFqB,SAAS,CAAE,CAAEC,QAAS,CACxB,CAAC,CAAGH,QAAQ,CAAC,CAAC,CAEd;AACA,KAAM,CAAAI,MAAM,CAAG,CACbC,OAAO,CAAE,CACP,cAAc,CAAE,kBAAkB,CAClCC,aAAa,CAAG,UAASH,QAAQ,CAACI,MAAO,EAC3C,CACF,CAAC,CAED;AACA,KAAM,CAAEC,IAAK,CAAC,CAAG,KAAM,CAAA5B,KAAK,CAAC6C,GAAG,CAC7B,oBAAmBE,IAAK,kBAAiB,CAC1CvB,MACF,CAAC,CAED;AACAL,QAAQ,CAAC,CACPE,IAAI,CAAEnB,qBAAqB,CAC3B4B,OAAO,CAAEF,IACX,CAAC,CAAC,CAEF,MAAO,CAAAA,IAAI,CACb,CAAE,MAAOG,KAAK,CAAE,CACd,GAAI,CAAAC,GAAG,CACLD,KAAK,CAACE,QAAQ,EAAIF,KAAK,CAACE,QAAQ,CAACL,IAAI,CAACM,MAAM,CACxCH,KAAK,CAACE,QAAQ,CAACL,IAAI,CAACM,MAAM,CAC1BH,KAAK,CAACsC,OAAO,EAAI,4CAA4C,CAEnE,GAAIrC,GAAG,GAAK,0CAA0C,CAAE,CACtDG,YAAY,CAACC,UAAU,CAAC,oBAAoB,CAAC,CAC7CC,QAAQ,CAACC,QAAQ,CAACC,IAAI,CAAG,GAAG,CAC9B,CAEApB,QAAQ,CAAC,CACPE,IAAI,CAAElB,kBAAkB,CACxB2B,OAAO,CAAEE,GACX,CAAC,CAAC,CAEF,MAAO,CAAEyC,SAAS,CAAE,EAAG,CAAC,CAC1B,CACF,CAAC,GAED;AACA,MAAO,MAAM,CAAAC,sBAAsB,CACjC,QAAAA,CACE3B,IAAI,KACJ,CAAAC,UAAU,CAAAC,SAAA,CAAAC,MAAA,IAAAD,SAAA,MAAAE,SAAA,CAAAF,SAAA,IAAG,EAAE,IACf,CAAAG,UAAU,CAAAH,SAAA,CAAAC,MAAA,IAAAD,SAAA,MAAAE,SAAA,CAAAF,SAAA,IAAG,EAAE,IACf,CAAAI,UAAU,CAAAJ,SAAA,CAAAC,MAAA,IAAAD,SAAA,MAAAE,SAAA,CAAAF,SAAA,IAAG,EAAE,IACf,CAAAK,aAAa,CAAAL,SAAA,CAAAC,MAAA,IAAAD,SAAA,MAAAE,SAAA,CAAAF,SAAA,IAAG,EAAE,IAClB,CAAAM,SAAS,CAAAN,SAAA,CAAAC,MAAA,IAAAD,SAAA,MAAAE,SAAA,CAAAF,SAAA,IAAG,EAAE,IACd,CAAAO,UAAU,CAAAP,SAAA,CAAAC,MAAA,IAAAD,SAAA,MAAAE,SAAA,CAAAF,SAAA,IAAG,EAAE,IACf,CAAAQ,UAAU,CAAAR,SAAA,CAAAC,MAAA,IAAAD,SAAA,MAAAE,SAAA,CAAAF,SAAA,IAAG,EAAE,OAEjB,OAAO9B,QAAQ,CAAEC,QAAQ,GAAK,CAC5B,GAAI,CACF;AACAD,QAAQ,CAAC,CACPE,IAAI,CAAEpB,qBACR,CAAC,CAAC,CAEF;AACA,GAAI,CACFqB,SAAS,CAAE,CAAEC,QAAS,CACxB,CAAC,CAAGH,QAAQ,CAAC,CAAC,CAEd;AACA,KAAM,CAAAI,MAAM,CAAG,CACbC,OAAO,CAAE,CACP,cAAc,CAAE,kBAAkB,CAClCC,aAAa,CAAG,UAASH,QAAQ,CAACI,MAAO,EAC3C,CAAC,CACD;AACA+B,OAAO,CAAE,KAAO;AAClB,CAAC,CAEDC,OAAO,CAACC,GAAG,CAAC,qDAAqD,CAAC,CAElE;AACA,KAAM,CAAAC,YAAY,CAAG7D,KAAK,CAAC6C,GAAG,CAC3B,oBAAmBE,IAAK,eAAcC,UAAW,eAAcI,UAAW,eAAcC,UAAW,kBAAiBC,aAAc,aAAYC,SAAU,cAAaC,UAAW,cAAaC,UAAW,mBAAkB,CAC3NjC,MACF,CAAC,CAED;AACA,KAAM,CAAAsC,cAAc,CAAG,GAAI,CAAAC,OAAO,CAAC,CAACC,CAAC,CAAEC,MAAM,GAAK,CAChDC,UAAU,CAAC,IAAM,CACfD,MAAM,CAAC,GAAI,CAAAE,KAAK,CAAC,oCAAoC,CAAC,CAAC,CACzD,CAAC,CAAE,KAAK,CAAC,CACX,CAAC,CAAC,CAEF;AACA,KAAM,CAAEvC,IAAK,CAAC,CAAG,KAAM,CAAAmC,OAAO,CAACK,IAAI,CAAC,CAACP,YAAY,CAAEC,cAAc,CAAC,CAAC,CAEnEH,OAAO,CAACC,GAAG,CAAC,gDAAgD,CAAC,CAE7D;AACAzC,QAAQ,CAAC,CACPE,IAAI,CAAEnB,qBAAqB,CAC3B4B,OAAO,CAAEF,IACX,CAAC,CAAC,CAEF,MAAO,CAAAA,IAAI,CAAE;AACf,CAAE,MAAOG,KAAK,CAAE,CACd4B,OAAO,CAAC5B,KAAK,CAAC,0CAA0C,CAAEA,KAAK,CAAC,CAEhE;AACA,GAAI,CAAAC,GAAG,CACLD,KAAK,CAACE,QAAQ,EAAIF,KAAK,CAACE,QAAQ,CAACL,IAAI,CAACM,MAAM,CACxCH,KAAK,CAACE,QAAQ,CAACL,IAAI,CAACM,MAAM,CACzBH,KAAK,CAACG,MAAM,EAAIH,KAAK,CAACsC,OAAO,EAAI,eAAgB,CAExD,GAAIrC,GAAG,CAAE,CACP,GAAIA,GAAG,GAAK,0CAA0C,CAAE,CACtDG,YAAY,CAACC,UAAU,CAAC,oBAAoB,CAAC,CAC7CC,QAAQ,CAACC,QAAQ,CAACC,IAAI,CAAG,GAAG,CAC9B,CACF,CAEA;AACApB,QAAQ,CAAC,CACPE,IAAI,CAAElB,kBAAkB,CACxB2B,OAAO,CAAEE,GACX,CAAC,CAAC,CAEF;AACA,MAAO,CAAEsC,OAAO,CAAE,EAAG,CAAC,CACxB,CACF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}