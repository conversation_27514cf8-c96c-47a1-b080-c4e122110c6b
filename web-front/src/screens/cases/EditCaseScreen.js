import React, { useEffect, useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import {
  useLocation,
  useNavigate,
  useParams,
  useSearchParams,
} from "react-router-dom";
import DefaultLayout from "../../layouts/DefaultLayout";
import addreactionface from "../../images/icon/add_reaction.png";
import { toast } from "react-toastify";
import { providersListEditCase } from "../../redux/actions/providerActions";
import {
  addNewCase,
  detailCase,
  updateCase,
} from "../../redux/actions/caseActions";
import LoadingSpinner from "../../components/LoadingSpinner";
import GoogleComponent from "react-google-autocomplete";

import Select from "react-select";

import { useDropzone } from "react-dropzone";
import { insurancesListDashboard } from "../../redux/actions/insuranceActions";
import { coordinatorsListDashboard } from "../../redux/actions/userActions";
import { COUNTRIES, CURRENCYITEMS } from "../../constants";

const STEPSLIST = [
  {
    index: 0,
    title: "General Information",
    description:
      "Please enter the general information about the patient and the case.",
  },
  {
    index: 1,
    title: "Coordination Details",
    description:
      "Provide information about the initial coordination & appointment details for this case.",
  },
  {
    index: 2,
    title: "Medical Reports",
    description: "Upload any initial medical reports related to the case.",
  },
  {
    index: 3,
    title: "Invoices",
    description:
      "If there are any initial invoices related to the case, please provide the details and upload the documents.",
  },
  {
    index: 4,
    title: "Insurance Authorization",
    description:
      "Please provide the details of the insurance authorization for this case, and upload any relevant documents.",
  },
  {
    index: 5,
    title: "Finish",
    description: "You can go back to any step to make changes.",
  },
];

const thumbsContainer = {
  display: "flex",
  flexDirection: "row",
  flexWrap: "wrap",
  marginTop: 16,
};

function EditCaseScreen() {
  const navigate = useNavigate();
  const location = useLocation();
  const dispatch = useDispatch();
  let { id } = useParams();
  const [searchParams] = useSearchParams();
  const section = searchParams.get("section") || 0;

  //
  const [firstName, setFirstName] = useState("");
  const [firstNameError, setFirstNameError] = useState("");

  const [lastName, setLastName] = useState("");
  const [lastNameError, setLastNameError] = useState("");

  const [email, setEmail] = useState("");
  const [emailError, setEmailError] = useState("");

  const [birthDate, setBirthDate] = useState("");
  const [birthDateError, setBirthDateError] = useState("");

  const [phone, setPhone] = useState("");
  const [phoneError, setPhoneError] = useState("");

  const [address, setAddress] = useState("");
  const [addressError, setAddressError] = useState("");

  const [city, setCity] = useState("");
  const [cityError, setCityError] = useState("");

  const [country, setCountry] = useState("");
  const [countryError, setCountryError] = useState("");
  //
  const [coordinator, setCoordinator] = useState("");
  const [coordinatorId, setCoordinatorId] = useState("");
  const [coordinatorError, setCoordinatorError] = useState("");

  const [providerServices, setProviderServices] = useState([]);
  const [providerMultiSelect, setProviderMultiSelect] = useState([]);
  const [providerMultiSelectDelete, setProviderMultiSelectDelete] = useState(
    []
  );
  const [assistanceMultiSelect, setAssistanceMultiSelect] = useState([]);
  const [assistanceMultiSelectDelete, setAssistanceMultiSelectDelete] =
    useState([]);
  const [providerMultiSelectLast, setProviderMultiSelectLast] = useState([]);
  const [assistanceMultiSelectLast, setAssistanceMultiSelectLast] = useState(
    []
  );

  const [providerService, setProviderService] = useState("");
  const [providerServiceError, setProviderServiceError] = useState("");

  const [caseDate, setCaseDate] = useState(
    new Date().toISOString().split("T")[0]
  );
  const [caseDateError, setCaseDateError] = useState("");

  const [caseType, setCaseType] = useState("");
  const [caseTypeError, setCaseTypeError] = useState("");

  const [caseTypeItem, setCaseTypeItem] = useState("");
  const [caseTypeItemError, setCaseTypeItemError] = useState("");

  const [caseDescription, setCaseDescription] = useState("");
  const [caseDescriptionError, setCaseDescriptionError] = useState("");

  const [isPay, setIsPay] = useState(false);

  const [currencyCode, setCurrencyCode] = useState("");
  const [currencyCodeError, setCurrencyCodeError] = useState("");

  const [priceTotal, setPriceTotal] = useState(0);
  const [priceTotalError, setPriceTotalError] = useState("");
  //
  const [coordinatStatus, setCoordinatStatus] = useState("");
  const [coordinatStatusError, setCoordinatStatusError] = useState("");

  const [coordinatStatusList, setCoordinatStatusList] = useState([]);
  const [coordinatStatusListError, setCoordinatStatusListError] = useState("");

  const [appointmentDate, setAppointmentDate] = useState("");
  const [appointmentDateError, setAppointmentDateError] = useState("");

  const [startDate, setStartDate] = useState("");
  const [startDateError, setStartDateError] = useState("");

  const [endDate, setEndDate] = useState("");
  const [endDateError, setEndDateError] = useState("");

  const [serviceLocation, setServiceLocation] = useState("");
  const [serviceLocationError, setServiceLocationError] = useState("");
  //
  const [providerName, setProviderName] = useState("");
  const [providerNameError, setProviderNameError] = useState("");

  const [providerDate, setProviderDate] = useState("");
  const [providerDateError, setProviderDateError] = useState("");

  const [providerPhone, setProviderPhone] = useState("");
  const [providerPhoneError, setProviderPhoneError] = useState("");

  const [providerEmail, setProviderEmail] = useState("");
  const [providerEmailError, setProviderEmailError] = useState("");

  const [providerAddress, setProviderAddress] = useState("");
  const [providerAddressError, setProviderAddressError] = useState("");
  //
  const [invoiceNumber, setInvoiceNumber] = useState("");
  const [invoiceNumberError, setInvoiceNumberError] = useState("");

  const [dateIssued, setDateIssued] = useState("");
  const [dateIssuedError, setDateIssuedError] = useState("");

  const [amount, setAmount] = useState(0);
  const [amountError, setAmountError] = useState("");
  //
  const [insuranceCompany, setInsuranceCompany] = useState("");
  const [insuranceCompanyError, setInsuranceCompanyError] = useState("");

  const [insuranceNumber, setInsuranceNumber] = useState("");
  const [insuranceNumberError, setInsuranceNumberError] = useState("");

  const [policyNumber, setPolicyNumber] = useState("");
  const [policyNumberError, setPolicyNumberError] = useState("");

  const [initialStatus, setInitialStatus] = useState("");
  const [initialStatusError, setInitialStatusError] = useState("");

  // fiels deleted
  const [fileDeleted, setFileDeleted] = useState([]);
  const [itemsInitialMedicalReports, setItemsInitialMedicalReports] = useState(
    []
  );
  const [itemsUploadInvoice, setItemsUploadInvoice] = useState([]);
  const [
    itemsUploadAuthorizationDocuments,
    setItemsUploadAuthorizationDocuments,
  ] = useState([]);

  // fils
  // initialMedicalReports
  const [filesInitialMedicalReports, setFilesInitialMedicalReports] = useState(
    []
  );
  const {
    getRootProps: getRootPropsInitialMedical,
    getInputProps: getInputPropsInitialMedical,
  } = useDropzone({
    accept: {
      "*": [],
    },
    onDrop: (acceptedFiles) => {
      setFilesInitialMedicalReports((prevFiles) => [
        ...prevFiles,
        ...acceptedFiles.map((file) =>
          Object.assign(file, {
            preview: URL.createObjectURL(file),
          })
        ),
      ]);
    },
  });

  useEffect(() => {
    return () =>
      filesInitialMedicalReports.forEach((file) =>
        URL.revokeObjectURL(file.preview)
      );
  }, []);

  // Upload Invoice
  const [filesUploadInvoice, setFilesUploadInvoice] = useState([]);
  const {
    getRootProps: getRootPropsUploadInvoice,
    getInputProps: getInputPropsUploadInvoice,
  } = useDropzone({
    accept: {
      "*": [],
    },
    onDrop: (acceptedFiles) => {
      setFilesUploadInvoice((prevFiles) => [
        ...prevFiles,
        ...acceptedFiles.map((file) =>
          Object.assign(file, {
            preview: URL.createObjectURL(file),
          })
        ),
      ]);
    },
  });

  useEffect(() => {
    return () =>
      filesUploadInvoice.forEach((file) => URL.revokeObjectURL(file.preview));
  }, []);
  // Upload Authorization Documents
  const [
    filesUploadAuthorizationDocuments,
    setFilesUploadAuthorizationDocuments,
  ] = useState([]);
  const {
    getRootProps: getRootPropsUploadAuthorizationDocuments,
    getInputProps: getInputPropsUploadAuthorizationDocuments,
  } = useDropzone({
    accept: {
      "*": [],
    },
    onDrop: (acceptedFiles) => {
      setFilesUploadAuthorizationDocuments((prevFiles) => [
        ...prevFiles,
        ...acceptedFiles.map((file) =>
          Object.assign(file, {
            preview: URL.createObjectURL(file),
          })
        ),
      ]);
    },
  });

  useEffect(() => {
    return () =>
      filesUploadAuthorizationDocuments.forEach((file) =>
        URL.revokeObjectURL(file.preview)
      );
  }, []);

  // Configure react-dropzone

  //

  const [stepSelect, setStepSelect] = useState(parseInt(section) ?? 0);
  const [isLoading, setIsLoading] = useState(true);
  const [shouldNavigateToFinalStep, setShouldNavigateToFinalStep] = useState(false);

  const userLogin = useSelector((state) => state.userLogin);
  const { userInfo } = userLogin;

  const listProviders = useSelector((state) => state.providerList);
  const { providers, loadingProviders, errorProviders } = listProviders;

  // Debug log when providers data changes
  useEffect(() => {
    if (providers && providers.length > 0) {
      console.log("Providers data loaded successfully:", providers.length);
    }
  }, [providers]);

  const listInsurances = useSelector((state) => state.insuranceList);
  const { insurances, loadingInsurances, errorInsurances } = listInsurances;

  const caseDetail = useSelector((state) => state.detailCase);
  const { loadingCaseInfo, errorCaseInfo, successCaseInfo, caseInfo } =
    caseDetail;

  const listCoordinators = useSelector((state) => state.coordinatorsList);
  const { coordinators, loadingCoordinators, errorCoordinators } =
    listCoordinators;

  // Update coordinator when coordinators are loaded
  useEffect(() => {
    console.log("Coordinator useEffect triggered");

    if (coordinators && coordinators.length > 0 && coordinatorId) {
      console.log("Trying to find coordinator with ID:", coordinatorId);

      // Try to find coordinator by ID (as string to ensure type matching)
      const foundCoordinator = coordinators.find(
        (item) => String(item.id) === String(coordinatorId)
      );

      if (foundCoordinator) {
        console.log("Found coordinator:", foundCoordinator.full_name);
        // Set the coordinator with a slight delay to ensure the UI updates
        setTimeout(() => {
          setCoordinator({
            value: foundCoordinator.id,
            label: foundCoordinator.full_name,
          });
          // Force a re-render by updating the loading state
          setIsLoading(false);
        }, 100);
      } else {
        console.log("Coordinator not found in the list");
        // If coordinator not found, try to find it by name
        const coordinatorById = coordinators.find(
          (item) => item.id === coordinatorId
        );
        if (coordinatorById) {
          console.log(
            "Found coordinator by direct ID comparison:",
            coordinatorById.full_name
          );
          setCoordinator({
            value: coordinatorById.id,
            label: coordinatorById.full_name,
          });
        }
      }
    }
  }, [coordinators, coordinatorId]);

  const caseUpdate = useSelector((state) => state.updateCase);
  const { loadingCaseUpdate, errorCaseUpdate, successCaseUpdate } = caseUpdate;

  const redirect = "/";

  useEffect(() => {
    if (!userInfo) {
      navigate(redirect);
    } else {
      // Set loading state to true when starting to fetch data
      setIsLoading(true);

      // Load all required data at once with optimized actions
      dispatch(coordinatorsListDashboard("0"));
      dispatch(providersListEditCase("0"));
      dispatch(insurancesListDashboard("0"));
      dispatch(detailCase(id));

      // Set a maximum timeout for the loading indicator (30 seconds) as a fallback
      const timeoutId = setTimeout(() => {
        setIsLoading(false);
        console.log("Maximum loading time reached, hiding loading indicator");
      }, 6000);

      // Clean up the timeout when the component unmounts
      return () => clearTimeout(timeoutId);
    }
  }, [navigate, userInfo, dispatch, id]);

  useEffect(() => {
    if (successCaseUpdate) {
      if (shouldNavigateToFinalStep) {
        setStepSelect(5);
        setShouldNavigateToFinalStep(false); // Reset the flag
      }
      setIsLoading(false);
    }
  }, [successCaseUpdate, shouldNavigateToFinalStep]);

  // Set loading state when case update is in progress
  useEffect(() => {
    if (loadingCaseUpdate) {
      setIsLoading(true);
    }
  }, [loadingCaseUpdate]);

  // Update loading state based on data loading status
  useEffect(() => {
    // Check if essential data is loaded
    if (
      !loadingProviders &&
      !loadingCaseInfo &&
      providers &&
      providers.length > 0 &&
      caseInfo
    ) {
      // Hide loading indicator as soon as we have the essential data
      setIsLoading(false);
    } else if (loadingCaseUpdate) {
      // Show loading during case update
      setIsLoading(true);
    }
  }, [
    loadingProviders,
    loadingCaseInfo,
    loadingCaseUpdate,
    providers,
    caseInfo,
  ]);

  useEffect(() => {
    // Only proceed if caseInfo is available
    if (caseInfo !== undefined && caseInfo !== null) {
      if (caseInfo.patient) {
        setFirstName(caseInfo.patient.first_name ?? "");
        setLastName(caseInfo.patient.last_name ?? "");
        setBirthDate(caseInfo.patient.birth_day ?? "");
        setPhone(caseInfo.patient.patient_phone ?? "");
        setEmail(caseInfo.patient.patient_email ?? "");
        setAddress(caseInfo.patient.patient_address ?? "");

        const patientCountry = caseInfo.patient.patient_country ?? "";
        const foundCountry = COUNTRIES.find(
          (option) => option.title === patientCountry
        );

        if (foundCountry) {
          setCountry({
            value: foundCountry.title,
            label: (
              <div className="flex flex-row items-center">
                <span className="mr-2">{foundCountry.icon}</span>
                <span>{foundCountry.title}</span>
              </div>
            ),
          });
        } else {
          setCountry("");
        }

        setCity(caseInfo.patient.patient_city ?? "");
      }

      const patientCurrency = caseInfo.currency_price ?? "";

      const foundCurrency = CURRENCYITEMS?.find(
        (option) => option.code === patientCurrency
      );

      if (foundCurrency) {
        setCurrencyCode({
          value: foundCurrency.code,
          label:
            foundCurrency.name !== ""
              ? foundCurrency.name + " (" + foundCurrency.code + ") " || ""
              : "",
        });
      } else {
        setCurrencyCode("");
      }

      setIsPay(caseInfo.is_pay);
      setPriceTotal(caseInfo.price_tatal ?? 0);
      // Store coordinator ID for later use
      if (caseInfo.coordinator_user) {
        const initialCoordinator = caseInfo.coordinator_user?.id ?? "";
        console.log(
          "Setting coordinator ID from caseInfo:",
          initialCoordinator
        );
        console.log(
          "Coordinator user from caseInfo:",
          caseInfo.coordinator_user
        );

        // Set coordinator ID with a slight delay to ensure it's properly updated
        setTimeout(() => {
          setCoordinatorId(initialCoordinator);
          console.log("CoordinatorId has been set to:", initialCoordinator);
        }, 50);
      }
      setCaseDate(caseInfo.case_date ?? "");
      setCaseType(caseInfo.case_type ?? "");
      setCaseDescription(caseInfo.case_description ?? "");
      //
      const statuses =
        caseInfo?.case_status?.map((status) => status?.status_coordination) ||
        []; // Default to an empty array if case_status is undefined or not an array

      setCoordinatStatusList(statuses);

      //
      setCoordinatStatus(caseInfo.status_coordination ?? "");
      setAppointmentDate(caseInfo.appointment_date ?? "");
      setStartDate(caseInfo.start_date ?? "");
      setEndDate(caseInfo.end_date ?? "");
      setCaseTypeItem(caseInfo.case_type_item ?? "");
      setServiceLocation(caseInfo.service_location ?? "");
      if (caseInfo.provider) {
        var initialProvider = caseInfo.provider?.id ?? "";
        const foundProvider = providers?.find(
          (item) => item.id === initialProvider
        );
        if (foundProvider) {
          setProviderName({
            value: foundProvider.id,
            label: foundProvider.full_name,
          });
        } else {
          setProviderName("");
        }
      }
      if (caseInfo.provider_services) {
        setProviderMultiSelectLast(caseInfo.provider_services ?? []);
      }

      if (caseInfo.assistance_services) {
        setAssistanceMultiSelectLast(caseInfo.assistance_services ?? []);
      }
      //
      setItemsInitialMedicalReports([]);
      if (caseInfo.medical_reports) {
        setItemsInitialMedicalReports(caseInfo.medical_reports);
      }
      //
      setInvoiceNumber(caseInfo.invoice_number ?? "");
      setDateIssued(caseInfo.date_issued ?? "");
      setAmount(caseInfo.invoice_amount ?? 0);
      setItemsUploadInvoice([]);
      if (caseInfo.upload_invoices) {
        setItemsUploadInvoice(caseInfo.upload_invoices);
      }
      //
      if (caseInfo.assurance) {
        var initialInsurance = caseInfo.assurance?.id ?? "";

        var foundInsurance = insurances?.find(
          (item) => item.id === initialInsurance
        );

        if (foundInsurance) {
          console.log("here 2");
          setInsuranceCompany({
            value: foundInsurance.id,
            label: foundInsurance.assurance_name || "",
          });
        } else {
          console.log("here 3");
          setInsuranceCompany({
            value: "",
            label: "",
          });
        }
      }
      setPolicyNumber(caseInfo.policy_number ?? "");
      setInsuranceNumber(caseInfo.assurance_number ?? "");
      setInitialStatus(caseInfo.assurance_status ?? "");
      setItemsUploadAuthorizationDocuments([]);
      if (caseInfo.upload_authorization) {
        setItemsUploadAuthorizationDocuments(caseInfo.upload_authorization);
      }
      //
    }
  }, [caseInfo]);

  // Function to update case without advancing to next step
  const handleUpdateCurrentStep = async () => {
    try {
      let isValid = true;

      // Step-specific validation based on current step
      if (stepSelect === 0) {
        // Step 1: General Information validation
        setFirstNameError("");
        setLastNameError("");
        setPhoneError("");
        setEmailError("");
        setAddressError("");
        setCityError("");
        setCountryError("");
        setCaseDateError("");
        setCaseTypeError("");
        setCaseDescriptionError("");
        setPriceTotalError("");
        setCurrencyCodeError("");
        setCoordinatorError("");
        setBirthDateError("");

        // Required field validations for Step 1
        if (!firstName || firstName.trim() === "") {
          setFirstNameError("First name is required.");
          isValid = false;
        }

        if (!lastName || lastName.trim() === "") {
          setLastNameError("Last name is required.");
          isValid = false;
        }

        if (!phone || phone.trim() === "") {
          setPhoneError("Phone number is required.");
          isValid = false;
        } else {
          // Phone format validation
          const phoneRegex = /^[\+]?[1-9][\d]{0,15}$/;
          if (!phoneRegex.test(phone.replace(/[\s\-\(\)]/g, ''))) {
            setPhoneError("Please enter a valid phone number.");
            isValid = false;
          }
        }

        if (!email || email.trim() === "") {
          setEmailError("Email is required.");
          isValid = false;
        } else {
          // Email format validation
          const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
          if (!emailRegex.test(email)) {
            setEmailError("Please enter a valid email address.");
            isValid = false;
          }
        }

        if (!address || address.trim() === "") {
          setAddressError("Address is required.");
          isValid = false;
        }

        if (!city || city.trim() === "") {
          setCityError("City is required.");
          isValid = false;
        }

        if (!country || !country.value || country.value === "") {
          setCountryError("Country is required.");
          isValid = false;
        }

        if (!caseDate || caseDate.trim() === "") {
          setCaseDateError("Case date is required.");
          isValid = false;
        }

        if (!caseType || caseType.trim() === "") {
          setCaseTypeError("Case type is required.");
          isValid = false;
        }

        if (!caseDescription || caseDescription.trim() === "") {
          setCaseDescriptionError("Case description is required.");
          isValid = false;
        }

        if (!coordinator || !coordinator.value || coordinator.value === "") {
          setCoordinatorError("Coordinator is required.");
          isValid = false;
        }

        if (!currencyCode || !currencyCode.value || currencyCode.value === "") {
          setCurrencyCodeError("Currency is required.");
          isValid = false;
        }

        if (!priceTotal || priceTotal === "" || priceTotal <= 0) {
          setPriceTotalError("Price total is required and must be greater than 0.");
          isValid = false;
        }

        // Optional field validations for Step 1
        if (birthDate && birthDate.trim() !== "") {
          const birthDateObj = new Date(birthDate);
          const today = new Date();
          if (birthDateObj > today) {
            setBirthDateError("Birth date cannot be in the future.");
            isValid = false;
          }
        }

      } else if (stepSelect === 1) {
        // Step 2: Coordination Details validation
        setCoordinatStatusListError("");

        // Coordination status validation
        if (!coordinatStatusList || coordinatStatusList.length === 0) {
          setCoordinatStatusListError("At least one coordination status is required.");
          isValid = false;
        }

      } else if (stepSelect === 2) {
        // Step 3: Medical Reports validation
        // No specific required validations for medical reports step
        // Files are optional

      } else if (stepSelect === 3) {
        // Step 4: Invoices validation
        setInvoiceNumberError("");
        setAmountError("");

        // Optional field validations for Step 4
        if (invoiceNumber && invoiceNumber.trim() !== "") {
          if (invoiceNumber.length < 3) {
            setInvoiceNumberError("Invoice number must be at least 3 characters.");
            isValid = false;
          }
        }

        if (amount && amount !== "" && amount < 0) {
          setAmountError("Amount cannot be negative.");
          isValid = false;
        }

      } else if (stepSelect === 4) {
        // Step 5: Insurance Authorization validation
        setInsuranceNumberError("");
        setPolicyNumberError("");

        // Optional field validations for Step 5
        if (insuranceNumber && insuranceNumber.trim() !== "") {
          if (insuranceNumber.length < 3) {
            setInsuranceNumberError("Insurance number must be at least 3 characters.");
            isValid = false;
          }
        }

        if (policyNumber && policyNumber.trim() !== "") {
          if (policyNumber.length < 3) {
            setPolicyNumberError("Policy number must be at least 3 characters.");
            isValid = false;
          }
        }
      }

      // If validation fails, show error and return
      if (!isValid) {
        toast.error("Please fix the validation errors before updating.");
        return;
      }

      setIsLoading(true);

      // Map assistance items with their provider services
      const assistanceItems = assistanceMultiSelect.map((item) => ({
        start_date: item.start_date,
        end_date: item.end_date,
        appointment_date: item.appointment_date,
        service_location: item.service_location,
        provider_services: item.provider_services.map((providerService) => ({
          provider: providerService.provider?.id,
          service: providerService.provider_service?.id,
          date: providerService.provider_date,
        })),
      }));

      const providerItems = providerMultiSelect.map((item) => ({
        service: item.service?.id,
        provider: item.provider?.id,
        date: item.date,
      }));

      // Update case with all current data
      await dispatch(
        updateCase(id, {
          first_name: firstName,
          last_name: lastName,
          full_name: firstName + " " + lastName,
          birth_day: birthDate ?? "",
          patient_phone: phone,
          patient_email: email,
          patient_address: address,
          patient_city: city,
          patient_country: country.value,
          //
          coordinator: coordinator.value ?? "",
          case_date: caseDate,
          case_type: caseType,
          case_type_item: caseType === "Medical" ? caseTypeItem : "",
          case_description: caseDescription,
          //
          status_coordination: coordinatStatus,
          case_status: coordinatStatusList,
          appointment_date: caseTypeItem === "Inpatient" ? "" : appointmentDate,
          start_date: caseTypeItem === "Inpatient" ? startDate : "",
          end_date: caseTypeItem === "Inpatient" ? endDate : "",
          service_location: serviceLocation,
          provider: providerName.value ?? "",
          //
          invoice_number: invoiceNumber,
          date_issued: dateIssued,
          invoice_amount: amount,
          assurance: insuranceCompany.value ?? "",
          assurance_number: insuranceNumber,
          policy_number: policyNumber,
          assurance_status: initialStatus,
          // files
          initial_medical_reports: filesInitialMedicalReports,
          upload_invoice: filesUploadInvoice,
          upload_authorization_documents: filesUploadAuthorizationDocuments,
          files_deleted: fileDeleted,
          providers: providerItems ?? [],
          assistances: assistanceItems ?? [],
          providers_deleted: providerMultiSelectDelete ?? [],
          assistance_deleted: assistanceMultiSelectDelete ?? [],
          //
          is_pay: isPay ? "True" : "False",
          price_tatal: priceTotal,
          currency_price: currencyCode.value ?? "",
        })
      );

      setIsLoading(false);
      toast.success("Case updated successfully!");
    } catch (error) {
      setIsLoading(false);
      toast.error("Failed to update case. Please try again.");
    }
  };

  return (
    <DefaultLayout>
      {/* Global Loading Indicator */}
      {isLoading && (
        <div className="fixed top-0 left-0 w-full h-full bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white p-5 rounded-lg shadow-lg flex flex-col items-center">
            <div className="animate-spin rounded-full h-16 w-16 border-t-4 border-b-4 border-[#0388A6] mb-3"></div>
            <div className="text-gray-700 font-medium">Loading data...</div>
          </div>
        </div>
      )}

      <div className="">
        <div className="flex flex-row text-sm items-center my-1">
          {/* home */}
          <a href="/dashboard">
            <div className="flex flex-row  items-center hover:text-black ">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                fill="none"
                viewBox="0 0 24 24"
                stroke-width="1.5"
                stroke="currentColor"
                className="w-4 h-4"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  d="m2.25 12 8.954-8.955c.44-.439 1.152-.439 1.591 0L21.75 12M4.5 9.75v10.125c0 .621.504 1.125 1.125 1.125H9.75v-4.875c0-.621.504-1.125 1.125-1.125h2.25c.621 0 1.125.504 1.125 1.125V21h4.125c.621 0 1.125-.504 1.125-1.125V9.75M8.25 21h8.25"
                />
              </svg>
              <span className="mx-1">Dashboard</span>
            </div>
          </a>
          <span>
            <svg
              xmlns="http://www.w3.org/2000/svg"
              fill="none"
              viewBox="0 0 24 24"
              stroke-width="1.5"
              stroke="currentColor"
              className="w-4 h-4"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                d="m8.25 4.5 7.5 7.5-7.5 7.5"
              />
            </svg>
          </span>
          <div className="">Edit Case</div>
        </div>
        {/*  */}
        <div className="py-5 px-4 flex justify-between">
          <h4 className=" uppercase font-semibold text-black dark:text-white">
            Edit Case
          </h4>
        </div>
        {/*  */}
        <div className="rounded-sm border border-stroke bg-white px-5 pt-6 pb-2.5 shadow-default dark:border-strokedark dark:bg-boxdark sm:px-7.5 xl:pb-1">
          <div className="flex md:flex-row flex-col">
            <div className="md:w-1/3 w-full flex md:flex-col flex-wrap px-3  relative">
              <div className="w-[1px] h-full absolute top-0 bg-[#0388A6] bg-opacity-60 left-[27.5px] md:block hidden"></div>
              {STEPSLIST?.map((step, index) => (
                <div
                  onClick={() => {
                    if (stepSelect > step.index && stepSelect !== 5) {
                      setStepSelect(step.index);
                    }
                  }}
                  className={`flex flex-row mb-3 md:min-h-20 ${
                    stepSelect > step.index && stepSelect !== 5
                      ? "cursor-pointer"
                      : ""
                  } md:items-start items-center`}
                >
                  {stepSelect < step.index ? (
                    <div className="size-8 bg-[#E2EFF2] z-10  rounded-full text-center items-center flex justify-center text-[#0388A6]">
                      <img
                        src={addreactionface}
                        className="size-5"
                        onError={(e) => {
                          e.target.onerror = null;
                          e.target.src = "/assets/placeholder.png";
                        }}
                      />
                    </div>
                  ) : stepSelect === step.index ? (
                    <div className="size-8 bg-white z-10  border-[11px] rounded-full"></div>
                  ) : (
                    <div className="size-8 bg-[#E2EFF2] z-10  rounded-full text-center items-center flex justify-center text-[#0388A6]">
                      <svg
                        xmlns="http://www.w3.org/2000/svg"
                        fill="none"
                        viewBox="0 0 24 24"
                        stroke-width="1.5"
                        stroke="currentColor"
                        className="size-5"
                      >
                        <path
                          stroke-linecap="round"
                          stroke-linejoin="round"
                          d="m4.5 12.75 6 6 9-13.5"
                        />
                      </svg>
                    </div>
                  )}

                  <div className="text-black flex-1 px-2">
                    <div className="font-medium text-sm">{step.title}</div>
                    {stepSelect === step.index ? (
                      <div className="text-xs font-light md:block hidden">
                        {step.description}
                      </div>
                    ) : null}
                  </div>
                </div>
              ))}
            </div>
            <div className="md:w-2/3 w-full  px-3 py-4  bg-[#F8F9FF]">
              {/* step 1 - General Information */}
              {stepSelect === 0 ? (
                <div className="">
                  <div className="text-[#0388A6] font-semibold text-xl">
                    General Information
                  </div>
                  {/* Patient Details: */}
                  <div className="text-xs font-medium mt-5 mb-2 text-black">
                    Patient Details:
                  </div>
                  <div className="my-2 bg-white py-4 px-2 rounded-md">
                    <div className="flex md:flex-row flex-col  ">
                      <div className="md:w-1/2 w-full  md:pr-1 my-1">
                        <div className="text-[#B4B4B4] text-xs  mb-1">
                          First Name <strong className="text-danger">*</strong>
                        </div>
                        <div>
                          <input
                            className={` outline-none border ${
                              firstNameError
                                ? "border-danger"
                                : "border-[#F1F3FF]"
                            } px-3 py-2 w-full rounded text-sm`}
                            type="text"
                            placeholder="First Name"
                            value={firstName}
                            onChange={(v) => setFirstName(v.target.value)}
                          />
                          <div className=" text-[8px] text-danger">
                            {firstNameError ? firstNameError : ""}
                          </div>
                        </div>
                      </div>
                      {/*  */}
                      <div className="md:w-1/2 w-full  md:pl-1 my-1">
                        <div className="text-[#B4B4B4] text-xs mb-1">
                          Last Name
                        </div>
                        <div>
                          <input
                            className=" outline-none border border-[#F1F3FF] px-3 py-2 w-full rounded text-sm"
                            type="text"
                            placeholder="Last Name"
                            value={lastName}
                            onChange={(v) => setLastName(v.target.value)}
                          />
                        </div>
                      </div>
                    </div>
                    {/*  */}
                    <div className="flex md:flex-row flex-col ">
                      <div className="md:w-1/2 w-full md:pr-1 my-1">
                        <div className="text-[#B4B4B4] text-xs  mb-1">
                          Email
                        </div>
                        <div>
                          <input
                            className={` outline-none border ${
                              emailError ? "border-danger" : "border-[#F1F3FF]"
                            } px-3 py-2 w-full rounded text-sm`}
                            type="email"
                            placeholder="Email Address"
                            value={email}
                            onChange={(v) => setEmail(v.target.value)}
                          />
                          <div className=" text-[8px] text-danger">
                            {emailError ? emailError : ""}
                          </div>
                        </div>
                      </div>
                      {/*  */}
                      <div className="md:w-1/2 w-full  md:pl-1 my-1">
                        <div className="text-[#B4B4B4] text-xs mb-1">
                          phone <strong className="text-danger">*</strong>
                        </div>
                        <div>
                          <input
                            className={`outline-none border ${
                              phoneError ? "border-danger" : "border-[#F1F3FF]"
                            } px-3 py-2 w-full rounded text-sm`}
                            type="text"
                            placeholder="Phone no"
                            value={phone}
                            onChange={(v) => setPhone(v.target.value)}
                          />
                          <div className=" text-[8px] text-danger">
                            {phoneError ? phoneError : ""}
                          </div>
                        </div>
                      </div>
                    </div>
                    {/*  */}
                    <div className="flex md:flex-row flex-col ">
                      <div className="w-full md:pr-1 my-1">
                        <div className="text-[#B4B4B4] text-xs  mb-1">
                          Country <strong className="text-danger">*</strong>
                        </div>
                        <div>
                          <Select
                            value={country}
                            onChange={(option) => {
                              setCountry(option);
                            }}
                            className="text-sm"
                            options={COUNTRIES.map((country) => ({
                              value: country.title,
                              label: (
                                <div
                                  className={`${
                                    country.title === "" ? "py-2" : ""
                                  } flex flex-row items-center`}
                                >
                                  <span className="mr-2">{country.icon}</span>
                                  <span>{country.title}</span>
                                </div>
                              ),
                            }))}
                            placeholder="Select a country..."
                            isSearchable
                            styles={{
                              control: (base, state) => ({
                                ...base,
                                background: "#fff",
                                border: countryError
                                  ? "1px solid #d34053"
                                  : "1px solid #F1F3FF",
                                boxShadow: state.isFocused ? "none" : "none",
                                "&:hover": {
                                  border: "1px solid #F1F3FF",
                                },
                              }),
                              option: (base) => ({
                                ...base,
                                display: "flex",
                                alignItems: "center",
                              }),
                              singleValue: (base) => ({
                                ...base,
                                display: "flex",
                                alignItems: "center",
                              }),
                            }}
                          />
                          <div className=" text-[8px] text-danger">
                            {countryError ? countryError : ""}
                          </div>
                        </div>
                      </div>
                      <div className="w-full md:pr-1 my-1">
                        <div className="text-[#B4B4B4] text-xs  mb-1">
                          City <strong className="text-danger">*</strong>
                        </div>
                        <div>
                          <GoogleComponent
                            apiKey="AIzaSyD5rhJGfXPbttVQzftoMPVjJOWbBvfcTFE"
                            className={` outline-none border ${
                              cityError ? "border-danger" : "border-[#F1F3FF]"
                            } px-3 py-2 w-full rounded text-sm`}
                            onChange={(v) => {
                              setCity(v.target.value);
                            }}
                            onPlaceSelected={(place) => {
                              if (place && place.geometry) {
                                setCity(place.formatted_address ?? "");
                                // setCityVl(place.formatted_address ?? "");
                                //   const latitude = place.geometry.location.lat();
                                //   const longitude = place.geometry.location.lng();
                                //   setLocationX(latitude ?? "");
                                //   setLocationY(longitude ?? "");
                              }
                            }}
                            defaultValue={city}
                            types={["city"]}
                            language="en"
                          />
                          {/* <input
                            className={` outline-none border ${
                              cityError ? "border-danger" : "border-[#F1F3FF]"
                            }  px-3 py-2 w-full rounded text-sm`}
                            type="text"
                            placeholder="City"
                            value={city}
                            onChange={(v) => setCity(v.target.value)}
                          /> */}
                          <div className=" text-[8px] text-danger">
                            {cityError ? cityError : ""}
                          </div>
                        </div>
                      </div>
                    </div>
                    {/*  */}
                    <div className="flex md:flex-row flex-col ">
                      <div className="md:w-1/2 w-full  md:pr-1 my-1">
                        <div className="text-[#B4B4B4] text-xs  mb-1">CIA</div>
                        <div>
                          <Select
                            value={insuranceCompany}
                            onChange={(option) => {
                              setInsuranceCompany(option);
                            }}
                            options={insurances?.map((assurance) => ({
                              value: assurance.id,
                              label: assurance.assurance_name || "",
                            }))}
                            filterOption={(option, inputValue) =>
                              option.label
                                .toLowerCase()
                                .includes(inputValue.toLowerCase())
                            }
                            className="text-sm"
                            placeholder="Select Insurance..."
                            isSearchable
                            styles={{
                              control: (base, state) => ({
                                ...base,
                                background: "#fff",
                                border: insuranceCompanyError
                                  ? "1px solid #d34053"
                                  : "1px solid #F1F3FF",
                                boxShadow: state.isFocused ? "none" : "none",
                                "&:hover": {
                                  border: "1px solid #F1F3FF",
                                },
                              }),
                              option: (base) => ({
                                ...base,
                                display: "flex",
                                alignItems: "center",
                              }),
                              singleValue: (base) => ({
                                ...base,
                                display: "flex",
                                alignItems: "center",
                              }),
                            }}
                          />
                          <div className=" text-[8px] text-danger">
                            {insuranceCompanyError ? insuranceCompanyError : ""}
                          </div>
                        </div>
                      </div>
                      <div className="md:w-1/2 w-full  md:pr-1 my-1">
                        <div className="text-[#B4B4B4] text-xs  mb-1">
                          CIA Reference
                        </div>
                        <div>
                          <input
                            className={` outline-none border ${
                              insuranceNumberError
                                ? "border-danger"
                                : "border-[#F1F3FF]"
                            }  px-3 py-2 w-full rounded text-sm`}
                            type="text"
                            placeholder="CIA Reference"
                            value={insuranceNumber}
                            onChange={(v) => setInsuranceNumber(v.target.value)}
                          />
                          <div className=" text-[8px] text-danger">
                            {insuranceNumberError ? insuranceNumberError : ""}
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                  {/* Case Details: */}
                  <div className="text-xs font-medium mt-5 mb-2 text-black">
                    Case Details:
                  </div>
                  <div className="my-2 bg-white py-4 px-2 rounded-md">
                    <div className="flex md:flex-row flex-col  ">
                      <div className=" w-full  md:pr-1 my-1">
                        <div className="text-[#B4B4B4] text-xs  mb-1">
                          Assigned Coordinator{" "}
                          <strong className="text-danger">*</strong>
                        </div>
                        <div>
                          <Select
                            value={coordinator}
                            onChange={(option) => {
                              setCoordinator(option);
                            }}
                            className="text-sm"
                            options={coordinators?.map((item) => ({
                              value: item.id,
                              label: item.full_name || "",
                            }))}
                            filterOption={(option, inputValue) =>
                              option.label
                                .toLowerCase()
                                .includes(inputValue.toLowerCase())
                            }
                            placeholder="Select Coordinator..."
                            isSearchable
                            styles={{
                              control: (base, state) => ({
                                ...base,
                                background: "#fff",
                                border: coordinatorError
                                  ? "1px solid #d34053"
                                  : "1px solid #F1F3FF",
                                boxShadow: state.isFocused ? "none" : "none",
                                "&:hover": {
                                  border: "1px solid #F1F3FF",
                                },
                              }),
                              option: (base) => ({
                                ...base,
                                display: "flex",
                                alignItems: "center",
                              }),
                              singleValue: (base) => ({
                                ...base,
                                display: "flex",
                                alignItems: "center",
                              }),
                            }}
                          />
                          <div className=" text-[8px] text-danger">
                            {coordinatorError ? coordinatorError : ""}
                          </div>
                        </div>
                      </div>
                      {/*  */}
                    </div>
                    {/*  */}
                    <div className="flex md:flex-row flex-col  ">
                      <div className="md:w-1/2 w-full  md:pr-1 my-1">
                        <div className="text-[#B4B4B4] text-xs mb-1">
                          Case Creation Date{" "}
                          <strong className="text-danger">*</strong>
                        </div>
                        <div>
                          <input
                            className={` outline-none border ${
                              caseDateError
                                ? "border-danger"
                                : "border-[#F1F3FF]"
                            } px-3 py-2 w-full rounded text-sm`}
                            type="date"
                            placeholder="Case Creation Date"
                            value={caseDate}
                            onChange={(v) => setCaseDate(v.target.value)}
                          />
                          <div className=" text-[8px] text-danger">
                            {caseDateError ? caseDateError : ""}
                          </div>
                        </div>
                      </div>
                      <div className="md:w-1/2  w-full  md:pl-1 my-1">
                        <div className="text-[#B4B4B4] text-xs  mb-1">
                          Type <strong className="text-danger">*</strong>
                        </div>
                        <div>
                          <select
                            value={caseType}
                            onChange={(v) => setCaseType(v.target.value)}
                            className={` outline-none border ${
                              caseTypeError
                                ? "border-danger"
                                : "border-[#F1F3FF]"
                            } px-3 py-3 w-full rounded text-sm`}
                          >
                            <option value={""}>Select Type</option>
                            <option value={"Medical"}>Medical</option>
                            <option value={"Technical"}>Technical</option>
                          </select>
                          <div className=" text-[8px] text-danger">
                            {caseTypeError ? caseTypeError : ""}
                          </div>
                        </div>
                      </div>
                    </div>
                    {caseType === "Medical" && (
                      <div className="md:w-1/2  w-full  md:pl-1 my-1">
                        <div className="text-[#B4B4B4] text-xs  mb-1">
                          Type Item <strong className="text-danger">*</strong>
                        </div>
                        <div>
                          <select
                            value={caseTypeItem}
                            onChange={(v) => setCaseTypeItem(v.target.value)}
                            className={` outline-none border ${
                              caseTypeItemError
                                ? "border-danger"
                                : "border-[#F1F3FF]"
                            } px-3 py-3 w-full rounded text-sm`}
                          >
                            <option value={""}>Select Type Item</option>
                            <option value={"Outpatient"}>Outpatient</option>
                            <option value={"Inpatient"}>Inpatient</option>
                          </select>
                          <div className=" text-[8px] text-danger">
                            {caseTypeItemError ? caseTypeItemError : ""}
                          </div>
                        </div>
                      </div>
                    )}
                    {/*  */}
                    <div className="flex md:flex-row flex-col ">
                      <div className="md:w-1/2 w-full  md:pr-1 my-1">
                        <div className="text-[#B4B4B4] text-xs  mb-1">
                          Currency Code{" "}
                          <strong className="text-danger">*</strong>
                        </div>
                        <div>
                          <Select
                            value={currencyCode}
                            onChange={(option) => {
                              setCurrencyCode(option);
                            }}
                            options={CURRENCYITEMS?.map((currency) => ({
                              value: currency.code,
                              label:
                                currency.name !== ""
                                  ? currency.name +
                                      " (" +
                                      currency.code +
                                      ") " || ""
                                  : "",
                            }))}
                            filterOption={(option, inputValue) =>
                              option.label
                                .toLowerCase()
                                .includes(inputValue.toLowerCase())
                            }
                            className="text-sm"
                            placeholder="Select Currency Code ..."
                            isSearchable
                            styles={{
                              control: (base, state) => ({
                                ...base,
                                background: "#fff",
                                border: currencyCodeError
                                  ? "1px solid #d34053"
                                  : "1px solid #F1F3FF",
                                boxShadow: state.isFocused ? "none" : "none",
                                "&:hover": {
                                  border: "1px solid #F1F3FF",
                                },
                              }),
                              option: (base) => ({
                                ...base,
                                display: "flex",
                                alignItems: "center",
                              }),
                              singleValue: (base) => ({
                                ...base,
                                display: "flex",
                                alignItems: "center",
                              }),
                            }}
                          />
                          <div className=" text-[8px] text-danger">
                            {currencyCodeError ? currencyCodeError : ""}
                          </div>
                        </div>
                      </div>
                      <div className="md:w-1/2 w-full  md:pl-1 my-1">
                        <div className="text-[#B4B4B4] text-xs  mb-1">
                          Price of service{" "}
                          <strong className="text-danger">*</strong>
                        </div>
                        <div>
                          <input
                            className={` outline-none border ${
                              priceTotalError
                                ? "border-danger"
                                : "border-[#F1F3FF]"
                            }  px-3 py-2 w-full rounded text-sm`}
                            type="number"
                            min={0}
                            step={0.01}
                            placeholder="0.00"
                            value={priceTotal}
                            onChange={(v) => setPriceTotal(v.target.value)}
                          />
                          <div className=" text-[8px] text-danger">
                            {priceTotalError ? priceTotalError : ""}
                          </div>
                        </div>
                      </div>
                    </div>
                    <div className="flex md:flex-row flex-col ">
                      <div className="md:w-1/2 w-full  md:pr-1 my-1">
                        <div>
                          <input
                            type={"checkbox"}
                            name="ispay"
                            id="ispay"
                            checked={isPay === true}
                            onChange={(v) => {
                              setIsPay(true);
                            }}
                          />
                          <label
                            className="mx-1 text-[#B4B4B4] text-sm  cursor-pointer"
                            for="ispay"
                          >
                            Paid
                          </label>
                        </div>
                      </div>
                      <div className="md:w-1/2 w-full  md:pr-1 my-1">
                        <div>
                          <input
                            type={"checkbox"}
                            name="notpay"
                            id="notpay"
                            checked={isPay === false}
                            onChange={(v) => {
                              setIsPay(false);
                            }}
                          />
                          <label
                            className="mx-1 text-[#B4B4B4] text-sm  cursor-pointer"
                            for="notpay"
                          >
                            Unpaid
                          </label>
                        </div>
                      </div>
                    </div>

                    {/*  */}
                    <div className="flex md:flex-row flex-col  ">
                      <div className=" w-full  md:pr-1 my-1">
                        <div className="text-[#B4B4B4] text-xs  mb-1">
                          Description
                        </div>
                        <div>
                          <textarea
                            value={caseDescription}
                            rows={5}
                            onChange={(v) => setCaseDescription(v.target.value)}
                            className=" outline-none border border-[#F1F3FF] px-3 py-2 w-full rounded text-sm"
                          ></textarea>
                        </div>
                      </div>
                    </div>
                  </div>

                  {/* Save & Continue - step 1 */}
                  <div className="flex flex-row items-center justify-end my-3 gap-3">
                    <button
                      onClick={handleUpdateCurrentStep}
                      disabled={loadingCaseUpdate}
                      className="bg-gray-100 hover:bg-gray-200 text-[#344054] text-sm px-5 py-3 rounded-full transition-colors duration-200 disabled:opacity-50"
                    >
                      {loadingCaseUpdate ? "Updating..." : "Update"}
                    </button>
                    <button
                      onClick={() => {
                        var check = true;
                        setFirstNameError("");
                        setLastNameError("");
                        setBirthDateError("");
                        setPhoneError("");
                        setEmailError("");
                        setAddressError("");
                        setCaseTypeError("");
                        setCaseTypeItemError("");
                        setCaseDateError("");
                        setCoordinatorError("");
                        setCityError("");
                        setCountryError("");
                        setCurrencyCodeError("");
                        setPriceTotalError("");

                        if (firstName === "") {
                          setFirstNameError("This field is required.");
                          check = false;
                        }

                        if (phone === "") {
                          setPhoneError("This field is required.");
                          check = false;
                        }

                        if (country === "" || country.value === "") {
                          setCountryError("This field is required.");
                          check = false;
                        }

                        if (coordinator === "" || coordinator.value === "") {
                          setCoordinatorError("This field is required.");
                          check = false;
                        }

                        if (caseType === "") {
                          setCaseTypeError("This field is required.");
                          check = false;
                        } else if (
                          caseType === "Medical" &&
                          caseTypeItem === ""
                        ) {
                          setCaseTypeItemError("This field is required.");
                          check = false;
                        }
                        if (caseDate === "") {
                          setCaseDateError("This field is required.");
                          check = false;
                        }
                        if (currencyCode === "" || currencyCode.value === "") {
                          setCurrencyCodeError("This field is required.");
                          check = false;
                        }
                        if (priceTotal === "") {
                          setPriceTotalError("This field is required.");
                          check = false;
                        }
                        if (check) {
                          setStepSelect(1);
                        } else {
                          toast.error(
                            "Some fields are empty or invalid. please try again"
                          );
                        }
                      }}
                      className="text-white bg-[#0388A6] text-sm px-5 py-3 rounded-full"
                    >
                      Save & Continue
                    </button>
                  </div>
                </div>
              ) : null}
              {/* step 2 */}
              {stepSelect === 1 ? (
                <div className="">
                  <div className="text-[#0388A6] font-semibold text-xl">
                    Coordination Details
                  </div>
                  {/* Initial Coordination Status: */}
                  <div className="text-xs font-medium mt-5 mb-2 text-black">
                    Initial Coordination Status:
                  </div>
                  <div className="my-2 bg-white py-4 px-2 rounded-md">
                    <div className="flex md:flex-row flex-col  ">
                      <div className="w-full  md:pr-1 my-1">
                        <div className="text-[#B4B4B4] text-xs  mb-1">
                          Status <strong className="text-danger">*</strong>
                        </div>
                        <div>
                          <div className="flex flex-wrap">
                            <div className="flex flex-row text-xs items-center my-3 text-danger">
                              <input
                                onChange={(v) => {
                                  if (
                                    !coordinatStatusList.includes(
                                      "pending-coordination"
                                    )
                                  ) {
                                    setCoordinatStatusList([
                                      ...coordinatStatusList,
                                      "pending-coordination",
                                    ]);
                                  } else {
                                    setCoordinatStatusList(
                                      coordinatStatusList.filter(
                                        (status) =>
                                          status !== "pending-coordination"
                                      )
                                    );
                                  }
                                }}
                                id="pending-coordination"
                                type={"checkbox"}
                                checked={coordinatStatusList.includes(
                                  "pending-coordination"
                                )}
                                className="mx-1"
                              />
                              <label
                                for="pending-coordination"
                                className="flex-1 mx-1  cursor-pointer "
                              >
                                Pending Coordination
                              </label>
                            </div>
                            <div className="flex flex-row text-xs items-center my-3 text-[#FFA500]">
                              <input
                                onChange={(v) => {
                                  if (
                                    !coordinatStatusList.includes(
                                      "coordinated-missing-m-r"
                                    )
                                  ) {
                                    setCoordinatStatusList([
                                      ...coordinatStatusList,
                                      "coordinated-missing-m-r",
                                    ]);
                                  } else {
                                    setCoordinatStatusList(
                                      coordinatStatusList.filter(
                                        (status) =>
                                          status !== "coordinated-missing-m-r"
                                      )
                                    );
                                  }
                                }}
                                checked={coordinatStatusList.includes(
                                  "coordinated-missing-m-r"
                                )}
                                id="coordinated-Missing-m-r"
                                type={"checkbox"}
                                className="mx-1"
                              />
                              <label
                                for="coordinated-Missing-m-r"
                                className="flex-1 mx-1  cursor-pointer "
                              >
                                Coordinated, Missing M.R.
                              </label>
                            </div>
                            <div className="flex flex-row text-xs items-center my-3 text-[#FFA500]">
                              <input
                                onChange={(v) => {
                                  if (
                                    !coordinatStatusList.includes(
                                      "coordinated-missing-invoice"
                                    )
                                  ) {
                                    setCoordinatStatusList([
                                      ...coordinatStatusList,
                                      "coordinated-missing-invoice",
                                    ]);
                                  } else {
                                    setCoordinatStatusList(
                                      coordinatStatusList.filter(
                                        (status) =>
                                          status !==
                                          "coordinated-missing-invoice"
                                      )
                                    );
                                  }
                                }}
                                checked={coordinatStatusList.includes(
                                  "coordinated-missing-invoice"
                                )}
                                id="coordinated-missing-invoice"
                                type={"checkbox"}
                                className="mx-1"
                              />
                              <label
                                for="coordinated-missing-invoice"
                                className="flex-1 mx-1  cursor-pointer "
                              >
                                Coordinated, Missing Invoice
                              </label>
                            </div>
                            <div className="flex flex-row text-xs items-center my-3 text-primary">
                              <input
                                onChange={(v) => {
                                  if (
                                    !coordinatStatusList.includes(
                                      "waiting-for-insurance-authorization"
                                    )
                                  ) {
                                    setCoordinatStatusList([
                                      ...coordinatStatusList,
                                      "waiting-for-insurance-authorization",
                                    ]);
                                  } else {
                                    setCoordinatStatusList(
                                      coordinatStatusList.filter(
                                        (status) =>
                                          status !==
                                          "waiting-for-insurance-authorization"
                                      )
                                    );
                                  }
                                }}
                                checked={coordinatStatusList.includes(
                                  "waiting-for-insurance-authorization"
                                )}
                                id="waiting-for-insurance-authorization"
                                type={"checkbox"}
                                className="mx-1"
                              />
                              <label
                                for="waiting-for-insurance-authorization"
                                className="flex-1 mx-1  cursor-pointer "
                              >
                                Waiting for Insurance Authorization
                              </label>
                            </div>
                            <div className="flex flex-row text-xs items-center my-3 text-primary">
                              <input
                                onChange={(v) => {
                                  if (
                                    !coordinatStatusList.includes(
                                      "coordinated-patient-not-seen-yet"
                                    )
                                  ) {
                                    setCoordinatStatusList([
                                      ...coordinatStatusList,
                                      "coordinated-patient-not-seen-yet",
                                    ]);
                                  } else {
                                    setCoordinatStatusList(
                                      coordinatStatusList.filter(
                                        (status) =>
                                          status !==
                                          "coordinated-patient-not-seen-yet"
                                      )
                                    );
                                  }
                                }}
                                checked={coordinatStatusList.includes(
                                  "coordinated-patient-not-seen-yet"
                                )}
                                id="coordinated-patient-not-seen-yet"
                                type={"checkbox"}
                                className="mx-1"
                              />
                              <label
                                for="coordinated-patient-not-seen-yet"
                                className="flex-1 mx-1  cursor-pointer "
                              >
                                Coordinated, Patient not seen yet
                              </label>
                            </div>
                            {/*  */}
                            <div className="flex flex-row text-xs items-center my-3 text-primary">
                              <input
                                onChange={(v) => {
                                  if (
                                    !coordinatStatusList.includes(
                                      "coordination-fee"
                                    )
                                  ) {
                                    setCoordinatStatusList([
                                      ...coordinatStatusList,
                                      "coordination-fee",
                                    ]);
                                  } else {
                                    setCoordinatStatusList(
                                      coordinatStatusList.filter(
                                        (status) =>
                                          status !== "coordination-fee"
                                      )
                                    );
                                  }
                                }}
                                checked={coordinatStatusList.includes(
                                  "coordination-fee"
                                )}
                                id="coordination-fee"
                                type={"checkbox"}
                                className="mx-1"
                              />
                              <label
                                for="coordination-fee"
                                className="flex-1 mx-1  cursor-pointer "
                              >
                                Coordination Fee
                              </label>
                            </div>
                            {/*  */}
                            <div className="flex flex-row text-xs items-center my-3 text-primary">
                              <input
                                onChange={(v) => {
                                  if (
                                    !coordinatStatusList.includes(
                                      "coordinated-missing-payment"
                                    )
                                  ) {
                                    setCoordinatStatusList([
                                      ...coordinatStatusList,
                                      "coordinated-missing-payment",
                                    ]);
                                  } else {
                                    setCoordinatStatusList(
                                      coordinatStatusList.filter(
                                        (status) =>
                                          status !==
                                          "coordinated-missing-payment"
                                      )
                                    );
                                  }
                                }}
                                checked={coordinatStatusList.includes(
                                  "coordinated-missing-payment"
                                )}
                                id="coordinated-missing-payment"
                                type={"checkbox"}
                                className="mx-1"
                              />
                              <label
                                for="coordinated-missing-payment"
                                className="flex-1 mx-1  cursor-pointer "
                              >
                                Coordinated, Missing Payment
                              </label>
                            </div>

                            {/*  */}
                            <div className="flex flex-row text-xs items-center my-3 text-[#008000]">
                              <input
                                onChange={(v) => {
                                  if (
                                    !coordinatStatusList.includes(
                                      "fully-coordinated"
                                    )
                                  ) {
                                    setCoordinatStatusList([
                                      ...coordinatStatusList,
                                      "fully-coordinated",
                                    ]);
                                  } else {
                                    setCoordinatStatusList(
                                      coordinatStatusList.filter(
                                        (status) =>
                                          status !== "fully-coordinated"
                                      )
                                    );
                                  }
                                }}
                                checked={coordinatStatusList.includes(
                                  "fully-coordinated"
                                )}
                                id="fully-coordinated"
                                type={"checkbox"}
                                className="mx-1"
                              />
                              <label
                                for="fully-coordinated"
                                className="flex-1 mx-1  cursor-pointer "
                              >
                                Fully Coordinated
                              </label>
                            </div>
                            <div className="flex flex-row text-xs items-center my-3 text-[#d34053]">
                              <input
                                onChange={(v) => {
                                  if (!coordinatStatusList.includes("failed")) {
                                    setCoordinatStatusList([
                                      ...coordinatStatusList,
                                      "failed",
                                    ]);
                                  } else {
                                    setCoordinatStatusList(
                                      coordinatStatusList.filter(
                                        (status) => status !== "failed"
                                      )
                                    );
                                  }
                                }}
                                checked={coordinatStatusList.includes("failed")}
                                id="failed"
                                type={"checkbox"}
                                className="mx-1"
                              />
                              <label
                                for="failed"
                                className="flex-1 mx-1  cursor-pointer "
                              >
                                Failed
                              </label>
                            </div>
                          </div>
                          {/* <select
                            value={coordinatStatus}
                            onChange={(v) => setCoordinatStatus(v.target.value)}
                            className={`outline-none border ${
                              coordinatStatusError
                                ? "border-danger"
                                : "border-[#F1F3FF]"
                            }  px-3 py-2 w-full rounded text-sm`}
                          >
                            <option value={""}>Select Status</option>
                            <option value={"pending-coordination"}>
                              Pending Coordination
                            </option>
                            <option value={"coordinated-missing-m-r"}>
                              Coordinated, Missing M.R.
                            </option>
                            <option value={"coordinated-missing-invoice"}>
                              Coordinated, Missing Invoice
                            </option>
                            <option
                              value={"waiting-for-insurance-authorization"}
                            >
                              Waiting for Insurance Authorization
                            </option>
                            <option value={"coordinated-patient-not-seen-yet"}>
                              Coordinated, Patient not seen yet
                            </option>
                            <option value={"fully-coordinated"}>
                              Fully Coordinated
                            </option>
                            <option value={"failed"}>Failed</option>
                          </select> */}
                          <div className=" text-[8px] text-danger">
                            {coordinatStatusListError
                              ? coordinatStatusListError
                              : ""}
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                  {/*  */}
                  <div className="text-xs font-medium mt-5 mb-2 text-black">
                    Assistance Details:
                  </div>

                  <div className="my-2 bg-white py-4 px-2 rounded-md">
                    {/* form add new assistance */}
                    <div className="text-xs font-medium mt-2 mb-2 text-black">
                      Add new Assistance Details:
                    </div>
                    <div className="mx-2 my-1 p-2 shadow rounded">
                      {/* Appointment Details: */}
                      <div className="text-xs font-medium mt-2 mb-2 text-black">
                        Appointment Details:
                      </div>
                      <div className="flex md:flex-row flex-col w-full ">
                        {caseType === "Medical" &&
                        caseTypeItem === "Inpatient" ? (
                          <div className="flex md:flex-row flex-col w-full">
                            <div className="md:w-1/2 w-full  md:pr-1 my-1">
                              <div className="text-[#B4B4B4] text-xs  mb-1">
                                Hospital Starting Date{" "}
                                <strong className="text-danger">*</strong>
                              </div>
                              <div>
                                <input
                                  type="date"
                                  className={` outline-none border ${
                                    startDateError
                                      ? "border-danger"
                                      : "border-[#F1F3FF]"
                                  } px-3 py-2 w-full rounded text-sm`}
                                  placeholder="Hospital Starting Date"
                                  value={startDate}
                                  onChange={(v) => {
                                    setStartDate(v.target.value);
                                    // If end date is earlier than new start date, update end date
                                    if (endDate && endDate < v.target.value) {
                                      setEndDate(v.target.value);
                                    }
                                  }}
                                />
                                <div className=" text-[8px] text-danger">
                                  {startDateError ? startDateError : ""}
                                </div>
                              </div>
                            </div>
                            <div className="md:w-1/2 w-full  md:pr-1 my-1">
                              <div className="text-[#B4B4B4] text-xs  mb-1">
                                Hospital Ending Date{" "}
                                <strong className="text-danger">*</strong>
                              </div>
                              <div>
                                <input
                                  type="date"
                                  className={` outline-none border ${
                                    endDateError
                                      ? "border-danger"
                                      : "border-[#F1F3FF]"
                                  } px-3 py-2 w-full rounded text-sm`}
                                  placeholder="Hospital Ending Date"
                                  value={endDate}
                                  onChange={(v) => setEndDate(v.target.value)}
                                  disabled={!startDate}
                                  min={startDate}
                                />
                                <div className=" text-[8px] text-danger">
                                  {endDateError ? endDateError : ""}
                                </div>
                              </div>
                            </div>
                          </div>
                        ) : (
                          <div className=" w-full  md:pr-1 my-1">
                            <div className="text-[#B4B4B4] text-xs  mb-1">
                              Appointment Date{" "}
                              <strong className="text-danger">*</strong>
                            </div>
                            <div>
                              <input
                                className={` outline-none border ${
                                  appointmentDateError
                                    ? "border-danger"
                                    : "border-[#F1F3FF]"
                                } px-3 py-2 w-full rounded text-sm`}
                                type="date"
                                placeholder="Appointment Date"
                                value={appointmentDate}
                                onChange={(v) =>
                                  setAppointmentDate(v.target.value)
                                }
                              />
                              <div className=" text-[8px] text-danger">
                                {appointmentDateError
                                  ? appointmentDateError
                                  : ""}
                              </div>
                            </div>
                          </div>
                        )}
                      </div>

                      <div className="flex md:flex-row flex-col  ">
                        {/*  */}
                        <div className="md:w-1/2 w-full  md:pr-1 my-1">
                          <div className="text-[#B4B4B4] text-xs  mb-1">
                            Service Location
                          </div>
                          <div>
                            <input
                              type="text"
                              className={` outline-none border ${
                                serviceLocationError
                                  ? "border-danger"
                                  : "border-[#F1F3FF]"
                              } px-3 py-2 w-full rounded text-sm`}
                              placeholder=" Service Location"
                              value={serviceLocation}
                              onChange={(v) =>
                                setServiceLocation(v.target.value)
                              }
                            />
                            <div className=" text-[8px] text-danger">
                              {serviceLocationError ? serviceLocationError : ""}
                            </div>
                          </div>
                        </div>
                      </div>

                      {/* Provider Information: */}
                      <div className="text-xs font-medium mt-5 mb-2 text-black">
                        Provider Information:
                      </div>
                      <div className="flex md:flex-row flex-col  ">
                        <div className="md:w-1/2  w-full  md:pr-1 my-1">
                          <div className="text-[#B4B4B4] text-xs  mb-1">
                            Provider Name{" "}
                            <strong className="text-danger">*</strong>
                          </div>
                          <div>
                            <Select
                              value={providerName}
                              onChange={(option) => {
                                setProviderName(option);
                                //
                                var initialProvider = option?.value ?? "";
                                // Show loading indicator while fetching provider services
                                setIsLoading(true);

                                const foundProvider = providers?.find(
                                  (item) => item.id === initialProvider
                                );
                                if (foundProvider) {
                                  setProviderServices(
                                    foundProvider.services ?? []
                                  );
                                  // Hide loading indicator after services are loaded
                                  setTimeout(() => {
                                    setIsLoading(false);
                                  }, 100);
                                } else {
                                  setProviderServices([]);
                                  setIsLoading(false);
                                }
                              }}
                              className="text-sm"
                              options={providers?.map((item) => ({
                                value: item.id,
                                label: item.full_name || "",
                              }))}
                              filterOption={(option, inputValue) =>
                                option.label
                                  ?.toLowerCase()
                                  .includes(inputValue?.toLowerCase())
                              }
                              placeholder="Select Provider..."
                              isSearchable
                              // Add loading indicator
                              isLoading={loadingProviders}
                              // Show loading indicator when menu opens
                              onMenuOpen={() => {
                                console.log("Provider dropdown opened");
                              }}
                              styles={{
                                control: (base, state) => ({
                                  ...base,
                                  background: "#fff",
                                  border: providerNameError
                                    ? "1px solid #d34053"
                                    : "1px solid #F1F3FF",
                                  boxShadow: state.isFocused ? "none" : "none",
                                  "&:hover": {
                                    border: "1px solid #F1F3FF",
                                  },
                                }),
                                option: (base) => ({
                                  ...base,
                                  display: "flex",
                                  alignItems: "center",
                                }),
                                singleValue: (base) => ({
                                  ...base,
                                  display: "flex",
                                  alignItems: "center",
                                }),
                              }}
                            />
                            <div className=" text-[8px] text-danger">
                              {providerNameError ? providerNameError : ""}
                            </div>
                          </div>
                        </div>
                        {/*  */}
                        <div className="md:w-1/2  w-full  md:pr-1 my-1">
                          <div className="text-[#B4B4B4] text-xs  mb-1">
                            Provider Service{" "}
                            <strong className="text-danger">*</strong>
                          </div>
                          <div>
                            <select
                              className={`outline-none border ${
                                providerServiceError
                                  ? "border-danger"
                                  : "border-[#F1F3FF]"
                              }  px-3 py-2 w-full rounded text-sm`}
                              onChange={(v) => {
                                setProviderService(v.target.value);
                              }}
                              value={providerService}
                            >
                              <option value={""}></option>
                              {providerServices?.map((service, index) => (
                                <option value={service.id}>
                                  {service.service_type ?? ""}
                                  {service.service_specialist !== ""
                                    ? " : " + service.service_specialist
                                    : ""}
                                </option>
                              ))}
                            </select>
                            <div className=" text-[8px] text-danger">
                              {providerServiceError ? providerServiceError : ""}
                            </div>
                          </div>
                        </div>
                      </div>
                      <div className="flex md:flex-row flex-col  ">
                        <div className=" w-full  md:pr-1 my-1">
                          <div className="text-[#B4B4B4] text-xs  mb-1">
                            Visit Date{" "}
                            <strong className="text-danger">*</strong>
                          </div>
                          <div>
                            <input
                              className={`outline-none border ${
                                providerDateError
                                  ? "border-danger"
                                  : "border-[#F1F3FF]"
                              }  px-3 py-2 w-full rounded text-sm`}
                              type="date"
                              placeholder=" Visit Date"
                              value={providerDate}
                              onChange={(v) => setProviderDate(v.target.value)}
                            />
                            <div className=" text-[8px] text-danger">
                              {providerDateError ? providerDateError : ""}
                            </div>
                          </div>
                        </div>
                      </div>
                      {/* add  */}
                      <div className="flex flex-col  ">
                        <button
                          onClick={() => {
                            // providerMultiSelect
                            var check = true;
                            setProviderNameError("");
                            setProviderServiceError("");
                            setProviderDateError("");
                            if (
                              providerName === "" ||
                              providerName.value === ""
                            ) {
                              setProviderNameError(
                                "These fields are required."
                              );
                              toast.error(" Provider is required");
                              check = false;
                            }
                            if (providerService === "") {
                              setProviderServiceError(
                                "These fields are required."
                              );
                              toast.error(" Provider Service is required");
                              check = false;
                            }
                            if (providerDate === "") {
                              setProviderDateError(
                                "These fields are required."
                              );
                              toast.error(" Visit Date is required");
                              check = false;
                            }

                            if (check) {
                              const exists = false;
                              // const exists = providerMultiSelect.some(
                              //   (provider) =>
                              //     String(provider?.provider?.id) ===
                              //       String(providerName.value) &&
                              //     String(provider?.service?.id) ===
                              //       String(providerService)
                              // );
                              const existsLast = false;

                              // const existsLast = providerMultiSelectLast.some(
                              //   (provider) =>
                              //     String(provider?.provider?.id) ===
                              //       String(providerName.value) &&
                              //     String(provider?.provider_service?.id) ===
                              //       String(providerService)
                              // );

                              if (!exists && !existsLast) {
                                // find provider
                                var initialProvider = providerName.value ?? "";
                                const foundProvider = providers?.find(
                                  (item) =>
                                    String(item.id) === String(initialProvider)
                                );
                                

                                if (foundProvider) {
                                  // found service
                                  var initialService = providerService ?? "";

                                  foundProvider?.services?.forEach(
                                    (element) => {
                                      console.log(element.id);
                                    }
                                  );

                                  const foundService =
                                    foundProvider?.services?.find(
                                      (item) =>
                                        String(item.id) ===
                                        String(initialService)
                                    );

                                  if (foundService) {
                                    // Add the new item if it doesn't exist
                                    setProviderMultiSelect([
                                      ...providerMultiSelect,
                                      {
                                        provider: foundProvider,
                                        service: foundService,
                                        date: providerDate,
                                      },
                                    ]);
                                    setProviderName("");
                                    setProviderService("");
                                    setProviderDate("");
                                    console.log(providerMultiSelect);
                                  } else {
                                    setProviderNameError(
                                      "This provider service not exist!"
                                    );
                                    toast.error(
                                      "This provider service not exist!"
                                    );
                                  }
                                } else {
                                  setProviderNameError(
                                    "This provider not exist!"
                                  );
                                  toast.error("This provider not exist!");
                                }
                              } else {
                                setProviderNameError(
                                  "This provider or service is already added!"
                                );
                                toast.error(
                                  "This provider or service is already added!"
                                );
                              }
                            }
                          }}
                          className="text-primary  flex flex-row items-center my-2 text-sm"
                        >
                          <svg
                            xmlns="http://www.w3.org/2000/svg"
                            fill="none"
                            viewBox="0 0 24 24"
                            stroke-width="1.5"
                            stroke="currentColor"
                            class="size-4"
                          >
                            <path
                              stroke-linecap="round"
                              stroke-linejoin="round"
                              d="M12 9v6m3-3H9m12 0a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z"
                            />
                          </svg>
                          <span> Add Provider </span>
                        </button>
                        {/* providers list added */}
                        {providerMultiSelect?.map((itemProvider, index) => (
                          <div
                            key={index}
                            className="flex flex-row items-center my-1"
                          >
                            <div className="min-w-6 text-center">
                              <button
                                onClick={() => {
                                  const updatedServices =
                                    providerMultiSelect.filter(
                                      (_, indexF) => indexF !== index
                                    );
                                  setProviderMultiSelect(updatedServices);
                                }}
                              >
                                <svg
                                  xmlns="http://www.w3.org/2000/svg"
                                  fill="none"
                                  viewBox="0 0 24 24"
                                  stroke-width="1.5"
                                  stroke="currentColor"
                                  class="size-6"
                                >
                                  <path
                                    stroke-linecap="round"
                                    stroke-linejoin="round"
                                    d="m9.75 9.75 4.5 4.5m0-4.5-4.5 4.5M21 12a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z"
                                  />
                                </svg>
                              </button>
                            </div>
                            <div className="flex-1 mx-1 border-l px-1">
                              <div>
                                <b>Provider:</b>{" "}
                                {itemProvider.provider?.full_name ?? "---"}
                              </div>
                              <div>
                                <b>Service:</b>{" "}
                                {itemProvider.service?.service_type ?? "--"}
                              </div>
                              <div>
                                <b>Speciality:</b>{" "}
                                {itemProvider.service?.service_specialist ??
                                  "---"}
                              </div>
                              <div>
                                <b>Date:</b> {itemProvider.date ?? "---"}
                              </div>
                            </div>
                          </div>
                        ))}
                        {/* end providers list added */}
                      </div>
                    </div>
                    <div>
                      <button
                        onClick={() => {
                          // Validate assistance fields
                          let isValid = true;
                          setStartDateError("");
                          setEndDateError("");
                          setAppointmentDateError("");
                          setServiceLocationError("");
                          setProviderNameError("");
                          // Check if we have the required appointment date information
                          if (
                            caseType === "Medical" &&
                            caseTypeItem === "Inpatient"
                          ) {
                            // For inpatient, check start and end dates
                            if (!startDate) {
                              setStartDateError(
                                "Hospital Starting Date is required"
                              );
                              toast.error("Hospital Starting Date is required");
                              isValid = false;
                            }
                            if (!endDate) {
                              setEndDateError(
                                "Hospital Ending Date is required"
                              );
                              toast.error("Hospital Ending Date is required");
                              isValid = false;
                            }
                          } else {
                            // For outpatient, check appointment date
                            if (!appointmentDate) {
                              setAppointmentDateError(
                                "Appointment Date is required"
                              );
                              toast.error("Appointment Date is required");
                              isValid = false;
                            }
                          }

                          // Check service location
                          // if (!serviceLocation) {
                          //   setServiceLocationError(
                          //     "Service Location is required"
                          //   );
                          //   toast.error("Service Location is required");
                          //   isValid = false;
                          // }

                          // Check if at least one provider is added
                          if (providerMultiSelect.length === 0) {
                            setProviderNameError(
                              "At least one provider must be added"
                            );
                            toast.error("At least one provider must be added");
                            isValid = false;
                          }

                          if (isValid) {
                            // Create new assistance object
                            const newAssistance = {
                              id: Date.now(), // Generate a temporary ID
                              start_date: startDate || null,
                              end_date: endDate || null,
                              appointment_date: appointmentDate || null,
                              service_location: serviceLocation??"",
                              provider_services: providerMultiSelect.map(
                                (item) => ({
                                  provider: item.provider,
                                  provider_service: item.service,
                                  provider_date: item.date,
                                })
                              ),
                            };

                            // Add to assistanceMultiSelect array
                            setAssistanceMultiSelect((prev) => [
                              ...prev,
                              newAssistance,
                            ]);

                            // Also add to assistanceMultiSelectLast for display
                            // setAssistanceMultiSelectLast(prev => [...prev, newAssistance]);

                            // Clear all input fields
                            setStartDate("");
                            setEndDate("");
                            setAppointmentDate("");
                            setServiceLocation("");
                            setProviderMultiSelect([]);
                            setProviderName("");
                            setProviderService("");
                            setProviderDate("");

                            toast.success("Assistance added successfully");
                          }
                        }}
                        className="bg-[#0388A6] text-white hover:bg-[#026e84] transition-colors duration-300 flex flex-row items-center justify-center py-2 px-4 rounded-md my-4 text-sm"
                      >
                        <svg
                          xmlns="http://www.w3.org/2000/svg"
                          fill="none"
                          viewBox="0 0 24 24"
                          strokeWidth="1.5"
                          stroke="currentColor"
                          className="size-4 mr-2"
                        >
                          <path
                            strokeLinecap="round"
                            strokeLinejoin="round"
                            d="M12 9v6m3-3H9m12 0a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z"
                          />
                        </svg>
                        <span>Add New Assistance</span>
                      </button>
                    </div>
                    {/* end form add new assistance */}
                    <div>
                      <div className=" w-full  md:pr-1 my-1">
                        <div className="flex items-center mb-3">
                          <div className="w-8 h-8 rounded-full bg-blue-50 flex items-center justify-center mr-2">
                            <svg
                              xmlns="http://www.w3.org/2000/svg"
                              fill="none"
                              viewBox="0 0 24 24"
                              strokeWidth={1.5}
                              stroke="#3C50E0"
                              className="w-4 h-4"
                            >
                              <path
                                strokeLinecap="round"
                                strokeLinejoin="round"
                                d="M9 12h3.75M9 15h3.75M9 18h3.75m3 .75H18a2.25 2.25 0 0 0 2.25-2.25V6.108c0-1.135-.845-2.098-1.976-2.192a48.424 48.424 0 0 0-1.123-.08m-5.801 0c-.065.21-.1.433-.1.664 0 .414.336.75.75.75h4.5a.75.75 0 0 0 .75-.75 2.25 2.25 0 0 0-.1-.664m-5.8 0A2.251 2.251 0 0 1 13.5 2.25H15c1.012 0 1.867.668 2.15 1.586m-5.8 0c-.376.023-.75.05-1.124.08C9.095 4.01 8.25 4.973 8.25 6.108V19.5a2.25 2.25 0 0 0 2.25 2.25h.75m0-3.75h3.75M9 15h3.75M9 12h3.75m3-3h.008v.008h-.008V9Z"
                              />
                            </svg>
                          </div>
                          <h4 className="text-sm font-semibold text-gray-800">
                            Assistances
                          </h4>
                        </div>

                        <div className="pl-10">
                          {/* last data */}
                          {assistanceMultiSelectLast?.length > 0 ? (
                            assistanceMultiSelectLast.map(
                              (itemAssistance, indexAssistance) => (
                                <div
                                  key={indexAssistance}
                                  className="bg-white rounded-lg shadow-sm border border-gray-100 mb-4 overflow-hidden hover:shadow-md transition-all duration-300"
                                >
                                  {/* Card Header */}
                                  <div className="bg-gradient-to-r from-[#F8FAFC] to-white px-4 py-3 flex justify-between items-center border-b border-gray-100">
                                    <h3 className="font-medium text-sm text-gray-800">
                                      Appointment #{indexAssistance + 1}
                                    </h3>
                                    <button
                                      onClick={() => {
                                        // Implement delete functionality here
                                        const updatedAssistances =
                                          assistanceMultiSelectLast.filter(
                                            (_, index) =>
                                              index !== indexAssistance
                                          );
                                        setAssistanceMultiSelectDelete([
                                          ...assistanceMultiSelectDelete,
                                          itemAssistance.id,
                                        ]);
                                        setAssistanceMultiSelectLast(
                                          updatedAssistances
                                        );
                                      }}
                                      className="text-gray-400 hover:text-red-500 transition-colors duration-200 p-1 rounded-full hover:bg-red-50"
                                      aria-label="Delete assistance"
                                    >
                                      <svg
                                        xmlns="http://www.w3.org/2000/svg"
                                        fill="none"
                                        viewBox="0 0 24 24"
                                        strokeWidth="1.5"
                                        stroke="currentColor"
                                        className="w-5 h-5"
                                      >
                                        <path
                                          strokeLinecap="round"
                                          strokeLinejoin="round"
                                          d="m9.75 9.75 4.5 4.5m0-4.5-4.5 4.5M21 12a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z"
                                        />
                                      </svg>
                                    </button>
                                  </div>

                                  {/* Card Content */}
                                  <div className="p-4">
                                    {/* Appointment Info Section */}
                                    <div className="mb-4">
                                      <div className="flex items-center mb-2">
                                        <div className="w-8 h-8 rounded-full bg-blue-50 flex items-center justify-center mr-2">
                                          <svg
                                            xmlns="http://www.w3.org/2000/svg"
                                            fill="none"
                                            viewBox="0 0 24 24"
                                            strokeWidth={1.5}
                                            stroke="#3C50E0"
                                            className="w-4 h-4"
                                          >
                                            <path
                                              strokeLinecap="round"
                                              strokeLinejoin="round"
                                              d="M6.75 3v2.25M17.25 3v2.25M3 18.75V7.5a2.25 2.25 0 0 1 2.25-2.25h13.5A2.25 2.25 0 0 1 21 7.5v11.25m-18 0A2.25 2.25 0 0 0 5.25 21h13.5A2.25 2.25 0 0 0 21 18.75m-18 0v-7.5A2.25 2.25 0 0 1 5.25 9h13.5A2.25 2.25 0 0 1 21 11.25v7.5"
                                            />
                                          </svg>
                                        </div>
                                        <h4 className="text-sm font-semibold text-gray-800">
                                          Appointment Info
                                        </h4>
                                      </div>

                                      <div className="ml-10 space-y-2">
                                        {caseType === "Medical" &&
                                        caseTypeItem === "Inpatient" ? (
                                          <>
                                            <div className="flex">
                                              <span className="text-xs text-gray-500 w-40">
                                                Hospital Starting Date:
                                              </span>
                                              <span className="text-xs font-medium">
                                                {itemAssistance.start_date ??
                                                  "---"}
                                              </span>
                                            </div>
                                            <div className="flex">
                                              <span className="text-xs text-gray-500 w-40">
                                                Hospital Ending Date:
                                              </span>
                                              <span className="text-xs font-medium">
                                                {itemAssistance.end_date ??
                                                  "---"}
                                              </span>
                                            </div>
                                          </>
                                        ) : (
                                          <div className="flex">
                                            <span className="text-xs text-gray-500 w-40">
                                              Appointment Date:
                                            </span>
                                            <span className="text-xs font-medium">
                                              {itemAssistance.appointment_date ??
                                                "---"}
                                            </span>
                                          </div>
                                        )}
                                        <div className="flex">
                                          <span className="text-xs text-gray-500 w-40">
                                            Service Location:
                                          </span>
                                          <span className="text-xs font-medium">
                                            {itemAssistance.service_location ??
                                              "---"}
                                          </span>
                                        </div>
                                      </div>
                                    </div>

                                    {/* Providers Section */}
                                    <div>
                                      <div className="flex items-center mb-2">
                                        <div className="w-8 h-8 rounded-full bg-purple-50 flex items-center justify-center mr-2">
                                          <svg
                                            xmlns="http://www.w3.org/2000/svg"
                                            fill="none"
                                            viewBox="0 0 24 24"
                                            strokeWidth={1.5}
                                            stroke="#7C3AED"
                                            className="w-4 h-4"
                                          >
                                            <path
                                              strokeLinecap="round"
                                              strokeLinejoin="round"
                                              d="M15.75 6a3.75 3.75 0 1 1-7.5 0 3.75 3.75 0 0 1 7.5 0ZM4.501 20.118a7.5 7.5 0 0 1 14.998 0A17.933 17.933 0 0 1 12 21.75c-2.676 0-5.216-.584-7.499-1.632Z"
                                            />
                                          </svg>
                                        </div>
                                        <h4 className="text-sm font-semibold text-gray-800">
                                          Providers
                                        </h4>
                                      </div>

                                      {itemAssistance.provider_services
                                        ?.length > 0 ? (
                                        <div className="ml-10 space-y-4">
                                          {itemAssistance.provider_services.map(
                                            (itemProvider, idx) => (
                                              <div
                                                key={idx}
                                                className="p-3 bg-gray-50 rounded-md border border-gray-100"
                                              >
                                                <div className="grid grid-cols-2 gap-2">
                                                  <div className="flex flex-col">
                                                    <span className="text-xs text-gray-500">
                                                      Provider
                                                    </span>
                                                    <span className="text-xs font-medium">
                                                      {itemProvider.provider
                                                        ?.full_name ?? "---"}
                                                    </span>
                                                  </div>
                                                  <div className="flex flex-col">
                                                    <span className="text-xs text-gray-500">
                                                      Service
                                                    </span>
                                                    <span className="text-xs font-medium">
                                                      {itemProvider
                                                        .provider_service
                                                        ?.service_type ?? "---"}
                                                    </span>
                                                  </div>
                                                  <div className="flex flex-col">
                                                    <span className="text-xs text-gray-500">
                                                      Speciality
                                                    </span>
                                                    <span className="text-xs font-medium">
                                                      {itemProvider
                                                        .provider_service
                                                        ?.service_specialist ??
                                                        "---"}
                                                    </span>
                                                  </div>
                                                  <div className="flex flex-col">
                                                    <span className="text-xs text-gray-500">
                                                      Date
                                                    </span>
                                                    <span className="text-xs font-medium">
                                                      {itemProvider.provider_date ??
                                                        "---"}
                                                    </span>
                                                  </div>
                                                </div>
                                              </div>
                                            )
                                          )}
                                        </div>
                                      ) : (
                                        <div className="ml-10 py-2 px-3 bg-gray-50 rounded-md text-xs text-gray-500">
                                          No providers assigned
                                        </div>
                                      )}
                                    </div>
                                  </div>
                                </div>
                              )
                            )
                          ) : (
                            <div className="py-3 px-4 bg-gray-50 rounded-md text-sm text-gray-500 text-center">
                              No assistances added yet
                            </div>
                          )}

                          {/* end last data */}

                          {/* new data */}
                          {assistanceMultiSelect?.length > 0 ? (
                            assistanceMultiSelect.map(
                              (itemAssistance, indexAssistance) => (
                                <div
                                  key={indexAssistance}
                                  className="bg-white rounded-lg shadow-sm border border-gray-100 mb-4 overflow-hidden hover:shadow-md transition-all duration-300"
                                >
                                  {/* Card Header */}
                                  <div className="bg-gradient-to-r from-[#F8FAFC] to-white px-4 py-3 flex justify-between items-center border-b border-gray-100">
                                    <h3 className="font-medium text-sm text-gray-800">
                                      Appointment #
                                      {indexAssistance +
                                        assistanceMultiSelectLast?.length +
                                        1}
                                    </h3>
                                    <button
                                      onClick={() => {
                                        const updatedServices =
                                          assistanceMultiSelect.filter(
                                            (_, indexF) =>
                                              indexF !== indexAssistance
                                          );
                                        setAssistanceMultiSelect(
                                          updatedServices
                                        );
                                        // Implement delete functionality here
                                        // const updatedAssistances = assistanceMultiSelectLast.filter(
                                        //   (_, index) => index !== indexAssistance
                                        // );
                                        // setAssistanceMultiSelectDelete([
                                        //   ...assistanceMultiSelectDelete,
                                        //   itemAssistance.id,
                                        // ]);
                                        // setAssistanceMultiSelectLast(updatedAssistances);
                                      }}
                                      className="text-gray-400 hover:text-red-500 transition-colors duration-200 p-1 rounded-full hover:bg-red-50"
                                      aria-label="Delete assistance"
                                    >
                                      <svg
                                        xmlns="http://www.w3.org/2000/svg"
                                        fill="none"
                                        viewBox="0 0 24 24"
                                        strokeWidth="1.5"
                                        stroke="currentColor"
                                        className="w-5 h-5"
                                      >
                                        <path
                                          strokeLinecap="round"
                                          strokeLinejoin="round"
                                          d="m9.75 9.75 4.5 4.5m0-4.5-4.5 4.5M21 12a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z"
                                        />
                                      </svg>
                                    </button>
                                  </div>

                                  {/* Card Content */}
                                  <div className="p-4">
                                    {/* Appointment Info Section */}
                                    <div className="mb-4">
                                      <div className="flex items-center mb-2">
                                        <div className="w-8 h-8 rounded-full bg-blue-50 flex items-center justify-center mr-2">
                                          <svg
                                            xmlns="http://www.w3.org/2000/svg"
                                            fill="none"
                                            viewBox="0 0 24 24"
                                            strokeWidth={1.5}
                                            stroke="#3C50E0"
                                            className="w-4 h-4"
                                          >
                                            <path
                                              strokeLinecap="round"
                                              strokeLinejoin="round"
                                              d="M6.75 3v2.25M17.25 3v2.25M3 18.75V7.5a2.25 2.25 0 0 1 2.25-2.25h13.5A2.25 2.25 0 0 1 21 7.5v11.25m-18 0A2.25 2.25 0 0 0 5.25 21h13.5A2.25 2.25 0 0 0 21 18.75m-18 0v-7.5A2.25 2.25 0 0 1 5.25 9h13.5A2.25 2.25 0 0 1 21 11.25v7.5"
                                            />
                                          </svg>
                                        </div>
                                        <h4 className="text-sm font-semibold text-gray-800">
                                          Appointment Info
                                        </h4>
                                      </div>

                                      <div className="ml-10 space-y-2">
                                        {caseType === "Medical" &&
                                        caseTypeItem === "Inpatient" ? (
                                          <>
                                            <div className="flex">
                                              <span className="text-xs text-gray-500 w-40">
                                                Hospital Starting Date:
                                              </span>
                                              <span className="text-xs font-medium">
                                                {itemAssistance.start_date ??
                                                  "---"}
                                              </span>
                                            </div>
                                            <div className="flex">
                                              <span className="text-xs text-gray-500 w-40">
                                                Hospital Ending Date:
                                              </span>
                                              <span className="text-xs font-medium">
                                                {itemAssistance.end_date ??
                                                  "---"}
                                              </span>
                                            </div>
                                          </>
                                        ) : (
                                          <div className="flex">
                                            <span className="text-xs text-gray-500 w-40">
                                              Appointment Date:
                                            </span>
                                            <span className="text-xs font-medium">
                                              {itemAssistance.appointment_date ??
                                                "---"}
                                            </span>
                                          </div>
                                        )}
                                        <div className="flex">
                                          <span className="text-xs text-gray-500 w-40">
                                            Service Location:
                                          </span>
                                          <span className="text-xs font-medium">
                                            {itemAssistance.service_location ??
                                              "---"}
                                          </span>
                                        </div>
                                      </div>
                                    </div>

                                    {/* Providers Section */}
                                    <div>
                                      <div className="flex items-center mb-2">
                                        <div className="w-8 h-8 rounded-full bg-purple-50 flex items-center justify-center mr-2">
                                          <svg
                                            xmlns="http://www.w3.org/2000/svg"
                                            fill="none"
                                            viewBox="0 0 24 24"
                                            strokeWidth={1.5}
                                            stroke="#7C3AED"
                                            className="w-4 h-4"
                                          >
                                            <path
                                              strokeLinecap="round"
                                              strokeLinejoin="round"
                                              d="M15.75 6a3.75 3.75 0 1 1-7.5 0 3.75 3.75 0 0 1 7.5 0ZM4.501 20.118a7.5 7.5 0 0 1 14.998 0A17.933 17.933 0 0 1 12 21.75c-2.676 0-5.216-.584-7.499-1.632Z"
                                            />
                                          </svg>
                                        </div>
                                        <h4 className="text-sm font-semibold text-gray-800">
                                          Providers
                                        </h4>
                                      </div>

                                      {itemAssistance.provider_services
                                        ?.length > 0 ? (
                                        <div className="ml-10 space-y-4">
                                          {itemAssistance.provider_services.map(
                                            (itemProvider, idx) => (
                                              <div
                                                key={idx}
                                                className="p-3 bg-gray-50 rounded-md border border-gray-100"
                                              >
                                                <div className="grid grid-cols-2 gap-2">
                                                  <div className="flex flex-col">
                                                    <span className="text-xs text-gray-500">
                                                      Provider
                                                    </span>
                                                    <span className="text-xs font-medium">
                                                      {itemProvider.provider
                                                        ?.full_name ?? "---"}
                                                    </span>
                                                  </div>
                                                  <div className="flex flex-col">
                                                    <span className="text-xs text-gray-500">
                                                      Service
                                                    </span>
                                                    <span className="text-xs font-medium">
                                                      {itemProvider
                                                        .provider_service
                                                        ?.service_type ?? "---"}
                                                    </span>
                                                  </div>
                                                  <div className="flex flex-col">
                                                    <span className="text-xs text-gray-500">
                                                      Speciality
                                                    </span>
                                                    <span className="text-xs font-medium">
                                                      {itemProvider
                                                        .provider_service
                                                        ?.service_specialist ??
                                                        "---"}
                                                    </span>
                                                  </div>
                                                  <div className="flex flex-col">
                                                    <span className="text-xs text-gray-500">
                                                      Date
                                                    </span>
                                                    <span className="text-xs font-medium">
                                                      {itemProvider.provider_date ??
                                                        "---"}
                                                    </span>
                                                  </div>
                                                </div>
                                              </div>
                                            )
                                          )}
                                        </div>
                                      ) : (
                                        <div className="ml-10 py-2 px-3 bg-gray-50 rounded-md text-xs text-gray-500">
                                          No providers assigned
                                        </div>
                                      )}
                                    </div>
                                  </div>
                                </div>
                              )
                            )
                          ) : (
                            <div className="py-3 px-4 bg-gray-50 rounded-md text-sm text-gray-500 text-center">
                              No new assistances added yet
                            </div>
                          )}

                          {/* end new data */}
                        </div>
                      </div>
                    </div>
                  </div>

                  {/* Save & Continue - step 2 */}
                  <div className="flex flex-row items-center justify-end my-3 gap-3">
                    <button
                      onClick={() => setStepSelect(0)}
                      className="bg-white text-[#0388A6] text-sm px-5 py-3 rounded-full"
                    >
                      Back
                    </button>
                    <button
                      onClick={handleUpdateCurrentStep}
                      disabled={loadingCaseUpdate}
                      className="bg-gray-100 hover:bg-gray-200 text-[#344054] text-sm px-5 py-3 rounded-full transition-colors duration-200 disabled:opacity-50"
                    >
                      {loadingCaseUpdate ? "Updating..." : "Update"}
                    </button>
                    <button
                      onClick={() => {
                        var check = true;
                        setCoordinatStatusError("");
                        setCoordinatStatusListError("");

                        if (coordinatStatusList.length === 0) {
                          toast.error(
                            "Initial Coordination Status empty or invalid. please try again"
                          );
                          setCoordinatStatusListError(
                            "Initial Coordination Status is required."
                          );
                          check = false;
                        }

                        if (check) {
                          setStepSelect(2);
                        } else {
                          toast.error(
                            "Some fields are empty or invalid. please try again"
                          );
                        }
                      }}
                      className="text-white bg-[#0388A6] text-sm px-5 py-3 rounded-full"
                    >
                      Save & Continue
                    </button>
                  </div>
                </div>
              ) : null}
              {/* step 3 */}
              {stepSelect === 2 ? (
                <div className="">
                  <div className="text-[#0388A6] font-semibold text-xl">
                    Medical Reports
                  </div>
                  {/* Initial Medical Reports: */}
                  <div className="text-xs font-medium mt-5 mb-2 text-black">
                    Initial Medical Reports:
                  </div>
                  <div className="my-2 bg-white py-4 px-2 rounded-md">
                    <div
                      {...getRootPropsInitialMedical({ className: "dropzone" })}
                      // style={dropzoneStyle}
                      className="bg-[#F5F6FF] w-full min-h-50 flex flex-col items-center justify-center"
                    >
                      <input {...getInputPropsInitialMedical()} />
                      <div className="my-2">
                        <svg
                          xmlns="http://www.w3.org/2000/svg"
                          fill="none"
                          viewBox="0 0 24 24"
                          stroke-width="1.5"
                          stroke="currentColor"
                          className="size-8 p-2 bg-[#0388A6] rounded-full text-white"
                        >
                          <path
                            stroke-linecap="round"
                            stroke-linejoin="round"
                            d="M3 16.5v2.25A2.25 2.25 0 0 0 5.25 21h13.5A2.25 2.25 0 0 0 21 18.75V16.5m-13.5-9L12 3m0 0 4.5 4.5M12 3v13.5"
                          />
                        </svg>
                      </div>
                      <div className="my-2">
                        Drag & Drop Image File or BROWSE
                      </div>
                    </div>
                    <aside style={thumbsContainer}>
                      <div className="w-full flex flex-col ">
                        {itemsInitialMedicalReports
                          ?.filter((file) => !fileDeleted.includes(file.id))
                          .map((file, index) => (
                            <div
                              className="bg-[#F3F5F7] rounded-xl px-5 py-5 w-full my-2 flex flex-row items-center"
                              key={file.file_name}
                            >
                              <div className=" rounded-full  bg-white text-[#81838E] text-center p-2 shadow-1 ">
                                <svg
                                  xmlns="http://www.w3.org/2000/svg"
                                  viewBox="0 0 24 24"
                                  fill="currentColor"
                                  class="size-4"
                                >
                                  <path d="M5.625 1.5c-1.036 0-1.875.84-1.875 1.875v17.25c0 1.035.84 1.875 1.875 1.875h12.75c1.035 0 1.875-.84 1.875-1.875V12.75A3.75 3.75 0 0 0 16.5 9h-1.875a1.875 1.875 0 0 1-1.875-1.875V5.25A3.75 3.75 0 0 0 9 1.5H5.625Z" />
                                  <path d="M12.971 1.816A5.23 5.23 0 0 1 14.25 5.25v1.875c0 .207.168.375.375.375H16.5a5.23 5.23 0 0 1 3.434 1.279 9.768 9.768 0 0 0-6.963-6.963Z" />
                                </svg>
                              </div>
                              <div className="flex-1 px-5 text-[#303030] text-sm">
                                <div className="whitespace-nowrap overflow-hidden text-ellipsis max-w-full flex-shrink text-xs">
                                  {file.file_name}
                                </div>
                                <div>
                                  {parseFloat(file.file_size).toFixed(2)} mb
                                </div>
                              </div>
                              <button
                                onClick={() => {
                                  setFileDeleted([...fileDeleted, file.id]);
                                }}
                                className="rounded-full p-1 bg-[#E6E7E8] font-bold text-[#9D9D9D]"
                              >
                                <svg
                                  xmlns="http://www.w3.org/2000/svg"
                                  fill="none"
                                  viewBox="0 0 24 24"
                                  stroke-width="1.5"
                                  stroke="currentColor"
                                  class="size-5"
                                >
                                  <path
                                    stroke-linecap="round"
                                    stroke-linejoin="round"
                                    d="M6 18 18 6M6 6l12 12"
                                  />
                                </svg>
                              </button>
                            </div>
                          ))}
                        {filesInitialMedicalReports?.map((file, index) => (
                          <div
                            className="bg-[#F3F5F7] rounded-xl px-5 py-5 w-full my-2 flex flex-row items-center"
                            key={file.name}
                          >
                            <div className=" rounded-full  bg-white text-[#81838E] text-center p-2 shadow-1 ">
                              <svg
                                xmlns="http://www.w3.org/2000/svg"
                                viewBox="0 0 24 24"
                                fill="currentColor"
                                class="size-4"
                              >
                                <path d="M5.625 1.5c-1.036 0-1.875.84-1.875 1.875v17.25c0 1.035.84 1.875 1.875 1.875h12.75c1.035 0 1.875-.84 1.875-1.875V12.75A3.75 3.75 0 0 0 16.5 9h-1.875a1.875 1.875 0 0 1-1.875-1.875V5.25A3.75 3.75 0 0 0 9 1.5H5.625Z" />
                                <path d="M12.971 1.816A5.23 5.23 0 0 1 14.25 5.25v1.875c0 .207.168.375.375.375H16.5a5.23 5.23 0 0 1 3.434 1.279 9.768 9.768 0 0 0-6.963-6.963Z" />
                              </svg>
                            </div>
                            <div className="flex-1 px-5 text-[#303030] text-sm">
                              <div className="whitespace-nowrap overflow-hidden text-ellipsis max-w-full flex-shrink text-xs">
                                {file.name}
                              </div>
                              <div>
                                {(file.size / (1024 * 1024)).toFixed(2)} mb
                              </div>
                            </div>
                            <button
                              onClick={() => {
                                setFilesInitialMedicalReports((prevFiles) =>
                                  prevFiles.filter(
                                    (_, indexToRemove) =>
                                      index !== indexToRemove
                                  )
                                );
                              }}
                              className="rounded-full p-1 bg-[#E6E7E8] font-bold text-[#9D9D9D]"
                            >
                              <svg
                                xmlns="http://www.w3.org/2000/svg"
                                fill="none"
                                viewBox="0 0 24 24"
                                stroke-width="1.5"
                                stroke="currentColor"
                                class="size-5"
                              >
                                <path
                                  stroke-linecap="round"
                                  stroke-linejoin="round"
                                  d="M6 18 18 6M6 6l12 12"
                                />
                              </svg>
                            </button>
                          </div>
                        ))}
                      </div>
                    </aside>
                  </div>
                  {/* Save & Continue - step 3 */}
                  <div className="flex flex-row items-center justify-end my-3 gap-3">
                    <button
                      onClick={() => setStepSelect(1)}
                      className="bg-white text-[#0388A6] text-sm px-5 py-3 rounded-full"
                    >
                      Back
                    </button>
                    <button
                      onClick={handleUpdateCurrentStep}
                      disabled={loadingCaseUpdate}
                      className="bg-gray-100 hover:bg-gray-200 text-[#344054] text-sm px-5 py-3 rounded-full transition-colors duration-200 disabled:opacity-50"
                    >
                      {loadingCaseUpdate ? "Updating..." : "Update"}
                    </button>
                    <button
                      onClick={() => setStepSelect(3)}
                      className="text-white bg-[#0388A6] text-sm px-5 py-3 rounded-full"
                    >
                      Save & Continue
                    </button>
                  </div>
                </div>
              ) : null}
              {/* step 4 */}
              {stepSelect === 3 ? (
                <div className="">
                  <div className="text-[#0388A6] font-semibold text-xl">
                    Invoices
                  </div>
                  {/* Invoice Information: */}
                  <div className="text-xs font-medium mt-5 mb-2 text-black">
                    Invoice Information:
                  </div>
                  <div className="my-2 bg-white py-4 px-2 rounded-md">
                    <div className="flex md:flex-row flex-col  ">
                      <div className="md:w-1/2 w-full  md:pr-1 my-1">
                        <div className="text-[#B4B4B4] text-xs  mb-1">
                          Invoice Number (Optional)
                        </div>
                        <div>
                          <input
                            className=" outline-none border border-[#F1F3FF] px-3 py-2 w-full rounded text-sm"
                            type="text"
                            placeholder="Invoice Number (Optional)"
                            value={invoiceNumber}
                            onChange={(v) => setInvoiceNumber(v.target.value)}
                          />
                        </div>
                      </div>
                      {/*  */}
                      <div className="md:w-1/2 w-full  md:pr-1 my-1">
                        <div className="text-[#B4B4B4] text-xs  mb-1">
                          Date Issued (Optional)
                        </div>
                        <div>
                          <input
                            className=" outline-none border border-[#F1F3FF] px-3 py-2 w-full rounded text-sm"
                            type="date"
                            placeholder="Date Issued (Optional)"
                            value={dateIssued}
                            onChange={(v) => setDateIssued(v.target.value)}
                          />
                        </div>
                      </div>
                    </div>
                    {/*  */}
                    <div className="flex md:flex-row flex-col  ">
                      <div className="w-full  md:pr-1 my-1">
                        <div className="text-[#B4B4B4] text-xs  mb-1">
                          Amount (Optional)
                        </div>
                        <div>
                          <input
                            className=" outline-none border border-[#F1F3FF] px-3 py-2 w-full rounded text-sm"
                            type="number"
                            placeholder="Amount (Optional)"
                            value={amount}
                            onChange={(v) => setAmount(v.target.value)}
                          />
                        </div>
                      </div>
                    </div>
                  </div>
                  <div className="text-xs font-medium mt-5 mb-2 text-black">
                    Upload Invoice
                  </div>
                  <div className="my-2 bg-white py-4 px-2 rounded-md">
                    <div
                      {...getRootPropsUploadInvoice({ className: "dropzone" })}
                      // style={dropzoneStyle}
                      className="bg-[#F5F6FF] w-full min-h-50 flex flex-col items-center justify-center"
                    >
                      <input {...getInputPropsUploadInvoice()} />
                      <div className="my-2">
                        <svg
                          xmlns="http://www.w3.org/2000/svg"
                          fill="none"
                          viewBox="0 0 24 24"
                          stroke-width="1.5"
                          stroke="currentColor"
                          className="size-8 p-2 bg-[#0388A6] rounded-full text-white"
                        >
                          <path
                            stroke-linecap="round"
                            stroke-linejoin="round"
                            d="M3 16.5v2.25A2.25 2.25 0 0 0 5.25 21h13.5A2.25 2.25 0 0 0 21 18.75V16.5m-13.5-9L12 3m0 0 4.5 4.5M12 3v13.5"
                          />
                        </svg>
                      </div>
                      <div className="my-2">
                        Drag & Drop Image File or BROWSE
                      </div>
                    </div>
                    <aside style={thumbsContainer}>
                      <div className="w-full flex flex-col ">
                        {itemsUploadInvoice
                          ?.filter((file) => !fileDeleted.includes(file.id))
                          .map((file, index) => (
                            <div
                              className="bg-[#F3F5F7] rounded-xl px-5 py-5 w-full my-2 flex flex-row items-center"
                              key={file.file_name}
                            >
                              <div className=" rounded-full  bg-white text-[#81838E] text-center p-2 shadow-1 ">
                                <svg
                                  xmlns="http://www.w3.org/2000/svg"
                                  viewBox="0 0 24 24"
                                  fill="currentColor"
                                  class="size-4"
                                >
                                  <path d="M5.625 1.5c-1.036 0-1.875.84-1.875 1.875v17.25c0 1.035.84 1.875 1.875 1.875h12.75c1.035 0 1.875-.84 1.875-1.875V12.75A3.75 3.75 0 0 0 16.5 9h-1.875a1.875 1.875 0 0 1-1.875-1.875V5.25A3.75 3.75 0 0 0 9 1.5H5.625Z" />
                                  <path d="M12.971 1.816A5.23 5.23 0 0 1 14.25 5.25v1.875c0 .207.168.375.375.375H16.5a5.23 5.23 0 0 1 3.434 1.279 9.768 9.768 0 0 0-6.963-6.963Z" />
                                </svg>
                              </div>
                              <div className="flex-1 px-5 text-[#303030] text-sm">
                                <div className="whitespace-nowrap overflow-hidden text-ellipsis max-w-full flex-shrink text-xs">
                                  {file.file_name}
                                </div>
                                <div>
                                  {parseFloat(file.file_size).toFixed(2)} mb
                                </div>
                              </div>
                              <button
                                onClick={() => {
                                  setFileDeleted([...fileDeleted, file.id]);
                                }}
                                className="rounded-full p-1 bg-[#E6E7E8] font-bold text-[#9D9D9D]"
                              >
                                <svg
                                  xmlns="http://www.w3.org/2000/svg"
                                  fill="none"
                                  viewBox="0 0 24 24"
                                  stroke-width="1.5"
                                  stroke="currentColor"
                                  class="size-5"
                                >
                                  <path
                                    stroke-linecap="round"
                                    stroke-linejoin="round"
                                    d="M6 18 18 6M6 6l12 12"
                                  />
                                </svg>
                              </button>
                            </div>
                          ))}
                        {filesUploadInvoice?.map((file, index) => (
                          <div
                            className="bg-[#F3F5F7] rounded-xl px-5 py-5 w-full my-2 flex flex-row items-center"
                            key={file.name}
                          >
                            <div className=" rounded-full  bg-white text-[#81838E] text-center p-2 shadow-1 ">
                              <svg
                                xmlns="http://www.w3.org/2000/svg"
                                viewBox="0 0 24 24"
                                fill="currentColor"
                                class="size-4"
                              >
                                <path d="M5.625 1.5c-1.036 0-1.875.84-1.875 1.875v17.25c0 1.035.84 1.875 1.875 1.875h12.75c1.035 0 1.875-.84 1.875-1.875V12.75A3.75 3.75 0 0 0 16.5 9h-1.875a1.875 1.875 0 0 1-1.875-1.875V5.25A3.75 3.75 0 0 0 9 1.5H5.625Z" />
                                <path d="M12.971 1.816A5.23 5.23 0 0 1 14.25 5.25v1.875c0 .207.168.375.375.375H16.5a5.23 5.23 0 0 1 3.434 1.279 9.768 9.768 0 0 0-6.963-6.963Z" />
                              </svg>
                            </div>
                            <div className="flex-1 px-5 text-[#303030] text-sm">
                              <div className="whitespace-nowrap overflow-hidden text-ellipsis max-w-full flex-shrink text-xs">
                                {file.name}
                              </div>
                              <div>
                                {(file.size / (1024 * 1024)).toFixed(2)} mb
                              </div>
                            </div>
                            <button
                              onClick={() => {
                                setFilesUploadInvoice((prevFiles) =>
                                  prevFiles.filter(
                                    (_, indexToRemove) =>
                                      index !== indexToRemove
                                  )
                                );
                              }}
                              className="rounded-full p-1 bg-[#E6E7E8] font-bold text-[#9D9D9D]"
                            >
                              <svg
                                xmlns="http://www.w3.org/2000/svg"
                                fill="none"
                                viewBox="0 0 24 24"
                                stroke-width="1.5"
                                stroke="currentColor"
                                class="size-5"
                              >
                                <path
                                  stroke-linecap="round"
                                  stroke-linejoin="round"
                                  d="M6 18 18 6M6 6l12 12"
                                />
                              </svg>
                            </button>
                          </div>
                        ))}
                      </div>
                    </aside>
                  </div>

                  {/* Save & Continue - step 4 */}
                  <div className="flex flex-row items-center justify-end my-3 gap-3">
                    <button
                      onClick={() => setStepSelect(2)}
                      className="bg-white text-[#0388A6] text-sm px-5 py-3 rounded-full"
                    >
                      Back
                    </button>
                    <button
                      onClick={handleUpdateCurrentStep}
                      disabled={loadingCaseUpdate}
                      className="bg-gray-100 hover:bg-gray-200 text-[#344054] text-sm px-5 py-3 rounded-full transition-colors duration-200 disabled:opacity-50"
                    >
                      {loadingCaseUpdate ? "Updating..." : "Update"}
                    </button>
                    <button
                      onClick={() => setStepSelect(4)}
                      className="text-white bg-[#0388A6] text-sm px-5 py-3 rounded-full"
                    >
                      Save & Continue
                    </button>
                  </div>
                </div>
              ) : null}
              {/* step 5 */}
              {stepSelect === 4 ? (
                <div className="">
                  <div className="text-[#0388A6] font-semibold text-xl">
                    Insurance Authorization
                  </div>
                  {/* Insurance Details: */}
                  <div className="text-xs font-medium mt-5 mb-2 text-black">
                    Insurance Details:
                  </div>
                  <div className="my-2 bg-white py-4 px-2 rounded-md">
                    <div className="flex md:flex-row flex-col  ">
                      <div className="md:w-1/2 w-full  md:pr-1 my-1">
                        <div className="text-[#B4B4B4] text-xs  mb-1">
                          Insurance Company Name
                        </div>
                        <div>
                          <Select
                            value={insuranceCompany}
                            onChange={(option) => {
                              setInsuranceCompany(option);
                            }}
                            options={insurances?.map((assurance) => ({
                              value: assurance.id,
                              label: assurance.assurance_name || "",
                            }))}
                            filterOption={(option, inputValue) =>
                              option.label
                                .toLowerCase()
                                .includes(inputValue.toLowerCase())
                            }
                            className="text-sm"
                            placeholder="Select Insurance..."
                            isSearchable
                            styles={{
                              control: (base, state) => ({
                                ...base,
                                background: "#fff",
                                border: insuranceCompanyError
                                  ? "1px solid #d34053"
                                  : "1px solid #F1F3FF",
                                boxShadow: state.isFocused ? "none" : "none",
                                "&:hover": {
                                  border: "1px solid #F1F3FF",
                                },
                              }),
                              option: (base) => ({
                                ...base,
                                display: "flex",
                                alignItems: "center",
                              }),
                              singleValue: (base) => ({
                                ...base,
                                display: "flex",
                                alignItems: "center",
                              }),
                            }}
                          />
                        </div>
                      </div>
                      {/*  */}
                      <div className="md:w-1/2 w-full  md:pr-1 my-1">
                        <div className="text-[#B4B4B4] text-xs  mb-1">
                          Policy Number
                        </div>
                        <div>
                          <input
                            className=" outline-none border border-[#F1F3FF] px-3 py-2 w-full rounded text-sm"
                            type="text"
                            placeholder="Policy Number"
                            value={policyNumber}
                            onChange={(v) => setPolicyNumber(v.target.value)}
                          />
                        </div>
                      </div>
                    </div>
                  </div>
                  {/* Authorization Status: */}
                  <div className="text-xs font-medium mt-5 mb-2 text-black">
                    Authorization Status:
                  </div>
                  <div className="my-2 bg-white py-4 px-2 rounded-md">
                    <div className="flex md:flex-row flex-col  ">
                      <div className="w-full  md:pr-1 my-1">
                        <div className="text-[#B4B4B4] text-xs  mb-1">
                          Initial Status
                        </div>
                        <div>
                          <select
                            value={initialStatus}
                            onChange={(v) => setInitialStatus(v.target.value)}
                            className=" outline-none border border-[#F1F3FF] px-3 py-2 w-full rounded text-sm"
                          >
                            <option value={""}>Select Status</option>
                            <option value={"Pending"}>Pending</option>
                            <option value={"Approved"}>Approved</option>
                            <option value={"Denied"}>Denied</option>
                          </select>
                        </div>
                      </div>
                    </div>
                  </div>
                  {/* Upload Authorization Documents */}
                  <div className="text-xs font-medium mt-5 mb-2 text-black">
                    Upload Authorization Documents
                  </div>
                  <div className="my-2 bg-white py-4 px-2 rounded-md">
                    <div
                      {...getRootPropsUploadAuthorizationDocuments({
                        className: "dropzone",
                      })}
                      // style={dropzoneStyle}
                      className="bg-[#F5F6FF] w-full min-h-50 flex flex-col items-center justify-center"
                    >
                      <input {...getInputPropsUploadAuthorizationDocuments()} />
                      <div className="my-2">
                        <svg
                          xmlns="http://www.w3.org/2000/svg"
                          fill="none"
                          viewBox="0 0 24 24"
                          stroke-width="1.5"
                          stroke="currentColor"
                          className="size-8 p-2 bg-[#0388A6] rounded-full text-white"
                        >
                          <path
                            stroke-linecap="round"
                            stroke-linejoin="round"
                            d="M3 16.5v2.25A2.25 2.25 0 0 0 5.25 21h13.5A2.25 2.25 0 0 0 21 18.75V16.5m-13.5-9L12 3m0 0 4.5 4.5M12 3v13.5"
                          />
                        </svg>
                      </div>
                      <div className="my-2">
                        Drag & Drop Image File or BROWSE
                      </div>
                    </div>
                    <aside style={thumbsContainer}>
                      <div className="w-full flex flex-col ">
                        {itemsUploadAuthorizationDocuments
                          ?.filter((file) => !fileDeleted.includes(file.id))
                          .map((file, index) => (
                            <div
                              className="bg-[#F3F5F7] rounded-xl px-5 py-5 w-full my-2 flex flex-row items-center"
                              key={file.file_name}
                            >
                              <div className=" rounded-full  bg-white text-[#81838E] text-center p-2 shadow-1 ">
                                <svg
                                  xmlns="http://www.w3.org/2000/svg"
                                  viewBox="0 0 24 24"
                                  fill="currentColor"
                                  class="size-4"
                                >
                                  <path d="M5.625 1.5c-1.036 0-1.875.84-1.875 1.875v17.25c0 1.035.84 1.875 1.875 1.875h12.75c1.035 0 1.875-.84 1.875-1.875V12.75A3.75 3.75 0 0 0 16.5 9h-1.875a1.875 1.875 0 0 1-1.875-1.875V5.25A3.75 3.75 0 0 0 9 1.5H5.625Z" />
                                  <path d="M12.971 1.816A5.23 5.23 0 0 1 14.25 5.25v1.875c0 .207.168.375.375.375H16.5a5.23 5.23 0 0 1 3.434 1.279 9.768 9.768 0 0 0-6.963-6.963Z" />
                                </svg>
                              </div>
                              <div className="flex-1 px-5 text-[#303030] text-sm">
                                <div className="whitespace-nowrap overflow-hidden text-ellipsis max-w-full flex-shrink text-xs">
                                  {file.file_name}
                                </div>
                                <div>
                                  {parseFloat(file.file_size).toFixed(2)} mb
                                </div>
                              </div>
                              <button
                                onClick={() => {
                                  setFileDeleted([...fileDeleted, file.id]);
                                }}
                                className="rounded-full p-1 bg-[#E6E7E8] font-bold text-[#9D9D9D]"
                              >
                                <svg
                                  xmlns="http://www.w3.org/2000/svg"
                                  fill="none"
                                  viewBox="0 0 24 24"
                                  stroke-width="1.5"
                                  stroke="currentColor"
                                  class="size-5"
                                >
                                  <path
                                    stroke-linecap="round"
                                    stroke-linejoin="round"
                                    d="M6 18 18 6M6 6l12 12"
                                  />
                                </svg>
                              </button>
                            </div>
                          ))}
                        {filesUploadAuthorizationDocuments?.map(
                          (file, index) => (
                            <div
                              className="bg-[#F3F5F7] rounded-xl px-5 py-5 w-full my-2 flex flex-row items-center"
                              key={file.name}
                            >
                              <div className=" rounded-full  bg-white text-[#81838E] text-center p-2 shadow-1 ">
                                <svg
                                  xmlns="http://www.w3.org/2000/svg"
                                  viewBox="0 0 24 24"
                                  fill="currentColor"
                                  class="size-4"
                                >
                                  <path d="M5.625 1.5c-1.036 0-1.875.84-1.875 1.875v17.25c0 1.035.84 1.875 1.875 1.875h12.75c1.035 0 1.875-.84 1.875-1.875V12.75A3.75 3.75 0 0 0 16.5 9h-1.875a1.875 1.875 0 0 1-1.875-1.875V5.25A3.75 3.75 0 0 0 9 1.5H5.625Z" />
                                  <path d="M12.971 1.816A5.23 5.23 0 0 1 14.25 5.25v1.875c0 .207.168.375.375.375H16.5a5.23 5.23 0 0 1 3.434 1.279 9.768 9.768 0 0 0-6.963-6.963Z" />
                                </svg>
                              </div>
                              <div className="flex-1 px-5 text-[#303030] text-sm">
                                <div className="whitespace-nowrap overflow-hidden text-ellipsis max-w-full flex-shrink text-xs">
                                  {file.name}
                                </div>
                                <div>
                                  {(file.size / (1024 * 1024)).toFixed(2)} mb
                                </div>
                              </div>
                              <button
                                onClick={() => {
                                  setFilesUploadAuthorizationDocuments(
                                    (prevFiles) =>
                                      prevFiles.filter(
                                        (_, indexToRemove) =>
                                          index !== indexToRemove
                                      )
                                  );
                                }}
                                className="rounded-full p-1 bg-[#E6E7E8] font-bold text-[#9D9D9D]"
                              >
                                <svg
                                  xmlns="http://www.w3.org/2000/svg"
                                  fill="none"
                                  viewBox="0 0 24 24"
                                  stroke-width="1.5"
                                  stroke="currentColor"
                                  class="size-5"
                                >
                                  <path
                                    stroke-linecap="round"
                                    stroke-linejoin="round"
                                    d="M6 18 18 6M6 6l12 12"
                                  />
                                </svg>
                              </button>
                            </div>
                          )
                        )}
                      </div>
                    </aside>
                  </div>
                  {/* Save & Continue - step 5 */}
                  <div className="flex flex-row items-center justify-end my-3 gap-3">
                    <button
                      onClick={() => setStepSelect(3)}
                      className="bg-white text-[#0388A6] text-sm px-5 py-3 rounded-full"
                    >
                      Back
                    </button>
                    <button
                      onClick={handleUpdateCurrentStep}
                      disabled={loadingCaseUpdate}
                      className="bg-gray-100 hover:bg-gray-200 text-[#344054] text-sm px-5 py-3 rounded-full transition-colors duration-200 disabled:opacity-50"
                    >
                      {loadingCaseUpdate ? "Updating..." : "Update"}
                    </button>
                    <button
                      disabled={loadingCaseUpdate}
                      onClick={async () => {
                        // Show loading indicator while submitting the form
                        setIsLoading(true);
                        setShouldNavigateToFinalStep(true); // Set flag to navigate to final step

                        // Map assistance items with their provider services
                        const assistanceItems = assistanceMultiSelect.map(
                          (item) => ({
                            start_date: item.start_date,
                            end_date: item.end_date,
                            appointment_date: item.appointment_date,
                            service_location: item.service_location,
                            provider_services: item.provider_services.map(
                              (providerService) => ({
                                provider: providerService.provider?.id,
                                service: providerService.provider_service?.id,
                                date: providerService.provider_date,
                              })
                            ),
                          })
                        );

                        const providerItems = providerMultiSelect.map(
                          (item) => ({
                            service: item.service?.id,
                            provider: item.provider?.id,
                            date: item.date,
                          })
                        );
                        // update
                        await dispatch(
                          updateCase(id, {
                            first_name: firstName,
                            last_name: lastName,
                            full_name: firstName + " " + lastName,
                            birth_day: birthDate ?? "",
                            patient_phone: phone,
                            patient_email: email,
                            patient_address: address,
                            patient_city: city,
                            patient_country: country.value,
                            //
                            coordinator: coordinator.value ?? "",
                            case_date: caseDate,
                            case_type: caseType,
                            case_type_item:
                              caseType === "Medical" ? caseTypeItem : "",
                            case_description: caseDescription,
                            //
                            status_coordination: coordinatStatus,
                            case_status: coordinatStatusList,
                            appointment_date:
                              caseTypeItem === "Inpatient"
                                ? ""
                                : appointmentDate,
                            start_date:
                              caseTypeItem === "Inpatient" ? startDate : "",
                            end_date:
                              caseTypeItem === "Inpatient" ? endDate : "",
                            service_location: serviceLocation,
                            provider: providerName.value ?? "",
                            //
                            invoice_number: invoiceNumber,
                            date_issued: dateIssued,
                            invoice_amount: amount,
                            assurance: insuranceCompany.value ?? "",
                            assurance_number: insuranceNumber,
                            policy_number: policyNumber,
                            assurance_status: initialStatus,
                            // files
                            initial_medical_reports: filesInitialMedicalReports,
                            upload_invoice: filesUploadInvoice,
                            upload_authorization_documents:
                              filesUploadAuthorizationDocuments,
                            files_deleted: fileDeleted,
                            providers: providerItems ?? [],
                            assistances: assistanceItems ?? [],
                            providers_deleted: providerMultiSelectDelete ?? [],
                            assistance_deleted:
                              assistanceMultiSelectDelete ?? [],
                            //
                            is_pay: isPay ? "True" : "False",
                            price_tatal: priceTotal,
                            currency_price: currencyCode.value ?? "",
                          })
                        );
                      }}
                      className="text-white bg-[#0388A6] text-sm px-5 py-3 rounded-full"
                    >
                      {loadingCaseUpdate ? "Loading.." : "Save & Complete"}
                    </button>
                  </div>
                </div>
              ) : null}
              {/* step 6 */}
              {stepSelect === 5 ? (
                <div className="">
                  <div className="my-2 bg-white py-4 px-2 rounded-md">
                    <div className="min-h-30 flex flex-col items-center justify-center">
                      <svg
                        xmlns="http://www.w3.org/2000/svg"
                        fill="none"
                        viewBox="0 0 24 24"
                        stroke-width="1.5"
                        stroke="currentColor"
                        className="size-13 p-3 rounded-full text-[#0388A6] bg-[#0388A6] bg-opacity-10"
                      >
                        <path
                          stroke-linecap="round"
                          stroke-linejoin="round"
                          d="m4.5 12.75 6 6 9-13.5"
                        />
                      </svg>
                      <div className="my-5 font-semibold text-2xl text-black">
                        Case Updated Successfully!
                      </div>
                      <div className="text-base text-center md:w-2/3 mx-auto w-full px-3">
                        Your case has been successfully updates and saved. You
                        can now view the case details or create another case.
                      </div>
                      <div className="flex flex-row items-center justify-end my-3">
                        {/* <button
                          onClick={() => {
                            setStepSelect(4);
                          }}
                          className="text-white bg-[#0388A6] text-sm px-5 py-3 rounded-full"
                        >
                          Go to Dahboard
                        </button> */}
                        <a
                          href="/dashboard"
                          className="text-white bg-[#0388A6] text-sm px-5 py-3 rounded-full"
                        >
                          Go to Dahboard
                        </a>
                      </div>
                    </div>
                  </div>
                </div>
              ) : null}
            </div>
          </div>
        </div>
      </div>
    </DefaultLayout>
  );
}

export default EditCaseScreen;
